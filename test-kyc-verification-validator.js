/**
 * Test script for the KYC verification validator
 * 
 * This script demonstrates the KYC verification validator functionality
 * by testing various binary patterns and operators.
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

/**
 * Test the KYC verification validator with different scenarios
 */
async function testKycVerificationValidator() {
  console.log('🧪 Testing KYC Verification Validator...\n');

  // Test scenarios
  const testScenarios = [
    {
      name: 'Exact match - Email and Phone only (0110)',
      pattern: '0110',
      operator: 'eq',
      description: 'Customer must have exactly phone and identity verification, no email or address'
    },
    {
      name: 'At least - Phone and Address (0101)',
      pattern: '0101',
      operator: 'gte',
      description: 'Customer must have at least phone and address verification (can have others too)'
    },
    {
      name: 'Exact match - All KYC verifications (1111)',
      pattern: '1111',
      operator: 'eq',
      description: 'Customer must have all 4 KYC verifications exactly'
    },
    {
      name: 'At least - Email only (1000)',
      pattern: '1000',
      operator: 'gte',
      description: 'Customer must have at least email verification'
    },
    {
      name: 'Exact match - No KYC verifications (0000)',
      pattern: '0000',
      operator: 'eq',
      description: 'Customer must have no KYC verifications at all'
    }
  ];

  for (const scenario of testScenarios) {
    console.log(`📋 Testing: ${scenario.name}`);
    console.log(`   Pattern: ${scenario.pattern} (Email:${scenario.pattern[0]}, Phone:${scenario.pattern[1]}, Identity:${scenario.pattern[2]}, Address:${scenario.pattern[3]})`);
    console.log(`   Operator: ${scenario.operator}`);
    console.log(`   Description: ${scenario.description}`);
    
    // Create a test bonus rule for this scenario
    const testPayload = {
      customerId: 1, // Test customer ID
      bonusId: 1,    // Test bonus ID
      testRule: {
        criterium: 'kycVerification',
        firstOperand: scenario.pattern,
        secondOperand: null,
        operator: scenario.operator,
        startsAt: new Date().toISOString(),
        endsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
      }
    };

    try {
      console.log('   📤 Sending test request...');
      
      const response = await fetch(`${BASE_URL}/debug/test-bonus-rule-checker`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload),
      });

      const responseData = await response.json();
      
      if (response.ok && responseData.success) {
        console.log(`   ✅ Test completed successfully`);
        console.log(`   📊 Result: ${responseData.data?.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'}`);
        if (responseData.data?.reason) {
          console.log(`   📝 Reason: ${responseData.data.reason}`);
        }
      } else {
        console.log(`   ❌ Test failed: ${responseData.message || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Test error: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }
}

/**
 * Test invalid scenarios to ensure proper error handling
 */
async function testInvalidScenarios() {
  console.log('🧪 Testing Invalid Scenarios...\n');

  const invalidScenarios = [
    {
      name: 'Invalid binary pattern - too short',
      pattern: '101',
      operator: 'eq',
      expectedError: 'Invalid binary string format'
    },
    {
      name: 'Invalid binary pattern - too long',
      pattern: '11011',
      operator: 'eq',
      expectedError: 'Invalid binary string format'
    },
    {
      name: 'Invalid binary pattern - contains non-binary characters',
      pattern: '1a01',
      operator: 'eq',
      expectedError: 'Invalid binary string format'
    },
    {
      name: 'Unsupported operator - greater than',
      pattern: '1010',
      operator: 'gt',
      expectedError: 'Unsupported operator'
    },
    {
      name: 'Unsupported operator - less than',
      pattern: '1010',
      operator: 'lt',
      expectedError: 'Unsupported operator'
    },
    {
      name: 'Missing first operand',
      pattern: null,
      operator: 'eq',
      expectedError: 'Missing first operand'
    }
  ];

  for (const scenario of invalidScenarios) {
    console.log(`📋 Testing: ${scenario.name}`);
    console.log(`   Pattern: ${scenario.pattern}`);
    console.log(`   Operator: ${scenario.operator}`);
    console.log(`   Expected Error: ${scenario.expectedError}`);
    
    const testPayload = {
      customerId: 1,
      bonusId: 1,
      testRule: {
        criterium: 'kycVerification',
        firstOperand: scenario.pattern,
        secondOperand: null,
        operator: scenario.operator,
        startsAt: new Date().toISOString(),
        endsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }
    };

    try {
      const response = await fetch(`${BASE_URL}/debug/test-bonus-rule-checker`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload),
      });

      const responseData = await response.json();
      
      if (!responseData.success && responseData.data?.reason?.includes(scenario.expectedError)) {
        console.log(`   ✅ Correctly handled invalid scenario`);
      } else {
        console.log(`   ⚠️ Unexpected response for invalid scenario`);
        console.log(`   📊 Response: ${JSON.stringify(responseData, null, 2)}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Test error: ${error.message}`);
    }
    
    console.log('');
  }
}

/**
 * Display KYC pattern explanation
 */
function displayKycPatternExplanation() {
  console.log('📚 KYC Verification Pattern Explanation');
  console.log('=' .repeat(50));
  console.log('Each position in the 4-digit binary string represents:');
  console.log('Position 1 (leftmost):  Email verification    (1 = verified, 0 = not verified)');
  console.log('Position 2:             Phone verification    (1 = verified, 0 = not verified)');
  console.log('Position 3:             Identity verification (1 = verified, 0 = not verified)');
  console.log('Position 4 (rightmost): Address verification  (1 = verified, 0 = not verified)');
  console.log('');
  console.log('Examples:');
  console.log('1111 = All verifications completed');
  console.log('1000 = Only email verified');
  console.log('0110 = Phone and identity verified');
  console.log('0101 = Phone and address verified');
  console.log('0000 = No verifications completed');
  console.log('');
  console.log('Operators:');
  console.log('eq  (equals)              = Exact match required');
  console.log('gte (greater than equal)  = At least the specified verifications required');
  console.log('');
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting KYC Verification Validator Tests...\n');
  
  displayKycPatternExplanation();
  
  await testKycVerificationValidator();
  await testInvalidScenarios();
  
  console.log('🏁 All KYC verification validator tests completed!');
}

// Run the tests
runAllTests().catch(console.error);
