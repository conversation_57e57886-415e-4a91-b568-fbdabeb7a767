/**
 * Test script for the bonus rule checker debug endpoint
 * 
 * This script tests the POST /debug/test-bonus-rule-checker endpoint
 * to ensure it properly simulates the bonus rule checker functionality.
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testBonusRuleChecker() {
  console.log('🧪 Testing POST /debug/test-bonus-rule-checker endpoint...');

  const testPayload = {
    customerId: 1,
    bonusId: 1
  };

  try {
    const response = await fetch(`${BASE_URL}/debug/test-bonus-rule-checker`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    });

    const responseData = await response.json();

    console.log('\n📊 Response Status:', response.status);
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()));
    console.log('📊 Response Body:', JSON.stringify(responseData, null, 2));

    if (response.ok) {
      console.log('\n✅ Test passed! Bonus rule checker debug endpoint is working.');
      
      if (responseData.data && responseData.data.result) {
        console.log('\n🎯 Bonus Rule Check Result:');
        console.log(`   - Customer ID: ${responseData.data.customerId}`);
        console.log(`   - Bonus ID: ${responseData.data.bonusId}`);
        console.log(`   - Is Eligible: ${responseData.data.result.isEligible}`);
        console.log(`   - Reasons: ${responseData.data.result.reasons ? responseData.data.result.reasons.join('; ') : 'N/A'}`);
        console.log(`   - Supported Bonus Types: ${responseData.data.supportedBonusTypes.join(', ')}`);
      }
    } else {
      console.log('\n❌ Test failed! Response indicates an error.');
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
  }
}

async function testWithInvalidData() {
  console.log('\n🧪 Testing with invalid data...');

  const invalidPayloads = [
    { customerId: 'invalid', bonusId: 1 },
    { customerId: 1 },
    { bonusId: 1 },
    {}
  ];

  for (const payload of invalidPayloads) {
    try {
      console.log(`\n📤 Testing payload: ${JSON.stringify(payload)}`);
      
      const response = await fetch(`${BASE_URL}/debug/test-bonus-rule-checker`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const responseData = await response.json();
      
      console.log(`📊 Status: ${response.status}`);
      console.log(`📊 Response: ${JSON.stringify(responseData, null, 2)}`);
      
      if (response.status === 500 && responseData.success === false) {
        console.log('✅ Correctly handled invalid data');
      } else {
        console.log('⚠️  Unexpected response for invalid data');
      }
      
    } catch (error) {
      console.error('❌ Error testing invalid payload:', error.message);
    }
  }
}

async function runAllTests() {
  console.log('🚀 Starting bonus rule checker debug endpoint tests...\n');
  
  await testBonusRuleChecker();
  await testWithInvalidData();
  
  console.log('\n🏁 All tests completed!');
}

// Run the tests
runAllTests().catch(console.error);
