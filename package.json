{"name": "bo-panel-api", "version": "1.0.0", "main": "dist/src/server.js", "license": "MIT", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "concurrently \"swc src -w -d dist\" \"nodemon dist/src/server.js\"", "build": "swc src -d dist", "start": "node dist/src/server.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "migration:create": "typeorm migration:create"}, "dependencies": {"@slack/web-api": "^7.9.3", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "amqplib": "^0.10.8", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "fast-xml-parser": "^5.2.5", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.6", "node-html-parser": "^7.0.1", "otplib": "^12.0.1", "pg": "^8.16.2", "reflect-metadata": "^0.2.2", "totp-generator": "^1.0.0", "typeorm": "^0.3.25", "websocket": "^1.0.35"}, "devDependencies": {"@swc/cli": "^0.7.7", "@swc/core": "^1.11.29", "@types/amqplib": "^0.10.7", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.29", "@types/node-fetch": "^2.6.12", "@types/pg": "^8.15.4", "@types/websocket": "^1.0.10", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}