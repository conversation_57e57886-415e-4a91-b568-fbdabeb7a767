import { DataSource } from 'typeorm';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

if (fs.existsSync('/mnt/aws-secrets/secret.properties')) {
  const secrets = JSON.parse(fs.readFileSync('/mnt/aws-secrets/secret.properties').toString());

  for (const key in secrets) {
    process.env[key] = secrets[key];
  }
}

export const AppDataSource = new DataSource({
  type: 'postgres',
  url: process.env['POSTGRES_URI'] || '',
  // synchronize: process.env.NODE_ENV === 'development', // Only sync in development
  // synchronize: true,
  migrationsRun: true,
  logging: process.env['NODE_ENV'] === 'development',
  entities: [
    // Entity files will be added here as we create them
    __dirname + '/../entities/**/*.{ts,js}',
  ],
  migrations: [__dirname + '/../migrations/**/*.{ts,js}'],
  subscribers: [__dirname + '/../subscribers/**/*.{ts,js}'],
});
