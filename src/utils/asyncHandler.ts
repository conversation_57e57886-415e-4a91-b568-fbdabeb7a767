import { Request, Response, NextFunction } from 'express';

// Utility function to wrap async route handlers and catch errors
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((err) => {
      if (err.errors && Array.isArray(err.errors)) {
        const message = err.errors.map((e: any) => e.message).join(', ');
        next(new Error(message));
      } else if (!err.message) {
        const message = 'Internal Server Error';
        next(new Error(message));
      } else {
        next(err);
      }
    });
  };
};

// Utility function to create standardized API responses
export const createResponse = <T>(success: boolean, message: string, data?: T, _statusCode: number = 200) => {
  return {
    success,
    message,
    data,
    timestamp: new Date().toISOString(),
  };
};

// Utility function to validate required fields
export const validateRequiredFields = (body: Record<string, any>, requiredFields: string[]): string[] => {
  const missingFields: string[] = [];

  for (const field of requiredFields) {
    if (!body[field] || (typeof body[field] === 'string' && body[field].trim() === '')) {
      missingFields.push(field);
    }
  }

  return missingFields;
};
