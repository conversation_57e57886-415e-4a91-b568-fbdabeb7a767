/**
 * Periodic Validator Utility
 * 
 * This utility provides functions to determine whether a given date falls within
 * the current day, week, or month. It's designed to be used across the codebase
 * for mission validations, participation checks, and other time-based logic.
 * 
 * <AUTHOR> Agent
 * @created 2025-06-26
 */

/**
 * Enum for different time periods that can be validated
 */
export enum TimePeriod {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month'
}

/**
 * Interface for validation result with additional context
 */
export interface ValidationResult {
  isValid: boolean;
  period: TimePeriod;
  targetDate: Date;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
}

/**
 * Options for configuring the periodic validator
 */
export interface ValidatorOptions {
  /** Timezone offset in minutes (default: local timezone) */
  timezoneOffset?: number;
  /** Start of week (0 = Sunday, 1 = Monday, etc.) - default: 1 (Monday) */
  weekStartDay?: number;
}

/**
 * Periodic Validator Class
 * 
 * Provides methods to validate if dates fall within current time periods.
 * All methods are static for easy usage across the codebase.
 */
export class PeriodicValidator {
  
  /**
   * Check if a given date falls within the current day
   * 
   * @param targetDate - The date to check (Date object, timestamp, or ISO string)
   * @param options - Optional configuration for timezone and other settings
   * @returns boolean - true if the date is within the current day
   * 
   * @example
   * ```typescript
   * const isToday = PeriodicValidator.isWithinCurrentDay(new Date());
   * const wasYesterday = PeriodicValidator.isWithinCurrentDay(Date.now() - 86400000);
   * ```
   */
  static isWithinCurrentDay(
    targetDate: Date | number | string, 
    options: ValidatorOptions = {}
  ): boolean {
    const result = this.validatePeriod(targetDate, TimePeriod.DAY, options);
    return result.isValid;
  }

  /**
   * Check if a given date falls within the current week
   * 
   * @param targetDate - The date to check (Date object, timestamp, or ISO string)
   * @param options - Optional configuration for timezone and week start day
   * @returns boolean - true if the date is within the current week
   * 
   * @example
   * ```typescript
   * const isThisWeek = PeriodicValidator.isWithinCurrentWeek(new Date());
   * const wasLastWeek = PeriodicValidator.isWithinCurrentWeek(Date.now() - 7 * 86400000);
   * ```
   */
  static isWithinCurrentWeek(
    targetDate: Date | number | string, 
    options: ValidatorOptions = {}
  ): boolean {
    const result = this.validatePeriod(targetDate, TimePeriod.WEEK, options);
    return result.isValid;
  }

  /**
   * Check if a given date falls within the current month
   * 
   * @param targetDate - The date to check (Date object, timestamp, or ISO string)
   * @param options - Optional configuration for timezone
   * @returns boolean - true if the date is within the current month
   * 
   * @example
   * ```typescript
   * const isThisMonth = PeriodicValidator.isWithinCurrentMonth(new Date());
   * const wasLastMonth = PeriodicValidator.isWithinCurrentMonth(Date.now() - 30 * 86400000);
   * ```
   */
  static isWithinCurrentMonth(
    targetDate: Date | number | string, 
    options: ValidatorOptions = {}
  ): boolean {
    const result = this.validatePeriod(targetDate, TimePeriod.MONTH, options);
    return result.isValid;
  }

  /**
   * Get detailed validation result with period boundaries
   * 
   * @param targetDate - The date to check (Date object, timestamp, or ISO string)
   * @param period - The time period to validate against
   * @param options - Optional configuration for timezone and other settings
   * @returns ValidationResult - Detailed result with period boundaries
   * 
   * @example
   * ```typescript
   * const result = PeriodicValidator.validatePeriod(new Date(), TimePeriod.WEEK);
   * console.log(`Valid: ${result.isValid}, Week starts: ${result.currentPeriodStart}`);
   * ```
   */
  static validatePeriod(
    targetDate: Date | number | string,
    period: TimePeriod,
    options: ValidatorOptions = {}
  ): ValidationResult {
    // Convert input to Date object
    const date = this.normalizeDate(targetDate);
    
    // Get current time with timezone consideration
    const now = new Date();
    if (options.timezoneOffset !== undefined) {
      now.setMinutes(now.getMinutes() + options.timezoneOffset);
    }

    // Calculate period boundaries based on the requested period
    const { start, end } = this.getPeriodBoundaries(now, period, options);

    // Check if the target date falls within the current period
    const isValid = date >= start && date <= end;

    return {
      isValid,
      period,
      targetDate: date,
      currentPeriodStart: start,
      currentPeriodEnd: end
    };
  }

  /**
   * Utility method to check multiple dates against a period
   * 
   * @param targetDates - Array of dates to check
   * @param period - The time period to validate against
   * @param options - Optional configuration
   * @returns Array of boolean results in the same order as input
   * 
   * @example
   * ```typescript
   * const dates = [new Date(), Date.now() - 86400000, Date.now() + 86400000];
   * const results = PeriodicValidator.validateMultipleDates(dates, TimePeriod.DAY);
   * // [true, false, false] - only first date (today) is valid
   * ```
   */
  static validateMultipleDates(
    targetDates: (Date | number | string)[],
    period: TimePeriod,
    options: ValidatorOptions = {}
  ): boolean[] {
    return targetDates.map(date => {
      const result = this.validatePeriod(date, period, options);
      return result.isValid;
    });
  }

  /**
   * Helper method to normalize different date input types to Date objects
   *
   * @param input - Date input in various formats
   * @returns Date object
   * @throws Error if the input cannot be converted to a valid date
   */
  static normalizeDate(input: Date | number | string): Date {
    let date: Date;

    if (input instanceof Date) {
      date = new Date(input.getTime()); // Create a copy to avoid mutations
    } else if (typeof input === 'number') {
      // Handle both seconds and milliseconds timestamps
      // If the number is less than a reasonable millisecond timestamp, assume it's seconds
      const timestamp = input < 10000000000 ? input * 1000 : input;
      date = new Date(timestamp);
    } else if (typeof input === 'string') {
      date = new Date(input);
    } else {
      throw new Error(`Invalid date input: ${input}. Expected Date, number, or string.`);
    }

    // Validate that we got a valid date
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date: ${input} could not be converted to a valid Date object.`);
    }

    return date;
  }

  /**
   * Helper method to calculate period boundaries
   *
   * @param referenceDate - The reference date (usually current time)
   * @param period - The time period to calculate boundaries for
   * @param options - Configuration options
   * @returns Object with start and end Date objects
   */
  static getPeriodBoundaries(
    referenceDate: Date,
    period: TimePeriod,
    options: ValidatorOptions
  ): { start: Date; end: Date } {
    const date = new Date(referenceDate.getTime());

    switch (period) {
      case TimePeriod.DAY:
        return this.getDayBoundaries(date);
      
      case TimePeriod.WEEK:
        return this.getWeekBoundaries(date, options.weekStartDay || 1);
      
      case TimePeriod.MONTH:
        return this.getMonthBoundaries(date);
      
      default:
        throw new Error(`Unsupported time period: ${period}`);
    }
  }

  /**
   * Calculate day boundaries (start and end of the current day)
   * 
   * @param date - Reference date
   * @returns Object with start and end of the day
   */
  private static getDayBoundaries(date: Date): { start: Date; end: Date } {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0); // Start of day: 00:00:00.000

    const end = new Date(date);
    end.setHours(23, 59, 59, 999); // End of day: 23:59:59.999

    return { start, end };
  }

  /**
   * Calculate week boundaries (start and end of the current week)
   * 
   * @param date - Reference date
   * @param weekStartDay - Day of week that starts the week (0 = Sunday, 1 = Monday, etc.)
   * @returns Object with start and end of the week
   */
  private static getWeekBoundaries(date: Date, weekStartDay: number): { start: Date; end: Date } {
    const currentDay = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Calculate days to subtract to get to the start of the week
    let daysToSubtract = (currentDay - weekStartDay + 7) % 7;
    
    const start = new Date(date);
    start.setDate(date.getDate() - daysToSubtract);
    start.setHours(0, 0, 0, 0); // Start of week day

    const end = new Date(start);
    end.setDate(start.getDate() + 6); // Add 6 days to get end of week
    end.setHours(23, 59, 59, 999); // End of week day

    return { start, end };
  }

  /**
   * Calculate month boundaries (start and end of the current month)
   *
   * @param date - Reference date
   * @returns Object with start and end of the month
   */
  private static getMonthBoundaries(date: Date): { start: Date; end: Date } {
    const start = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0, 0);

    // Get the last day of the month by going to the first day of next month and subtracting 1 day
    const end = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);

    return { start, end };
  }
}

/**
 * Convenience functions for quick usage without instantiating the class
 * These are the primary functions that will be used throughout the codebase
 */

/**
 * Quick check if a date is within the current day
 *
 * @param targetDate - The date to check
 * @param options - Optional configuration
 * @returns boolean - true if within current day
 *
 * @example
 * ```typescript
 * import { isWithinCurrentDay } from '@/utils/periodicValidator';
 *
 * const missionParticipation = await getMissionParticipation(userId, missionId);
 * if (isWithinCurrentDay(missionParticipation.createdAt)) {
 *   console.log('User participated today');
 * }
 * ```
 */
export const isWithinCurrentDay = (
  targetDate: Date | number | string,
  options?: ValidatorOptions
): boolean => {
  return PeriodicValidator.isWithinCurrentDay(targetDate, options);
};

/**
 * Quick check if a date is within the current week
 *
 * @param targetDate - The date to check
 * @param options - Optional configuration
 * @returns boolean - true if within current week
 *
 * @example
 * ```typescript
 * import { isWithinCurrentWeek } from '@/utils/periodicValidator';
 *
 * const lastClaim = await getLastFinalMissionClaim(userId);
 * if (!isWithinCurrentWeek(lastClaim.createdAt)) {
 *   // User can claim weekly final mission again
 *   await enableWeeklyMission(userId);
 * }
 * ```
 */
export const isWithinCurrentWeek = (
  targetDate: Date | number | string,
  options?: ValidatorOptions
): boolean => {
  return PeriodicValidator.isWithinCurrentWeek(targetDate, options);
};

/**
 * Quick check if a date is within the current month
 *
 * @param targetDate - The date to check
 * @param options - Optional configuration
 * @returns boolean - true if within current month
 *
 * @example
 * ```typescript
 * import { isWithinCurrentMonth } from '@/utils/periodicValidator';
 *
 * const monthlyMissionProgress = await getMonthlyMissionProgress(userId);
 * if (!isWithinCurrentMonth(monthlyMissionProgress.lastResetAt)) {
 *   // Reset monthly mission progress
 *   await resetMonthlyProgress(userId);
 * }
 * ```
 */
export const isWithinCurrentMonth = (
  targetDate: Date | number | string,
  options?: ValidatorOptions
): boolean => {
  return PeriodicValidator.isWithinCurrentMonth(targetDate, options);
};

/**
 * Calculate the number of seconds until the next allowed participation
 * for a periodic mission (daily/weekly/monthly)
 *
 * @param lastParticipationDate - The date of the last participation
 * @param period - The time period (day/week/month)
 * @param options - Optional configuration
 * @returns number - Seconds until next allowed participation (0 if can participate now)
 *
 * @example
 * ```typescript
 * import { getSecondsUntilNextAllowedParticipation, TimePeriod } from '@/utils/periodicValidator';
 *
 * const lastParticipation = new Date('2025-06-27T10:00:00Z');
 * const secondsUntilNext = getSecondsUntilNextAllowedParticipation(lastParticipation, TimePeriod.DAY);
 * console.log(`Can participate again in ${secondsUntilNext} seconds`);
 * ```
 */
export const getSecondsUntilNextAllowedParticipation = (
  lastParticipationDate: Date | number | string,
  period: TimePeriod,
  options?: ValidatorOptions
): number => {
  const lastDate = PeriodicValidator.normalizeDate(lastParticipationDate);
  const now = new Date();

  // If the last participation was not in the current period, user can participate now
  const isInCurrentPeriod = PeriodicValidator.validatePeriod(lastDate, period, options).isValid;
  if (!isInCurrentPeriod) {
    return 0;
  }

  // Calculate the end of the current period
  const { end: currentPeriodEnd } = PeriodicValidator.getPeriodBoundaries(now, period, options || {});

  // Add 1 second to get to the start of the next period
  const nextPeriodStart = new Date(currentPeriodEnd.getTime() + 1000);

  // Calculate seconds until next period starts
  const secondsUntilNext = Math.max(0, Math.floor((nextPeriodStart.getTime() - now.getTime()) / 1000));

  return secondsUntilNext;
};

/**
 * Check if a mission participation is still within the current period
 * based on the mission type and participation creation date
 *
 * @param participationDate - The date when the participation was created
 * @param missionType - The mission type (daily/weekly/monthly/custom)
 * @param options - Optional configuration
 * @returns boolean - true if participation is still within current period
 *
 * @example
 * ```typescript
 * import { isParticipationWithinCurrentPeriod } from '@/utils/periodicValidator';
 * import { MissionType } from '@/enums/shared';
 *
 * const participation = await getParticipation(userId, missionId);
 * const isCurrentPeriod = isParticipationWithinCurrentPeriod(participation.createdAt, MissionType.DAILY);
 * if (isCurrentPeriod) {
 *   console.log('Participation is still valid for current period');
 * }
 * ```
 */
export const isParticipationWithinCurrentPeriod = (
  participationDate: Date | number | string,
  missionType: string, // Using string to avoid circular import with MissionType enum
  options?: ValidatorOptions
): boolean => {
  // For custom missions, participation is always valid once created (until completed)
  if (missionType === 'custom') {
    return true;
  }

  // Check if participation is within current period based on mission type
  switch (missionType) {
    case 'daily':
      return isWithinCurrentDay(participationDate, options);
    case 'weekly':
      return isWithinCurrentWeek(participationDate, options);
    case 'monthly':
      return isWithinCurrentMonth(participationDate, options);
    default:
      // Unknown mission type, assume valid
      return true;
  }
};

/**
 * Calculate the number of seconds until the next allowed participation
 * for a mission based on its type and last participation date
 *
 * @param lastParticipationDate - The date of the last participation
 * @param missionType - The mission type (daily/weekly/monthly/custom)
 * @param options - Optional configuration
 * @returns number - Seconds until next allowed participation (0 if can participate now or for custom missions)
 *
 * @example
 * ```typescript
 * import { getSecondsUntilNextMissionParticipation } from '@/utils/periodicValidator';
 * import { MissionType } from '@/enums/shared';
 *
 * const lastParticipation = new Date('2025-06-27T10:00:00Z');
 * const secondsUntilNext = getSecondsUntilNextMissionParticipation(lastParticipation, MissionType.DAILY);
 * console.log(`Can participate again in ${secondsUntilNext} seconds`);
 * ```
 */
export const getSecondsUntilNextMissionParticipation = (
  lastParticipationDate: Date | number | string,
  missionType: string, // Using string to avoid circular import with MissionType enum
  options?: ValidatorOptions
): number => {
  // For custom missions, users can't participate again once completed
  if (missionType === 'custom') {
    return 0; // This will be handled by business logic elsewhere
  }

  // Map mission type to time period
  let period: TimePeriod;
  switch (missionType) {
    case 'daily':
      period = TimePeriod.DAY;
      break;
    case 'weekly':
      period = TimePeriod.WEEK;
      break;
    case 'monthly':
      period = TimePeriod.MONTH;
      break;
    default:
      return 0; // Unknown mission type, allow participation
  }

  return getSecondsUntilNextAllowedParticipation(lastParticipationDate, period, options);
};

/**
 * Default export for the main class
 */
export default PeriodicValidator;

/**
 * Get seconds until the next daily reset (start of next day)
 *
 * @param options - Optional configuration for timezone
 * @returns number - Seconds until next day starts
 *
 * @example
 * ```typescript
 * import { getSecondsUntilNextDailyReset } from '@/utils/periodicValidator';
 *
 * const secondsUntilReset = getSecondsUntilNextDailyReset();
 * console.log(`Daily missions reset in ${secondsUntilReset} seconds`);
 * ```
 */
export const getSecondsUntilNextDailyReset = (options?: ValidatorOptions): number => {
  const result = PeriodicValidator.validatePeriod(new Date(), TimePeriod.DAY, options);
  const now = new Date();
  const nextReset = new Date(result.currentPeriodEnd.getTime() + 1000); // Add 1 second to get start of next period
  return Math.max(0, Math.floor((nextReset.getTime() - now.getTime()) / 1000));
};

/**
 * Get seconds until the next weekly reset (start of next week)
 *
 * @param options - Optional configuration for timezone and week start day
 * @returns number - Seconds until next week starts
 *
 * @example
 * ```typescript
 * import { getSecondsUntilNextWeeklyReset } from '@/utils/periodicValidator';
 *
 * const secondsUntilReset = getSecondsUntilNextWeeklyReset();
 * console.log(`Weekly missions reset in ${secondsUntilReset} seconds`);
 * ```
 */
export const getSecondsUntilNextWeeklyReset = (options?: ValidatorOptions): number => {
  const result = PeriodicValidator.validatePeriod(new Date(), TimePeriod.WEEK, options);
  const now = new Date();
  const nextReset = new Date(result.currentPeriodEnd.getTime() + 1000); // Add 1 second to get start of next period
  return Math.max(0, Math.floor((nextReset.getTime() - now.getTime()) / 1000));
};

/**
 * Get seconds until the next monthly reset (start of next month)
 *
 * @param options - Optional configuration for timezone
 * @returns number - Seconds until next month starts
 *
 * @example
 * ```typescript
 * import { getSecondsUntilNextMonthlyReset } from '@/utils/periodicValidator';
 *
 * const secondsUntilReset = getSecondsUntilNextMonthlyReset();
 * console.log(`Monthly missions reset in ${secondsUntilReset} seconds`);
 * ```
 */
export const getSecondsUntilNextMonthlyReset = (options?: ValidatorOptions): number => {
  const result = PeriodicValidator.validatePeriod(new Date(), TimePeriod.MONTH, options);
  const now = new Date();
  const nextReset = new Date(result.currentPeriodEnd.getTime() + 1000); // Add 1 second to get start of next period
  return Math.max(0, Math.floor((nextReset.getTime() - now.getTime()) / 1000));
};

/**
 * Calculate the end date timestamp for a mission objective assignment based on mission type
 * This function provides unified logic for determining when periodic missions expire
 *
 * @param missionType - The mission type (daily/weekly/monthly/custom)
 * @param startDate - The start date timestamp (in seconds) - defaults to current time
 * @param options - Optional configuration for timezone and other settings
 * @returns number | null - End date timestamp in seconds, or null for custom missions
 *
 * @example
 * ```typescript
 * import { calculateMissionObjectiveEndDate } from '@/utils/periodicValidator';
 * import { MissionType } from '@/enums/shared';
 *
 * const now = Math.floor(Date.now() / 1000);
 * const endDate = calculateMissionObjectiveEndDate(MissionType.DAILY, now);
 * console.log(`Daily mission ends at: ${new Date(endDate * 1000)}`);
 * ```
 */
export const calculateMissionObjectiveEndDate = (
  missionType: string, // Using string to avoid circular import with MissionType enum
  startDate?: number,
  options?: ValidatorOptions
): number | null => {
  // For custom missions, return null (no automatic expiration)
  if (missionType === 'custom') {
    return null;
  }

  // Use provided start date or current time
  const startTimestamp = startDate || Math.floor(Date.now() / 1000);
  const startDateObj = new Date(startTimestamp * 1000);

  // Map mission type to time period
  let period: TimePeriod;
  switch (missionType) {
    case 'daily':
      period = TimePeriod.DAY;
      break;
    case 'weekly':
      period = TimePeriod.WEEK;
      break;
    case 'monthly':
      period = TimePeriod.MONTH;
      break;
    default:
      // Unknown mission type, return null
      return null;
  }

  // Get the period boundaries for the start date
  const { end: periodEnd } = PeriodicValidator.getPeriodBoundaries(startDateObj, period, options || {});

  // Return the end timestamp in seconds
  return Math.floor(periodEnd.getTime() / 1000);
};

/**
 * Get localized text from i18n field with fallback to default field
 *
 * @param i18nField - The i18n JSON object containing translations
 * @param defaultValue - The default value to use if no translation is found
 * @param language - The language code to look for (defaults to 'en')
 * @returns The localized text or the default value
 *
 * @example
 * ```typescript
 * import { getLocalizedText } from '@/utils/periodicValidator';
 *
 * const mission = {
 *   name: 'Daily Login',
 *   name_i18n: { 'en': 'Daily Login', 'es': 'Inicio Diario', 'fr': 'Connexion Quotidienne' }
 * };
 *
 * const localizedName = getLocalizedText(mission.name_i18n, mission.name, 'es');
 * console.log(localizedName); // 'Inicio Diario'
 * ```
 */
export const getLocalizedText = (
  i18nField: Record<string, string> | null | undefined,
  defaultValue: string,
  language: string = 'en'
): string => {
  // If no i18n field or it's empty, return default
  if (!i18nField || typeof i18nField !== 'object' || Object.keys(i18nField).length === 0) {
    return defaultValue;
  }

  // Try to get the requested language
  if (i18nField[language]) {
    return i18nField[language];
  }

  // Fallback to English if available
  if (language !== 'en' && i18nField['en']) {
    return i18nField['en'];
  }

  // Fallback to first available language
  const firstAvailable = Object.values(i18nField)[0];
  if (firstAvailable) {
    return firstAvailable;
  }

  // Final fallback to default value
  return defaultValue;
};
