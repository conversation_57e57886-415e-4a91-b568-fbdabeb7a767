import crypto from 'crypto';

/**
 * Pronet Authentication Utilities
 * 
 * This module provides utilities for authenticating with the Pronet API,
 * including checksum generation, JSON minification, URL building, and credential management.
 */

/**
 * Generate SHA-512 checksum for Pronet API authentication
 * 
 * @param jsonBody - Minified JSON body as string
 * @param secret - API secret key
 * @returns Base64 encoded SHA-512 checksum
 */
export const generatePronetChecksum = (jsonBody: string, secret: string): string => {
  try {
    console.log('🔐 Generating Pronet checksum...');
    console.log('📝 JSON Body length:', jsonBody.length);
    console.log('🔑 Secret length:', secret.length);

    // Concatenate JSON body and secret
    const input = jsonBody + secret;

    // Generate SHA-512 hash
    const hash = crypto.createHash('sha512').update(input, 'utf8').digest();

    // Base64 encode the hash
    const checksum = hash.toString('base64');

    console.log('✅ Checksum generated successfully');
    console.log('📊 Checksum length:', checksum.length);

    return checksum;
  } catch (error) {
    console.error('❌ Failed to generate Pronet checksum:', error);
    throw new Error(`Failed to generate Pronet checksum: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Minify JSON string by parsing and re-stringifying
 * 
 * @param jsonString - JSON string to minify
 * @returns Minified JSON string
 */
export const minifyJsonString = (jsonString: string): string => {
  try {
    // Parse and stringify to remove all unnecessary whitespace
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed);
  } catch (error) {
    console.error('❌ Failed to minify JSON string:', error);
    throw new Error(`Invalid JSON string: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Convert JSON object to minified string
 * 
 * @param jsonObject - Object to convert to minified JSON string
 * @returns Minified JSON string
 */
export const minifyJsonObject = (jsonObject: any): string => {
  try {
    return JSON.stringify(jsonObject);
  } catch (error) {
    console.error('❌ Failed to minify JSON object:', error);
    throw new Error(`Failed to serialize JSON object: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Build Pronet API URL
 * 
 * @param endpoint - API endpoint (without leading slash)
 * @param isProxy - Whether this is a proxy request (affects URL construction)
 * @returns Full Pronet API URL
 */
export const buildPronetApiUrl = (endpoint: string, isProxy: boolean = false): string => {
  const pronetHost = process.env['PRONET_HOST'];
  
  if (!pronetHost) {
    throw new Error('PRONET_HOST environment variable is not configured');
  }

  // Remove leading slash from endpoint if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;

  let fullUrl: string;

  if (isProxy) {
    // For proxy requests: {{HOST}}/api/pronet/v1/smth becomes {{HOST}}/smth
    // This strips the /api/pronet/v1/ prefix when proxying to the actual Pronet API
    fullUrl = `${pronetHost}/${cleanEndpoint}`;
  } else {
    // For direct API calls: Construct the full URL: [PRONET_HOST]/api/pronet/v1/[endpoint]
    fullUrl = `${pronetHost}/api/pronet/v1/${cleanEndpoint}`;
  }

  console.log('🌐 Built Pronet API URL:', fullUrl);
  return fullUrl;
};

/**
 * Pronet credentials interface
 */
export interface PronetCredentials {
  host: string;
  username: string;
  apiKey: string;
}

/**
 * Get Pronet credentials from environment variables
 * 
 * @returns Pronet credentials object
 * @throws Error if any required environment variable is missing
 */
export const getPronetCredentials = (): PronetCredentials => {
  const host = process.env['PRONET_HOST'];
  const username = process.env['PUBLIC_API_USERNAME'];
  const apiKey = process.env['PRONET_API_KEY'];

  if (!host) {
    throw new Error('PRONET_HOST environment variable is required');
  }

  if (!username) {
    throw new Error('PUBLIC_API_USERNAME environment variable is required');
  }

  if (!apiKey) {
    throw new Error('PRONET_API_KEY environment variable is required');
  }

  return {
    host,
    username,
    apiKey,
  };
};

/**
 * Validate Pronet configuration
 * 
 * @returns true if configuration is valid, false otherwise
 */
export const validatePronetConfig = (): boolean => {
  try {
    getPronetCredentials();
    console.log('✅ Pronet configuration is valid');
    return true;
  } catch (error) {
    console.error('❌ Pronet configuration validation failed:', error);
    return false;
  }
};
