import fetch from 'node-fetch';
import crypto from 'crypto';

// Constants from reference.js
export const USER_AGENT =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36';
export const REFERER = 'https://studio.ebetlab.com/';
export const ORIGIN = 'https://studio.ebetlab.com';

/**
 * Calculate X-Fingerprint header value
 */
export const calculateXFingerprint = (publicKey = ''): string => {
  const payload = ''.concat(USER_AGENT, '|').concat('1920', 'x').concat('1080', '|').concat('en-US');

  const hmac = crypto.createHmac('sha256', publicKey);
  hmac.update(payload);
  const signature = hmac.digest('hex');

  return signature;
};

/**
 * Calculate X-Signature header value using HMAC-SHA256
 * @param publicKey - The public key for HMAC signing
 * @param fingerprint - The fingerprint value (optional, defaults to empty string)
 * @param timestamp - The timestamp value (optional, defaults to current timestamp)
 * @returns The HMAC-SHA256 signature as hex string
 */
export const getXSignature = (publicKey: string = '', fingerprint: string = '', timestamp?: number): string => {
  // Get current date in UTC and format as "YYYY-MM-DDHH:mm"
  const now = new Date();
  const utcDate = new Date(now.getTime() + now.getTimezoneOffset() * 60000);

  const year = utcDate.getFullYear();
  const month = String(utcDate.getMonth() + 1).padStart(2, '0');
  const day = String(utcDate.getDate()).padStart(2, '0');
  const hours = String(utcDate.getHours()).padStart(2, '0');
  const minutes = String(utcDate.getMinutes()).padStart(2, '0');

  const d = `${year}-${month}-${day}${hours}:${minutes}`;

  // Use provided timestamp or current timestamp
  const rt = timestamp || Math.floor(Date.now() / 1000);

  // Create the payload object
  const payload = {
    fingerprint,
    rt,
    d,
  };

  // Convert payload to JSON string
  const payloadString = JSON.stringify(payload);

  // Calculate HMAC-SHA256
  const hmac = crypto.createHmac('sha256', publicKey);
  hmac.update(payloadString);
  const signature = hmac.digest('hex');

  return signature;
};

/**
 * Initialize Cloudflare and get cf_clearance cookie
 */
export const cfInit = async (): Promise<string> => {
  const response = await fetch(
    'https://studio.ebetlab.com/cdn-cgi/challenge-platform/h/g/jsd/r/0.0109690382174867:1748619014:lSQugtBZwQiRc6touoDXZTljW0Nv0rklfmgiK15NkXs/9480185ec86b70c0',
    {
      method: 'POST',
      headers: {
        'User-Agent': USER_AGENT,
        Referer: REFERER,
        Origin: ORIGIN,
      },
    },
  );

  if (!response.ok) {
    throw new Error(`Failed to init cf: ${await response.text()}`);
  }

  const setCookieHeaders = response.headers.raw()['set-cookie'];
  if (!setCookieHeaders || setCookieHeaders.length === 0) {
    throw new Error('No set-cookie headers found');
  }

  const cf_clearance = setCookieHeaders[0];

  if (!cf_clearance || !cf_clearance.includes('cf_clearance=')) {
    throw new Error('Failed to retrieve cf cookie');
  }

  const pair = cf_clearance.split(';').shift();
  if (!pair) {
    throw new Error('Failed to parse cf cookie');
  }

  const [_, value] = pair.split('=');
  return value || '';
};

/**
 * Get signed challenge cookie
 */
export const getSignedChallenge = async (cf_clearance: string, x_fingerprint: string): Promise<string> => {
  const response = await fetch('https://service.ebetlab.com/api/operator/configuration/cookie', {
    method: 'POST',
    headers: {
      'X-Fingerprint': x_fingerprint,
      'User-Agent': USER_AGENT,
      Referer: REFERER,
      Origin: ORIGIN,
      Cookie: `cf_clearance=${cf_clearance}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get signed cookie: ${await response.text()}`);
  }

  const setCookieHeaders = response.headers.raw()['set-cookie'];
  if (!setCookieHeaders || setCookieHeaders.length === 0) {
    throw new Error('No set-cookie headers found');
  }

  const signed_challenge = setCookieHeaders[0];

  if (!signed_challenge || !signed_challenge.includes('signed_challenge=')) {
    throw new Error('Failed to retrieve signed challenge cookie');
  }

  const pair = signed_challenge.split(';').shift();
  if (!pair) {
    throw new Error('Failed to parse signed challenge cookie');
  }

  const [_, value] = pair.split('=');
  return value || '';
};

/**
 * Get auth signature token
 */
export const getAuthSignature = async (
  cf_clearance: string,
  signed_challenge: string,
  x_fingerprint: string,
): Promise<string> => {
  const response = await fetch('https://service.ebetlab.com/api/operator/configuration/token', {
    method: 'POST',
    headers: {
      'X-Fingerprint': x_fingerprint,
      'User-Agent': USER_AGENT,
      Referer: REFERER,
      Origin: ORIGIN,
      Cookie: `cf_clearance=${cf_clearance}; signed_challenge=${signed_challenge}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get auth signature: ${await response.text()}`);
  }

  const data = (await response.json()) as any;
  if (data.token) {
    return data.token;
  } else {
    throw new Error(`Failed to get auth signature: unexpected response body: ${JSON.stringify(data)}`);
  }
};

// Note: Login functionality has been moved to EbetLabService.login()
// This file now only contains utility functions for auth data collection
