import fetch from 'node-fetch';
import { AuthData, AgentInfo, EbetLabLoginPayload } from '@/types/auth';
import {
  calculateXFingerprint,
  cfInit,
  getAuthSignature,
  getSignedChallenge,
  getXSignature,
  ORIGIN,
  REFERER,
  USER_AGENT,
} from '@/utils/authUtils';
import { cache } from './cache';

// Constants
const BASE_URL = 'https://service.ebetlab.com/api';

/**
 * Service class for making authenticated requests to EbetLab API
 */
export class EbetLabService {
  private authData: AuthData;
  private token: string;

  constructor(authData: AuthData, token: string) {
    this.authData = authData;
    this.token = token;
  }

  /**
   * Make an authenticated request to EbetLab API
   */
  async makeAuthenticatedRequest(
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST',
    body?: any,
    additionalHeaders?: Record<string, string>,
  ): Promise<any> {
    try {
      console.log('🚀 Making authenticated request...');
      console.log(`📍 URL: ${method} ${url}`);

      // Get auth signature for the request
      const authSignature = await getAuthSignature(
        this.authData.cf_clearance,
        this.authData.signed_challenge,
        this.authData.x_fingerprint,
      );

      const headers: Record<string, string> = {
        Authorization: `Bearer ${this.token}`,
        'X-Fingerprint': this.authData.x_fingerprint,
        'X-Auth-Signature': authSignature,
        'User-Agent': USER_AGENT,
        Referer: REFERER,
        Origin: ORIGIN,
        Cookie: `cf_clearance=${this.authData.cf_clearance}; signed_challenge=${this.authData.signed_challenge}`,
        ...additionalHeaders,
      };

      if (body && (method === 'POST' || method === 'PUT')) {
        headers['Content-Type'] = 'application/json';
      }

      // Log request details
      console.log('📤 Request Headers:', JSON.stringify(headers, null, 2));
      if (body) {
        console.log('📤 Request Body:', JSON.stringify(body, null, 2));
      }

      const response = await fetch(url, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined,
      });

      // Log response details
      console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
      console.log('📥 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response Error Body:', errorText);
        throw new Error(`Request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = (await response.json()) as any;
      console.log('📥 Response Body:', JSON.stringify(data, null, 2));

      if ('success' in data && data.success !== true) {
        console.error('❌ API returned success: false');
        throw new Error(`Request failed: ${JSON.stringify(data)}`);
      }

      console.log('✅ Request completed successfully');
      return data;
    } catch (error) {
      console.error('❌ Authenticated request failed:', error);
      throw error;
    }
  }

  /**
   * List games with pagination
   */
  async listGames(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      name: null,
      description: null,
      is_active: null,
      system_active: null,
      merchant_id: null,
      category: null,
      sortBy: null,
      direction: null,
      page,
      limit,
      rt: (Date.now() / 1000).toFixed(),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `${BASE_URL}/api/operator/games/configuration/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get CSS settings with pagination
   */
  async getCssSettings(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      page,
      limit,
      rt: (Date.now() / 1000).toFixed(),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/css-settings/index2/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get transactions summary from dashboard or general transactions summary
   */
  async getTransactionsSummary(rangeOrFilters?: string | any, timezone?: string): Promise<any> {
    // If first parameter is a string, it's the dashboard endpoint
    if (typeof rangeOrFilters === 'string' || rangeOrFilters === undefined) {
      const endpoint = `/operator/dashboard/transactions/summary`;
      return this.makeAuthenticatedRequest(`https://service.ebetlab.com/api${endpoint}`, 'POST', {
        range: rangeOrFilters,
        timezone,
      });
    }

    // Otherwise, it's the general transactions summary endpoint
    const body = {
      id: null,
      method: null,
      provider: null,
      username: null,
      currency: null,
      operator_id: null,
      type: null,
      affiliator: null,
      ref_code: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      amount_min: null,
      amount_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000),
      ...rangeOrFilters,
    };

    return this.makeAuthenticatedRequest(`https://service.ebetlab.com/api/operator/transactions/summary`, 'POST', body);
  }

  /**
   * Get big win/lose debits from dashboard
   */
  async getBigWinLoseDebits(additionalParams?: Record<string, string>): Promise<any> {
    const body: Record<string, any> = {
      rt: (Date.now() / 1000).toFixed(),
      ...additionalParams,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/dashboard/debits/big-win-lose`,
      'POST',
      body,
    );
  }

  /**
   * Get game win/lose debits from dashboard
   */
  async getGameWinLoseDebits(additionalParams?: Record<string, string>): Promise<any> {
    const body: Record<string, any> = {
      rt: (Date.now() / 1000).toFixed(),
      ...additionalParams,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/dashboard/debits/game-win-lose`,
      'POST',
      body,
    );
  }

  /**
   * Get dashboard widget stats
   */
  async getWidgetStats(from: number, to: number): Promise<any> {
    const body = {
      from,
      to,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/dashboard/widgets/stats`,
      'POST',
      body,
    );
  }

  /**
   * Get customers with pagination and filters
   */
  async getCustomers(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      affiliator: null,
      referrer: null,
      username: null,
      name: null,
      birthday: null,
      surname: null,
      ref_code: null,
      identity_no: null,
      verification_level: null,
      email: null,
      ip: null,
      status_id: null,
      operator_id: null,
      registration_country: null,
      language: null,
      rank: null,
      phone: null,
      register_start: null,
      register_end: null,
      first_deposit_start: null,
      first_deposit_end: null,
      last_deposit_start: null,
      last_deposit_end: null,
      last_login_start: null,
      last_login_end: null,
      total_reload_min: null,
      total_reload_max: null,
      total_rain_min: null,
      total_rain_max: null,
      total_deposit_greater: null,
      total_deposit_lower: null,
      total_withdraw_greater: null,
      total_withdraw_lower: null,
      total_bonus_drop_min: null,
      total_bonus_drop_max: null,
      total_turnover_greater: null,
      total_turnover_lower: null,
      net_percentage_min: null,
      net_percentage_max: null,
      rakebackMin: null,
      rakebackMax: null,
      sortBy: null,
      direction: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get individual customer details by ID
   */
  async getCustomerById(customerId: string): Promise<any> {
    const body = {
      id: customerId,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/show/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Check sessions for conflicting users with same IP
   */
  async checkSessions(customerId: string): Promise<any> {
    const body = {
      id: customerId,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/sessions/check/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Get customer summary range (daily/weekly/monthly/yearly deposits and withdrawals)
   */
  async getCustomerSummaryRange(customerId: string): Promise<any> {
    const body = {
      id: customerId,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/summary-range/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Get customer info (last deposit, withdraw, discount, corrections, bonus)
   */
  async getCustomerInfo(customerId: string): Promise<any> {
    const body = {
      customer_id: customerId,
      id: customerId,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/info/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Get general limits for a customer
   */
  async getGeneralLimits(customerId: string): Promise<any> {
    const body = {
      id: customerId,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/general-limits/show/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Apply/update general limits for a customer
   */
  async applyGeneralLimits(customerId: string, limits: any): Promise<any> {
    const body = {
      ...limits,
      id: customerId,
      rt: Math.floor(Date.now() / 1000),
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/general-limits/apply/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Get financial limits for a customer
   */
  async getFinancialLimits(customerId: string, requestBody?: any): Promise<any> {
    const body = {
      id: customerId,
      rt: Math.floor(Date.now() / 1000),
      ...requestBody,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customer-limits/show/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Update financial limits for a customer
   */
  async updateFinancialLimits(customerId: string, limits: any): Promise<any> {
    const body = {
      ...limits,
      id: customerId,
      rt: Math.floor(Date.now() / 1000),
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customer-limits/update/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Update customer password
   */
  async updateCustomerPassword(customerId: string, password: string): Promise<any> {
    const body = {
      password,
      id: parseInt(customerId),
      rt: Math.floor(Date.now() / 1000),
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/password/${customerId}`,
      'POST',
      body,
    );
  }

  /**
   * Get transactions with pagination and filters
   */
  async getTransactions(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      currency: null,
      operator_id: null,
      type: null,
      status_id: null,
      tx: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      is_manuel: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/transactions/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get FTD (First Time Deposit) transactions with pagination and filters
   */
  async getFtdTransactions(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      method: null,
      provider: null,
      username: null,
      affiliator: null,
      ref_code: null,
      currency: null,
      operator_id: null,
      type: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/transactions/ftd/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get FTW (First Time Withdrawal) transactions with pagination and filters
   */
  async getFtwTransactions(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      method: null,
      provider: null,
      username: null,
      affiliator: null,
      ref_code: null,
      currency: null,
      operator_id: null,
      type: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/transactions/ftw/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get fiat transactions (waiting deposits) with pagination and filters
   */
  async getFiatTransactions(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      method: null,
      provider: null,
      username: null,
      currency: null,
      operator_id: null,
      type: 1, // Default to deposits (type 1)
      is_manuel: null,
      status_id: 1, // Default to waiting status (status_id 1)
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/transactions/fiats/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get casino debits with pagination and filters
   */
  async getCasinoDebits(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      game: null,
      provider_id: null,
      type: null,
      status: null,
      currency: null,
      wallet_currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      amount_min: null,
      amount_max: null,
      multiplier_min: null,
      multiplier_max: null,
      income_min: null,
      income_max: null,
      net_min: null,
      net_max: null,
      income_usd_min: null,
      income_usd_max: null,
      wallet_amount_min: null,
      wallet_amount_max: null,
      win_wallet_amount_min: null,
      win_wallet_amount_max: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/debits/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get sportsbook players with pagination
   */
  async getSportsbookPlayers(page: number = 1, limit: number = 20, customerId: string): Promise<any> {
    const body = {
      customer_id: customerId,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/players/sportsbook/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get sportsbook debits with pagination and filters
   */
  async getSportsbookDebits(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      username: null,
      game: null,
      provider_id: null,
      type: null,
      status: null,
      currency: null,
      wallet_currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      amount_min: null,
      amount_max: null,
      multiplier_min: null,
      multiplier_max: null,
      income_min: null,
      income_max: null,
      net_min: null,
      net_max: null,
      income_usd_min: null,
      income_usd_max: null,
      wallet_amount_min: null,
      wallet_amount_max: null,
      win_wallet_amount_min: null,
      win_wallet_amount_max: null,
      sortBy: null,
      direction: null,
      odd_min: null,
      odd_max: null,
      page,
      limit,
      customer_id: null,
      timezone: -3,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/sportsbook-debits/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get discounts summary
   */
  async getDiscountsSummary(filters?: any): Promise<any> {
    const body = {
      username: null,
      currency: null,
      from: null,
      to: null,
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(`https://service.ebetlab.com/api/operator/discounts/summary`, 'POST', body);
  }

  /**
   * Get discounts with pagination and filters
   */
  async getDiscounts(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      username: null,
      currency: null,
      from: null,
      to: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/discounts/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get customer-specific big win/lose debits from dashboard
   */
  async getCustomerBigWinLoseDebits(additionalParams?: Record<string, any>): Promise<any> {
    const body: Record<string, any> = {
      rt: (Date.now() / 1000).toFixed(),
      ...additionalParams,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/dashboard/debits/big-win-lose`,
      'POST',
      body,
    );
  }

  /**
   * Get customer-specific game win/lose debits from dashboard
   */
  async getCustomerGameWinLoseDebits(additionalParams?: Record<string, any>): Promise<any> {
    const body: Record<string, any> = {
      rt: (Date.now() / 1000).toFixed(),
      ...additionalParams,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/customers/dashboard/debits/game-win-lose`,
      'POST',
      body,
    );
  }

  /**
   * Get wallets with pagination for a specific customer
   */
  async getWallets(page: number = 1, limit: number = 20, customerId: string): Promise<any> {
    const body = {
      limit,
      page,
      customer_id: customerId,
      rt: Math.floor(Date.now() / 1000),
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/wallets/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get vaults with pagination and filters
   */
  async getVaults(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      sender: null,
      taker: null,
      currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/vaults/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get VIP state changes with pagination for a specific customer
   */
  async getVipStateChanges(page: number = 1, limit: number = 20, customerId: string): Promise<any> {
    const body = {
      limit,
      page,
      customer_id: customerId,
      rt: Math.floor(Date.now() / 1000),
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/vip-state-changes/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get player actions with pagination and filters
   */
  async getPlayerActions(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      timestamp_start: null,
      timestamp_end: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/player-actions/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get commits with pagination and filters
   */
  async getCommits(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      model: [
        'debit',
        'debit-trade',
        'bet-cancel',
        'bonus-drop',
        'boost',
        'campaign',
        'correction-up',
        'correction-up-cancel',
        'correction-down',
        'correction-down-cancel',
        'deposit',
        'discount',
        'freespin',
        'initiative-bonus',
        'rain',
        'rakeback',
        'reload',
        'rank-gift',
        'tip',
        'transaction',
        'transaction-cancel',
        'withdraw-cancel',
        'vault-withdraw',
        'vault-deposit',
        'welcome-bonus',
        'tournament-result',
        'race-result',
        'raffle-result',
        'wheel-ticket-prize',
        'unified-balance',
        'unify-reset',
      ],
      way: null,
      currency: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      amount_max: null,
      amount_min: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/commits/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get rakeback usages with pagination and filters
   */
  async getRakebackUsages(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      from: null,
      to: null,
      limit,
      page,
      customer_id: null,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/rakeback-usages/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get rakeback availables with pagination and filters
   */
  async getRakebackAvailables(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      limit,
      page,
      customer_id: null,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/rakeback-availables/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get tips with pagination and filters
   */
  async getTips(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      sender: null,
      taker: null,
      currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/tips/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get trade debits with pagination and filters
   */
  async getTradeDebits(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      game: null,
      provider_id: null,
      type: null,
      status: null,
      currency: null,
      wallet_currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      amount_min: null,
      amount_max: null,
      multiplier_min: null,
      multiplier_max: null,
      income_min: null,
      income_max: null,
      net_min: null,
      net_max: null,
      income_usd_min: null,
      income_usd_max: null,
      wallet_amount_min: null,
      wallet_amount_max: null,
      win_wallet_amount_min: null,
      win_wallet_amount_max: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/trade-debits/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get sessions with pagination and filters
   */
  async getSessions(page: number = 1, limit: number = 100, filters?: any): Promise<any> {
    const body = {
      from: null,
      to: null,
      customer_id: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/sessions/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get bonus redeems with pagination and filters
   */
  async getBonusRedeems(page: number = 1, limit: number = 50, filters?: any): Promise<any> {
    const body = {
      id: null,
      username: null,
      currency: null,
      operator_id: null,
      is_active: null,
      usd_min: null,
      usd_max: null,
      amount_min: null,
      amount_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      type: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/bonus-redeems/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Get balance corrections summary
   */
  async getBalanceCorrectionsSummary(username?: string | null): Promise<any> {
    const body = {
      username: username || null,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/balance-corrections/summary`,
      'POST',
      body,
    );
  }

  /**
   * Get balance corrections with pagination and filters
   */
  async getBalanceCorrections(page: number = 1, limit: number = 20, filters?: any): Promise<any> {
    const body = {
      id: null,
      username: null,
      model: null,
      currency: null,
      operator_id: null,
      way: null,
      note: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page,
      limit,
      rt: Math.floor(Date.now() / 1000),
      ...filters,
    };

    return this.makeAuthenticatedRequest(
      `https://service.ebetlab.com/api/operator/balance-corrections/index/${page}/${limit}`,
      'POST',
      body,
    );
  }

  /**
   * Generic method to call any EbetLab API endpoint
   */
  async callApi(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST', body?: any): Promise<any> {
    const url = endpoint.startsWith('http') ? endpoint : `${BASE_URL}${endpoint}`;

    return this.makeAuthenticatedRequest(url, method, body);
  }

  /**
   * Static method to perform login and return both auth data and login result
   */
  static async login(
    username: string,
    password: string,
    otp: string,
    secret: string = '',
    authData: AuthData | null = null,
  ): Promise<{ loginResult: any }> {
    if (secret === '') {
      console.log('🔐 Starting EbetLab login process...');

      // Step 1: Get cf_clearance
      console.log('📡 Getting cf_clearance...');
      const cf_clearance = await cfInit();
      console.log('✅ cf_clearance obtained');

      // Step 2: Calculate X-Fingerprint
      console.log('🔍 Calculating X-Fingerprint...');
      const x_fingerprint = calculateXFingerprint();
      console.log('✅ X-Fingerprint calculated');

      // Step 4: Perform login
      console.log('🚀 Performing login...');
      const agentInfo: AgentInfo = {
        browserName: 'Chrome',
        isMobile: false,
        isTablet: false,
        isBrowser: true,
        browserVersion: '136',
        osVersion: '10.15.7',
        engineVersion: '136.0.0.0',
        osName: 'Mac OS',
        engineName: 'Blink',
      };

      const payload: EbetLabLoginPayload = {
        email: username,
        password: password,
        agent: agentInfo,
        otp: '',
        sms: '',
        secret: '',
        fingerprint: {},
        rt: Number((Date.now() / 1000).toFixed()),
      };
      console.log('Pre-auth:', payload);

      const response = await fetch('https://service.ebetlab.com/api/operator/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-fingerprint': x_fingerprint,
          'User-Agent': USER_AGENT,
          Host: 'service.ebetlab.com',
          Referer: REFERER,
          Origin: ORIGIN,
          Cookie: `cf_clearance=${cf_clearance}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to login: ${await response.text()}`);
      }

      const data = (await response.json()) as any;

      if (data.data) {
        if (data.data.type === '2factor') {
          return this.login(username, password, otp, data.data.secret, {
            cf_clearance,
            x_fingerprint,
            signed_challenge: '',
          });
        } else {
          return {
            loginResult: data.data,
          };
        }
      } else if (data.errors) {
        throw new Error(`Failed to login: ${data.errors}`);
      } else {
        throw new Error(`Failed to login: unexpected response body: ${JSON.stringify(data)}`);
      }
    } else {
      console.log('🔐 Starting EbetLab login process...');

      if (!authData) {
        throw new Error('Failed to login: missing auth data');
      }

      // Step 1: Get cf_clearance
      console.log('📡 Getting cf_clearance...');
      const cf_clearance = await cfInit();
      console.log('✅ cf_clearance obtained');

      // Step 2: Calculate X-Fingerprint
      console.log('🔍 Calculating X-Fingerprint...');
      const x_fingerprint = calculateXFingerprint();
      console.log('✅ X-Fingerprint calculated');

      // Step 4: Perform login
      console.log('🚀 Performing login...');
      const agentInfo: AgentInfo = {
        browserName: 'Chrome',
        isMobile: false,
        isTablet: false,
        isBrowser: true,
        browserVersion: '136',
        osVersion: '10.15.7',
        engineVersion: '136.0.0.0',
        osName: 'Mac OS',
        engineName: 'Blink',
      };

      const payload: EbetLabLoginPayload = {
        email: username,
        password: password,
        agent: agentInfo,
        otp: otp,
        sms: '',
        fingerprint: {},
        secret: secret,
        rt: Number((Date.now() / 1000).toFixed()),
      };

      // Required!
      await new Promise((res) => setTimeout(res, 3 * 1000));

      const response = await fetch('https://service.ebetlab.com/api/operator/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-fingerprint': x_fingerprint,
          'User-Agent': USER_AGENT,
          Host: 'service.ebetlab.com',
          Referer: REFERER,
          Origin: ORIGIN,
          Cookie: `cf_clearance=${cf_clearance}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const text = await response.text();
        throw new Error(`Failed to login: ${text}`);
      }

      const data = (await response.json()) as any;

      if (data.data) {
        console.log('✅ Login successful');
        return {
          loginResult: data.data,
        };
      } else if (data.errors) {
        throw new Error(`Failed to login: ${data.errors}`);
      } else {
        throw new Error(`Failed to login: unexpected response body: ${JSON.stringify(data)}`);
      }
    }
  }
}

/**
 * Factory function to create EbetLabService instance with caching
 */
export const createEbetLabService = async (authorization: string): Promise<EbetLabService> => {
  let authData = cache.get('ebetlab:auth_data') as AuthData;
  if (!authData) {
    // Step 1: Get cf_clearance
    console.log('📡 Getting cf_clearance...');
    const cf_clearance = await cfInit();
    console.log('✅ cf_clearance obtained');

    // Step 2: Calculate X-Fingerprint
    console.log('🔍 Calculating X-Fingerprint...');
    const x_fingerprint = calculateXFingerprint();
    console.log('✅ X-Fingerprint calculated');

    // Step 3: Get signed_challenge
    console.log('🔑 Getting signed_challenge...');
    const signed_challenge = await getSignedChallenge(cf_clearance, x_fingerprint);
    console.log('✅ signed_challenge obtained');

    authData = { cf_clearance, x_fingerprint, signed_challenge };
    cache.set('ebetlab:auth_data', authData);
  }

  const [, token] = authorization.split(' ');

  return new EbetLabService(authData, token || '');
};

/**
 * Factory function to create EbetLabService instance directly with auth data
 */
export const createEbetLabServiceWithAuth = (authData: AuthData, authorization: string): EbetLabService => {
  const [, token] = authorization.split(' ');

  return new EbetLabService(authData, token || '');
};
