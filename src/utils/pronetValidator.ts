import { generatePronetChecksum, minifyJsonObject, validatePronetConfig } from './pronetAuth';

/**
 * Pronet System Validator
 * 
 * This module provides validation and testing utilities for the Pronet API system,
 * including checksum testing, system validation, and sample request generation.
 */

/**
 * Test result interface
 */
export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * System validation result interface
 */
export interface SystemValidationResult {
  success: boolean;
  message: string;
  details: {
    configValid: boolean;
    checksumTest: TestResult;
    environmentVariables: {
      PRONET_HOST: boolean;
      PUBLIC_API_USERNAME: boolean;
      PRONET_API_KEY: boolean;
    };
  };
}

/**
 * Test Pronet checksum generation functionality
 * 
 * @returns Test result with details
 */
export const testPronetChecksum = (): TestResult => {
  try {
    console.log('🧪 Testing Pronet checksum generation...');

    // Test data
    const testBody = {
      test: 'data',
      number: 123,
      nested: {
        value: 'test',
      },
    };
    const testSecret = 'test-secret-key';

    // Test minification
    const minified = minifyJsonObject(testBody);
    console.log('📝 Test body minified:', minified);

    // Test checksum generation
    const checksum = generatePronetChecksum(minified, testSecret);
    console.log('🔐 Test checksum generated:', checksum);

    // Verify checksum is base64 encoded
    const isBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(checksum);
    if (!isBase64) {
      throw new Error('Generated checksum is not valid base64');
    }

    // Verify checksum length (SHA-512 base64 should be 88 characters)
    if (checksum.length !== 88) {
      console.warn(`⚠️ Unexpected checksum length: ${checksum.length} (expected 88)`);
    }

    console.log('✅ Pronet checksum test passed');

    return {
      success: true,
      message: 'Pronet checksum generation test passed',
      details: {
        testBody,
        minifiedBody: minified,
        checksum,
        checksumLength: checksum.length,
        isValidBase64: isBase64,
      },
    };
  } catch (error) {
    console.error('❌ Pronet checksum test failed:', error);
    return {
      success: false,
      message: `Pronet checksum test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};

/**
 * Validate the entire Pronet system setup
 * 
 * @returns System validation result with detailed information
 */
export const validatePronetSystem = (): SystemValidationResult => {
  try {
    console.log('🔍 Validating Pronet system setup...');

    const results = {
      configValid: false,
      checksumTest: {
        success: false,
        message: '',
      },
      environmentVariables: {
        PRONET_HOST: !!process.env['PRONET_HOST'],
        PUBLIC_API_USERNAME: !!process.env['PUBLIC_API_USERNAME'],
        PRONET_API_KEY: !!process.env['PRONET_API_KEY'],
      },
    };

    // Test configuration
    results.configValid = validatePronetConfig();

    // Test checksum generation
    results.checksumTest = testPronetChecksum();

    const allValid = results.configValid && results.checksumTest.success;

    console.log(allValid ? '✅ Pronet system validation passed' : '❌ Pronet system validation failed');

    return {
      success: allValid,
      message: allValid
        ? 'Pronet system is properly configured and functional'
        : 'Pronet system has configuration or functionality issues',
      details: results,
    };
  } catch (error) {
    console.error('❌ Pronet system validation failed:', error);
    return {
      success: false,
      message: `Pronet system validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: {
        configValid: false,
        checksumTest: {
          success: false,
          message: 'Validation failed',
        },
        environmentVariables: {
          PRONET_HOST: !!process.env['PRONET_HOST'],
          PUBLIC_API_USERNAME: !!process.env['PUBLIC_API_USERNAME'],
          PRONET_API_KEY: !!process.env['PRONET_API_KEY'],
        },
        error: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
};

/**
 * Sample request interface
 */
export interface SamplePronetRequest {
  success: boolean;
  message: string;
  details?: {
    url: string;
    method: string;
    headers: {
      'Content-Type': string;
      checksum: string;
    };
    body: string;
    credentials: {
      username: string;
      host: string;
      hasApiKey: boolean;
    };
  };
}

/**
 * Generate a sample Pronet API request for testing
 * 
 * @param endpoint - API endpoint to generate request for
 * @param body - Request body object (default: empty object)
 * @returns Sample request details
 */
export const generateSamplePronetRequest = (endpoint: string, body: any = {}): SamplePronetRequest => {
  try {
    console.log('📋 Generating sample Pronet request...');

    if (!validatePronetConfig()) {
      throw new Error('Pronet configuration is invalid');
    }

    const pronetHost = process.env['PRONET_HOST']!;
    const username = process.env['PUBLIC_API_USERNAME']!;
    const apiKey = process.env['PRONET_API_KEY']!;

    const fullUrl = `${pronetHost}/api/pronet/v1/${endpoint.startsWith('/') ? endpoint.slice(1) : endpoint}`;
    const minifiedBody = minifyJsonObject(body);
    const checksum = generatePronetChecksum(minifiedBody, apiKey);

    const sampleRequest = {
      url: fullUrl,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        checksum: checksum,
      },
      body: minifiedBody,
      credentials: {
        username,
        host: pronetHost,
        hasApiKey: !!apiKey,
      },
    };

    console.log('✅ Sample Pronet request generated');

    return {
      success: true,
      message: 'Sample Pronet request generated successfully',
      details: sampleRequest,
    };
  } catch (error) {
    console.error('❌ Failed to generate sample Pronet request:', error);
    return {
      success: false,
      message: `Failed to generate sample request: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};
