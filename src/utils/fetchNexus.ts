import fetch, { Response } from 'node-fetch';
import { cache } from './cache';

interface NexusCookies {
  __nxqsec?: string;
  [key: string]: string | undefined;
}

interface CachedNexusData {
  cookies: NexusCookies;
  timestamp: number;
  domain: string;
}

// Cache duration: 30 minutes (cookies typically last longer but we refresh proactively)
const CACHE_DURATION = 30 * 60 * 1000;

/**
 * Extract domain from URL
 */
const extractDomain = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return 'makrobet685.com'; // fallback
  }
};

/**
 * Check if a URL belongs to a makrobet domain
 */
export const isMakrobetDomain = (url: string): boolean => {
  try {
    const domain = extractDomain(url);
    return domain.includes('makrobet');
  } catch {
    return false;
  }
};

/**
 * Get NEXUS cookies for a specific domain using CT API client pattern
 */
const getNexusCookies = async (domain: string): Promise<NexusCookies> => {
  const cacheKey = `nexus:cookies:${domain}`;
  const cached = cache.get(cacheKey) as CachedNexusData;

  // Check if we have valid cached cookies
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    console.log(`🔄 Using cached NEXUS cookies for ${domain}`);
    return cached.cookies;
  }

  console.log(`🔍 Fetching NEXUS cookies for ${domain} using CT pattern`);

  try {
    let nxquid: string | null = null;
    let nxqsec: string | null = null;

    // Step 1: Initial request with manual redirect handling (like CT client)
    const mainPageUrl = `https://${domain}`;
    console.log(`📡 Making initial request to: ${mainPageUrl}`);

    let response = await fetch(mainPageUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      },
      redirect: 'manual', // Handle redirects manually like CT client
    });

    // Step 2: Handle 302 redirect to get __nxquid (like CT client)
    if (response.status === 302) {
      console.log(`🔄 Got 302 redirect, extracting __nxquid cookie`);

      let setCookieHeaders = response.headers.raw()['set-cookie'];
      if (!setCookieHeaders || setCookieHeaders.length === 0) {
        throw new Error('No set-cookie headers found in redirect');
      }

      const nxquidHeader = setCookieHeaders.find((header) => header.includes('__nxquid'));
      if (!nxquidHeader) {
        throw new Error('__nxquid cookie not found in redirect');
      }
      nxquid = nxquidHeader.split(';')[0]?.split('=').slice(1).join('=') || '';
      console.log(`🔑 Extracted __nxquid: ${nxquid}`);

      // Step 3: Second request with __nxquid to get __nxqsec
      console.log(`📡 Making second request with __nxquid`);
      response = await fetch(mainPageUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          Cookie: `__nxquid=${nxquid}`,
        },
      });

      setCookieHeaders = response.headers.raw()['set-cookie'];
      if (setCookieHeaders && setCookieHeaders.length > 0) {
        const nxqsecHeader = setCookieHeaders.find((header) => header.includes('__nxqsec'));
        if (nxqsecHeader) {
          nxqsec = nxqsecHeader.split(';')[0]?.split('=').slice(1).join('=') || '';
          console.log(`🔑 Extracted __nxqsec: ${nxqsec}`);
        }
      }
    } else if (!response.ok) {
      throw new Error(`Failed to fetch main page: ${response.status} ${response.statusText}`);
    }

    // Step 4: Check for nexusguard challenge (like CT client)
    let data = await response.text();
    if (data.includes('nexusguard')) {
      console.log(`🛡️ Detected nexusguard challenge, extracting vcode`);

      // Look for vcode in the response (similar to CT client pattern)
      const vcodeMatch = data.match(/\?vcode=([^"'&]+)/);
      if (!vcodeMatch || !vcodeMatch[1]) {
        throw new Error('Could not find vcode in nexusguard challenge');
      }

      const vcode = vcodeMatch[1];
      console.log(`🔑 Found vcode: ${vcode}`);

      // Step 5: Request with vcode and existing cookies
      const vcodeUrl = `https://${domain}/?vcode=${vcode}`;
      console.log(`🍪 Making vcode request to: ${vcodeUrl}`);

      response = await fetch(vcodeUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          Cookie: nxquid && nxqsec ? `__nxquid=${nxquid}; __nxqsec=${nxqsec}` : (nxquid ? `__nxquid=${nxquid}` : ''),
        },
        redirect: 'manual',
      });

      // Handle potential redirect after vcode
      if (response.status === 302) {
        const setCookieHeaders = response.headers.raw()['set-cookie'];
        if (setCookieHeaders && setCookieHeaders.length > 0) {
          const nxqsecHeader = setCookieHeaders.find((header) => header.includes('__nxqsec'));
          if (nxqsecHeader) {
            nxqsec = nxqsecHeader.split(';')[0]?.split('=').slice(1).join('=') || '';
            console.log(`🔑 Updated __nxqsec from vcode redirect: ${nxqsec}`);
          }
        }

        // Final request to get the actual page
        response = await fetch(mainPageUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            Cookie: `__nxquid=${nxquid}; __nxqsec=${nxqsec}`,
          },
        });
      }

      if (!response.ok) {
        throw new Error(`Failed to complete nexusguard challenge: ${response.status} ${response.statusText}`);
      }
    }

    // Step 6: Extract all cookies from final response
    const finalSetCookieHeaders = response.headers.raw()['set-cookie'];
    const cookies: NexusCookies = {};

    // Add the NEXUS cookies we collected
    if (nxquid) cookies['__nxquid'] = nxquid;
    if (nxqsec) cookies['__nxqsec'] = nxqsec;

    // Extract any additional cookies from the final response
    if (finalSetCookieHeaders && finalSetCookieHeaders.length > 0) {
      finalSetCookieHeaders.forEach(header => {
        const cookiePair = header.split(';')[0];
        if (cookiePair) {
          const [name, value] = cookiePair.split('=');
          if (name && value) {
            cookies[name.trim()] = value.trim();
          }
        }
      });
    }

    console.log(`✅ Successfully extracted cookies for ${domain}:`, Object.keys(cookies));

    // Cache the cookies
    const cacheData: CachedNexusData = {
      cookies,
      timestamp: Date.now(),
      domain,
    };
    cache.set(cacheKey, cacheData);

    return cookies;
  } catch (error) {
    console.error(`❌ Failed to get NEXUS cookies for ${domain}:`, error);
    throw error;
  }
};

/**
 * Wrapper for the fetch function to comply with the NEXUS anti-DDOS protection
 * Returns the raw Response object, letting the caller decide how to parse it
 */
export const fetchNexus = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const domain = extractDomain(url);

  try {
    // Get NEXUS cookies for the domain
    const nexusCookies = await getNexusCookies(domain);

    // Build cookie string from the NEXUS cookies
    const cookieStrings: string[] = [];
    Object.entries(nexusCookies).forEach(([name, value]) => {
      if (value) {
        cookieStrings.push(`${name}=${value}`);
      }
    });

    // Merge with existing cookies from options
    let existingCookies = '';
    if (options.headers) {
      // Handle different header types
      if (options.headers instanceof Headers) {
        existingCookies = options.headers.get('Cookie') || options.headers.get('cookie') || '';
      } else if (Array.isArray(options.headers)) {
        // Handle array format [['Cookie', 'value']]
        const cookieHeader = options.headers.find(([key]) =>
          key && key.toLowerCase() === 'cookie'
        );
        existingCookies = cookieHeader ? (cookieHeader[1] || '') : '';
      } else {
        // Handle object format { Cookie: 'value' }
        const headersObj = options.headers as Record<string, string>;
        existingCookies = headersObj['Cookie'] || headersObj['cookie'] || '';
      }
    }

    const allCookies = existingCookies
      ? `${existingCookies}; ${cookieStrings.join('; ')}`
      : cookieStrings.join('; ');

    // Prepare headers with NEXUS cookies
    const headers: Record<string, string> = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      Cookie: allCookies,
    };

    // Merge with existing headers, but ensure we override Cookie
    if (options.headers) {
      if (options.headers instanceof Headers) {
        options.headers.forEach((value, key) => {
          if (key.toLowerCase() !== 'cookie') {
            headers[key] = value;
          }
        });
      } else if (Array.isArray(options.headers)) {
        options.headers.forEach(([key, value]) => {
          if (key && key.toLowerCase() !== 'cookie') {
            headers[key] = value || '';
          }
        });
      } else {
        const headersObj = options.headers as Record<string, string>;
        Object.entries(headersObj).forEach(([key, value]) => {
          if (key.toLowerCase() !== 'cookie') {
            headers[key] = value;
          }
        });
      }
    }

    console.log(`🚀 Making NEXUS-protected request to: ${url}`);
    console.log(`🍪 Using cookies: ${allCookies}`);

    // Make the actual request with NEXUS cookies
    const response = await fetch(url, {
      ...options,
      headers,
    } as any);

    if (!response.ok) {
      throw new Error(`Request failed: ${response.status} ${response.statusText}`);
    }

    // Return the raw response, let the caller decide how to parse it
    return response;
  } catch (error) {
    console.error(`❌ fetchNexus failed for ${url}:`, error);
    throw error;
  }
};
