import jwt from 'jsonwebtoken';

/**
 * Interface for the decoded JWT token payload from Betroz
 */
export interface BetrозJwtPayload {
  iss: string;
  iat: number;
  exp: number;
  nbf: number;
  jti: string;
  sub: string; // User ID
  prv: string;
  website_id: number;
}

/**
 * Decode a JWT token without verification (since we don't have the secret)
 * This is safe for extracting user information from trusted tokens
 */
export const decodeJwtToken = (token: string): BetrозJwtPayload | null => {
  try {
    // Remove 'Bearer ' prefix if present
    const cleanToken = token.replace(/^Bearer\s+/i, '');
    
    // Decode without verification (we don't have the secret key)
    const decoded = jwt.decode(cleanToken) as BetrозJwtPayload;
    
    if (!decoded || typeof decoded !== 'object') {
      return null;
    }
    
    // Validate that required fields are present
    if (!decoded.sub || !decoded.iss || !decoded.exp) {
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
};

/**
 * Extract user ID from JWT token
 */
export const extractUserIdFromToken = (token: string): number | null => {
  const decoded = decodeJwtToken(token);
  
  if (!decoded || !decoded.sub) {
    return null;
  }
  
  const userId = parseInt(decoded.sub, 10);
  return isNaN(userId) ? null : userId;
};

/**
 * Check if JWT token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const decoded = decodeJwtToken(token);
  
  if (!decoded || !decoded.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return decoded.exp < currentTime;
};

/**
 * Validate JWT token structure and expiration
 */
export const validateJwtToken = (token: string): { valid: boolean; reason?: string } => {
  if (!token) {
    return { valid: false, reason: 'Token is required' };
  }
  
  const decoded = decodeJwtToken(token);
  
  if (!decoded) {
    return { valid: false, reason: 'Invalid token format' };
  }
  
  if (isTokenExpired(token)) {
    return { valid: false, reason: 'Token has expired' };
  }
  
  return { valid: true };
};
