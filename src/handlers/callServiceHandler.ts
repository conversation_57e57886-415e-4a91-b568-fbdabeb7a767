import { Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ler<PERSON>til<PERSON> } from './index';
import { CallServiceValidator, CallServiceRequest } from '../validators/callServiceValidator';
import fetch from 'node-fetch';
import { fetchNexus, isMakrobetDomain } from '../utils/fetchNexus';

/**
 * Customer Data Interface
 * 
 * Defines the expected structure of customer data from getCurrentCustomer endpoint
 */
interface CustomerData {
  success: boolean;
  data: {
    customerId: number;
    firstName: string;
    surname: string;
    username: string;
    phone: string;
    balance: number;
    lockedBalance: number;
    vip: boolean;
  };
}

/**
 * Call Service Response Interface
 * 
 * Defines the expected structure of call service demand responses
 */
interface CallServiceResponse {
  err: boolean;
  message?: string;
  data?: {
    customer_id: number;
    promotion_id: number;
    promotion_type: number;
    status: number;
    operator: number;
    date: number;
    id: number;
  };
}

/**
 * Call Service Handler
 *
 * Handles call service demand requests by:
 * 1. Proxying the request to the third-party endpoint (pn17.pfnow.net)
 * 2. If successful and not an error response, fetching customer data from dynamic origin
 * 3. Formatting and returning a Slack message with customer information
 *
 * Features:
 * - Dynamic origin detection from request headers (e.g., makrobet683.com, makrobet685.com)
 * - SSL certificate bypass for both endpoints
 * - Comprehensive error handling and logging
 * - Skips notifications for error responses (pending_demand, not_allowed_action, etc.)
 */
export class CallServiceHandler extends BaseHandler {
  private static readonly TARGET_URL = 'https://pn17.pfnow.net/api/callService/demand';
  private static readonly CUSTOMER_ENDPOINT_PATH = '/odin/api/user/getCurrentCustomer';

  /**
   * Handle call service demand request
   *
   * @param req - Express request object
   * @param res - Express response object (not used for response, only for logging)
   * @returns Handler result with Slack message or skip reason
   */
  static async handle(req: Request, res: Response): Promise<HandlerResult> {
    try {
      HandlerUtils.logActivity('CallServiceHandler', 'Processing call service demand request');

      // Extract validated call service data
      const callServiceData = CallServiceValidator.extractCallServiceData(req);

      // Step 1: Proxy the request to the third-party endpoint
      const proxyResult = await this.proxyCallServiceRequest(callServiceData);

      if (!proxyResult.success) {
        HandlerUtils.logError('CallServiceHandler', `Proxy request failed: ${proxyResult.error}`);
        return this.error(`Failed to proxy call service request: ${proxyResult.error}`);
      }

      // Get the original response data and status (always return this to client)
      const originalResponse = proxyResult.data;
      const originalStatusCode = proxyResult.statusCode || 200;

      // Step 2: Check if the response indicates we should skip Slack notification
      const shouldSkip = this.shouldSkipNotification(proxyResult.data!);

      if (shouldSkip.skip) {
        HandlerUtils.logActivity('CallServiceHandler', `Skipping notification: ${shouldSkip.reason}`);
        return this.skip(shouldSkip.reason || 'Unknown reason', originalResponse, originalStatusCode);
      }

      // Step 3: Fetch customer data
      const customerData = await this.fetchCustomerData(callServiceData.token, req);

      if (!customerData.success) {
        HandlerUtils.logError('CallServiceHandler', `Failed to fetch customer data: ${customerData.error}`);
        // Still return original response, but skip Slack notification
        return this.skip(`Failed to fetch customer data: ${customerData.error}`, originalResponse, originalStatusCode);
      }

      // Step 4: Format Slack message
      const message = this.formatSlackMessage(customerData.data!, callServiceData);

      HandlerUtils.logActivity('CallServiceHandler', 'Successfully generated Slack message');
      return this.success(message, originalResponse, originalStatusCode);

    } catch (error) {
      HandlerUtils.logError('CallServiceHandler', error instanceof Error ? error : 'Unknown error');
      return this.error(`Handler error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Proxy the call service request to the third-party endpoint
   */
  private static async proxyCallServiceRequest(data: CallServiceRequest): Promise<{
    success: boolean;
    data?: CallServiceResponse;
    error?: string;
    statusCode?: number;
  }> {
    HandlerUtils.logActivity('CallServiceHandler', `Proxying request to ${this.TARGET_URL}`);

    try {
      console.log('📤 Sending POST request to:', this.TARGET_URL);
      console.log('📤 Request body:', JSON.stringify(data, null, 2));

      // Create agent that ignores SSL certificate errors (like original code)
      const https = require('https');
      const agent = new https.Agent({
        rejectUnauthorized: false
      });

      // Use node-fetch directly like the original working code
      const response = await fetch(this.TARGET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        agent: agent, // Disable SSL verification
      });

      console.log(`📥 Response Status: ${response.status}`);
      console.log(`📥 Response Status Text: ${response.statusText}`);

      if (!response.ok) {
        // Log the error response body for debugging
        const errorText = await response.text();
        console.log('❌ Error response body:', errorText);
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText} - ${errorText}`,
          statusCode: response.status,
        };
      }

      // Parse response
      const responseData = await response.json();

      console.log('📥 Response data:', JSON.stringify(responseData, null, 2));

      return {
        success: true,
        data: responseData as CallServiceResponse,
        statusCode: response.status,
      };

    } catch (error) {
      console.error('❌ Proxy request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Check if we should skip Slack notification based on response
   */
  private static shouldSkipNotification(response: CallServiceResponse): {
    skip: boolean;
    reason?: string;
  } {
    // Skip if response has error flag set to true
    if (response.err === true) {
      const message = response.message || 'unknown error';
      return {
        skip: true,
        reason: `Call service returned error: ${message}`,
      };
    }

    return { skip: false };
  }

  /**
   * Fetch customer data from internal endpoint
   */
  private static async fetchCustomerData(token: string, req: Request): Promise<{
    success: boolean;
    data?: CustomerData;
    error?: string;
  }> {
    const cleanToken = HandlerUtils.cleanToken(token);

    HandlerUtils.logActivity('CallServiceHandler', `Fetching customer data for token: ${cleanToken}`);

    // Get origin from request headers and build URLs
    const origin = req.headers.origin || 'https://makrobet683.com';
    const refererUrl = `${origin}/en`;
    const customerEndpointUrl = `${origin}${this.CUSTOMER_ENDPOINT_PATH}`;

    HandlerUtils.logActivity('CallServiceHandler', `Using referer URL: ${refererUrl}`);
    HandlerUtils.logActivity('CallServiceHandler', `Using customer endpoint: ${customerEndpointUrl}`);

    try {
      let customerResponse: any;

      // Check if this is a makrobet domain and use appropriate fetch method
      if (isMakrobetDomain(customerEndpointUrl)) {
        HandlerUtils.logActivity('CallServiceHandler', 'Using fetchNexus for makrobet domain');

        // Use fetchNexus for makrobet domains (it handles NEXUS anti-DDOS protection)
        customerResponse = await fetchNexus(customerEndpointUrl, {
          method: 'GET',
          headers: {
            's7oryo9stv': cleanToken,
            'referer': refererUrl,
            // fetchNexus already sets User-Agent, but we can override if needed
          },
        });
      } else {
        HandlerUtils.logActivity('CallServiceHandler', 'Using regular fetch for non-makrobet domain');

        // Create agent that ignores SSL certificate errors (like original code)
        const https = require('https');
        const agent = new https.Agent({
          rejectUnauthorized: false
        });

        // Use node-fetch directly like the original working code
        customerResponse = await fetch(customerEndpointUrl, {
          method: 'GET',
          headers: {
            's7oryo9stv': cleanToken,
            'referer': refererUrl,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          },
          agent: agent, // Use same SSL bypass agent
        });
      }

      console.log(`📊 Customer endpoint response status: ${customerResponse.status}`);

      if (!customerResponse.ok) {
        const errorText = await customerResponse.text();
        console.log('⚠️ Customer endpoint error:', errorText);
        return {
          success: false,
          error: `Customer endpoint error: ${customerResponse.status} ${customerResponse.statusText}`,
        };
      }

      const customerData = await customerResponse.json();

      console.log('👤 Customer data retrieved:', JSON.stringify(customerData, null, 2));

      // Validate customer data structure
      const isValidStructure = HandlerUtils.validateResponseStructure(customerData, [
        'success',
        'data.customerId',
        'data.firstName',
        'data.surname',
        'data.username',
        'data.phone',
        'data.balance',
        'data.lockedBalance',
        'data.vip'
      ]);

      if (!isValidStructure) {
        return {
          success: false,
          error: 'Invalid customer data structure received',
        };
      }

      return {
        success: true,
        data: customerData as CustomerData,
      };

    } catch (error) {
      console.error('❌ Failed to fetch customer data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Format Slack message with customer information
   */
  private static formatSlackMessage(customerData: CustomerData, callServiceData: CallServiceRequest): string {
    const customer = customerData.data;

    // Format the message according to specification
    const title = this.bold(`🔔 ${callServiceData.time} için Çağrı Hizmeti Talebi`);

    const sections = [
      {
        title: this.bold('👤 Müşteri Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'İsim', value: `${customer.firstName} ${customer.surname}` },
          { key: 'Kullanıcı Adı', value: customer.username },
          { key: 'Kullanıcı ID', value: customer.customerId },
        ]),
      },
      {
        title: this.bold('💰 Bakiye Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'Bakiye', value: this.formatCurrency(customer.balance) },
          { key: 'Kilitli Bakiye', value: this.formatCurrency(customer.lockedBalance) },
          { key: 'VIP', value: this.formatBoolean(customer.vip) },
        ]),
      },
    ];

    return this.formatSectionedMessage(title, sections);
  }
}

/**
 * Exported handler function for use with registerSlackBot
 */
export const callServiceHandler = async (req: Request, res: Response): Promise<HandlerResult> => {
  return CallServiceHandler.handle(req, res);
};
