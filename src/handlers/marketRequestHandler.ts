import { Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON>, HandlerResult, HandlerUtils } from './index';
import { MarketRequestValidator, MarketRequest, MarketRequestResponse } from '../validators/marketRequestValidator';
import fetch from 'node-fetch';
import { fetchNexus, isMakrobetDomain } from '../utils/fetchNexus';

/**
 * Customer Data Interface (reused from existing handlers)
 */
interface CustomerData {
  data: {
    customerId: string;
    username: string;
    firstName: string;
    surname: string;
    balance: number;
    lockedBalance: number;
    vip: boolean;
    [key: string]: any;
  };
}

/**
 * Market Request Handler
 *
 * Handles market requests by:
 * 1. Proxying the request to the third-party endpoint (pn17.pfnow.net)
 * 2. Always sending Slack notification (unlike call service bot that skips on errors)
 * 3. Using token to fetch customer data from dynamic origin
 * 4. Formatting and returning a Slack message with customer information and market product ID
 *
 * Features:
 * - Dynamic origin detection from request headers (e.g., makrobet683.com, makrobet685.com)
 * - SSL certificate bypass for both endpoints
 * - Comprehensive error handling and logging
 * - Always sends notifications (similar to bonus request bot)
 */
export class MarketRequestHandler extends BaseHandler {
  private static readonly TARGET_URL = 'https://pn17.pfnow.net/api/giftshop/order';
  private static readonly CUSTOMER_ENDPOINT_PATH = '/odin/api/user/getCurrentCustomer';

  /**
   * Handle market request
   *
   * @param req - Express request object
   * @param res - Express response object (not used for response, only for logging)
   * @returns Handler result with Slack message or skip reason
   */
  static async handle(req: Request, res: Response): Promise<HandlerResult> {
    try {
      HandlerUtils.logActivity('MarketRequestHandler', 'Processing market request');

      // Extract validated market request data
      const marketRequestData = MarketRequestValidator.extractMarketRequestData(req);

      // Step 1: Proxy the request to the third-party endpoint
      const proxyResult = await this.proxyMarketRequest(marketRequestData);

      if (!proxyResult.success) {
        HandlerUtils.logError('MarketRequestHandler', `Proxy request failed: ${proxyResult.error}`);
        return this.error(`Failed to proxy market request: ${proxyResult.error}`);
      }

      // Get the original response data and status (always return this to client)
      const originalResponse = proxyResult.data;
      const originalStatusCode = proxyResult.statusCode || 200;

      // Step 2: Check if we should skip Slack notification (but market requests always send)
      const shouldSkip = this.shouldSkipNotification(proxyResult.data!);

      if (shouldSkip.skip) {
        HandlerUtils.logActivity('MarketRequestHandler', `Skipping notification: ${shouldSkip.reason}`);
        return this.skip(shouldSkip.reason || 'Unknown reason', originalResponse, originalStatusCode);
      }

      // Market requests should always notify, even on errors
      HandlerUtils.logActivity('MarketRequestHandler', 'Market request processed, preparing Slack notification');

      // Step 3: Fetch customer data using token
      let customerData: CustomerData | null = null;

      if (marketRequestData.token && marketRequestData.token.trim() !== '') {
        HandlerUtils.logActivity('MarketRequestHandler', `Token available (${marketRequestData.token.length} chars), fetching customer data...`);
        const customerResult = await this.fetchCustomerData(marketRequestData.token, req);
        if (customerResult.success) {
          customerData = customerResult.data!;
          HandlerUtils.logActivity('MarketRequestHandler', `Customer data fetched successfully for user: ${customerData.data.username}`);
        } else {
          HandlerUtils.logError('MarketRequestHandler', `Failed to fetch customer data: ${customerResult.error}`);
          // Continue without customer data - will show basic message
        }
      } else {
        HandlerUtils.logActivity('MarketRequestHandler', `No valid token provided (token: ${marketRequestData.token}), skipping customer data fetch`);
      }

      // Step 4: Format Slack message
      const message = this.formatSlackMessage(marketRequestData, customerData, originalResponse);

      HandlerUtils.logActivity('MarketRequestHandler', 'Successfully generated Slack message');
      return this.success(message, originalResponse, originalStatusCode);

    } catch (error) {
      HandlerUtils.logError('MarketRequestHandler', error instanceof Error ? error : 'Unknown error');
      return this.error(`Handler error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Proxy the market request to the third-party endpoint
   */
  private static async proxyMarketRequest(data: MarketRequest): Promise<{
    success: boolean;
    data?: MarketRequestResponse;
    error?: string;
    statusCode?: number;
  }> {
    HandlerUtils.logActivity('MarketRequestHandler', `Proxying request to ${this.TARGET_URL}`);

    try {
      console.log('📤 Proxy data:', JSON.stringify(data, null, 2));

      // Create agent that ignores SSL certificate errors (like bonus request handler)
      const https = require('https');
      const agent = new https.Agent({
        rejectUnauthorized: false
      });

      // Use node-fetch directly like the bonus request handler
      const response = await fetch(this.TARGET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        agent: agent, // Disable SSL verification
      } as any);

      console.log(`📥 Response Status: ${response.status}`);

      // Parse response
      const responseData = await response.json();

      console.log('📥 Response data:', JSON.stringify(responseData, null, 2));

      return {
        success: true,
        data: responseData as MarketRequestResponse,
        statusCode: response.status,
      };

    } catch (error) {
      console.error('❌ Proxy request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Check if we should skip Slack notification based on response
   * For market requests, we always send notifications (unlike other bots)
   */
  private static shouldSkipNotification(_response: MarketRequestResponse): {
    skip: boolean;
    reason?: string;
  } {
    // Market requests always send notifications, regardless of response
    // This method exists for consistency with other handlers
    return { skip: false };
  }

  /**
   * Fetch customer data from internal endpoint
   */
  private static async fetchCustomerData(token: string, req: Request): Promise<{
    success: boolean;
    data?: CustomerData;
    error?: string;
  }> {
    const cleanToken = HandlerUtils.cleanToken(token);

    HandlerUtils.logActivity('MarketRequestHandler', `Fetching customer data for token: ${cleanToken}`);

    // Get origin from request headers and build URLs
    const origin = req.headers.origin || 'https://makrobet683.com';
    const refererUrl = `${origin}/en`;
    const customerEndpointUrl = `${origin}${this.CUSTOMER_ENDPOINT_PATH}`;

    HandlerUtils.logActivity('MarketRequestHandler', `Using referer URL: ${refererUrl}`);
    HandlerUtils.logActivity('MarketRequestHandler', `Using customer endpoint: ${customerEndpointUrl}`);

    try {
      let customerResponse: any;

      // Check if this is a makrobet domain and use appropriate fetch method
      if (isMakrobetDomain(customerEndpointUrl)) {
        HandlerUtils.logActivity('MarketRequestHandler', 'Using fetchNexus for makrobet domain');

        // Use fetchNexus for makrobet domains (it handles NEXUS anti-DDOS protection)
        customerResponse = await fetchNexus(customerEndpointUrl, {
          method: 'GET',
          headers: {
            's7oryo9stv': cleanToken,
            'referer': refererUrl,
            // fetchNexus already sets User-Agent, but we can override if needed
          },
        });
      } else {
        HandlerUtils.logActivity('MarketRequestHandler', 'Using regular fetch for non-makrobet domain');

        // Create agent that ignores SSL certificate errors (like existing handlers)
        const https = require('https');
        const agent = new https.Agent({
          rejectUnauthorized: false
        });

        // Use node-fetch directly like the existing handlers
        customerResponse = await fetch(customerEndpointUrl, {
          method: 'GET',
          headers: {
            's7oryo9stv': cleanToken,
            'referer': refererUrl,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          },
          agent: agent, // Use same SSL bypass agent
        } as any);
      }

      console.log(`📥 Customer Response Status: ${customerResponse.status}`);

      if (!customerResponse.ok) {
        return {
          success: false,
          error: `Customer API returned ${customerResponse.status}: ${customerResponse.statusText}`,
        };
      }

      // Parse customer response
      const customerData = await customerResponse.json();

      console.log('📥 Customer data received:', JSON.stringify(customerData, null, 2));

      // Validate customer data structure (similar to existing handlers)
      const isValidStructure = HandlerUtils.validateResponseStructure(customerData, [
        'data.customerId',
        'data.username',
        'data.firstName',
        'data.surname',
        'data.balance',
        'data.lockedBalance',
        'data.vip',
      ]);

      if (!isValidStructure) {
        return {
          success: false,
          error: 'Invalid customer data structure received',
        };
      }

      return {
        success: true,
        data: customerData as CustomerData,
      };

    } catch (error) {
      console.error('❌ Failed to fetch customer data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Format Slack message with customer information and market product details
   */
  private static formatSlackMessage(
    marketRequestData: MarketRequest,
    customerData: CustomerData | null,
    originalResponse: MarketRequestResponse | undefined
  ): string {
    // Create title with market product information
    const title = this.bold(`🛒 Market Ürün Talebi - ID: ${marketRequestData.id}`);

    const sections = [];

    // Add market request details section
    sections.push({
      title: this.bold('📦 Ürün Detayları:'),
      content: this.formatKeyValueList([
        { key: 'Ürün ID', value: marketRequestData.id },
        { key: 'Miktar', value: marketRequestData.quantity },
        { key: 'Müşteri ID', value: marketRequestData.cid },
        { key: 'Yanıt', value: originalResponse?.result || 'Mevcut değil' },
      ]),
    });

    // Add customer information section if available
    if (customerData) {
      const customer = customerData.data;
      sections.push({
        title: this.bold('👤 Müşteri Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'İsim', value: `${customer.firstName} ${customer.surname}` },
          { key: 'Kullanıcı Adı', value: customer.username },
          { key: 'Kullanıcı ID', value: customer.customerId },
        ]),
      });

      sections.push({
        title: this.bold('💰 Bakiye Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'Bakiye', value: this.formatCurrency(customer.balance) },
          { key: 'Kilitli Bakiye', value: this.formatCurrency(customer.lockedBalance) },
          { key: 'VIP', value: this.formatBoolean(customer.vip) },
        ]),
      });
    } else {
      // Add basic info section when customer data is not available
      sections.push({
        title: this.bold('👤 Müşteri Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'Müşteri ID', value: marketRequestData.cid },
          { key: 'Token Durumu', value: marketRequestData.token ? 'Mevcut' : 'Sağlanmadı' },
          { key: 'Not', value: 'Tam müşteri detayları mevcut değil' },
        ]),
      });
    }

    return this.formatSectionedMessage(title, sections);
  }
}

/**
 * Exported handler function for use with registerSlackBot
 */
export const marketRequestHandler = async (req: Request, res: Response): Promise<HandlerResult> => {
  return MarketRequestHandler.handle(req, res);
};
