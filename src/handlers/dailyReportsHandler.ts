import { Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON>, HandlerResult, HandlerUtils } from './index';
import { DailyReportsValidator } from '../validators/dailyReportsValidator';
import { 
  DailyReportGenerator, 
  DailyReportMessageFormatter
} from './dailyReportsHandler/index';

/**
 * Daily Reports Handler
 *
 * Handles daily report requests by:
 * 1. Processing the validated request data
 * 2. Generating or fetching the requested daily report
 * 3. Formatting and sending a Slack notification with report summary
 * 4. Returning the original response data
 */
export class DailyReportsHandler extends BaseHandler {
  /**
   * Handle daily report request
   * 
   * @param req - Express request object
   * @param res - Express response object (not used for response, only for logging)
   * @returns Handler result with Slack message or skip reason
   */
  static async handle(req: Request, res: Response): Promise<HandlerResult> {
    try {
      HandlerUtils.logActivity('DailyReportsHandler', 'Processing daily report request');

      // Extract validated daily report data
      const reportData = DailyReportsValidator.extractDailyReportData(req);
      
      // Step 1: Generate or fetch the daily report
      const reportResult = await DailyReportGenerator.generateDailyReport(reportData);

      if (!reportResult.success) {
        HandlerUtils.logError('DailyReportsHandler', `Report generation failed: ${reportResult.error}`);
        return this.error(`Failed to generate daily report: ${reportResult.error}`);
      }

      // Get the original response data and status
      const originalResponse = reportResult.data;
      const originalStatusCode = 200;

      // Step 2: Format Slack message
      const message = DailyReportMessageFormatter.formatSlackMessage(reportData, reportResult.data!);

      HandlerUtils.logActivity('DailyReportsHandler', 'Successfully generated Slack message for daily report');
      return this.success(message, originalResponse, originalStatusCode);

    } catch (error) {
      HandlerUtils.logError('DailyReportsHandler', error instanceof Error ? error : 'Unknown error');
      return this.error(`Handler error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Exported handler function for use with registerSlackBot
 */
export const dailyReportsHandler = async (req: Request, res: Response): Promise<HandlerResult> => {
  return DailyReportsHandler.handle(req, res);
};
