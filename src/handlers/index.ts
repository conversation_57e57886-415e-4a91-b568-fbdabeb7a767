import { Request, Response } from 'express';

/**
 * Slack Bot Handler Result
 *
 * Standard return type for all handler functions
 */
export interface HandlerResult {
  shouldSend: boolean;
  message?: string;
  error?: string;
  originalResponse?: any;
  originalStatusCode?: number;
}

/**
 * Slack Bot Handler Function Type
 * 
 * All handler functions must implement this interface for consistency
 */
export type SlackBotHandler = (req: Request, res: Response) => Promise<HandlerResult>;

/**
 * Base Handler Class
 * 
 * Provides common handler utilities that can be extended by specific handlers
 */
export abstract class BaseHandler {
  /**
   * Create a successful handler result with message
   */
  protected static success(message: string, originalResponse?: any, originalStatusCode?: number): HandlerResult {
    const result: HandlerResult = {
      shouldSend: true,
      message,
    };
    if (originalResponse !== undefined) result.originalResponse = originalResponse;
    if (originalStatusCode !== undefined) result.originalStatusCode = originalStatusCode;
    return result;
  }

  /**
   * Create a handler result that skips sending (with reason)
   */
  protected static skip(reason: string, originalResponse?: any, originalStatusCode?: number): HandlerResult {
    const result: HandlerResult = {
      shouldSend: false,
      error: reason,
    };
    if (originalResponse !== undefined) result.originalResponse = originalResponse;
    if (originalStatusCode !== undefined) result.originalStatusCode = originalStatusCode;
    return result;
  }

  /**
   * Create an error handler result
   */
  protected static error(error: string, originalResponse?: any, originalStatusCode?: number): HandlerResult {
    const result: HandlerResult = {
      shouldSend: false,
      error,
    };
    if (originalResponse !== undefined) result.originalResponse = originalResponse;
    if (originalStatusCode !== undefined) result.originalStatusCode = originalStatusCode;
    return result;
  }

  /**
   * Format a basic Slack message with title and content
   */
  protected static formatMessage(title: string, content: string): string {
    return `${title}\n\n${content}`;
  }

  /**
   * Format a Slack message with sections
   */
  protected static formatSectionedMessage(title: string, sections: { title: string; content: string }[]): string {
    let message = `${title}\n\n`;
    
    sections.forEach((section, index) => {
      message += `${section.title}\n${section.content}`;
      if (index < sections.length - 1) {
        message += '\n\n';
      }
    });

    return message;
  }

  /**
   * Format a list of key-value pairs
   */
  protected static formatKeyValueList(items: { key: string; value: any }[]): string {
    return items
      .map(item => `• ${item.key}: ${item.value}`)
      .join('\n');
  }

  /**
   * Format currency value
   */
  protected static formatCurrency(value: number, currency: string = ''): string {
    const formatted = value.toFixed(2);
    return currency ? `${formatted} ${currency}` : formatted;
  }

  /**
   * Format boolean as Yes/No in Turkish
   */
  protected static formatBoolean(value: boolean): string {
    return value ? 'Evet' : 'Hayır';
  }

  /**
   * Format date/timestamp in prettier format
   * Example: "July 10th 10:00 (GMT+3)" instead of "2025-07-10T10:00:00.000Z"
   */
  protected static formatDate(timestamp: number | string | Date): string {
    const date = new Date(timestamp);

    // Get month names in English
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Get ordinal suffix for day
    const getOrdinalSuffix = (day: number): string => {
      if (day >= 11 && day <= 13) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };

    // Convert UTC date to GMT+3 for display
    const gmtPlus3Offset = 3 * 60 * 60 * 1000; // 3 hours in milliseconds
    const gmtPlus3Date = new Date(date.getTime() + gmtPlus3Offset);

    const month = monthNames[gmtPlus3Date.getUTCMonth()];
    const day = gmtPlus3Date.getUTCDate();
    const ordinalSuffix = getOrdinalSuffix(day);
    const hours = gmtPlus3Date.getUTCHours().toString().padStart(2, '0');
    const minutes = gmtPlus3Date.getUTCMinutes().toString().padStart(2, '0');

    return `${month} ${day}${ordinalSuffix} ${hours}:${minutes} (GMT+3)`;
  }

  /**
   * Format date range for reports
   * Example: "July 10th 09:00 - 10:00 (GMT+3)"
   */
  protected static formatDateRange(startDate: Date, endDate: Date): string {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const getOrdinalSuffix = (day: number): string => {
      if (day >= 11 && day <= 13) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };

    // Convert UTC dates to GMT+3 for display
    const gmtPlus3Offset = 3 * 60 * 60 * 1000; // 3 hours in milliseconds
    const startDateGMT3 = new Date(startDate.getTime() + gmtPlus3Offset);
    const endDateGMT3 = new Date(endDate.getTime() + gmtPlus3Offset);

    const startMonth = monthNames[startDateGMT3.getUTCMonth()];
    const startDay = startDateGMT3.getUTCDate();
    const startOrdinalSuffix = getOrdinalSuffix(startDay);
    const startHours = startDateGMT3.getUTCHours().toString().padStart(2, '0');
    const startMinutes = startDateGMT3.getUTCMinutes().toString().padStart(2, '0');

    const endHours = endDateGMT3.getUTCHours().toString().padStart(2, '0');
    const endMinutes = endDateGMT3.getUTCMinutes().toString().padStart(2, '0');

    // Check if same day (using GMT+3 dates)
    if (startDateGMT3.toDateString() === endDateGMT3.toDateString()) {
      return `${startMonth} ${startDay}${startOrdinalSuffix} ${startHours}:${startMinutes} - ${endHours}:${endMinutes} (GMT+3)`;
    } else {
      const endMonth = monthNames[endDateGMT3.getUTCMonth()];
      const endDay = endDateGMT3.getUTCDate();
      const endOrdinalSuffix = getOrdinalSuffix(endDay);
      return `${startMonth} ${startDay}${startOrdinalSuffix} ${startHours}:${startMinutes} - ${endMonth} ${endDay}${endOrdinalSuffix} ${endHours}:${endMinutes} (GMT+3)`;
    }
  }

  /**
   * Truncate text to maximum length
   */
  protected static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - 3) + '...';
  }

  /**
   * Escape special Slack characters
   */
  protected static escapeSlackText(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');
  }

  /**
   * Make text bold in Slack
   */
  protected static bold(text: string): string {
    return `*${text}*`;
  }

  /**
   * Make text italic in Slack
   */
  protected static italic(text: string): string {
    return `_${text}_`;
  }

  /**
   * Make text code in Slack
   */
  protected static code(text: string): string {
    return `\`${text}\``;
  }

  /**
   * Create a code block in Slack
   */
  protected static codeBlock(text: string): string {
    return `\`\`\`${text}\`\`\``;
  }

  /**
   * Create a link in Slack
   */
  protected static link(url: string, text?: string): string {
    return text ? `<${url}|${text}>` : `<${url}>`;
  }
}

/**
 * Common handler utilities
 */
export class HandlerUtils {
  /**
   * Make an HTTP request with error handling
   */
  static async makeRequest(url: string, options: RequestInit & { agent?: any } = {}): Promise<{
    success: boolean;
    data?: any;
    error?: string;
    status?: number;
  }> {
    try {
      const response = await fetch(url, options);

      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
        };
      }

      const data = await response.json();

      return {
        success: true,
        data,
        status: response.status,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create HTTPS agent that bypasses SSL verification
   */
  static createInsecureAgent(): any {
    const https = require('https');
    return new https.Agent({
      rejectUnauthorized: false
    });
  }

  /**
   * Extract headers for forwarding (excluding system headers)
   */
  static extractForwardingHeaders(req: Request): Record<string, string> {
    const headersToForward: Record<string, string> = {};
    const excludedHeaders = new Set([
      'host',
      'content-length',
      'content-encoding',
      'transfer-encoding',
      'connection',
      'upgrade',
      'proxy-authenticate',
      'proxy-authorization',
      'te',
      'trailer',
    ]);

    Object.entries(req.headers).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      if (!excludedHeaders.has(lowerKey) && value) {
        const headerValue = Array.isArray(value) ? value.join(', ') : value;
        headersToForward[key] = headerValue;
      }
    });

    return headersToForward;
  }

  /**
   * Clean token by removing quotes
   */
  static cleanToken(token: string): string {
    return token.replace(/"/g, '');
  }

  /**
   * Validate response has expected structure
   */
  static validateResponseStructure(data: any, requiredFields: string[]): boolean {
    if (!data || typeof data !== 'object') {
      return false;
    }

    return requiredFields.every(field => {
      const keys = field.split('.');
      let current = data;
      
      for (const key of keys) {
        if (!current || typeof current !== 'object' || !(key in current)) {
          return false;
        }
        current = current[key];
      }
      
      return true;
    });
  }

  /**
   * Log handler activity
   */
  static logActivity(handlerName: string, action: string, details?: any): void {
    console.log(`🤖 [${handlerName}] ${action}`);
    if (details) {
      console.log(`📋 Details:`, JSON.stringify(details, null, 2));
    }
  }

  /**
   * Log handler error
   */
  static logError(handlerName: string, error: string | Error, context?: any): void {
    console.error(`❌ [${handlerName}] ${error instanceof Error ? error.message : error}`);
    if (context) {
      console.error(`📋 Context:`, JSON.stringify(context, null, 2));
    }
  }
}

/**
 * Re-export types for convenience
 */
export type { SlackBotHandler as SlackBotHandlerType } from '../services/slackBot.service';
