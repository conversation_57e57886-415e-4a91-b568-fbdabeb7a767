import { Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON>, HandlerResult, HandlerUtils } from './index';
import { HourlyReportsValidator } from '../validators/hourlyReportsValidator';
import {
  HourlyReportGenerator,
  HourlyReportMessageFormatter
} from './hourlyReportsHandler/index';

/**
 * Hourly Reports Handler
 *
 * Handles hourly report requests by:
 * 1. Processing the validated request data
 * 2. Generating or fetching the requested hourly report
 * 3. Formatting and sending a Slack notification with report summary
 * 4. Returning the original response data
 */
export class HourlyReportsHandler extends BaseHandler {
  /**
   * Handle hourly report request
   *
   * @param req - Express request object
   * @param res - Express response object (not used for response, only for logging)
   * @returns Handler result with Slack message or skip reason
   */
  static async handle(req: Request, res: Response): Promise<HandlerResult> {
    try {
      HandlerUtils.logActivity('HourlyReportsHandler', 'Processing hourly report request');

      // Extract validated hourly report data
      const reportData = HourlyReportsValidator.extractHourlyReportData(req);

      // Step 1: Generate or fetch the hourly report
      const reportResult = await HourlyReportGenerator.generateHourlyReport(reportData);

      if (!reportResult.success) {
        HandlerUtils.logError('HourlyReportsHandler', `Report generation failed: ${reportResult.error}`);
        return this.error(`Failed to generate hourly report: ${reportResult.error}`);
      }

      // Get the original response data and status
      const originalResponse = reportResult.data;
      const originalStatusCode = 200;

      // Step 2: Format Slack message
      const message = HourlyReportMessageFormatter.formatSlackMessage(reportResult.data!);

      HandlerUtils.logActivity('HourlyReportsHandler', 'Successfully generated Slack message for hourly report');
      return this.success(message, originalResponse, originalStatusCode);

    } catch (error) {
      HandlerUtils.logError('HourlyReportsHandler', error instanceof Error ? error : 'Unknown error');
      return this.error(`Handler error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Exported handler function for use with registerSlackBot
 */
export const hourlyReportsHandler = async (req: Request, res: Response): Promise<HandlerResult> => {
  return HourlyReportsHandler.handle(req, res);
};