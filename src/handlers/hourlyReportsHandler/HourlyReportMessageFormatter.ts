import { HourlyReportResponse, BonusMetrics, BetReportData } from './types';

/**
 * Service class for formatting hourly report Slack messages
 *
 * BONUS DATA CALCULATION APPROACH:
 * ================================
 *
 * This class implements a new approach for calculating bonus data in hourly reports
 * that differs from the daily reports system:
 *
 * DAILY REPORTS (ActivityReports endpoint):
 * - Uses detailed ActivityReports API with sport/casino breakdown
 * - Provides: totalBonusAmount, sportBonusAmount, casinoBonusAmount, etc.
 * - Available only for daily timeframes
 *
 * HOURLY REPORTS (Bet Report Records):
 * - Uses bet report records from database with aggregated bonus data
 * - Provides: totBonBalPlayAmount, totBonBalWinReturn, totBonBalVoidReturn, totFreeBalPlayAmount
 * - Available for both hourly and cumulative timeframes
 * - No sport/casino breakdown available
 *
 * BONUS FIELD MAPPING:
 * - "Verilen Toplam Bonus" = totBonBalWinReturn + totBonBalVoidReturn
 * - "Harcanan Toplam Bonus" = totBonBalPlayAmount
 * - "Harcanan Toplam Freebet" = totFreeBalPlayAmount
 * - "Verilen Toplam Freebet" = Not available (always 0)
 *
 * DATA SOURCES:
 * - Hourly section: Uses differenceRecord (hourly difference data)
 * - Daily section: Uses mainRecord (cumulative data from start of day)
 * - First report of day: Only mainRecord available, hourly shows zeros
 */
export class HourlyReportMessageFormatter {
  private static readonly GMT_PLUS_3_OFFSET = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

  /**
   * Convert UTC date to GMT+3
   * @param utcDate - UTC date
   * @returns GMT+3 date
   */
  private static toGMTPlus3(utcDate: Date): Date {
    return new Date(utcDate.getTime() + this.GMT_PLUS_3_OFFSET);
  }
  /**
   * Format Slack message for hourly report
   *
   * @param responseData - Report response data
   * @returns Formatted Slack message
   */
  static formatSlackMessage(responseData: HourlyReportResponse): string {
    // Extract date and hour information - convert UTC baseDate to GMT+3
    const baseDate = new Date(responseData.baseDate || new Date());
    const gmtPlus3Date = this.toGMTPlus3(baseDate);

    const displayDate = this.formatDailyDate(gmtPlus3Date);

    const currentHour = gmtPlus3Date.getUTCHours();
    const previousHour = currentHour - 1;

    // Build the comprehensive hourly report message with exact format as specified
    let message = `⏰ Saatlik İşlem Raporu\n\n`;
    message += `📅 Tarih: ${displayDate}  \n`;
    message += `🕐 Saat Aralığı: ${previousHour.toString().padStart(2, '0')}:00 – ${currentHour.toString().padStart(2, '0')}:00 (GMT+3)\n\n`;

    // Transaction Summary Section (as specified by boss)
    if (responseData.data?.summary) {
      const summary = responseData.data.summary;

      // 📊 İşlem Özeti
      message += `📊 İşlem Özeti  \n`;
      message += `• Yatırım İşlem Sayısı: ${summary.totalDeposits}  \n`;
      message += `• Çekim İşlem Sayısı: ${summary.totalWithdrawals}  \n`;
      message += `• Benzersiz Müşteri Sayısı: ${summary.uniqueCustomers}  \n`;

      // Calculate average amounts
      const avgDepositAmount = summary.totalDeposits > 0 ? summary.totalDepositAmount / summary.totalDeposits : 0;
      const avgWithdrawalAmount = summary.totalWithdrawals > 0 ? summary.totalWithdrawalAmount / summary.totalWithdrawals : avgDepositAmount;

      message += `• Ortalama Yatırım Tutarı: ${this.formatCurrency(avgDepositAmount)}  \n`;
      message += `• Ortalama Çekim Tutarı: ${this.formatCurrency(avgWithdrawalAmount)}\n\n`;

      // 💰 Mali Özet
      message += `💰 Mali Özet  \n`;
      message += `• Toplam Yatırım: ${this.formatCurrency(summary.totalDepositAmount)}  \n`;
      message += `• Toplam Çekim: ${this.formatCurrency(summary.totalWithdrawalAmount)}  \n`;
      message += `• Kar / Zarar (Kasa Farkı): ${this.formatCurrency(summary.netCashFlow)}\n\n`;

      // Add GGR data from bet report
      if (responseData.data?.betReportData) {
        const betData = responseData.data.betReportData;
        const differenceRecord = betData.differenceRecord;

        // 🧮 Saatlik GGR (from difference record if available, otherwise show 0)
        if (differenceRecord && !betData.isFirstReportOfDay) {
          const startTime = this.formatHourlyDateTime(new Date(differenceRecord.startDate));
          const endTime = this.formatHourlyDateTime(new Date(differenceRecord.endDate));
          message += `🧮 Saatlik GGR (${startTime} - ${endTime})  \n`;
          message += `• Total GGR: ${this.formatCurrency(differenceRecord.ggr || 0)}\n\n`;
        } else {
          message += `🧮 Saatlik GGR  \n`;
          message += `• Total GGR: ${this.formatCurrency(0)} (İlk rapor)\n\n`;
        }

        message += `💼 Toplam Müşteri Bakiyesi (Anlık)  \n`;
        const customerBalance = responseData.data?.totalCustomerBalance ?? 0;
        message += `• ${this.formatCurrency(customerBalance)}\n\n`;
      } else {
        // If no bet report data, show zero values
        message += `🧮 Saatlik GGR  \n`;
        message += `• Total GGR: ${this.formatCurrency(0)}\n\n`;

        message += `💼 Toplam Müşteri Bakiyesi (Anlık)  \n`;
        const customerBalance = responseData.data?.totalCustomerBalance ?? 0;
        message += `• ${this.formatCurrency(customerBalance)}\n\n`;
      }

      // 🎁 Bonus Bilgileri (Saatlik) - from bet report records
      if (responseData.data?.betReportData) {
        console.log('HourlyReportMessageFormatter - Processing hourly bonus section with bet report data');
        const bonusData = this.calculateBonusData(responseData.data.betReportData);
        const isFirstReportOfDay = responseData.data.betReportData.isFirstReportOfDay || false;
        message += this.formatBonusSection(bonusData.hourly, true, isFirstReportOfDay);
      } else {
        console.warn('HourlyReportMessageFormatter - No bet report data available, using fallback bonus section');
        // Fallback to zeros if bet report data is not available
        message += `🎁 Bonus Bilgileri (Saatlik)  \n`;
        message += `• Verilen Toplam Bonus: ${this.formatCurrency(0)}  \n`;
        message += `• Harcanan Toplam Bonus: ${this.formatCurrency(0)}  \n`;
        message += `• Harcanan Toplam Freebet: ${this.formatCurrency(0)}  \n`;
        message += `• Bonus Avcısı: Yok\n\n`;
      }

      // 🏦 Ödeme Yöntemi Dağılımı (Saatlik)
      message += `🏦 Ödeme Yöntemi Dağılımı (Saatlik)  \n`;

      if (summary.bankBreakdown && summary.bankBreakdown.length > 0) {
        // Separate deposits and withdrawals
        const deposits = summary.bankBreakdown.filter(bank => bank.type === 'deposit');
        const withdrawals = summary.bankBreakdown.filter(bank => bank.type === 'withdrawal');

        message += `💳 Yatırım:\n\n`;
        if (deposits.length > 0) {
          deposits.slice(0, 10).forEach(bank => {
            message += `${bank.bankName} ${bank.count} ${this.formatCurrency(bank.amount)} %${bank.percentage.toFixed(2)}  \n`;
          });
        } else {
          message += `– 0 ${this.formatCurrency(0)} %0\n`;
        }

        message += `🏧 Çekim:\n\n`;
        if (withdrawals.length > 0) {
          withdrawals.slice(0, 10).forEach(bank => {
            message += `${bank.bankName} ${bank.count} ${this.formatCurrency(bank.amount)} %${bank.percentage.toFixed(2)}  \n`;
          });
        } else {
          message += `– 0 ${this.formatCurrency(0)} %0\n`;
        }
      } else {
        message += `💳 Yatırım:\n\n`;
        message += `– 0 ${this.formatCurrency(0)} %0\n\n`;
        message += `🏧 Çekim:\n\n`;
        message += `– 0 ${this.formatCurrency(0)} %0\n`;
      }
    } else {
      message += `📊 Rapor Durumu  \n`;
      message += `• Durum: Bu saat aralığı için işlem verisi mevcut değil\n`;
      message += `• Saat Aralığı: ${previousHour.toString().padStart(2, '0')}:00 – ${currentHour.toString().padStart(2, '0')}:00\n\n`;
    }

      // 👥 Yeni Üye Verileri (Saatlik)
      message += `\n👥 Yeni Üye Verileri (Saatlik)   \n`;
      const hourlyNewUserCount = responseData.data?.hourlyNewUserData?.newUserCount ?? 0;
      const hourlyNewUserDepositAmount = responseData.data?.hourlyNewUserData?.newUserDepositAmount ?? 0;

      // Debug logging for hourly new user data verification
      console.log('HourlyReportMessageFormatter - Hourly new user data verification:', {
        hourlyNewUserCount,
        hourlyNewUserDepositAmount,
        calculationMethod: 'transaction-based',
        customerIdsCount: responseData.data?.hourlyNewUserData?.newUserCustomerIds?.length || 0
      });

      message += `• Yeni Üye Sayısı: ${hourlyNewUserCount}   \n`;
      message += `• Yeni Üye Yatırımı: ${this.formatCurrency(hourlyNewUserDepositAmount)}\n\n`;

    // Daily Summary Section (Context information - comes after hourly details)
    if (responseData.data?.dailySummary) {
      const dailySummary = responseData.data.dailySummary;

      // Convert UTC hours to GMT+3 for display
      const baseDateForSummary = new Date(responseData.baseDate || new Date());
      const gmtPlus3DateForSummary = this.toGMTPlus3(baseDateForSummary);
      const currentHour = gmtPlus3DateForSummary.getUTCHours();

      message += `\n📅 GÜNLÜK GENEL ÖZET (00:00 – ${currentHour.toString().padStart(2, '0')}:00 itibarıyla)\n\n`;

      // 💰 Günlük Mali Veriler
      message += `💰 Günlük Mali Veriler  \n`;
      message += `• Toplam Yatırım: ${this.formatCurrency(dailySummary.totalDepositAmount)}  \n`;
      message += `• Toplam Çekim: ${this.formatCurrency(dailySummary.totalWithdrawAmount)}  \n`;
      message += `• Günlük Kar / Zarar: ${this.formatCurrency(dailySummary.dailyNetCashFlow)}\n\n`;

      // 🧮 Günlük GGR (Brüt Kâr) - from bet report cumulative data
      if (responseData.data?.betReportData?.mainRecord) {
        const mainRecord = responseData.data.betReportData.mainRecord;
        const startTime = this.formatHourlyDateTime(new Date(mainRecord.startDate));
        const endTime = this.formatHourlyDateTime(new Date(mainRecord.endDate));
        message += `🧮 Günlük GGR (Brüt Kâr) (${startTime} - ${endTime})  \n`;
        message += `• Total GGR: ${this.formatCurrency(mainRecord.ggr || 0)}  \n`;
        message += `• Total NGR: ${this.formatCurrency(mainRecord.ngr || 0)}  \n`;
        message += `• Toplam Kupon Sayısı: ${(mainRecord.totalCoupons || 0).toLocaleString()}  \n`;
        message += `• Toplam Bahis Tutarı: ${this.formatCurrency(mainRecord.totalPlayAmount || 0)}\n\n`;
      } else {
        message += `🧮 Günlük GGR (Brüt Kâr)  \n`;
        message += `• Total GGR: ${this.formatCurrency(0)}  \n`;
        message += `• Total NGR: ${this.formatCurrency(0)}  \n`;
        message += `• Toplam Kupon Sayısı: 0  \n`;
        message += `• Toplam Bahis Tutarı: ${this.formatCurrency(0)}\n\n`;
      }

      // 🎁 Bonus Bilgileri (Günlük) - from bet report records
      if (responseData.data?.betReportData) {
        console.log('HourlyReportMessageFormatter - Processing daily bonus section with bet report data');
        const bonusData = this.calculateBonusData(responseData.data.betReportData);
        message += this.formatBonusSection(bonusData.upToHour, false, false); // Daily section is never "first report"
      } else {
        console.warn('HourlyReportMessageFormatter - No bet report data available for daily section, using fallback bonus section');
        // Fallback to zeros if bet report data is not available
        message += `🎁 Bonus Bilgileri (Günlük)  \n`;
        message += `• Verilen Toplam Bonus: ${this.formatCurrency(0)}  \n`;
        message += `• Harcanan Toplam Bonus: ${this.formatCurrency(0)}  \n`;
        message += `• Harcanan Toplam Freebet: ${this.formatCurrency(0)}  \n`;
        message += `• Bonus Avcısı: Yok\n\n`;
      }

      // 👥 Günlük Yeni Üye Verileri
      message += `👥 Günlük Yeni Üye Verileri  \n`;
      const upToHourNewUserCount = responseData.data?.upToHourNewUserData?.newUserCount ?? 0;
      const upToHourNewUserDepositAmount = responseData.data?.upToHourNewUserData?.newUserDepositAmount ?? 0;

      // Debug logging for up-to-hour new user data verification
      console.log('HourlyReportMessageFormatter - Up-to-hour new user data verification:', {
        upToHourNewUserCount,
        upToHourNewUserDepositAmount,
        calculationMethod: 'transaction-based',
        customerIdsCount: responseData.data?.upToHourNewUserData?.newUserCustomerIds?.length || 0
      });

      message += `• Yeni Üye Sayısı: ${upToHourNewUserCount}  \n`;
      message += `• Yeni Üye Yatırımı: ${this.formatCurrency(upToHourNewUserDepositAmount)}`;
    }

    return message;
  }

  /**
   * Calculate bonus data from bet report records with validation and error handling
   *
   * This method replaces the previous approach that used ActivityReports endpoint,
   * which was not available for hourly reports. Instead, it uses bet report records
   * that contain bonus-related fields from the betting system.
   *
   * Bonus Data Mapping:
   * - givenBonus = totBonBalWinReturn + totBonBalVoidReturn (money returned to bonus balance)
   * - spentBonus = totBonBalPlayAmount (money spent from bonus balance)
   * - spentFreebet = totFreeBalPlayAmount (money spent from freebet balance)
   * - givenFreebet = 0 (not available in bet report records)
   *
   * Data Sources:
   * - Hourly data: Uses differenceRecord (difference between current and previous report)
   * - Up-to-hour data: Uses mainRecord (cumulative data from start of day)
   * - First report of day: Only mainRecord is available, hourly data shows zeros
   *
   * @param betReportData - Bet report data containing main and difference records
   * @returns Calculated bonus metrics for both hourly and up-to-hour periods
   */
  private static calculateBonusData(betReportData: BetReportData | null): {
    hourly: BonusMetrics;
    upToHour: BonusMetrics;
  } {
    const hourlyMetrics: BonusMetrics = {
      givenBonus: 0,
      spentBonus: 0,
      givenFreebet: 0,
      spentFreebet: 0,
    };

    const upToHourMetrics: BonusMetrics = {
      givenBonus: 0,
      spentBonus: 0,
      givenFreebet: 0,
      spentFreebet: 0,
    };

    try {
      // Validate bet report data structure
      if (!betReportData || typeof betReportData !== 'object') {
        console.warn('HourlyReportMessageFormatter - Invalid bet report data structure, using zero values');
        return { hourly: hourlyMetrics, upToHour: upToHourMetrics };
      }

      // Calculate up-to-hour metrics from main record (cumulative data)
      if (betReportData.mainRecord && typeof betReportData.mainRecord === 'object') {
        const mainRecord = betReportData.mainRecord;

        // Validate and safely extract numeric values
        const bonWinReturn = this.safeParseNumber(mainRecord.totBonBalWinReturn);
        const bonVoidReturn = this.safeParseNumber(mainRecord.totBonBalVoidReturn);
        const bonPlayAmount = this.safeParseNumber(mainRecord.totBonBalPlayAmount);
        const freePlayAmount = this.safeParseNumber(mainRecord.totFreeBalPlayAmount);

        upToHourMetrics.givenBonus = bonWinReturn + bonVoidReturn;
        upToHourMetrics.spentBonus = bonPlayAmount;
        upToHourMetrics.spentFreebet = freePlayAmount;
        // Note: We don't have freebet win/void return data, so givenFreebet remains 0

        console.log('HourlyReportMessageFormatter - Up-to-hour bonus data calculated:', upToHourMetrics);
      } else {
        console.warn('HourlyReportMessageFormatter - Main record not available, using zero values for up-to-hour bonus data');
      }

      // Calculate hourly metrics from difference record (hourly data)
      if (betReportData.differenceRecord &&
          typeof betReportData.differenceRecord === 'object' &&
          !betReportData.isFirstReportOfDay) {

        const diffRecord = betReportData.differenceRecord;

        // Validate and safely extract numeric values
        const bonWinReturn = this.safeParseNumber(diffRecord.totBonBalWinReturn);
        const bonVoidReturn = this.safeParseNumber(diffRecord.totBonBalVoidReturn);
        const bonPlayAmount = this.safeParseNumber(diffRecord.totBonBalPlayAmount);
        const freePlayAmount = this.safeParseNumber(diffRecord.totFreeBalPlayAmount);

        hourlyMetrics.givenBonus = bonWinReturn + bonVoidReturn;
        hourlyMetrics.spentBonus = bonPlayAmount;
        hourlyMetrics.spentFreebet = freePlayAmount;
        // Note: We don't have freebet win/void return data, so givenFreebet remains 0

        console.log('HourlyReportMessageFormatter - Hourly bonus data calculated:', hourlyMetrics);
      } else {
        if (betReportData.isFirstReportOfDay) {
          console.log('HourlyReportMessageFormatter - First report of day, using zero values for hourly bonus data');
        } else {
          console.warn('HourlyReportMessageFormatter - Difference record not available, using zero values for hourly bonus data');
        }
      }

    } catch (error) {
      console.error('HourlyReportMessageFormatter - Error calculating bonus data:', error);
      // Return zero values on any error
    }

    return {
      hourly: hourlyMetrics,
      upToHour: upToHourMetrics,
    };
  }

  /**
   * Safely parse a number value, returning 0 if invalid
   *
   * @param value - Value to parse as number
   * @returns Parsed number or 0 if invalid
   */
  private static safeParseNumber(value: any): number {
    if (value === null || value === undefined) {
      return 0;
    }

    const parsed = typeof value === 'number' ? value : parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }

  /**
   * Format bonus section for hourly report
   *
   * @param bonusData - Calculated bonus metrics
   * @param isHourlySection - Whether this is for the hourly section (true) or up-to-hour section (false)
   * @param isFirstReportOfDay - Whether this is the first report of the day
   * @returns Formatted bonus section string
   */
  private static formatBonusSection(bonusData: BonusMetrics, isHourlySection: boolean = true, isFirstReportOfDay: boolean = false): string {
    const sectionType = isHourlySection ? 'hourly' : 'up-to-hour';
    let sectionTitle = isHourlySection ? '🎁 Bonus Bilgileri (Saatlik)' : '🎁 Bonus Bilgileri (Günlük)';

    // Add special note for first report of day in hourly section
    if (isHourlySection && isFirstReportOfDay) {
      sectionTitle = '🎁 Bonus Bilgileri (Saatlik - İlk Rapor)';
    }

    console.log(`HourlyReportMessageFormatter - Formatting ${sectionType} bonus section:`, {
      isFirstReportOfDay,
      givenBonus: bonusData.givenBonus,
      spentBonus: bonusData.spentBonus,
      spentFreebet: bonusData.spentFreebet,
      formattedGivenBonus: this.formatCurrency(bonusData.givenBonus),
      formattedSpentBonus: this.formatCurrency(bonusData.spentBonus),
      formattedSpentFreebet: this.formatCurrency(bonusData.spentFreebet)
    });

    let message = `${sectionTitle}  \n`;
    message += `• Verilen Toplam Bonus: ${this.formatCurrency(bonusData.givenBonus)}  \n`;
    message += `• Harcanan Toplam Bonus: ${this.formatCurrency(bonusData.spentBonus)}  \n`;
    message += `• Harcanan Toplam Freebet: ${this.formatCurrency(bonusData.spentFreebet)}  \n`;
    message += `• Bonus Avcısı: Yok\n\n`;

    return message;
  }

  /**
   * Format currency values for display with Turkish Lira symbol
   *
   * @param amount - Numeric amount to format
   * @returns Formatted currency string with ₺ symbol
   */
  private static formatCurrency(amount: number): string {
    if (amount === 0) return '₺0.00';
    const formatted = amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
    return `₺${formatted}`;
  }

  /**
   * Format datetime for hourly report timeframes
   *
   * @param date - Date object to format (assumed to be UTC from database)
   * @returns Formatted time string in HH:MM format (GMT+3)
   */
  private static formatHourlyDateTime(date: Date): string {
    // The date from database is stored as UTC, convert to GMT+3 for display
    const gmtPlus3Date = this.toGMTPlus3(date);

    const hours = gmtPlus3Date.getUTCHours().toString().padStart(2, '0');
    const minutes = gmtPlus3Date.getUTCMinutes().toString().padStart(2, '0');

    return `${hours}:${minutes}`;
  }

  /**
   * Format daily date from Date object or DD.MM.YYYY format to prettier format
   * Example: "11.07.2025" -> "11.07.2025"
   *
   * @param dateInput - Date object or date string in DD.MM.YYYY format
   * @returns Formatted date string
   */
  private static formatDailyDate(dateInput: string | Date): string {
    try {
      // If it's a string and already in DD.MM.YYYY format, return as is
      if (typeof dateInput === 'string' && dateInput.includes('.') && dateInput.split('.').length === 3) {
        return dateInput;
      }

      // Convert to Date object if it's a string, or use directly if it's already a Date
      const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

      // Format as DD.MM.YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}.${month}.${year}`;
    } catch (error) {
      // Fallback to original string if parsing fails
      return typeof dateInput === 'string' ? dateInput : dateInput.toLocaleDateString('tr-TR');
    }
  }
}
