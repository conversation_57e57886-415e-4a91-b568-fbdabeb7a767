/**
 * Transaction Summary Interface
 */
export interface TransactionSummary {
  totalTransactions: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalDepositAmount: number;
  totalWithdrawalAmount: number;
  netCashFlow: number;
  uniqueCustomers: number;
  averageTransactionAmount: number;
  bankBreakdown: Array<{
    bankName: string;
    type: 'deposit' | 'withdrawal';
    count: number;
    amount: number;
    percentage: number;
  }>;
  hourlyNewUserDepositAmount?: number;
  upToHourNewUserDepositAmount?: number;
}

/**
 * New User Data Interface
 */
export interface NewUserData {
  newUserCount: number;
  newUserDepositAmount: number;
  newUserCustomerIds: number[];
}

/**
 * Interface for bonus metrics calculated from bet report records
 *
 * This interface defines the structure for bonus data used in hourly reports.
 * Unlike daily reports that use ActivityReports endpoint with detailed breakdowns,
 * hourly reports use bet report records which provide aggregated bonus data.
 *
 * Field Mappings from Bet Report Records:
 * - givenBonus: totBonBalWinReturn + totBonBalVoidReturn
 * - spentBonus: totBonBalPlayAmount
 * - spentFreebet: totFreeBalPlayAmount
 * - givenFreebet: Not available (always 0)
 *
 * Note: Sport vs Casino breakdown is not available in bet report records,
 * so hourly reports show aggregated totals only.
 */
export interface BonusMetrics {
  givenBonus: number;    // Money returned to bonus balance (win + void returns)
  spentBonus: number;    // Money spent from bonus balance
  givenFreebet: number;  // Money returned to freebet balance (not available in current data)
  spentFreebet: number;  // Money spent from freebet balance
}

/**
 * Bet Report Record Interface
 * Represents the structure of bet report records from the database
 */
export interface BetReportRecord {
  id: number;
  totalCoupons: number;
  totalPlayAmount: number;
  totalOpenCoupons: number;
  totalOpenReturn: number;
  totalOpenAmount: number;
  totalWinCoupons: number;
  totalWinAmount: number;
  totalWinReturn: number;
  totalLoseCoupons: number;
  totalLoseAmount: number;
  totalVoidCoupons: number;
  totalVoidAmount: number;
  totalVoidReturn: number;
  totalPartCashoutAmount: number;
  totalPartCashoutCount: number;
  totalCompCashoutAmount: number;
  totalCompCashoutCount: number;
  totRealBalPlayAmount: number;
  totBonBalPlayAmount: number;
  totFreeBalPlayAmount: number;
  totRealBalWinReturn: number;
  totBonBalWinReturn: number;
  totRealBalVoidReturn: number;
  totBonBalVoidReturn: number;
  ngr: number;
  ggr: number;
  transactionCurrency: string | null;
  startDate: Date;
  endDate: Date;
  isHourlyReport: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Bet Report Data Interface
 * Represents the structure of bet report data used in hourly reports
 *
 * This interface defines the bet report data structure that replaces ActivityReports
 * for bonus data calculation in hourly reports. The data comes from the
 * /api/v1/makroz/admin/bet-report-records/invoke endpoint.
 *
 * Data Structure:
 * - mainRecord: Cumulative data from start of day (00:00) to current time
 * - differenceRecord: Difference data from previous hour to current time (null for first report)
 * - isFirstReportOfDay: Flag indicating if this is the first report of the day
 *
 * Bonus Calculation Usage:
 * - Up-to-hour bonus data: Calculated from mainRecord
 * - Hourly bonus data: Calculated from differenceRecord (if available)
 * - First report handling: Only mainRecord is used, hourly shows zeros
 */
export interface BetReportData {
  mainRecord: BetReportRecord | null;        // Cumulative report (0:00-present)
  differenceRecord: BetReportRecord | null;  // Hourly report (last-present) or null
  summary: {
    isFirstReportOfDay: boolean;
  };
  isFirstReportOfDay: boolean;
}

/**
 * Daily Summary Interface for Hourly Reports
 * Note: adjustmentTopUpAmount and totalDiscountAmount are not included
 * because they are only available in daily activity reports, not in transaction-based hourly data
 */
export interface DailySummary {
  totalDepositAmount: number;
  totalWithdrawAmount: number;
  dailyNetCashFlow: number;
  numberOfSportBets: number;
  numberOfVirtualBets: number;
  numberOfCasinoBets: number;
  totalSportPlayAmount: number;
  totalCasinoPlayAmount: number;
  totalSportOpenAmount: number;
  sportGGR: number;
  casinoGGR: number;
  totalBonusAmount: number;
  totalSpentBonusAmount: number;
  sportBonusAmount: number;
  sportSpentBonusAmount: number;
  casinoBonusAmount: number;
  casinoSpentBonusAmount: number;
  totalGivenFreebetAmount: number;
  newUserData?: NewUserData;
}

/**
 * Hourly Report Response Interface
 *
 * Defines the expected structure of hourly report responses
 */
export interface HourlyReportResponse {
  success: boolean;
  reportId?: string;
  generatedAt?: string;
  baseDate?: string;
  hourRange?: string;
  data?: {
    summary?: TransactionSummary;
    rawTransactions?: any[];
    hourlyNewUserData?: NewUserData | null;
    upToHourNewUserData?: NewUserData | null;
    dailySummary?: DailySummary | null;
    betReportData?: BetReportData | null;
    totalCustomerBalance?: number;
  };
  message?: string;
  error?: string;
}
