import { HandlerUtils } from '../index';
import { pgDagurApiClient, pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import fetch from 'node-fetch';
import { CustomerReportListPreloadRequest, CustomerReportListRequest, CustomerReportListWithCursorRequest } from '@/network/pg-dagur/requests/customer/CustomerReportListRequest';
import { TransactionSummary, NewUserData, DailySummary } from './types';
import { BalanceService, BalanceServiceResult } from '@/services/BalanceService';

/**
 * Service class for fetching and processing hourly report data
 */
export class HourlyReportDataService {
  /**
   * Fetch bet report data using the invoke bet report endpoint
   *
   * This method calls the /api/v1/makroz/admin/bet-report-records/invoke endpoint
   * to get both cumulative (0:00-present) and hourly (last-present) GGR data
   *
   * @returns Bet report data with both main and difference records, or null if failed
   */
  static async fetchBetReportData(): Promise<any> {
    try {
      HandlerUtils.logActivity('HourlyReportDataService', 'Fetching bet report data via invoke endpoint');

      // Get the base URL for our own server
      const baseUrl = process.env['BASE_URL'] || 'http://localhost:3000';
      const invokeEndpoint = `${baseUrl}/api/v1/makroz/admin/bet-report-records/invoke`;

      HandlerUtils.logActivity('HourlyReportDataService', `Calling invoke endpoint: ${invokeEndpoint}`);

      const response = await fetch(invokeEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // Empty body, endpoint handles date calculation
      });

      if (!response.ok) {
        throw new Error(`Invoke endpoint failed with status: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json() as any;

      if (!responseData.success) {
        throw new Error(`Invoke endpoint returned error: ${responseData.message}`);
      }

      HandlerUtils.logActivity('HourlyReportDataService', 'Successfully fetched bet report data');

      return {
        mainRecord: responseData.data.mainRecord,        // Cumulative report (0:00-present)
        differenceRecord: responseData.data.differenceRecord, // Hourly report (last-present) or null
        summary: responseData.data.summary,
        isFirstReportOfDay: responseData.data.summary.isFirstReportOfDay,
      };

    } catch (error) {
      HandlerUtils.logError('HourlyReportDataService', `Failed to fetch bet report data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Fetch total customer balance for hourly reports
   *
   * This method adds balance fetching capability to hourly reports,
   * replacing the previous hardcoded 0 values in the message formatter.
   *
   * IMPLEMENTATION:
   * - Uses the same balance API as daily reports
   * - BaseDate is used as-is (no end-of-day formatting)
   * - Provides real-time balance data for hourly snapshots
   *
   * @param baseDate - Base date/time for the hourly report (current hour)
   * @returns Balance service result with TRY balance or error details
   */
  static async fetchHourlyBalance(baseDate: string): Promise<BalanceServiceResult> {
    try {
      HandlerUtils.logActivity('HourlyReportDataService', `Fetching hourly balance for baseDate: ${baseDate}`);

      const balanceResult = await BalanceService.fetchHourlyBalance(baseDate);

      if (balanceResult.success) {
        HandlerUtils.logActivity('HourlyReportDataService',
          `Successfully fetched hourly balance: ${balanceResult.balance}`);
      } else {
        HandlerUtils.logError('HourlyReportDataService',
          `Failed to fetch hourly balance: ${balanceResult.error}`);
      }

      return balanceResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      HandlerUtils.logError('HourlyReportDataService', `Balance fetch error: ${errorMessage}`);

      return BalanceService.getFallbackBalance(errorMessage);
    }
  }

  /**
   * Fetch new user data for the entire day and filter manually by registration time
   *
   * @param dayStart - Start of the day (00:00)
   * @param dayEnd - End of the day (23:59)
   * @param hourStart - Start of the specific hour
   * @param hourEnd - End of the specific hour
   * @returns Object with hourly and up-to-hour new user data
   */
  static async fetchNewUserDataForDay(dayStart: Date, dayEnd: Date, hourStart: Date, hourEnd: Date): Promise<{ hourlyNewUserData: NewUserData | null, upToHourNewUserData: NewUserData | null }> {
    try {
      HandlerUtils.logActivity('HourlyReportDataService',
        `Fetching all new users for day ${dayStart.toISOString()} to ${dayEnd.toISOString()}, then filtering manually`);

      // Step 1: Get preload data for the customer report request (ONLY CURSORLESS REQUEST)
      let viewState = '';
      let javax: any = null;

      const preloadRequest = new CustomerReportListPreloadRequest();
      const preloadResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!preloadResult.success) {
        throw new Error(`Failed to preload customer report: ${preloadResult.message || 'Unknown error'}`);
      }

      viewState = preloadResult.viewState || '';
      javax = preloadResult.data;

      // Step 2: Make initial request with full day range to get cursor (ONLY CURSORLESS REQUEST)
      const cursorRequest = new CustomerReportListRequest({
        javax,
        registrationStartDate: dayStart,
        registrationEndDate: dayEnd,
        cursor: viewState,
        limit: 10000, // Large limit to get cursor
      });

      const cursorResult = await pgDagurAdminHttpClient.makeRequest(cursorRequest, viewState);

      if (!cursorResult.success) {
        throw new Error(`Failed to get cursor: ${cursorResult.message || 'Unknown error'}`);
      }

      // Check if cursor is successfully obtained (must be a string value)
      let cursor = null;
      if (cursorResult.data && typeof cursorResult.data.cursor === 'string' && cursorResult.data.cursor.length > 0) {
        cursor = cursorResult.data.cursor;
      } else {
        // Check for cursor in other possible locations using any type for flexibility
        const response = cursorResult as any;
        if (response.cursor && typeof response.cursor === 'string' && response.cursor.length > 0) {
          cursor = response.cursor;
        } else if (response.data && response.data.cursor && typeof response.data.cursor === 'string' && response.data.cursor.length > 0) {
          cursor = response.data.cursor;
        }
      }

      if (!cursor) {
        throw new Error(`Failed to obtain valid cursor from response. Response structure: ${JSON.stringify(cursorResult)}`);
      }

      // Step 3: Use cursor to fetch all users for the day (CURSOR-BASED REQUEST)
      HandlerUtils.logActivity('HourlyReportDataService',
        `Fetching all users for the day using cursor`);

      const actualRequest = new CustomerReportListWithCursorRequest({
        javax,
        registrationStartDate: dayStart,
        registrationEndDate: dayEnd,
        cursor,
        limit: 10000, // Large limit to get all users
      });

      const actualResult = await pgDagurAdminHttpClient.makeRequest(actualRequest, cursor);

      if (!actualResult.success) {
        HandlerUtils.logError('HourlyReportDataService', `Failed to fetch daily users: ${actualResult.message || 'Unknown error'}`);
        return {
          hourlyNewUserData: null,
          upToHourNewUserData: null,
        };
      }

      // Process all users and filter by registration time
      const allUsers = actualResult.data.items || [];

      HandlerUtils.logActivity('HourlyReportDataService',
        `Retrieved ${allUsers.length} users for the day, now filtering by registration time`);

      // Filter users for the specific hour
      const hourlyUsers = allUsers.filter((user: any) => {
        if (!user.registerDate) return false;

        // Parse registerDate format: "11.07.2025 10:16:12"
        const registerDate = this.parseRegisterDate(user.registerDate);
        if (!registerDate) return false;

        return registerDate >= hourStart && registerDate < hourEnd;
      });

      // Filter users from start of day to current hour
      const upToHourUsers = allUsers.filter((user: any) => {
        if (!user.registerDate) return false;

        const registerDate = this.parseRegisterDate(user.registerDate);
        if (!registerDate) return false;

        return registerDate >= dayStart && registerDate < hourEnd;
      });

      // Calculate hourly data
      const hourlyNewUserCount = hourlyUsers.length;
      const hourlyNewUserDepositAmount = hourlyUsers.reduce((total: number, user: any) => {
        return total + (user.firstDepositAmount || 0);
      }, 0);
      const hourlyNewUserCustomerIds = hourlyUsers.map((user: any) => user.customerId).filter(Boolean);

      // Calculate up-to-hour data
      const upToHourNewUserCount = upToHourUsers.length;
      const upToHourNewUserDepositAmount = upToHourUsers.reduce((total: number, user: any) => {
        return total + (user.firstDepositAmount || 0);
      }, 0);
      const upToHourNewUserCustomerIds = upToHourUsers.map((user: any) => user.customerId).filter(Boolean);

      HandlerUtils.logActivity('HourlyReportDataService',
        `Filtered results: ${hourlyNewUserCount} hourly users, ${upToHourNewUserCount} up-to-hour users`);

      return {
        hourlyNewUserData: {
          newUserCount: hourlyNewUserCount,
          newUserDepositAmount: hourlyNewUserDepositAmount,
          newUserCustomerIds: hourlyNewUserCustomerIds,
        },
        upToHourNewUserData: {
          newUserCount: upToHourNewUserCount,
          newUserDepositAmount: upToHourNewUserDepositAmount,
          newUserCustomerIds: upToHourNewUserCustomerIds,
        },
      };

    } catch (error) {
      HandlerUtils.logError('HourlyReportDataService', `Failed to fetch new user data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        hourlyNewUserData: null,
        upToHourNewUserData: null,
      };
    }
  }

  /**
   * Parse register date string in format "11.07.2025 10:16:12" to Date object
   *
   * @param registerDateString - Date string in DD.MM.YYYY HH:mm:ss format
   * @returns Parsed Date object or null if parsing fails
   */
  private static parseRegisterDate(registerDateString: string): Date | null {
    try {
      // Format: "11.07.2025 10:16:12"
      const [datePart, timePart] = registerDateString.split(' ');
      if (!datePart || !timePart) return null;

      const [day, month, year] = datePart.split('.');
      const [hours, minutes, seconds] = timePart.split(':');

      if (!day || !month || !year || !hours || !minutes || !seconds) return null;

      // Create Date object (month is 0-indexed in JavaScript)
      const date = new Date(
        parseInt(year, 10),
        parseInt(month, 10) - 1,
        parseInt(day, 10),
        parseInt(hours, 10),
        parseInt(minutes, 10),
        parseInt(seconds, 10)
      );

      // Validate the date
      if (isNaN(date.getTime())) return null;

      return date;
    } catch (error) {
      HandlerUtils.logError('HourlyReportDataService', `Failed to parse register date: ${registerDateString}`);
      return null;
    }
  }

  /**
   * Fetch daily transaction data for hourly reports
   * Similar to daily reports but fetches from start of day to current hour
   *
   * @param currentHour - Current hour timestamp (end of window)
   * @returns All transactions from start of day to current hour
   */
  static async fetchDailyTransactionDataForHourly(currentHour: Date): Promise<any> {
    try {
      const startOfDay = new Date(currentHour);
      startOfDay.setHours(0, 0, 0, 0);

      HandlerUtils.logActivity('HourlyReportDataService',
        `Fetching daily transaction data for hourly report from ${startOfDay.toISOString()} to ${currentHour.toISOString()}`);

      // Define 4-hour time periods for the day
      const timePeriods = [
        { start: 0, end: 4 },   // 00:00 - 04:00
        { start: 4, end: 8 },   // 04:00 - 08:00
        { start: 8, end: 12 },  // 08:00 - 12:00
        { start: 12, end: 16 }, // 12:00 - 16:00
        { start: 16, end: 20 }, // 16:00 - 20:00
        { start: 20, end: 24 }, // 20:00 - 24:00
      ];

      // Determine which batches to fetch based on current hour (optimization)
      const currentHourOfDay = currentHour.getHours();
      const batchesToFetch = timePeriods.filter(period => {
        // Include batch if current hour falls within or after this period
        return currentHourOfDay >= period.start;
      });

      // Performance optimization: If it's early in the day, we need fewer batches
      const optimizedBatchCount = Math.min(batchesToFetch.length, Math.ceil((currentHourOfDay + 1) / 4));
      const finalBatchesToFetch = batchesToFetch.slice(0, optimizedBatchCount);

      HandlerUtils.logActivity('HourlyReportDataService',
        `Performance optimization: Current hour: ${currentHourOfDay}:00, fetching ${finalBatchesToFetch.length}/${batchesToFetch.length} batches for daily transaction data`);

      // Collect all transaction results
      const allTransactionResults: any[] = [];
      let totalFetchedTransactions = 0;

      // Fetch transactions for each relevant period
      for (let i = 0; i < finalBatchesToFetch.length; i++) {
        const period = finalBatchesToFetch[i];

        // Create start and end times for this period
        const periodStart = new Date(startOfDay);
        periodStart.setHours(period.start, 0, 0, 0);

        let periodEnd = new Date(startOfDay);
        periodEnd.setHours(period.end, 0, 0, 0);

        // For the last batch, don't go beyond current hour
        if (period.end > currentHourOfDay || (period.end === 24 && currentHourOfDay < 24)) {
          periodEnd = new Date(currentHour);
        }

        // Skip if period start is after current hour
        if (periodStart >= currentHour) {
          continue;
        }

        HandlerUtils.logActivity('HourlyReportDataService',
          `Fetching batch ${i + 1}/${finalBatchesToFetch.length}: ${periodStart.toISOString()} to ${periodEnd.toISOString()}`);

        try {
          const transactionResult = await pgDagurApiClient.accounting.listTransactions({
            startDate: periodStart,
            endDate: periodEnd,
            status: ['C'], // Only confirmed transactions
            page: 1,
            limit: 10000,
            loadSubtotals: true, // Reports need subtotals for aggregation
          });

          if (transactionResult && transactionResult.transactions) {
            allTransactionResults.push(transactionResult);
            const periodTransactionCount = transactionResult.transactions.items?.length || 0;
            totalFetchedTransactions += periodTransactionCount;

            HandlerUtils.logActivity('HourlyReportDataService',
              `Batch ${i + 1}/${finalBatchesToFetch.length} completed: ${periodTransactionCount} transactions`);
          }

          // Optimized delay: shorter delay for hourly reports
          if (i < finalBatchesToFetch.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 50)); // Reduced from 100ms to 50ms
          }

        } catch (error) {
          HandlerUtils.logError('HourlyReportDataService',
            `Failed to fetch batch ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          // Continue with other batches even if one fails
        }
      }

      if (allTransactionResults.length === 0) {
        HandlerUtils.logActivity('HourlyReportDataService', 'No transaction data retrieved from any batch');
        return null;
      }

      // Merge all transaction results
      const mergedResult = this.mergeTransactionResults(allTransactionResults);

      HandlerUtils.logActivity('HourlyReportDataService',
        `Daily transaction data for hourly report merged successfully: ${mergedResult.transactions.items.length} total transactions`);

      return mergedResult;

    } catch (error) {
      HandlerUtils.logError('HourlyReportDataService',
        `Failed to fetch daily transaction data for hourly report: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Fetch daily summary data from start of day to current hour
   * Uses 4-hour batches and skips future batches based on current hour
   *
   * @param currentHour - The current hour (end of hourly report period)
   * @returns Daily summary data or null if failed
   */
  static async fetchDailySummaryData(currentHour: Date): Promise<DailySummary | null> {
    try {
      const currentDate = new Date(currentHour);
      const startOfDay = new Date(currentDate);
      startOfDay.setHours(0, 0, 0, 0);

      HandlerUtils.logActivity('HourlyReportDataService',
        `Fetching daily summary from ${startOfDay.toISOString()} to ${currentHour.toISOString()} in 4-hour batches`);

      // Define 4-hour time periods for the day
      const timePeriods = [
        { start: 0, end: 4 },   // 00:00 - 04:00
        { start: 4, end: 8 },   // 04:00 - 08:00
        { start: 8, end: 12 },  // 08:00 - 12:00
        { start: 12, end: 16 }, // 12:00 - 16:00
        { start: 16, end: 20 }, // 16:00 - 20:00
        { start: 20, end: 24 }, // 20:00 - 24:00
      ];

      // Determine which batches to fetch based on current hour
      const currentHourOfDay = currentHour.getHours();
      const batchesToFetch = timePeriods.filter(period => {
        // Include batch if current hour falls within or after this period
        return currentHourOfDay >= period.start;
      });

      HandlerUtils.logActivity('HourlyReportDataService',
        `Current hour: ${currentHourOfDay}:00, fetching ${batchesToFetch.length} batches`);

      // Fetch transaction data in batches
      const allTransactionResults: any[] = [];
      let totalFetchedTransactions = 0;

      for (let i = 0; i < batchesToFetch.length; i++) {
        const period = batchesToFetch[i];

        // Create start and end times for this period
        const periodStart = new Date(startOfDay);
        periodStart.setHours(period.start, 0, 0, 0);

        let periodEnd = new Date(startOfDay);
        periodEnd.setHours(period.end, 0, 0, 0);

        // For the last batch, don't go beyond current hour
        if (period.end > currentHourOfDay || (period.end === 24 && currentHourOfDay < 24)) {
          periodEnd = new Date(currentHour);
        }

        // Skip if period start is after current hour
        if (periodStart >= currentHour) {
          continue;
        }

        HandlerUtils.logActivity('HourlyReportDataService',
          `Fetching batch ${i + 1}/${batchesToFetch.length}: ${periodStart.toISOString()} to ${periodEnd.toISOString()}`);

        try {
          const transactionResult = await pgDagurApiClient.accounting.listTransactions({
            startDate: periodStart,
            endDate: periodEnd,
            status: ['C'], // Only confirmed transactions
            page: 1,
            limit: 10000,
            loadSubtotals: true, // Reports need subtotals for aggregation
          });

          if (transactionResult && transactionResult.transactions) {
            allTransactionResults.push(transactionResult);
            const periodTransactionCount = transactionResult.transactions.items?.length || 0;
            totalFetchedTransactions += periodTransactionCount;

            HandlerUtils.logActivity('HourlyReportDataService',
              `Batch ${i + 1}/${batchesToFetch.length} completed: ${periodTransactionCount} transactions`);
          }

          // Add delay between requests
          if (i < batchesToFetch.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }

        } catch (periodError) {
          HandlerUtils.logError('HourlyReportDataService',
            `Failed to fetch batch ${i + 1}/${batchesToFetch.length}: ${periodError instanceof Error ? periodError.message : 'Unknown error'}`);
        }
      }

      HandlerUtils.logActivity('HourlyReportDataService',
        `Completed daily summary fetch. Total transactions: ${totalFetchedTransactions}`);

      // Merge transaction results to create daily summary
      const mergedTransactionResult = this.mergeTransactionResults(allTransactionResults);
      const dailySummary = this.combineDailySummaryData(mergedTransactionResult);

      return dailySummary;

    } catch (error) {
      HandlerUtils.logError('HourlyReportDataService', `Failed to fetch daily summary: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Merge multiple transaction results from different time periods
   */
  private static mergeTransactionResults(transactionResults: any[]): any {
    if (transactionResults.length === 0) {
      return {
        transactions: { items: [] },
        subtotals: {
          totalConfirmedDeposits: 0,
          totalConfirmedWithdrawals: 0,
        }
      };
    }

    const allTransactions: any[] = [];
    let totalConfirmedDeposits = 0;
    let totalConfirmedWithdrawals = 0;

    transactionResults.forEach(result => {
      if (result.transactions && result.transactions.items) {
        allTransactions.push(...result.transactions.items);
      }

      if (result.subtotals) {
        totalConfirmedDeposits += result.subtotals.totalConfirmedDeposits || 0;
        totalConfirmedWithdrawals += result.subtotals.totalConfirmedWithdrawals || 0;
      }
    });

    return {
      transactions: {
        items: allTransactions,
        total: allTransactions.length,
      },
      subtotals: {
        totalConfirmedDeposits,
        totalConfirmedWithdrawals,
      }
    };
  }

  /**
   * Create daily summary based only on transaction data
   */
  private static combineDailySummaryData(transactionData: any): DailySummary | null {
    if (!transactionData || !transactionData.subtotals) {
      return null;
    }

    const subtotals = transactionData.subtotals;

    HandlerUtils.logActivity('HourlyReportDataService',
      `Daily summary created from transaction data: deposits=${subtotals.totalConfirmedDeposits || 0}, withdrawals=${subtotals.totalConfirmedWithdrawals || 0}`);

    HandlerUtils.logActivity('HourlyReportDataService',
      'Note: adjustmentTopUpAmount and totalDiscountAmount are excluded from hourly reports as they are only available in daily activity reports, not in transaction-based hourly data');

    // Note: adjustmentTopUpAmount and totalDiscountAmount are not included
    // because they are only available in daily activity reports, not in transaction-based hourly data
    return {
      totalDepositAmount: subtotals.totalConfirmedDeposits || 0,
      totalWithdrawAmount: subtotals.totalConfirmedWithdrawals || 0,
      dailyNetCashFlow: (subtotals.totalConfirmedDeposits || 0) - (subtotals.totalConfirmedWithdrawals || 0),
      numberOfSportBets: 0, // Not available from transaction data
      numberOfVirtualBets: 0, // Not available from transaction data
      numberOfCasinoBets: 0, // Not available from transaction data
      totalSportPlayAmount: 0, // Not available from transaction data
      totalCasinoPlayAmount: 0, // Not available from transaction data
      totalSportOpenAmount: 0, // Not available from transaction data
      sportGGR: 0, // Not available from transaction data
      casinoGGR: 0, // Not available from transaction data
      totalBonusAmount: 0, // Not available from transaction data
      totalSpentBonusAmount: 0, // Not available from transaction data
      sportBonusAmount: 0, // Not available from transaction data
      sportSpentBonusAmount: 0, // Not available from transaction data
      casinoBonusAmount: 0, // Not available from transaction data
      casinoSpentBonusAmount: 0, // Not available from transaction data
      totalGivenFreebetAmount: 0, // Not available from transaction data
    };
  }
}
