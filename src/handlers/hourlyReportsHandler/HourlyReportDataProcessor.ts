import { TransactionSummary } from './types';
import { HandlerUtils } from '../index';

/**
 * Service class for processing and aggregating hourly report data
 */
export class HourlyReportDataProcessor {
  private static readonly GMT_PLUS_3_OFFSET = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

  /**
   * Convert UTC date to GMT+3
   * @param utcDate - UTC date
   * @returns GMT+3 date
   */
  private static toGMTPlus3(utcDate: Date): Date {
    return new Date(utcDate.getTime() + this.GMT_PLUS_3_OFFSET);
  }

  /**
   * Filter transactions by hourly time window (specific hour only)
   *
   * @param transactions - Array of transaction objects
   * @param hourStart - Start of the hour window
   * @param hourEnd - End of the hour window
   * @returns Filtered transactions within the hourly window
   */
  static filterTransactionsByHourlyWindow(transactions: any[], hourStart: Date, hourEnd: Date): any[] {
    if (!transactions || transactions.length === 0) {
      HandlerUtils.logActivity('HourlyReportDataProcessor',
        'No transactions provided for hourly window filtering');
      return [];
    }

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Filtering ${transactions.length} transactions for hourly window: ${hourStart.toISOString()} to ${hourEnd.toISOString()}`);

    const filteredTransactions = transactions.filter((t: any) => {
      if (!t || !t.transactionDate) return false;

      // Parse transaction timestamp
      const transactionTime = this.parseTransactionTimestamp(t.transactionDate);
      if (!transactionTime) return false;

      // Validate transaction falls within hourly window
      return this.validateTransactionTimeWindow(transactionTime, hourStart, hourEnd);
    });

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Hourly window filtering result: ${filteredTransactions.length} transactions within hour window`);

    // Validation: Log time range of filtered transactions
    if (filteredTransactions.length > 0) {
      const timestamps = filteredTransactions
        .map(t => this.parseTransactionTimestamp(t.transactionDate))
        .filter(Boolean)
        .sort((a, b) => a!.getTime() - b!.getTime());

      if (timestamps.length > 0) {
        HandlerUtils.logActivity('HourlyReportDataProcessor',
          `Hourly window validation: earliest=${timestamps[0]!.toISOString()}, latest=${timestamps[timestamps.length - 1]!.toISOString()}`);
      }
    }

    return filteredTransactions;
  }

  /**
   * Filter transactions by up-to-hour time window (start of day to current hour)
   *
   * @param transactions - Array of transaction objects
   * @param dayStart - Start of the day (00:00)
   * @param hourEnd - End of the current hour
   * @returns Filtered transactions within the up-to-hour window
   */
  static filterTransactionsByUpToHourWindow(transactions: any[], dayStart: Date, hourEnd: Date): any[] {
    if (!transactions || transactions.length === 0) {
      HandlerUtils.logActivity('HourlyReportDataProcessor',
        'No transactions provided for up-to-hour window filtering');
      return [];
    }

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Filtering ${transactions.length} transactions for up-to-hour window: ${dayStart.toISOString()} to ${hourEnd.toISOString()}`);

    const filteredTransactions = transactions.filter((t: any) => {
      if (!t || !t.transactionDate) return false;

      // Parse transaction timestamp
      const transactionTime = this.parseTransactionTimestamp(t.transactionDate);
      if (!transactionTime) return false;

      // Validate transaction falls within up-to-hour window
      return this.validateTransactionTimeWindow(transactionTime, dayStart, hourEnd);
    });

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Up-to-hour window filtering result: ${filteredTransactions.length} transactions within up-to-hour window`);

    // Validation: Log time range of filtered transactions
    if (filteredTransactions.length > 0) {
      const timestamps = filteredTransactions
        .map(t => this.parseTransactionTimestamp(t.transactionDate))
        .filter(Boolean)
        .sort((a, b) => a!.getTime() - b!.getTime());

      if (timestamps.length > 0) {
        HandlerUtils.logActivity('HourlyReportDataProcessor',
          `Up-to-hour window validation: earliest=${timestamps[0]!.toISOString()}, latest=${timestamps[timestamps.length - 1]!.toISOString()}`);
      }
    }

    return filteredTransactions;
  }

  /**
   * Parse transaction timestamp from various possible formats
   *
   * @param transactionDate - Transaction date field (could be timestamp or formatted date)
   * @returns Parsed Date object or null if parsing fails
   */
  static parseTransactionTimestamp(transactionDate: any): Date | null {
    try {
      if (!transactionDate) return null;

      // If it's already a Date object
      if (transactionDate instanceof Date) {
        // Validate it's a valid date
        if (isNaN(transactionDate.getTime())) return null;
        return transactionDate;
      }

      // If it's a number (timestamp in milliseconds)
      if (typeof transactionDate === 'number') {
        // Handle both milliseconds and seconds timestamps
        let timestamp = transactionDate;

        // If timestamp is in seconds (less than year 2100), convert to milliseconds
        if (timestamp < 4102444800) {
          timestamp = timestamp * 1000;
        }

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return null;

        // Validate timestamp is reasonable (between 2020 and 2030)
        const year = date.getFullYear();
        if (year < 2020 || year > 2030) {
          HandlerUtils.logActivity('HourlyReportDataProcessor',
            `Transaction timestamp outside reasonable range: ${date.toISOString()}`);
          return null;
        }

        return date;
      }

      // If it's a string, try various parsing methods
      if (typeof transactionDate === 'string') {
        // Try ISO string format first
        let parsed = new Date(transactionDate);
        if (!isNaN(parsed.getTime())) {
          return parsed;
        }

        // Try parsing as number string (timestamp)
        const numericValue = parseFloat(transactionDate);
        if (!isNaN(numericValue)) {
          return this.parseTransactionTimestamp(numericValue);
        }

        // Try common date formats
        const commonFormats = [
          // DD.MM.YYYY HH:mm:ss format (like user registration dates)
          /^(\d{2})\.(\d{2})\.(\d{4}) (\d{2}):(\d{2}):(\d{2})$/,
          // YYYY-MM-DD HH:mm:ss format
          /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/,
        ];

        for (const format of commonFormats) {
          const match = transactionDate.match(format);
          if (match) {
            // Handle DD.MM.YYYY format
            if (format.source.includes('\\.')) {
              const [, day, month, year, hour, minute, second] = match;
              parsed = new Date(
                parseInt(year, 10),
                parseInt(month, 10) - 1, // Month is 0-indexed
                parseInt(day, 10),
                parseInt(hour, 10),
                parseInt(minute, 10),
                parseInt(second, 10)
              );
            } else {
              // Handle YYYY-MM-DD format
              const [, year, month, day, hour, minute, second] = match;
              parsed = new Date(
                parseInt(year, 10),
                parseInt(month, 10) - 1, // Month is 0-indexed
                parseInt(day, 10),
                parseInt(hour, 10),
                parseInt(minute, 10),
                parseInt(second, 10)
              );
            }

            if (!isNaN(parsed.getTime())) {
              return parsed;
            }
          }
        }
      }

      return null;
    } catch (error) {
      HandlerUtils.logActivity('HourlyReportDataProcessor',
        `Failed to parse transaction timestamp: ${transactionDate}, error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Validate that a transaction timestamp falls within the expected time window
   *
   * @param transactionTime - Parsed transaction timestamp
   * @param windowStart - Start of the time window
   * @param windowEnd - End of the time window
   * @returns True if transaction is within the window
   */
  static validateTransactionTimeWindow(transactionTime: Date, windowStart: Date, windowEnd: Date): boolean {
    if (!transactionTime || !windowStart || !windowEnd) return false;

    const isValid = transactionTime >= windowStart && transactionTime < windowEnd;

    if (!isValid) {
      HandlerUtils.logActivity('HourlyReportDataProcessor',
        `Transaction timestamp ${transactionTime.toISOString()} outside window ${windowStart.toISOString()} to ${windowEnd.toISOString()}`);
    }

    return isValid;
  }

  /**
   * Calculate new user deposit amount for hourly window
   *
   * @param transactions - Array of all transaction objects
   * @param newUserCustomerIds - Array of customer IDs for new users
   * @param hourStart - Start of the hour window
   * @param hourEnd - End of the hour window
   * @returns Total deposit amount for new users within the hourly window
   */
  static calculateHourlyNewUserDepositAmount(
    transactions: any[],
    newUserCustomerIds: number[],
    hourStart: Date,
    hourEnd: Date
  ): number {
    if (!transactions || !newUserCustomerIds || newUserCustomerIds.length === 0) {
      HandlerUtils.logActivity('HourlyReportDataProcessor',
        'No transactions or new user customer IDs provided for hourly new user deposit calculation');
      return 0;
    }

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Calculating hourly new user deposits: ${transactions.length} transactions, ${newUserCustomerIds.length} new users, window: ${hourStart.toISOString()} to ${hourEnd.toISOString()}`);

    // Step 1: Filter transactions by time window
    const hourlyTransactions = this.filterTransactionsByHourlyWindow(transactions, hourStart, hourEnd);

    // Step 2: Performance optimization - Filter by new user customer IDs and deposits only
    const newUserIdSet = new Set(newUserCustomerIds);

    // Pre-filter deposits for better performance
    const hourlyDeposits = hourlyTransactions.filter((t: any) =>
      t && t.masterCode === 'D' && typeof t.amount === 'number'
    );

    const newUserHourlyDeposits = hourlyDeposits.filter((t: any) =>
      t.customerId && newUserIdSet.has(t.customerId)
    );

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Performance: ${hourlyTransactions.length} hourly → ${hourlyDeposits.length} deposits → ${newUserHourlyDeposits.length} new user deposits`);

    // Step 3: Calculate total amount and log detailed breakdown
    const totalAmount = newUserHourlyDeposits.reduce((sum: number, t: any) => sum + t.amount, 0);

    // Log detailed filtering results
    const uniqueCustomersInDeposits = new Set(newUserHourlyDeposits.map(t => t.customerId)).size;
    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Hourly filtering breakdown: ${hourlyTransactions.length} hourly transactions → ${newUserHourlyDeposits.length} new user deposits from ${uniqueCustomersInDeposits} unique customers, total: ${totalAmount}`);

    // Log sample transactions for verification
    if (newUserHourlyDeposits.length > 0) {
      const sampleDeposits = newUserHourlyDeposits.slice(0, 2);
      sampleDeposits.forEach((t: any, index: number) => {
        const transactionTime = this.parseTransactionTimestamp(t.transactionDate);
        HandlerUtils.logActivity('HourlyReportDataProcessor',
          `Sample hourly deposit ${index + 1}: customerId=${t.customerId}, amount=${t.amount}, time=${transactionTime?.toISOString() || 'N/A'}`);
      });
    }

    return totalAmount;
  }

  /**
   * Calculate new user deposit amount for up-to-hour window
   *
   * @param transactions - Array of all transaction objects
   * @param newUserCustomerIds - Array of customer IDs for new users
   * @param dayStart - Start of the day (00:00)
   * @param hourEnd - End of the current hour
   * @returns Total deposit amount for new users within the up-to-hour window
   */
  static calculateUpToHourNewUserDepositAmount(
    transactions: any[],
    newUserCustomerIds: number[],
    dayStart: Date,
    hourEnd: Date
  ): number {
    if (!transactions || !newUserCustomerIds || newUserCustomerIds.length === 0) {
      HandlerUtils.logActivity('HourlyReportDataProcessor',
        'No transactions or new user customer IDs provided for up-to-hour new user deposit calculation');
      return 0;
    }

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Calculating up-to-hour new user deposits: ${transactions.length} transactions, ${newUserCustomerIds.length} new users, window: ${dayStart.toISOString()} to ${hourEnd.toISOString()}`);

    // Step 1: Filter transactions by time window
    const upToHourTransactions = this.filterTransactionsByUpToHourWindow(transactions, dayStart, hourEnd);

    // Step 2: Performance optimization - Filter by new user customer IDs and deposits only
    const newUserIdSet = new Set(newUserCustomerIds);

    // Pre-filter deposits for better performance
    const upToHourDeposits = upToHourTransactions.filter((t: any) =>
      t && t.masterCode === 'D' && typeof t.amount === 'number'
    );

    const newUserUpToHourDeposits = upToHourDeposits.filter((t: any) =>
      t.customerId && newUserIdSet.has(t.customerId)
    );

    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Performance: ${upToHourTransactions.length} up-to-hour → ${upToHourDeposits.length} deposits → ${newUserUpToHourDeposits.length} new user deposits`);

    // Step 3: Calculate total amount and log detailed breakdown
    const totalAmount = newUserUpToHourDeposits.reduce((sum: number, t: any) => sum + t.amount, 0);

    // Log detailed filtering results
    const uniqueCustomersInDeposits = new Set(newUserUpToHourDeposits.map(t => t.customerId)).size;
    HandlerUtils.logActivity('HourlyReportDataProcessor',
      `Up-to-hour filtering breakdown: ${upToHourTransactions.length} up-to-hour transactions → ${newUserUpToHourDeposits.length} new user deposits from ${uniqueCustomersInDeposits} unique customers, total: ${totalAmount}`);

    // Log sample transactions for verification
    if (newUserUpToHourDeposits.length > 0) {
      const sampleDeposits = newUserUpToHourDeposits.slice(0, 2);
      sampleDeposits.forEach((t: any, index: number) => {
        const transactionTime = this.parseTransactionTimestamp(t.transactionDate);
        HandlerUtils.logActivity('HourlyReportDataProcessor',
          `Sample up-to-hour deposit ${index + 1}: customerId=${t.customerId}, amount=${t.amount}, time=${transactionTime?.toISOString() || 'N/A'}`);
      });
    }

    return totalAmount;
  }

  /**
   * Aggregate transaction data into summary statistics for hourly reports
   *
   * @param transactionResult - Raw transaction data from PG Dagur API
   * @param hourlyNewUserCustomerIds - Optional array of new user customer IDs for hourly window
   * @param upToHourNewUserCustomerIds - Optional array of new user customer IDs for up-to-hour window
   * @param hourStart - Start of the hourly window
   * @param hourEnd - End of the hourly window
   * @param dayStart - Start of the day
   * @returns Aggregated transaction summary with new user data
   */
  static aggregateTransactionData(
    transactionResult: any,
    hourlyNewUserCustomerIds?: number[],
    upToHourNewUserCustomerIds?: number[],
    hourStart?: Date,
    hourEnd?: Date,
    dayStart?: Date
  ): TransactionSummary {
    const transactions = transactionResult.transactions.items || [];
    const subtotals = transactionResult.subtotals;

    // Count transactions by type
    const deposits = transactions.filter((t: any) => t.masterCode === 'D');
    const withdrawals = transactions.filter((t: any) => t.masterCode === 'W');

    // Calculate unique customers
    const uniqueCustomers = new Set(transactions.map((t: any) => t.customerId)).size;

    // Calculate average transaction amount
    const totalAmount = transactions.reduce((sum: number, t: any) => sum + (t.amount || 0), 0);
    const averageTransactionAmount = transactions.length > 0 ? totalAmount / transactions.length : 0;

    // Calculate amounts
    const totalDepositAmount = subtotals?.totalConfirmedDeposits || 0;
    const totalWithdrawalAmount = subtotals?.totalConfirmedWithdrawals || 0;
    const netCashFlow = totalDepositAmount - totalWithdrawalAmount;

    // Aggregate by bank name instead of payment method
    const bankStats = new Map<string, { deposits: { count: number; amount: number }, withdrawals: { count: number; amount: number } }>();

    transactions.forEach((t: any) => {
      const bankName = t.bankName || 'Bilinmeyen Banka';
      if (!bankStats.has(bankName)) {
        bankStats.set(bankName, { deposits: { count: 0, amount: 0 }, withdrawals: { count: 0, amount: 0 } });
      }

      const stats = bankStats.get(bankName)!;
      if (t.masterCode === 'D') {
        stats.deposits.count++;
        stats.deposits.amount += t.amount || 0;
      } else if (t.masterCode === 'W') {
        stats.withdrawals.count++;
        stats.withdrawals.amount += t.amount || 0;
      }
    });

    // Create bank breakdown
    const bankBreakdown: Array<{
      bankName: string;
      type: 'deposit' | 'withdrawal';
      count: number;
      amount: number;
      percentage: number;
    }> = [];

    bankStats.forEach((stats, bankName) => {
      if (stats.deposits.count > 0) {
        bankBreakdown.push({
          bankName,
          type: 'deposit',
          count: stats.deposits.count,
          amount: stats.deposits.amount,
          percentage: totalDepositAmount > 0 ? (stats.deposits.amount / totalDepositAmount) * 100 : 0,
        });
      }
      if (stats.withdrawals.count > 0) {
        bankBreakdown.push({
          bankName,
          type: 'withdrawal',
          count: stats.withdrawals.count,
          amount: stats.withdrawals.amount,
          percentage: totalWithdrawalAmount > 0 ? (stats.withdrawals.amount / totalWithdrawalAmount) * 100 : 0,
        });
      }
    });

    // Sort by amount descending
    bankBreakdown.sort((a, b) => b.amount - a.amount);

    // Calculate new user deposit amounts if customer IDs and time windows are provided
    let hourlyNewUserDepositAmount = 0;
    let upToHourNewUserDepositAmount = 0;

    if (hourlyNewUserCustomerIds && hourlyNewUserCustomerIds.length > 0 && hourStart && hourEnd) {
      hourlyNewUserDepositAmount = this.calculateHourlyNewUserDepositAmount(
        transactions,
        hourlyNewUserCustomerIds,
        hourStart,
        hourEnd
      );

      HandlerUtils.logActivity('HourlyReportDataProcessor',
        `Calculated hourly new user deposit amount: ${hourlyNewUserDepositAmount} from ${hourlyNewUserCustomerIds.length} users`);
    }

    if (upToHourNewUserCustomerIds && upToHourNewUserCustomerIds.length > 0 && dayStart && hourEnd) {
      upToHourNewUserDepositAmount = this.calculateUpToHourNewUserDepositAmount(
        transactions,
        upToHourNewUserCustomerIds,
        dayStart,
        hourEnd
      );

      HandlerUtils.logActivity('HourlyReportDataProcessor',
        `Calculated up-to-hour new user deposit amount: ${upToHourNewUserDepositAmount} from ${upToHourNewUserCustomerIds.length} users`);
    }

    return {
      totalTransactions: transactions.length,
      totalDeposits: deposits.length,
      totalWithdrawals: withdrawals.length,
      totalDepositAmount,
      totalWithdrawalAmount,
      netCashFlow,
      uniqueCustomers,
      averageTransactionAmount,
      bankBreakdown,
      hourlyNewUserDepositAmount,
      upToHourNewUserDepositAmount,
    };
  }

  /**
   * Format date range for display (converts UTC to GMT+3)
   *
   * @param startDate - Start date (UTC)
   * @param endDate - End date (UTC)
   * @returns Formatted date range string in GMT+3
   */
  static formatDateRange(startDate: Date, endDate: Date): string {
    const formatTime = (date: Date) => {
      // Convert UTC to GMT+3
      const gmtPlus3Date = this.toGMTPlus3(date);
      const hours = gmtPlus3Date.getUTCHours().toString().padStart(2, '0');
      const minutes = gmtPlus3Date.getUTCMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    };

    // Convert start date to GMT+3 for date display
    const gmtPlus3StartDate = this.toGMTPlus3(startDate);
    const startTime = formatTime(startDate);
    const endTime = formatTime(endDate);
    const dateStr = gmtPlus3StartDate.toLocaleDateString('tr-TR');

    return `${dateStr} ${startTime} - ${endTime}`;
  }
}
