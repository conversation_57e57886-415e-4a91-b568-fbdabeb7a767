import { DailyTransactionSummary, NewUserData } from './types';
import { HandlerUtils } from '../index';

/**
 * Service class for processing and aggregating daily report data
 */
export class DailyReportDataProcessor {
  /**
   * Calculate new user deposit amount from transactions
   *
   * @param transactions - Array of transaction objects
   * @param newUserCustomerIds - Array of customer IDs for new users
   * @returns Total deposit amount for new users
   */
  static calculateNewUserDepositAmount(transactions: any[], newUserCustomerIds: number[]): number {
    if (!transactions || !newUserCustomerIds || newUserCustomerIds.length === 0) {
      HandlerUtils.logActivity('DailyReportDataProcessor',
        'No transactions or new user customer IDs provided for new user deposit calculation');
      return 0;
    }

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Starting new user deposit calculation with ${transactions.length} total transactions and ${newUserCustomerIds.length} new user IDs`);

    // Log sample new user customer IDs for debugging
    const sampleCustomerIds = newUserCustomerIds.slice(0, 5);
    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Sample new user customer IDs: [${sampleCustomerIds.join(', ')}]`);

    // Create a Set for faster lookup of new user customer IDs
    const newUserIdSet = new Set(newUserCustomerIds);

    // Filter transactions for new users and deposits only
    const newUserDeposits: any[] = [];
    let debugLogCount = 0;

    for (const t of transactions) {
      const isValidTransaction = t &&
        t.customerId &&
        newUserIdSet.has(t.customerId) &&
        t.masterCode === 'D' && // Only deposits
        typeof t.amount === 'number';

      if (isValidTransaction) {
        newUserDeposits.push(t);

        // Log first few matches for debugging
        if (debugLogCount < 3) {
          HandlerUtils.logActivity('DailyReportDataProcessor',
            `Found new user deposit: customerId=${t.customerId}, amount=${t.amount}, masterCode=${t.masterCode}, bankName=${t.bankName || 'N/A'}`);
          debugLogCount++;
        }
      }
    }

    // Calculate total deposit amount
    const totalAmount = newUserDeposits.reduce((sum: number, t: any) => sum + t.amount, 0);

    // Group deposits by customer ID for detailed analysis
    const depositsByCustomer = new Map<number, { count: number; total: number }>();
    newUserDeposits.forEach((t: any) => {
      const existing = depositsByCustomer.get(t.customerId) || { count: 0, total: 0 };
      depositsByCustomer.set(t.customerId, {
        count: existing.count + 1,
        total: existing.total + t.amount
      });
    });

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `New user deposit calculation complete: ${newUserDeposits.length} deposits from ${depositsByCustomer.size} unique new users, total: ${totalAmount}`);

    // Log detailed breakdown for first few customers
    let customerCount = 0;
    for (const [customerId, stats] of depositsByCustomer) {
      if (customerCount < 3) {
        HandlerUtils.logActivity('DailyReportDataProcessor',
          `Customer ${customerId}: ${stats.count} deposits, total: ${stats.total}`);
        customerCount++;
      } else {
        break;
      }
    }

    return totalAmount;
  }

  /**
   * Aggregate transaction data into summary statistics
   *
   * @param transactionResult - Raw transaction data from PG Dagur API
   * @param newUserCustomerIds - Optional array of new user customer IDs for accurate deposit calculation
   * @param totalCustomerBalance - Pre-fetched total customer balance from balance API
   * @returns Aggregated transaction summary
   */
  static aggregateTransactionData(transactionResult: any, newUserCustomerIds?: number[], totalCustomerBalance?: number): DailyTransactionSummary {
    try {
      // Validate input structure
      if (!transactionResult || !transactionResult.transactions) {
        HandlerUtils.logError('DailyReportDataProcessor', 'Invalid transaction result structure');
        return this.createEmptyTransactionSummary();
      }

      const transactions = transactionResult.transactions.items || [];
      const subtotals = transactionResult.subtotals;

      HandlerUtils.logActivity('DailyReportDataProcessor',
        `Starting transaction aggregation: ${transactions.length} total transactions`);

    // Debug: Analyze transaction structure (only for first few transactions)
    if (transactions.length > 0) {
      this.analyzeTransactionStructure(transactions);
    }

    // Validate transaction data structure
    const validTransactions = transactions.filter((t: any) => {
      const isValid = t &&
        typeof t.masterCode === 'string' &&
        typeof t.amount === 'number' &&
        t.customerId;

      if (!isValid) {
        HandlerUtils.logActivity('DailyReportDataProcessor',
          `Skipping invalid transaction: ${JSON.stringify(t)}`);
      }

      return isValid;
    });

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Validated transactions: ${validTransactions.length}/${transactions.length} valid`);

    // Count transactions by type
    const deposits = validTransactions.filter((t: any) => t.masterCode === 'D');
    const withdrawals = validTransactions.filter((t: any) => t.masterCode === 'W');

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Transaction breakdown: ${deposits.length} deposits, ${withdrawals.length} withdrawals`);

    // Calculate unique customers
    const uniqueCustomers = new Set(validTransactions.map((t: any) => t.customerId)).size;

    // Calculate amounts
    const totalDepositAmount = subtotals?.totalConfirmedDeposits || 0;
    const totalWithdrawalAmount = subtotals?.totalConfirmedWithdrawals || 0;
    const netCashFlow = totalDepositAmount - totalWithdrawalAmount;

    // Use pre-fetched total customer balance from balance API
    // If not provided, fall back to 0 (should not happen in normal operation)
    const finalTotalCustomerBalance = totalCustomerBalance ?? 0;

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Using ${totalCustomerBalance !== undefined ? 'API-fetched' : 'fallback'} customer balance: ${finalTotalCustomerBalance}`);

    // Aggregate by bank name instead of payment method
    // Use a more efficient approach with pre-allocated objects
    const bankStats = new Map<string, { deposits: { count: number; amount: number }, withdrawals: { count: number; amount: number } }>();

    // Single pass through transactions for all aggregations
    validTransactions.forEach((t: any) => {
      // Extract and clean bank name
      let bankName = t.bankName || t.customerTransMasterTypeName || 'Bilinmeyen Banka';

      // Clean up bank name by removing common suffixes (optimized regex)
      bankName = bankName.replace(/\s+(Deposit|Withdrawal)$/i, '').trim();

      // If still empty after cleanup, use fallback
      if (!bankName) {
        bankName = 'Bilinmeyen Banka';
      }

      // Get or create bank stats
      let stats = bankStats.get(bankName);
      if (!stats) {
        stats = { deposits: { count: 0, amount: 0 }, withdrawals: { count: 0, amount: 0 } };
        bankStats.set(bankName, stats);
      }

      // Update stats based on transaction type
      const amount = t.amount || 0;
      if (t.masterCode === 'D') {
        stats.deposits.count++;
        stats.deposits.amount += amount;
      } else if (t.masterCode === 'W') {
        stats.withdrawals.count++;
        stats.withdrawals.amount += amount;
      }
    });

    // Create bank breakdown
    const paymentMethodBreakdown: Array<{
      method: string;
      type: 'deposit' | 'withdrawal';
      count: number;
      amount: number;
      percentage: number;
    }> = [];

    bankStats.forEach((stats, bankName) => {
      if (stats.deposits.count > 0) {
        const percentage = totalDepositAmount > 0 && stats.deposits.amount > 0
          ? Math.round((stats.deposits.amount / totalDepositAmount) * 100 * 100) / 100 // Round to 2 decimal places
          : 0;

        paymentMethodBreakdown.push({
          method: bankName,
          type: 'deposit',
          count: stats.deposits.count,
          amount: stats.deposits.amount,
          percentage,
        });
      }
      if (stats.withdrawals.count > 0) {
        const percentage = totalWithdrawalAmount > 0 && stats.withdrawals.amount > 0
          ? Math.round((stats.withdrawals.amount / totalWithdrawalAmount) * 100 * 100) / 100 // Round to 2 decimal places
          : 0;

        paymentMethodBreakdown.push({
          method: bankName,
          type: 'withdrawal',
          count: stats.withdrawals.count,
          amount: stats.withdrawals.amount,
          percentage,
        });
      }
    });

    // Sort by amount descending
    paymentMethodBreakdown.sort((a, b) => b.amount - a.amount);

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Payment method breakdown created: ${paymentMethodBreakdown.length} entries, ${bankStats.size} unique banks`);

    // Debug: Log bank distribution summary
    this.logBankDistributionSummary(paymentMethodBreakdown);

    // Calculate new user data if customer IDs are provided
    let newUserData: NewUserData | undefined = undefined;
    if (newUserCustomerIds && newUserCustomerIds.length > 0) {
      const newUserDepositAmount = this.calculateNewUserDepositAmount(validTransactions, newUserCustomerIds);
      newUserData = {
        newUserCount: newUserCustomerIds.length,
        newUserDepositAmount,
        newUserCustomerIds,
      };

      HandlerUtils.logActivity('DailyReportDataProcessor',
        `New user data calculated: ${newUserData.newUserCount} users, ${newUserData.newUserDepositAmount} total deposits`);
    }

    return {
      totalTransactions: validTransactions.length,
      totalDeposits: deposits.length,
      totalWithdrawals: withdrawals.length,
      totalDepositAmount,
      totalWithdrawalAmount,
      netCashFlow,
      uniqueCustomers,
      totalCustomerBalance: finalTotalCustomerBalance,
      paymentMethodBreakdown,
      newUserData,
    };

    } catch (error) {
      HandlerUtils.logError('DailyReportDataProcessor',
        `Error during transaction aggregation: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return this.createEmptyTransactionSummary();
    }
  }

  /**
   * Create an empty transaction summary for error cases
   */
  private static createEmptyTransactionSummary(): DailyTransactionSummary {
    return {
      totalTransactions: 0,
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalDepositAmount: 0,
      totalWithdrawalAmount: 0,
      netCashFlow: 0,
      uniqueCustomers: 0,
      totalCustomerBalance: 0,
      paymentMethodBreakdown: [],
    };
  }

  /**
   * Format date for display (converts to GMT+3 if needed)
   *
   * @param date - Date object
   * @returns Formatted date string in DD.MM.YYYY format
   */
  static formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  }

  /**
   * Debug utility: Analyze transaction data structure
   *
   * @param transactions - Array of transaction objects
   * @returns Analysis summary
   */
  static analyzeTransactionStructure(transactions: any[]): void {
    if (!transactions || transactions.length === 0) {
      HandlerUtils.logActivity('DailyReportDataProcessor', 'No transactions to analyze');
      return;
    }

    const sample = transactions[0];
    const fields = Object.keys(sample);

    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Transaction structure analysis - Sample fields: ${fields.join(', ')}`);

    // Analyze field types and values
    const fieldAnalysis: any = {};
    transactions.slice(0, Math.min(10, transactions.length)).forEach((t, index) => {
      Object.keys(t).forEach(field => {
        if (!fieldAnalysis[field]) {
          fieldAnalysis[field] = { types: new Set(), sampleValues: [] };
        }
        fieldAnalysis[field].types.add(typeof t[field]);
        if (fieldAnalysis[field].sampleValues.length < 3) {
          fieldAnalysis[field].sampleValues.push(t[field]);
        }
      });
    });

    // Log field analysis
    Object.keys(fieldAnalysis).forEach(field => {
      const analysis = fieldAnalysis[field];
      HandlerUtils.logActivity('DailyReportDataProcessor',
        `Field '${field}': types=[${Array.from(analysis.types).join(', ')}], samples=[${analysis.sampleValues.join(', ')}]`);
    });

    // Check for critical fields
    const criticalFields = ['masterCode', 'amount', 'customerId', 'bankName', 'currentBalance'];
    const missingFields = criticalFields.filter(field => !fields.includes(field));

    if (missingFields.length > 0) {
      HandlerUtils.logActivity('DailyReportDataProcessor',
        `WARNING: Missing critical fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Debug utility: Log bank distribution summary
   *
   * @param paymentMethodBreakdown - Payment method breakdown array
   */
  static logBankDistributionSummary(paymentMethodBreakdown: any[]): void {
    HandlerUtils.logActivity('DailyReportDataProcessor',
      `Bank Distribution Summary (${paymentMethodBreakdown.length} entries):`);

    paymentMethodBreakdown.forEach((entry, index) => {
      HandlerUtils.logActivity('DailyReportDataProcessor',
        `  ${index + 1}. ${entry.method} (${entry.type}): ${entry.count} transactions, ${entry.amount} amount, ${entry.percentage}%`);
    });
  }
}
