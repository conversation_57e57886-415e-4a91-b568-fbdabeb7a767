import { HandlerUtils } from '../index';
import { pgDagurApiClient, pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { CustomerReportListPreloadRequest, CustomerReportListRequest, CustomerReportListWithCursorRequest } from '@/network/pg-dagur/requests/customer/CustomerReportListRequest';
import {
  CustomerActivityReportListPreloadRequest,
  CustomerActivityReportListRequest,
} from '@/network/pg-dagur/requests/customer/CustomerActivityReportListRequest';
import { NewUserData, DailyTransactionSummary } from './types';
import { DailyReportDataProcessor } from './DailyReportDataProcessor';
import { BalanceService, BalanceServiceResult } from '@/services/BalanceService';

/**
 * Service class for fetching daily report data
 */
export class DailyReportDataService {
  /**
   * Fetch daily activity report from Dagur API
   *
   * @param startDate - Start date string
   * @param endDate - End date string
   * @returns Activity report data or null if failed
   */
  static async fetchActivityReport(startDate: string, endDate: string): Promise<any> {
    try {
      HandlerUtils.logActivity('DailyReportDataService', `Fetching activity report from Dagur API: ${startDate} to ${endDate}`);

      // Step 1: Get preload data for the activity report request
      let viewState = '';
      let currencyInputId = '';

      const preloadRequest = new CustomerActivityReportListPreloadRequest();
      const preloadResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!preloadResult.success) {
        throw new Error(`Failed to preload customer activity report: ${preloadResult.message || 'Unknown error'}`);
      }

      viewState = preloadResult.viewState || '';
      currencyInputId = preloadResult.data.currencyInputId;

      // Step 2: Make the actual activity report request
      const request = new CustomerActivityReportListRequest({
        currencyInputId: currencyInputId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });

      const result = await pgDagurAdminHttpClient.makeRequest(request, viewState);

      if (!result.success) {
        throw new Error(`Failed to fetch activity report: ${result.message || 'Unknown error'}`);
      }

      HandlerUtils.logActivity('DailyReportDataService', 'Successfully fetched activity report');
      return result.data;

    } catch (error) {
      HandlerUtils.logError('DailyReportDataService', `Failed to fetch activity report: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Fetch total customer balance for daily reports
   *
   * This method replaces the previous transaction-based balance calculation
   * with a dedicated API call to the PG Dagur balance endpoint.
   *
   * CHANGE SUMMARY:
   * - OLD: Calculated balance by summing currentBalance from transaction data (unreliable)
   * - NEW: Uses dedicated /api/pg-dagur/v1/internal/customers/balances endpoint
   * - Date is formatted to end of day (23:59:59) for accurate daily balance
   *
   * @param targetDate - Target date for the daily report (will be formatted to end of day)
   * @returns Balance service result with TRY balance or error details
   */
  static async fetchDailyBalance(targetDate: string): Promise<BalanceServiceResult> {
    try {
      HandlerUtils.logActivity('DailyReportDataService', `Fetching daily balance for date: ${targetDate}`);

      const balanceResult = await BalanceService.fetchDailyBalance(targetDate);

      if (balanceResult.success) {
        HandlerUtils.logActivity('DailyReportDataService',
          `Successfully fetched daily balance: ${balanceResult.balance}`);
      } else {
        HandlerUtils.logError('DailyReportDataService',
          `Failed to fetch daily balance: ${balanceResult.error}`);
      }

      return balanceResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      HandlerUtils.logError('DailyReportDataService', `Balance fetch error: ${errorMessage}`);

      return BalanceService.getFallbackBalance(errorMessage);
    }
  }

  /**
   * Fetch daily transaction data from PG Dagur API
   * Breaks the day into 6 requests of 4 hours each to avoid response size issues
   *
   * @param startDate - Start date string
   * @param endDate - End date string
   * @param newUserCustomerIds - Optional array of new user customer IDs for accurate deposit calculation
   * @param totalCustomerBalance - Optional pre-fetched balance to use instead of calculating from transactions
   * @returns Transaction summary data
   */
  static async fetchDailyTransactionData(startDate: string, endDate: string, newUserCustomerIds?: number[], totalCustomerBalance?: number): Promise<DailyTransactionSummary | null> {
    try {
      HandlerUtils.logActivity('DailyReportDataService', `Fetching transaction data from ${startDate} to ${endDate} in 4-hour chunks`);

      // Parse the base date
      const baseDate = new Date(startDate);

      // Define 4-hour time periods for the day
      const timePeriods = [
        { start: 0, end: 4 },   // 00:00 - 04:00
        { start: 4, end: 8 },   // 04:00 - 08:00
        { start: 8, end: 12 },  // 08:00 - 12:00
        { start: 12, end: 16 }, // 12:00 - 16:00
        { start: 16, end: 20 }, // 16:00 - 20:00
        { start: 20, end: 24 }, // 20:00 - 24:00
      ];

      // Collect all transaction results
      const allTransactionResults: any[] = [];
      let totalFetchedTransactions = 0;

      // Fetch transactions for each 4-hour period
      for (let i = 0; i < timePeriods.length; i++) {
        const period = timePeriods[i];

        // Create start and end times for this period
        const periodStart = new Date(baseDate);
        periodStart.setHours(period.start, 0, 0, 0);

        const periodEnd = new Date(baseDate);
        periodEnd.setHours(period.end, 0, 0, 0);

        // For the last period, set end to 23:59:59 instead of 24:00:00
        if (period.end === 24) {
          periodEnd.setHours(23, 59, 59, 999);
        }

        HandlerUtils.logActivity('DailyReportDataService',
          `Fetching period ${i + 1}/6: ${periodStart.toISOString()} to ${periodEnd.toISOString()}`);

        try {
          // Fetch transaction data for this period
          const transactionResult = await pgDagurApiClient.accounting.listTransactions({
            startDate: periodStart,
            endDate: periodEnd,
            status: ['C'], // Only confirmed transactions
            page: 1,
            limit: 10000,
            loadSubtotals: true, // Reports need subtotals for aggregation
          });

          if (transactionResult && transactionResult.transactions) {
            allTransactionResults.push(transactionResult);
            const periodTransactionCount = transactionResult.transactions.items?.length || 0;
            totalFetchedTransactions += periodTransactionCount;

            HandlerUtils.logActivity('DailyReportDataService',
              `Period ${i + 1}/6 completed: ${periodTransactionCount} transactions fetched`);
          }

          // Add a small delay between requests to avoid overwhelming the API
          if (i < timePeriods.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }

        } catch (periodError) {
          HandlerUtils.logError('DailyReportDataService',
            `Failed to fetch period ${i + 1}/6 (${period.start}:00-${period.end}:00): ${periodError instanceof Error ? periodError.message : 'Unknown error'}`);
          // Continue with other periods even if one fails
        }
      }

      HandlerUtils.logActivity('DailyReportDataService',
        `Completed fetching all periods. Total transactions: ${totalFetchedTransactions} from ${allTransactionResults.length} successful periods`);

      if (allTransactionResults.length === 0) {
        HandlerUtils.logActivity('DailyReportDataService', 'No transaction data retrieved from any period');
        return null;
      }

      // Merge all transaction results
      const mergedResult = this.mergeTransactionResults(allTransactionResults);

      HandlerUtils.logActivity('DailyReportDataService',
        `Transaction data merged successfully: ${mergedResult.transactions.items.length} total transactions`);

      // Log transaction filtering details
      if (newUserCustomerIds && newUserCustomerIds.length > 0) {
        HandlerUtils.logActivity('DailyReportDataService',
          `Processing transactions with new user filtering: ${newUserCustomerIds.length} new user customer IDs provided`);
      } else {
        HandlerUtils.logActivity('DailyReportDataService',
          `Processing transactions without new user filtering: no customer IDs provided`);
      }

      // Process the merged transaction data to create summary with payment method breakdown
      const transactionSummary = DailyReportDataProcessor.aggregateTransactionData(mergedResult, newUserCustomerIds, totalCustomerBalance);

      HandlerUtils.logActivity('DailyReportDataService',
        `Transaction summary processed: ${transactionSummary.paymentMethodBreakdown.length} payment methods found`);

      // Log new user data results if available
      if (transactionSummary.newUserData) {
        HandlerUtils.logActivity('DailyReportDataService',
          `New user data in summary: ${transactionSummary.newUserData.newUserCount} users, ${transactionSummary.newUserData.newUserDepositAmount} total deposits`);
      } else {
        HandlerUtils.logActivity('DailyReportDataService',
          `No new user data in transaction summary`);
      }

      return transactionSummary;

    } catch (error) {
      HandlerUtils.logError('DailyReportDataService', `Failed to fetch transaction data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Merge multiple transaction results from different time periods
   *
   * @param transactionResults - Array of transaction results from different periods
   * @returns Merged transaction result
   */
  private static mergeTransactionResults(transactionResults: any[]): any {
    if (transactionResults.length === 0) {
      return {
        transactions: { items: [] },
        subtotals: {
          totalConfirmedDeposits: 0,
          totalConfirmedWithdrawals: 0,
        }
      };
    }

    // Merge all transaction items
    const allTransactions: any[] = [];
    let totalConfirmedDeposits = 0;
    let totalConfirmedWithdrawals = 0;

    transactionResults.forEach(result => {
      if (result.transactions && result.transactions.items) {
        allTransactions.push(...result.transactions.items);
      }

      if (result.subtotals) {
        totalConfirmedDeposits += result.subtotals.totalConfirmedDeposits || 0;
        totalConfirmedWithdrawals += result.subtotals.totalConfirmedWithdrawals || 0;
      }
    });

    // Return merged result in the same format as a single API call
    return {
      transactions: {
        items: allTransactions,
        total: allTransactions.length,
      },
      subtotals: {
        totalConfirmedDeposits,
        totalConfirmedWithdrawals,
      }
    };
  }

  /**
   * Fetch new user data for a specific time period using cursor-based approach
   *
   * IMPORTANT: This method implements the CORRECTED cursor pattern that matches the working
   * hourly handler implementation. The key insight is that both the cursor request and the
   * actual request MUST use identical date ranges for the cursor to work properly.
   *
   * CORRECTED PATTERN:
   * 1. Calculate full day range (00:00 to 23:59) from startDate
   * 2. Use full day range for BOTH cursor request and actual request
   * 3. Filter users manually by registration time to match requested period
   *
   * WHY THIS PATTERN IS REQUIRED:
   * - PG Dagur API cursor system ties the cursor to the original query parameters
   * - When parameters change between requests, the cursor becomes invalid
   * - This leads to incomplete or incorrect data retrieval
   *
   * PREVIOUS BROKEN PATTERN (DO NOT USE):
   * - Cursor request: 1-hour range (startDate to startDate + 1 hour)
   * - Actual request: different range (startDate to endDate)
   * - Result: Cursor mismatch causing data retrieval failures
   *
   * PERFORMANCE NOTES:
   * - Single-pass filtering and calculation for optimal performance
   * - Early exit conditions to skip invalid users
   * - Minimal memory usage with on-the-fly processing
   *
   * @param startDate - Start of the time period
   * @param endDate - End of the time period
   * @returns New user data or null if failed
   */
  static async fetchNewUserData(startDate: Date, endDate: Date): Promise<NewUserData | null> {
    try {
      HandlerUtils.logActivity('DailyReportDataService',
        `Fetching new user data from ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Step 1: Get preload data for the customer report request (ONLY CURSORLESS REQUEST)
      let viewState = '';
      let javax: any = null;

      const preloadRequest = new CustomerReportListPreloadRequest();
      const preloadResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!preloadResult.success) {
        throw new Error(`Failed to preload customer report: ${preloadResult.message || 'Unknown error'}`);
      }

      viewState = preloadResult.viewState || '';
      javax = preloadResult.data;

      // Step 2: Make initial request with full day range to get cursor (ONLY CURSORLESS REQUEST)
      // CRITICAL: Calculate full day range - use the day of startDate from 00:00 to 23:59
      // This MUST match the range used in the actual request for cursor to work properly
      const dayStart = new Date(startDate);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(startDate);
      dayEnd.setHours(23, 59, 59, 999);

      HandlerUtils.logActivity('DailyReportDataService',
        `Using full day range for cursor: ${dayStart.toISOString()} to ${dayEnd.toISOString()}`);

      const cursorRequest = new CustomerReportListRequest({
        javax,
        registrationStartDate: dayStart,
        registrationEndDate: dayEnd,
        cursor: viewState,
        limit: 10000, // Large limit to get cursor, matching hourly handler pattern
      });

      const cursorResult = await pgDagurAdminHttpClient.makeRequest(cursorRequest, viewState);

      if (!cursorResult.success) {
        throw new Error(`Failed to get cursor: ${cursorResult.message || 'Unknown error'}`);
      }

      // Check if cursor is successfully obtained (must be a string value)
      let cursor = null;
      if (cursorResult.data && typeof cursorResult.data.cursor === 'string' && cursorResult.data.cursor.length > 0) {
        cursor = cursorResult.data.cursor;
      } else {
        // Check for cursor in other possible locations using any type for flexibility
        const response = cursorResult as any;
        if (response.cursor && typeof response.cursor === 'string' && response.cursor.length > 0) {
          cursor = response.cursor;
        } else if (response.data && response.data.cursor && typeof response.data.cursor === 'string' && response.data.cursor.length > 0) {
          cursor = response.data.cursor;
        }
      }

      if (!cursor) {
        throw new Error(`Failed to obtain valid cursor from response. Response structure: ${JSON.stringify(cursorResult)}`);
      }

      // Step 3: Use cursor to fetch all users for the full day (CURSOR-BASED REQUEST)
      // CRITICAL: Must use IDENTICAL date range as cursor request for cursor to work properly
      // This is the core fix - both requests use dayStart/dayEnd instead of startDate/endDate
      HandlerUtils.logActivity('DailyReportDataService',
        `Fetching all users for the day using cursor, will filter manually later`);

      const actualRequest = new CustomerReportListWithCursorRequest({
        javax,
        registrationStartDate: dayStart,
        registrationEndDate: dayEnd,
        cursor,
        limit: 10000, // Large limit to get all users
      });

      const actualResult = await pgDagurAdminHttpClient.makeRequest(actualRequest, cursor);

      if (!actualResult.success) {
        throw new Error(`Failed to fetch new users: ${actualResult.message || 'Unknown error'}`);
      }

      // Step 4: Filter users manually by registration time to match requested period
      // Since we fetched the full day, we now filter to the specific time period requested
      // This manual filtering approach ensures we get accurate results while maintaining cursor validity
      const allUsers = actualResult.data.items || [];

      HandlerUtils.logActivity('DailyReportDataService',
        `Retrieved ${allUsers.length} users for the full day, now filtering by registration time`);

      // Debug: Log sample user data structure
      if (allUsers.length > 0) {
        HandlerUtils.logActivity('DailyReportDataService',
          `Sample user data structure: ${JSON.stringify(allUsers[0])}`);
      }

      // Filter users and calculate data in a single pass for optimal performance
      let newUserCount = 0;
      let newUserDepositAmount = 0;
      let usersWithDeposits = 0;
      const newUserCustomerIds: number[] = [];
      const sampleUsers: any[] = [];

      for (const user of allUsers) {
        // Skip users without registration date
        if (!user.registerDate) continue;

        // Parse registerDate format: "11.07.2025 10:16:12"
        const registerDate = this.parseRegisterDate(user.registerDate);
        if (!registerDate) continue;

        // Check if user is within the requested time period
        if (registerDate >= startDate && registerDate < endDate) {
          newUserCount++;

          // Collect customer ID for transaction filtering
          if (user.customerId) {
            newUserCustomerIds.push(user.customerId);
          }

          const depositAmount = user.firstDepositAmount || 0;
          newUserDepositAmount += depositAmount;

          if (depositAmount > 0) {
            usersWithDeposits++;
          }

          // Collect first few users for debugging
          if (sampleUsers.length < 3) {
            sampleUsers.push({
              customerId: user.customerId,
              registerDate: user.registerDate,
              firstDepositAmount: depositAmount,
              username: user.username || 'N/A'
            });
          }
        }
      }

      HandlerUtils.logActivity('DailyReportDataService',
        `Filtered to ${newUserCount} users within requested time period`);

      // Log sample users for debugging
      sampleUsers.forEach((user, index) => {
        HandlerUtils.logActivity('DailyReportDataService',
          `Filtered User ${index + 1}: customerId=${user.customerId}, registerDate=${user.registerDate}, firstDepositAmount=${user.firstDepositAmount}, username=${user.username}`);
      });

      HandlerUtils.logActivity('DailyReportDataService',
        `Final results: ${newUserCount} new users, ${usersWithDeposits} with deposits, total deposits: ${newUserDepositAmount}, collected ${newUserCustomerIds.length} customer IDs`);

      return {
        newUserCount,
        newUserDepositAmount,
        newUserCustomerIds,
      };

    } catch (error) {
      HandlerUtils.logError('DailyReportDataService', `Failed to fetch new user data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Parse register date string in format "11.07.2025 10:16:12" to Date object
   * Copied from HourlyReportDataService to maintain consistency
   *
   * @param registerDateString - Date string in DD.MM.YYYY HH:mm:ss format
   * @returns Parsed Date object or null if parsing fails
   */
  private static parseRegisterDate(registerDateString: string): Date | null {
    try {
      // Format: "11.07.2025 10:16:12"
      const [datePart, timePart] = registerDateString.split(' ');
      if (!datePart || !timePart) return null;

      const [day, month, year] = datePart.split('.');
      const [hours, minutes, seconds] = timePart.split(':');

      if (!day || !month || !year || !hours || !minutes || !seconds) return null;

      // Create Date object (month is 0-indexed in JavaScript)
      const date = new Date(
        parseInt(year, 10),
        parseInt(month, 10) - 1,
        parseInt(day, 10),
        parseInt(hours, 10),
        parseInt(minutes, 10),
        parseInt(seconds, 10)
      );

      // Validate the date
      if (isNaN(date.getTime())) return null;

      return date;
    } catch (error) {
      HandlerUtils.logError('DailyReportDataService', `Failed to parse register date: ${registerDateString}`);
      return null;
    }
  }
}
