/**
 * New User Data Interface
 */
export interface NewUserData {
  newUserCount: number;
  newUserDepositAmount: number;
  newUserCustomerIds: number[];
}

/**
 * Transaction Summary Interface for Daily Reports
 */
export interface DailyTransactionSummary {
  totalTransactions: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalDepositAmount: number;
  totalWithdrawalAmount: number;
  netCashFlow: number;
  uniqueCustomers: number;
  totalCustomerBalance: number;
  paymentMethodBreakdown: Array<{
    method: string;
    type: 'deposit' | 'withdrawal';
    count: number;
    amount: number;
    percentage: number;
  }>;
  newUserData?: NewUserData;
}

/**
 * Activity Report Item Interface
 *
 * Defines the structure of individual activity report items from Dagur API
 */
export interface ActivityReportItem {
  transactionDate: string;
  totalDepositAmount: number;
  totalWithdrawAmount: number;
  adjustmentTopUpAmount: number;
  numberOfSportBets: number;
  numberOfVirtualBets: number;
  totalSportPlayAmount: number;
  sportBonusAmount: number;
  sportSpentBonusAmount: number;
  casinoBonusAmount: number;
  casinoSpentBonusAmount: number;
  totalBonusAmount: number;
  totalSpentBonusAmount: number;
  totalDiscountAmount: number;
  totalSportOpenAmount: number;
  totalGivenFreebetAmount: number;
  sportGGR: number;
  totalAmountTransferredFromCasino: number;
  totalAmountTransferredToCasino: number;
  totalAmountTransferredFromKlasPoker: number;
  totalAmountTransferredToKlasPoker: number;
  numberOfCasinoBets: number;
  totalCasinoPlayAmount: number;
  casinoGGR: number;
  bonusSeeker: string;
}

/**
 * Daily Report Response Interface
 *
 * Defines the expected structure of daily report responses
 */
export interface DailyReportResponse {
  success: boolean;
  reportId?: string;
  generatedAt?: string;
  data?: {
    total: number;
    items: ActivityReportItem[];
    transactionSummary?: DailyTransactionSummary;
  };
  message?: string;
  error?: string;
}
