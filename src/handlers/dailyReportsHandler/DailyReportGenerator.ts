import { HandlerUtils } from '../index';
import { DailyReportRequest } from '../../validators/dailyReportsValidator';
import { DailyReportResponse } from './types';
import { DailyReportDataService } from './DailyReportDataService';
import { DailyReportDataProcessor } from './DailyReportDataProcessor';

/**
 * Service class for generating daily reports
 */
export class DailyReportGenerator {
  /**
   * Generate daily activity report from PG Dagur API
   *
   * @param data - Validated daily report request data
   * @returns Report generation result
   */
  static async generateDailyReport(data: DailyReportRequest): Promise<{
    success: boolean;
    data?: DailyReportResponse;
    error?: string;
  }> {
    try {
      HandlerUtils.logActivity('DailyReportGenerator', `Generating daily activity report for ${data.startDate} to ${data.endDate}`);

      const reportId = `daily_activity_${data.startDate}_${data.endDate}_${Date.now()}`;

      // Step 1: Fetch activity report from Dagur API
      const activityData = await DailyReportDataService.fetchActivityReport(data.startDate, data.endDate);

      if (!activityData) {
        throw new Error('Failed to fetch activity report data');
      }

      // Step 2: Fetch new user data first to get customer IDs for transaction filtering
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      HandlerUtils.logActivity('DailyReportGenerator',
        `Fetching new user data for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      let newUserData = null;
      let newUserCustomerIds: number[] = [];

      try {
        newUserData = await DailyReportDataService.fetchNewUserData(startDate, endDate);

        if (newUserData && newUserData.newUserCustomerIds) {
          newUserCustomerIds = newUserData.newUserCustomerIds;
          HandlerUtils.logActivity('DailyReportGenerator',
            `New user data fetched successfully: ${newUserData.newUserCount} users, ${newUserCustomerIds.length} customer IDs`);
        } else {
          HandlerUtils.logActivity('DailyReportGenerator',
            `New user data fetch returned null or missing customer IDs - proceeding with empty array`);
        }
      } catch (error) {
        HandlerUtils.logError('DailyReportGenerator',
          `Failed to fetch new user data: ${error instanceof Error ? error.message : 'Unknown error'} - proceeding without new user filtering`);
      }

      // Step 3: Fetch total customer balance from balance API
      const balanceStartTime = Date.now();
      HandlerUtils.logActivity('DailyReportGenerator',
        `Fetching total customer balance for date: ${data.endDate}`);

      let totalCustomerBalance: number | undefined = undefined;
      try {
        const balanceResult = await DailyReportDataService.fetchDailyBalance(data.endDate);
        const balanceDuration = Date.now() - balanceStartTime;

        if (balanceResult.success && balanceResult.balance !== undefined) {
          totalCustomerBalance = balanceResult.balance;
          HandlerUtils.logActivity('DailyReportGenerator',
            `Successfully fetched customer balance in ${balanceDuration}ms: ${totalCustomerBalance}`);
        } else {
          HandlerUtils.logError('DailyReportGenerator',
            `Failed to fetch customer balance: ${balanceResult.error} - will use fallback calculation`);
          // Leave totalCustomerBalance as undefined to trigger fallback calculation in processor
        }
      } catch (error) {
        HandlerUtils.logError('DailyReportGenerator',
          `Balance fetch error: ${error instanceof Error ? error.message : 'Unknown error'} - will use fallback calculation`);
        // Leave totalCustomerBalance as undefined to trigger fallback calculation in processor
      }

      // Step 4: Fetch transaction data with new user customer IDs and balance for accurate calculation
      HandlerUtils.logActivity('DailyReportGenerator',
        `Fetching transaction data with ${newUserCustomerIds.length} new user customer IDs and ${totalCustomerBalance !== undefined ? 'API-fetched' : 'fallback'} balance`);

      let transactionSummary = null;
      try {
        transactionSummary = await DailyReportDataService.fetchDailyTransactionData(data.startDate, data.endDate, newUserCustomerIds, totalCustomerBalance);

        if (!transactionSummary) {
          HandlerUtils.logError('DailyReportGenerator',
            `Transaction data fetch returned null - report will have limited data`);
        }
      } catch (error) {
        HandlerUtils.logError('DailyReportGenerator',
          `Failed to fetch transaction data: ${error instanceof Error ? error.message : 'Unknown error'} - proceeding without transaction summary`);
      }

      // Step 5: Validate and log the final new user data
      if (transactionSummary && transactionSummary.newUserData) {
        HandlerUtils.logActivity('DailyReportGenerator',
          `Final new user data: ${transactionSummary.newUserData.newUserCount} users, ${transactionSummary.newUserData.newUserDepositAmount} total deposits from transactions`);
      } else if (newUserData) {
        // Fallback: If transaction summary doesn't have new user data but we have it from the original fetch
        HandlerUtils.logActivity('DailyReportGenerator',
          `Using fallback new user data: ${newUserData.newUserCount} users, ${newUserData.newUserDepositAmount} deposits (from firstDepositAmount)`);

        if (transactionSummary) {
          transactionSummary.newUserData = newUserData;
        }
      } else {
        HandlerUtils.logActivity('DailyReportGenerator',
          `No new user data available from any source`);
      }

      // Debug: Log the final transaction summary structure
      HandlerUtils.logActivity('DailyReportGenerator',
        `Final transactionSummary structure: ${JSON.stringify({
          hasTransactionSummary: !!transactionSummary,
          hasNewUserData: !!(transactionSummary?.newUserData),
          newUserDataContent: transactionSummary?.newUserData
        })}`);

      // Final pipeline verification log
      if (transactionSummary?.newUserData) {
        HandlerUtils.logActivity('DailyReportGenerator',
          `✅ NEW USER DEPOSIT CALCULATION PIPELINE COMPLETE:
          - New users found: ${transactionSummary.newUserData.newUserCount}
          - Customer IDs collected: ${transactionSummary.newUserData.newUserCustomerIds?.length || 0}
          - Total deposit amount (from transactions): ${transactionSummary.newUserData.newUserDepositAmount}
          - Calculation method: Transaction-based filtering
          - Pipeline status: SUCCESS`);
      } else {
        HandlerUtils.logActivity('DailyReportGenerator',
          `⚠️ NEW USER DEPOSIT CALCULATION PIPELINE INCOMPLETE:
          - New user data: ${newUserData ? 'fetched' : 'not fetched'}
          - Transaction summary: ${transactionSummary ? 'available' : 'not available'}
          - Pipeline status: PARTIAL/FAILED`);
      }

      const reportResponse: DailyReportResponse = {
        success: true,
        reportId,
        generatedAt: new Date().toISOString(),
        data: {
          total: activityData.total || 0,
          items: activityData.items || [],
          transactionSummary,
        },
        message: `Günlük aktivite raporu başarıyla oluşturuldu: ${data.startDate} - ${data.endDate}`,
      };

      HandlerUtils.logActivity('DailyReportGenerator', `Report generated successfully: ${reportId}`);

      return {
        success: true,
        data: reportResponse,
      };

    } catch (error) {
      HandlerUtils.logError('DailyReportGenerator', `Report generation error: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown report generation error',
      };
    }
  }
}
