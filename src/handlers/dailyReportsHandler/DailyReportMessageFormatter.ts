import { DailyReportResponse } from './types';
import { DailyReportRequest } from '../../validators/dailyReportsValidator';

/**
 * Service class for formatting daily report Slack messages
 */
export class DailyReportMessageFormatter {
  /**
   * Format Slack message for daily report
   *
   * @param requestData - Original request data
   * @param responseData - Report response data
   * @returns Formatted Slack message
   */
  static formatSlackMessage(requestData: DailyReportRequest, responseData: DailyReportResponse): string {
    // Debug: Log the response data structure
    console.log('DailyReportMessageFormatter - responseData structure:', {
      hasData: !!responseData.data,
      hasTransactionSummary: !!(responseData.data?.transactionSummary),
      hasNewUserData: !!(responseData.data?.transactionSummary?.newUserData),
      newUserDataContent: responseData.data?.transactionSummary?.newUserData
    });

    // Use the actual transaction date from the response data if available
    let displayDate = '';
    if (responseData.data?.items && responseData.data.items.length > 0) {
      const transactionDate = responseData.data.items[0].transactionDate;
      // Handle different date formats (DD.MM.YYYY or ISO format)
      if (transactionDate.includes('.')) {
        // Format: "11.07.2025" - convert to prettier format
        displayDate = this.formatDailyDate(transactionDate);
      } else {
        // ISO format - convert to prettier format
        displayDate = this.formatDate(new Date(transactionDate));
      }
    } else {
      // Fallback to request dates if no data available
      const startDate = this.formatDate(new Date(requestData.startDate));
      const endDate = this.formatDate(new Date(requestData.endDate));
      displayDate = startDate === endDate ? startDate : `${startDate} - ${endDate}`;
    }

    // Build the comprehensive daily report message
    let message = `📅 Tarih: ${displayDate} (00:00 – 23:59)\n\n---\n\n`;

    // Activity Data Section
    if (responseData.data?.items && responseData.data.items.length > 0) {
      const item = responseData.data.items[0]; // Get the first (and likely only) item
      const transactionSummary = responseData.data.transactionSummary;

      // 💰 Mali Özet
      const netCashFlow = transactionSummary?.netCashFlow || (item.totalDepositAmount - item.totalWithdrawAmount);
      message += `*💰 Mali Özet*\n\n`;
      message += `• Toplam Yatırım: ${this.formatCurrency(transactionSummary?.totalDepositAmount || item.totalDepositAmount)}\n`;
      message += `• Toplam Çekim: ${this.formatCurrency(transactionSummary?.totalWithdrawalAmount || item.totalWithdrawAmount)}\n`;
      message += `• Düzeltme Yüklemeleri: ${this.formatCurrency(item.adjustmentTopUpAmount)}\n`;
      message += `• Toplam İndirim: ${this.formatCurrency(item.totalDiscountAmount)}\n`;
      message += `• Kar / Zarar (Net Kasa Farkı): ${this.formatCurrency(netCashFlow)}\n\n---\n\n`;

      // 🧮 GGR (Brüt Kâr)
      message += `*🧮 GGR (Brüt Kâr)*\n\n`;
      message += `• Spor GGR: ${this.formatCurrency(item.sportGGR)}\n`;
      message += `• Casino GGR: ${this.formatCurrency(item.casinoGGR)}\n`;
      message += `• Spor Açık Tutar: ${this.formatCurrency(item.totalSportOpenAmount)}\n\n---\n\n`;

      // 🎰 Oyun Aktivitesi
      message += `*🎰 Oyun Aktivitesi*\n\n`;
      message += `• Spor Bahis Sayısı: ${item.numberOfSportBets.toLocaleString()}\n`;
      message += `• Sanal Bahis Sayısı: ${item.numberOfVirtualBets.toLocaleString()}\n`;
      message += `• Casino Bahis Sayısı: ${item.numberOfCasinoBets.toLocaleString()}\n`;
      message += `• Spor Bahis Tutarı: ${this.formatCurrency(item.totalSportPlayAmount)}\n`;
      message += `• Casino Bahis Tutarı: ${this.formatCurrency(item.totalCasinoPlayAmount)}\n\n---\n\n`;

      // 🎁 Bonus Bilgileri
      message += `*🎁 Bonus Bilgileri*\n\n`;
      message += `• Verilen Toplam Bonus: ${this.formatCurrency(item.totalBonusAmount)}\n`;
      message += `• Harcanan Toplam Bonus: ${this.formatCurrency(item.totalSpentBonusAmount)}\n`;
      message += `• Verilen Spor Bonusu: ${this.formatCurrency(item.sportBonusAmount)}\n`;
      message += `• Harcanan Spor Bonusu: ${this.formatCurrency(item.sportSpentBonusAmount)}\n`;
      message += `• Verilen Casino Bonusu: ${this.formatCurrency(item.casinoBonusAmount)}\n`;
      message += `• Harcanan Casino Bonusu: ${this.formatCurrency(item.casinoSpentBonusAmount)}\n`;
      message += `• Verilen Toplam Freebet: ${this.formatCurrency(item.totalGivenFreebetAmount)}\n`;
      message += `• Bonus Avcısı: ${item.bonusSeeker || 'Yok'}\n\n---\n\n`;

      // 👥 Yeni Üye Verileri - now with real data
      message += `*👥 Yeni Üye Verileri*\n\n`;

      // Debug logging for new user data
      console.log('DailyReportMessageFormatter - transactionSummary:', transactionSummary ? 'exists' : 'null');
      console.log('DailyReportMessageFormatter - newUserData:', transactionSummary?.newUserData ? JSON.stringify(transactionSummary.newUserData) : 'null/undefined');

      // Additional logging for verification
      if (transactionSummary?.newUserData) {
        console.log('DailyReportMessageFormatter - New user deposit calculation verification:', {
          newUserCount: transactionSummary.newUserData.newUserCount,
          newUserDepositAmount: transactionSummary.newUserData.newUserDepositAmount,
          customerIdsCount: transactionSummary.newUserData.newUserCustomerIds?.length || 0,
          calculationMethod: 'transaction-based'
        });
      }

      // Validate and display new user data
      const newUserData = transactionSummary?.newUserData;
      const hasValidNewUserData = newUserData &&
        typeof newUserData.newUserCount === 'number' &&
        typeof newUserData.newUserDepositAmount === 'number';

      if (hasValidNewUserData) {
        message += `• Yeni Üye Sayısı: ${newUserData.newUserCount.toLocaleString()}\n`;
        message += `• Yeni Üye Yatırımı: ${this.formatCurrency(newUserData.newUserDepositAmount)}\n\n---\n\n`;
      } else {
        message += `• Yeni Üye Sayısı: –\n`;
        message += `• Yeni Üye Yatırımı: –\n\n---\n\n`;

        // Log why it's showing as dash
        if (transactionSummary) {
          console.log('DailyReportMessageFormatter - Showing dash because:', {
            hasTransactionSummary: true,
            hasNewUserData: !!newUserData,
            newUserDataType: typeof newUserData,
            hasValidCount: newUserData && typeof newUserData.newUserCount === 'number',
            hasValidAmount: newUserData && typeof newUserData.newUserDepositAmount === 'number'
          });
        } else {
          console.log('DailyReportMessageFormatter - Showing dash because transactionSummary is null/undefined');
        }
      }

      message += `*💼 Toplam Müşteri Bakiyesi*\n\n`;
      if (transactionSummary?.totalCustomerBalance !== undefined) {
        message += `• ${this.formatCurrency(transactionSummary.totalCustomerBalance)}\n\n---\n\n`;
      } else {
        message += `• –\n\n---\n\n`;
      }

      // 🏦 Ödeme Yöntemi Dağılımı
      message += `*🏦 Ödeme Yöntemi Dağılımı*\n\n`;

      if (transactionSummary && transactionSummary.paymentMethodBreakdown && transactionSummary.paymentMethodBreakdown.length > 0) {
        // Separate deposits and withdrawals
        const deposits = transactionSummary.paymentMethodBreakdown.filter(pm => pm.type === 'deposit');
        const withdrawals = transactionSummary.paymentMethodBreakdown.filter(pm => pm.type === 'withdrawal');

        if (deposits.length > 0) {
          message += `*💳 Yatırım İşlemleri:*\n\n`;
          deposits.slice(0, 5).forEach(pm => {
            message += `• ${pm.method}: ${pm.count} işlem - ${this.formatCurrency(pm.amount)} (%${pm.percentage.toFixed(1)})\n`;
          });
          message += `\n`;
        }

        if (withdrawals.length > 0) {
          message += `*🏧 Çekim İşlemleri:*\n\n`;
          withdrawals.slice(0, 5).forEach(pm => {
            message += `• ${pm.method}: ${pm.count} işlem - ${this.formatCurrency(pm.amount)} (%${pm.percentage.toFixed(1)})\n`;
          });
          message += `\n`;
        }
      } else {
        message += `*💳 Yatırım İşlemleri:*\n\n`;
        message += `• Veri mevcut değil\n\n`;
        message += `*🏧 Çekim İşlemleri:*\n\n`;
        message += `• Veri mevcut değil\n\n`;
      }

      message += `> 🛈 Not: Günlük ödeme yöntemi detayları rapora dahil edildi.\n\n---`;
    } else {
      message += `*📊 Rapor Durumu*\n\n`;
      message += `• Durum: Bu tarih aralığı için aktivite verisi mevcut değil\n`;
      message += `• Tarih Aralığı: ${displayDate}\n\n---`;
    }

    return message;
  }

  /**
   * Format currency values for display with Turkish Lira symbol
   *
   * @param amount - Numeric amount to format
   * @returns Formatted currency string with ₺ symbol
   */
  private static formatCurrency(amount: number): string {
    if (amount === 0) return '₺0.00';
    const formatted = amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
    return `₺${formatted}`;
  }

  /**
   * Format date for display
   *
   * @param date - Date object
   * @returns Formatted date string in DD.MM.YYYY format
   */
  private static formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  }

  /**
   * Format daily date from DD.MM.YYYY format to prettier format
   * Example: "11.07.2025" -> "July 11th (GMT+3)"
   *
   * @param dateString - Date string in DD.MM.YYYY format
   * @returns Formatted date string
   */
  private static formatDailyDate(dateString: string): string {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const getOrdinalSuffix = (day: number): string => {
      if (day >= 11 && day <= 13) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };

    try {
      // Parse DD.MM.YYYY format
      const [day, month, year] = dateString.split('.').map(Number);
      const monthName = monthNames[month - 1]; // month is 1-based
      const ordinalSuffix = getOrdinalSuffix(day);

      return `${monthName} ${day}${ordinalSuffix} (GMT+3)`;
    } catch (error) {
      // Fallback to original string if parsing fails
      return dateString;
    }
  }
}
