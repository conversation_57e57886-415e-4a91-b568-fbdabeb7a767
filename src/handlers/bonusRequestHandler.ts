import { Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lerUtils } from './index';
import { BonusRequestValidator, BonusRequest } from '../validators/bonusRequestValidator';
import fetch from 'node-fetch';
import { fetchNexus, isMakrobetDomain } from '../utils/fetchNexus';

/**
 * Customer Data Interface
 *
 * Defines the expected structure of customer data from getCurrentCustomer endpoint
 */
interface CustomerData {
  success: boolean;
  data: {
    customerId: number;
    firstName: string;
    surname: string;
    username: string;
    phone: string;
    balance: number;
    lockedBalance: number;
    vip: boolean;
  };
}

/**
 * Bonus Request Response Interface
 *
 * Defines the expected structure of bonus request responses
 */
interface BonusRequestResponse {
  err: boolean;
  message?: string;
  data?: any;
}

/**
 * Bonus Request Handler
 *
 * Handles bonus requests by:
 * 1. Proxying the request to the third-party endpoint (pn17.pfnow.net)
 * 2. If successful and not an error response, fetching customer data
 * 3. Sending a Slack notification with customer information
 * 4. Returning the original response from the endpoint
 */
export class Bonus<PERSON>equestHandler extends BaseHandler {
  private static readonly TARGET_URL = 'https://pn17.pfnow.net/api/promoTracker/demand';
  private static readonly CUSTOMER_ENDPOINT_PATH = '/odin/api/user/getCurrentCustomer';

  /**
   * Handle bonus request
   * 
   * @param req - Express request object
   * @param res - Express response object (not used for response, only for logging)
   * @returns Handler result with Slack message or skip reason
   */
  static async handle(req: Request, res: Response): Promise<HandlerResult> {
    try {
      HandlerUtils.logActivity('BonusRequestHandler', 'Processing bonus request');

      // Extract validated bonus data
      const bonusData = BonusRequestValidator.extractBonusData(req);
      
      // Step 1: Proxy the request to the third-party endpoint
      const proxyResult = await this.proxyBonusRequest(bonusData);
      
      if (!proxyResult.success) {
        HandlerUtils.logError('BonusRequestHandler', `Proxy request failed: ${proxyResult.error}`);
        return this.error(`Failed to proxy bonus request: ${proxyResult.error}`);
      }

      // Get the original response data and status (always return this to client)
      const originalResponse = proxyResult.data;
      const originalStatusCode = proxyResult.statusCode || 200;

      // Step 2: Check if we should skip Slack notification
      const shouldSkip = this.shouldSkipNotification(proxyResult.data!);
      
      if (shouldSkip.skip) {
        HandlerUtils.logActivity('BonusRequestHandler', `Skipping notification: ${shouldSkip.reason}`);
        return this.skip(shouldSkip.reason || 'Unknown reason', originalResponse, originalStatusCode);
      }

      // Step 3: Fetch customer data if token is available
      let customerData: CustomerData | null = null;

      console.log('🔍 Bonus data received:', JSON.stringify(bonusData, null, 2));
      console.log('🔍 Token value:', bonusData.token);
      console.log('🔍 Token type:', typeof bonusData.token);
      console.log('🔍 Token length:', bonusData.token ? bonusData.token.length : 'N/A');

      if (bonusData.token && bonusData.token.trim() !== '') {
        HandlerUtils.logActivity('BonusRequestHandler', `Token available (${bonusData.token.length} chars), fetching customer data...`);
        const customerResult = await this.fetchCustomerData(bonusData.token, req);
        if (customerResult.success) {
          customerData = customerResult.data!;
          HandlerUtils.logActivity('BonusRequestHandler', `Customer data fetched successfully for user: ${customerData.data.username}`);
        } else {
          HandlerUtils.logError('BonusRequestHandler', `Failed to fetch customer data: ${customerResult.error}`);
          // Continue without customer data - will show basic message
        }
      } else {
        HandlerUtils.logActivity('BonusRequestHandler', `No valid token provided (token: ${bonusData.token}), skipping customer data fetch`);
      }

      // Step 4: Format Slack message
      const message = this.formatSlackMessage(bonusData, customerData);

      HandlerUtils.logActivity('BonusRequestHandler', 'Successfully generated Slack message');
      return this.success(message, originalResponse, originalStatusCode);

    } catch (error) {
      HandlerUtils.logError('BonusRequestHandler', error instanceof Error ? error : 'Unknown error');
      return this.error(`Handler error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Proxy the bonus request to the third-party endpoint
   */
  private static async proxyBonusRequest(data: BonusRequest): Promise<{
    success: boolean;
    data?: BonusRequestResponse;
    error?: string;
    statusCode?: number;
  }> {
    HandlerUtils.logActivity('BonusRequestHandler', `Proxying request to ${this.TARGET_URL}`);

    try {
      // Create proxy data excluding metadata field
      const { metadata, ...proxyData } = data;

      console.log('📤 Proxy data (metadata excluded):', JSON.stringify(proxyData, null, 2));

      // Create agent that ignores SSL certificate errors (like call service handler)
      const https = require('https');
      const agent = new https.Agent({
        rejectUnauthorized: false
      });

      // Use node-fetch directly like the call service handler
      const response = await fetch(this.TARGET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(proxyData),
        agent: agent, // Disable SSL verification
      });

      console.log(`📥 Response Status: ${response.status}`);

      // Parse response
      const responseData = await response.json();
      
      console.log('📥 Response data:', JSON.stringify(responseData, null, 2));

      return {
        success: true,
        data: responseData as BonusRequestResponse,
        statusCode: response.status,
      };

    } catch (error) {
      console.error('❌ Proxy request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Check if we should skip Slack notification based on response
   */
  private static shouldSkipNotification(response: BonusRequestResponse): {
    skip: boolean;
    reason?: string;
  } {
    // Skip if response has error flag set to true
    if (response.err === true) {
      const message = response.message || 'unknown error';
      return {
        skip: true,
        reason: `Bonus request returned error: ${message}`,
      };
    }

    return { skip: false };
  }

  /**
   * Fetch customer data from internal endpoint
   */
  private static async fetchCustomerData(token: string, req: Request): Promise<{
    success: boolean;
    data?: CustomerData;
    error?: string;
  }> {
    const cleanToken = HandlerUtils.cleanToken(token);

    HandlerUtils.logActivity('BonusRequestHandler', `Fetching customer data for token: ${cleanToken}`);

    // Get origin from request headers and build URLs
    const origin = req.headers.origin || 'https://makrobet683.com';
    const refererUrl = `${origin}/en`;
    const customerEndpointUrl = `${origin}${this.CUSTOMER_ENDPOINT_PATH}`;

    HandlerUtils.logActivity('BonusRequestHandler', `Using referer URL: ${refererUrl}`);
    HandlerUtils.logActivity('BonusRequestHandler', `Using customer endpoint: ${customerEndpointUrl}`);

    try {
      let customerResponse: any;

      // Check if this is a makrobet domain and use appropriate fetch method
      if (isMakrobetDomain(customerEndpointUrl)) {
        HandlerUtils.logActivity('BonusRequestHandler', 'Using fetchNexus for makrobet domain');

        // Use fetchNexus for makrobet domains (it handles NEXUS anti-DDOS protection)
        customerResponse = await fetchNexus(customerEndpointUrl, {
          method: 'GET',
          headers: {
            's7oryo9stv': cleanToken,
            'referer': refererUrl,
            // fetchNexus already sets User-Agent, but we can override if needed
          },
        });
      } else {
        HandlerUtils.logActivity('BonusRequestHandler', 'Using regular fetch for non-makrobet domain');

        // Create agent that ignores SSL certificate errors
        const https = require('https');
        const agent = new https.Agent({
          rejectUnauthorized: false
        });

        // Use node-fetch to call customer endpoint
        customerResponse = await fetch(customerEndpointUrl, {
          method: 'GET',
          headers: {
            's7oryo9stv': cleanToken,
            'referer': refererUrl,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          },
          agent: agent,
        });
      }

      console.log(`📊 Customer endpoint response status: ${customerResponse.status}`);

      if (!customerResponse.ok) {
        const errorText = await customerResponse.text();
        console.log('⚠️ Customer endpoint error:', errorText);
        return {
          success: false,
          error: `Customer endpoint error: ${customerResponse.status} ${customerResponse.statusText}`,
        };
      }

      const customerData = await customerResponse.json();

      console.log('👤 Customer data retrieved:', JSON.stringify(customerData, null, 2));

      // Validate customer data structure
      const isValidStructure = HandlerUtils.validateResponseStructure(customerData, [
        'success',
        'data.customerId',
        'data.firstName',
        'data.surname',
        'data.username',
        'data.phone',
        'data.balance',
        'data.lockedBalance',
        'data.vip'
      ]);

      if (!isValidStructure) {
        return {
          success: false,
          error: 'Invalid customer data structure received',
        };
      }

      return {
        success: true,
        data: customerData as CustomerData,
      };

    } catch (error) {
      console.error('❌ Failed to fetch customer data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Format Slack message for bonus request with customer information
   */
  private static formatSlackMessage(bonusData: BonusRequest, customerData: CustomerData | null): string {
    // Use bonus_name from metadata if available, otherwise fall back to bonus ID
    const bonusDisplay = bonusData.metadata?.bonus_name || `Bonus ID ${bonusData.id}`;
    const title = this.bold(`🎁 Bonus Talebi - ${bonusDisplay}`);

    if (!customerData) {
      // Show structured message even without customer data
      const sections = [
        {
          title: this.bold('📋 Talep Bilgileri:'),
          content: this.formatKeyValueList([
            { key: 'Bonus', value: bonusDisplay },
            { key: 'Kullanıcı Kodu', value: bonusData.code },
            { key: 'Durum', value: 'Müşteri verisi mevcut değil' },
          ]),
        },
      ];
      return this.formatSectionedMessage(title, sections);
    }

    const customer = customerData.data;

    const sections = [
      {
        title: this.bold('👤 Müşteri Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'İsim', value: `${customer.firstName} ${customer.surname}` },
          { key: 'Kullanıcı Adı', value: customer.username },
          { key: 'Kullanıcı ID', value: customer.customerId },
          { key: 'Kod', value: bonusData.code },
        ]),
      },
      {
        title: this.bold('💰 Bakiye Bilgileri:'),
        content: this.formatKeyValueList([
          { key: 'Bakiye', value: this.formatCurrency(customer.balance) },
          { key: 'Kilitli Bakiye', value: this.formatCurrency(customer.lockedBalance) },
          { key: 'VIP', value: this.formatBoolean(customer.vip) },
        ]),
      },
    ];

    return this.formatSectionedMessage(title, sections);
  }
}

/**
 * Exported handler function for use with registerSlackBot
 */
export const bonusRequestHandler = async (req: Request, res: Response): Promise<HandlerResult> => {
  return BonusRequestHandler.handle(req, res);
};
