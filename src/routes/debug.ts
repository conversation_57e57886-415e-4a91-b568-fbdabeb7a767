import { Router } from 'express';
import { DebugController } from '@/controllers/debug.controller';

const router = Router();

/**
 * Debug routes for EbetLab API exploration
 *
 * These routes are designed to proxy requests to the actual EbetLab API
 * for debugging and exploration purposes. The /debug prefix is stripped
 * and the request is forwarded to the real EbetLab endpoint.
 *
 * Usage:
 * POST /debug/api/operator/customers/index/1/20
 * -> Proxies to: https://service.ebetlab.com/api/operator/customers/index/1/20
 */

// Debug endpoint for testing bonus rule checker
router.post('/test-bonus-rule-checker', DebugController.testBonusRuleChecker);

// Use middleware approach to handle all POST requests to any path
// This avoids complex route patterns that cause issues with path-to-regexp
router.use((req, res, next) => {
  // Only handle POST requests
  if (req.method === 'POST') {
    return DebugController.proxyEbetLabRequest(req, res, next);
  }
  next();
});

export default router;
