import { Router } from 'express';

import { TestController } from '@/controllers/pg-ct/internal/test.controller';
import { BonusController } from '@/controllers/pg-ct/internal/bonus.controller';
import { FreespinBonusController } from '@/controllers/pg-ct/internal/freespinBonus.controller';

const router = Router();

router.get('/v1/internal/test', TestController.test);

router.get('/v1/internal/bonuses', BonusController.listBonuses);
router.get('/v1/internal/bonuses/:id', BonusController.getBonus);
router.post('/v1/internal/bonuses/:id/claims', FreespinBonusController.assignTrialBonus);

router.post('/v1/internal/freespin-bonuses', FreespinBonusController.createFreespinBonus);
router.post('/v1/internal/freespin-bonuses/:id/cancellations', FreespinBonusController.cancelFreespinBonus);
router.get('/v1/internal/freespin-bonuses/providers', FreespinBonusController.listProviders);
router.get('/v1/internal/freespin-bonuses/providers/:providerId/games', FreespinBonusController.listProviderGames);
router.get('/v1/internal/freespin-bonuses/providers/bet-amounts', FreespinBonusController.listProviderBetAmounts);

export default router;
