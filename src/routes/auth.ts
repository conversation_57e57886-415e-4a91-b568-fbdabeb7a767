import { Router, Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { LoginRequest, AuthData, LoginResponse } from '@/types/auth';
import { ValidationError } from '@/types/errors';
import { asyncHandler, validateRequiredFields } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';

const router = Router();

// POST /auth/login - Login endpoint
router.post(
  '/login',
  asyncHandler(async (req: Request, res: Response) => {
    const { username, password, otp, fingerprint = {} }: LoginRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['username', 'password', 'otp']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    try {
      const data = await ebetlabApiClient.login(username, password, otp);

      // Prepare response with both auth data and login result
      const response: ApiResponse<LoginResponse> = {
        success: true,
        message: 'Login successful',
        data: data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Login failed:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (
          error.message.includes('Failed to login:') ||
          error.message.includes('Failed to init cf:') ||
          error.message.includes('Failed to get signed cookie:')
        ) {
          throw new ValidationError(`Authentication failed: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  }),
);

// POST /auth/refresh-auth-data - Refresh authentication data
router.post(
  '/refresh-auth-data',
  asyncHandler(async (_req: Request, res: Response) => {
    try {
      console.log('🔄 Refreshing authentication data...');

      // Use the static methods from authUtils to get fresh auth data
      const { cfInit, calculateXFingerprint, getSignedChallenge } = await import('@/utils/authUtils');

      // Step 1: Get cf_clearance
      const cf_clearance = await cfInit();

      // Step 2: Calculate X-Fingerprint
      const x_fingerprint = calculateXFingerprint();

      // Step 3: Get signed_challenge
      const signed_challenge = await getSignedChallenge(cf_clearance, x_fingerprint);

      const authData: AuthData = {
        cf_clearance,
        x_fingerprint,
        signed_challenge,
      };

      const response: ApiResponse<AuthData> = {
        success: true,
        message: 'Authentication data refreshed successfully',
        data: authData,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to refresh auth data:', error);
      throw error;
    }
  }),
);

// POST /auth/refresh-dagur-session - Refresh Dagur session
router.post(
  '/refresh-dagur-session',
  asyncHandler(async (_req: Request, res: Response) => {
    try {
      console.log('🔄 Refreshing Dagur session...');

      // Force refresh by clearing current session
      (pgDagurAdminHttpClient as any).session = null;
      (pgDagurAdminHttpClient as any).lastLoginTime = 0;

      // Get credentials from environment
      const username = process.env['PG_DAGUR_USERNAME'] || '';
      const password = process.env['PG_DAGUR_PASSWORD'] || '';
      const otpSecret = process.env['PG_DAGUR_OTP_SECRET'] || '';

      if (!username || !password || !otpSecret) {
        throw new ValidationError(
          'Missing Dagur credentials in environment variables (PG_DAGUR_USERNAME, PG_DAGUR_PASSWORD, PG_DAGUR_OTP_SECRET)',
        );
      }

      // Perform fresh login
      await pgDagurAdminHttpClient.loginWithOtpSecret(username, password, otpSecret);

      const response: ApiResponse<{ message: string }> = {
        success: true,
        message: 'Dagur session refreshed successfully',
        data: { message: 'Session refreshed and ready for use' },
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to refresh Dagur session:', error);
      throw error;
    }
  }),
);

export default router;
