import { Router } from 'express';
import { PronetController } from '@/controllers/pronet.controller';
import { pronetAuth } from '@/middleware/pronetAuth';

const router = Router();

/**
 * Pronet API Routes
 * 
 * All routes in this file are prefixed with /pronet
 * These routes proxy requests to the actual Pronet API with proper authentication
 */

// Health check endpoint (no auth middleware needed for health checks)
router.get('/health', PronetController.healthCheck);

// Configuration info endpoint (no auth middleware needed for config info)
router.get('/config', PronetController.getConfig);

// Test system endpoint (no auth middleware needed for testing)
router.get('/test', PronetController.testSystem);

// Wildcard proxy route for all other Pronet API endpoints
// This must be last to catch all remaining routes
router.all('/{*splat}', pronetAuth, PronetController.proxyPronetRequest);

export default router;
