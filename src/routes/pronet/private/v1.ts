import { Router } from 'express';
import { PronetCustomerController } from '@/controllers/pronet/private/customer.controller';
import { PronetBonusController } from '@/controllers/pronet/private/bonus.controller';

const router = Router();

router.post('/customers', PronetCustomerController.create);

router.post('/bonus-bulk-assignment-job-targets/:targetId/claims', PronetBonusController.assignBonusToTarget);

export default router;
