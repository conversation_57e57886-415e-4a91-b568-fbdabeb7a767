import { Router } from 'express';
import { AuthController } from '@/controllers/pronet/auth.controller';
import { PronetBonusController } from '@/controllers/pronet/bonus.controller';
import { PronetLossbackBonusController } from '@/controllers/pronet/lossbackBonus.controller';
import { WeeklyLossbackBonusController } from '@/controllers/pronet/weeklyLossbackBonus.controller';
import { PronetCashBonusController } from '@/controllers/pronet/cashBonus.controller';
import { PronetCustomersController } from '@/controllers/pronet/customers.controller';
import { SiteClaimableBonusController } from '@/controllers/pronet/siteClaimableBonus.controller';

const router = Router();

router.post('/auth/dagur/login', AuthController.loginWithDagur);

// Site claimable bonus endpoints
router.get('/site-claimable-bonuses', SiteClaimableBonusController.list);
router.post('/site-claimable-bonuses', SiteClaimableBonusController.createSlot);
router.put('/site-claimable-bonuses/:slotName', SiteClaimableBonusController.updateSlot);
router.delete('/site-claimable-bonuses/:slotName', SiteClaimableBonusController.deleteSlotByName);
// router.delete('/site-claimable-bonuses/id/:id', SiteClaimableBonusController.deleteSlotById);
router.get('/site-claimable-bonuses/check/:bonusId', SiteClaimableBonusController.checkClaimable);
router.get('/site-claimable-bonuses/claimable', SiteClaimableBonusController.getClaimableBonuses);
router.patch('/site-claimable-bonuses/:slotName/toggle', SiteClaimableBonusController.toggleSlotActive);
// Customer endpoints
router.get('/customers', PronetCustomersController.list);
router.get('/customers/total-balances', PronetCustomersController.getTotalBalances);
router.get('/customers/:id', PronetCustomersController.getById);
// Site claimable bonus endpoints
router.get('/site-claimable-bonuses', SiteClaimableBonusController.list);
router.put('/site-claimable-bonuses/:slotName', SiteClaimableBonusController.updateSlot);
router.get('/site-claimable-bonuses/check/:bonusId', SiteClaimableBonusController.checkClaimable);
router.get('/site-claimable-bonuses/claimable', SiteClaimableBonusController.getClaimableBonuses);
router.patch('/site-claimable-bonuses/:slotName/toggle', SiteClaimableBonusController.toggleSlotActive);

router.get('/freespin-bonuses', PronetBonusController.listFreespinBonuses);
router.get('/freespin-bonuses/providers', PronetBonusController.listFreespinBonusProviders);
router.get('/freespin-bonuses/providers/:id/games', PronetBonusController.listFreespinBonusGames);
router.get('/freespin-bonuses/providers/:id/bet-amounts', PronetBonusController.listFreespinBetAmounts);
router.get('/freespin-bonuses/providers/:id/currencies', PronetBonusController.listFreespinCurrencies);
router.get('/freespin-bonuses/:id', PronetBonusController.getFreespinBonus);
router.post('/freespin-bonuses/:id/bulk-assignment-jobs', PronetBonusController.createFreespinBonusBulkAssignmentJob);
router.post('/freespin-bonuses', PronetBonusController.createFreespinBonus);

// Trial Bonus routes
router.get('/trial-bonuses', PronetBonusController.listTrialBonuses);
router.get('/external-trial-bonuses', PronetBonusController.listExternalTrialBonuses);
router.get('/trial-bonuses/:id', PronetBonusController.getTrialBonus);
router.post('/trial-bonuses', PronetBonusController.createTrialBonus);
router.post('/trial-bonuses/:id/bulk-assignment-jobs', PronetBonusController.createTrialBonusBulkAssignmentJob);

// Trial Bonus Template routes
router.get('/trial-bonus-templates', PronetBonusController.listTrialBonusTemplates);
router.get('/trial-bonus-templates/:id', PronetBonusController.getTrialBonusTemplate);
router.post('/trial-bonus-templates', PronetBonusController.createTrialBonusTemplate);

// Happy Hours Bonus routes
router.get('/happy-hours-bonuses', PronetBonusController.listHappyHoursBonuses);
router.get('/external-happy-hours-bonuses', PronetBonusController.listExternalHappyHoursBonuses);
router.get('/happy-hours-bonuses/:id', PronetBonusController.getHappyHoursBonus);
router.post('/happy-hours-bonuses', PronetBonusController.createHappyHoursBonus);
router.post('/happy-hours-bonuses/:id/bulk-assignment-jobs', PronetBonusController.createHappyHoursBonusBulkAssignmentJob);

// Happy Hours Bonus Template routes
router.get('/happy-hours-bonus-templates', PronetBonusController.listHappyHoursBonusTemplates);
router.get('/happy-hours-bonus-templates/:id', PronetBonusController.getHappyHoursBonusTemplate);
router.post('/happy-hours-bonus-templates', PronetBonusController.createHappyHoursBonusTemplate);

router.get('/freespin-bonus-templates', PronetBonusController.listFreespinBonusTemplates);
router.get('/freespin-bonus-templates/:id', PronetBonusController.getFreespinBonusTemplate);
router.post('/freespin-bonus-templates', PronetBonusController.createFreespinBonusTemplate);

// Freebet Bonus routes
router.get('/freebet-bonuses', PronetBonusController.listFreebetBonuses);
router.get('/external-freebet-bonuses', PronetBonusController.listExternalFreebetBonuses);
router.get('/freebet-bonuses/:id', PronetBonusController.getFreebetBonus);
router.post('/freebet-bonuses', PronetBonusController.createFreebetBonus);
router.post('/freebet-bonuses/:id/bulk-assignment-jobs', PronetBonusController.createFreebetBonusBulkAssignmentJob);

// Freebet Bonus Template routes
router.get('/freebet-bonus-templates', PronetBonusController.listFreebetBonusTemplates);
router.get('/freebet-bonus-templates/:id', PronetBonusController.getFreebetBonusTemplate);
router.post('/freebet-bonus-templates', PronetBonusController.createFreebetBonusTemplate);

router.get('/bonuses', PronetBonusController.searchBonuses);
router.patch('/bonuses/:id', PronetBonusController.patchBonus);
router.delete('/bonuses/:id', PronetBonusController.softDeleteBonus);
router.post('/bonuses/:id/claims', PronetBonusController.claimBonus);
router.post('/bonuses/:id/promocodes', PronetBonusController.createBonusPromocode);

router.get('/bonus-claims', PronetBonusController.searchBonusClaims);

router.get('/bonus-promocodes', PronetBonusController.searchBonusPromocodes);
router.patch('/bonus-promocodes/:id', PronetBonusController.toggleBonusPromocode);
router.delete('/bonus-promocodes/:id', PronetBonusController.softDeleteBonusPromocode);
router.post('/bonus-promocodes/:code/activations', PronetBonusController.activateBonusPromocode);

router.get('/bonus-promocode-activations', PronetBonusController.searchBonusPromocodeActivations);

router.delete('/bonus-templates/:id', PronetBonusController.softDeleteBonusTemplate);

// Lossback bonus routes
router.get('/lossback-bonuses', PronetLossbackBonusController.listLossbackBonuses);
router.get('/lossback-bonuses/:id', PronetLossbackBonusController.getLossbackBonus);
router.post('/lossback-bonuses', PronetLossbackBonusController.createLossbackBonus);

router.get('/lossback-bonus-templates', PronetLossbackBonusController.listLossbackBonusTemplates);
router.get('/lossback-bonus-templates/:id', PronetLossbackBonusController.getLossbackBonusTemplate);
router.post('/lossback-bonus-templates', PronetLossbackBonusController.createLossbackBonusTemplate);

// Weekly lossback bonus routes
router.get('/weekly-lossback-bonuses', WeeklyLossbackBonusController.listWeeklyLossbackBonuses);
router.get('/weekly-lossback-bonuses/:id', WeeklyLossbackBonusController.getWeeklyLossbackBonus);
router.post('/weekly-lossback-bonuses', WeeklyLossbackBonusController.createWeeklyLossbackBonus);

router.get('/weekly-lossback-bonus-templates', WeeklyLossbackBonusController.listWeeklyLossbackBonusTemplates);
router.get('/weekly-lossback-bonus-templates/:id', WeeklyLossbackBonusController.getWeeklyLossbackBonusTemplate);
router.post('/weekly-lossback-bonus-templates', WeeklyLossbackBonusController.createWeeklyLossbackBonusTemplate);

// Cash bonus routes
router.get('/cash-bonuses', PronetCashBonusController.listCashBonuses);
router.get('/cash-bonuses/:id', PronetCashBonusController.getCashBonus);
router.post('/cash-bonuses', PronetCashBonusController.createCashBonus);
router.post('/cash-bonuses/:id/bulk-assignment-jobs', PronetCashBonusController.createCashBonusBulkAssignmentJob);

router.get('/cash-bonus-templates', PronetCashBonusController.listCashBonusTemplates);
router.get('/cash-bonus-templates/:id', PronetCashBonusController.getCashBonusTemplate);
router.post('/cash-bonus-templates', PronetCashBonusController.createCashBonusTemplate);

router.get('/bonus-bulk-assignment-jobs', PronetBonusController.listBonusBulkAssignmentJobs);
router.get('/bonus-bulk-assignment-jobs/:id', PronetBonusController.getBonusBulkAssignmentJob);
router.post('/bonus-bulk-assignment-jobs/:id/cancellations', PronetBonusController.cancelBonusBulkAssignmentJob);
router.post('/bonus-bulk-assignment-jobs/:id/retries', PronetBonusController.retryBonusBulkAssignmentJob);

export default router;
