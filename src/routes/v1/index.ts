import { Router } from 'express';
import { CustomersController } from '@/controllers/customers.controller';
import { SessionsController } from '@/controllers/sessions.controller';
import { DashboardController } from '@/controllers/dashboard.controller';
import { BonusController } from '@/controllers/bonus.controller';
import { GameController } from '@/controllers/game.controller';
import { AutoBonusRuleController } from '@/controllers/autoBonusRule.controller';
import { BalanceCorrectionRuleController } from '@/controllers/balanceCorrectionRule.controller';
import { BalanceCorrectionsController } from '@/controllers/balanceCorrections.controller';
import { BonusRequestContentController } from '@/controllers/bonusRequestContent.controller';
import { WelcomeBonusController } from '@/controllers/welcomeBonus.controller';
import { BonusDropController } from '@/controllers/bonusDrop.controller';
import { UserController } from '@/controllers/user.controller';
import { CurrencyController } from '@/controllers/currency.controller';
import { ConfigurationController } from '@/controllers/configuration.controller';
import { SelfExclusionController } from '@/controllers/selfExclusion.controller';
import { VerificationCodeController } from '@/controllers/verificationCode.controller';
import { ChatBlacklistController } from '@/controllers/chatBlacklist.controller';
import { CssSettingsController } from '@/controllers/cssSettings.controller';
import { PronetController } from '@/controllers/pronet.controller';

// Slack Bot System imports
import { createSlackBotRoutes, createHealthCheckRoute } from '@/middleware/slackBotRoutes';
import registerAllSlackBots from '@/registerSlackBots';
import {
  createMission,
  getMissions,
  getMissionById,
  updateMission,
  deleteMission,
} from '@/controllers/mission.controller';
import {
  createExtendedUser,
  getExtendedUsers,
  getExtendedUserById,
  updateExtendedUser,
  deleteExtendedUser,
  bulkUpdateExtendedUsers,
  createExtendedUserByToken,
  getExtendedUserByToken,
  getExtendedUserByExternalId,
  getUserMissionStats,
  getExtendedUserPointsStats,
} from '@/controllers/extendedUser.controller';
import {
  createMissionRule,
  getMissionRules,
  getMissionRuleById,
  updateMissionRule,
  deleteMissionRule,
} from '@/controllers/missionRule.controller';
import {
  createMissionObjective,
  getMissionObjectives,
  getMissionObjectiveById,
  updateMissionObjective,
  deleteMissionObjective,
  getMissionObjectivesByMissionId,
} from '@/controllers/missionObjective.controller';
import {
  createMissionParticipation,
  getMissionParticipations,
  getMissionParticipationById,
  updateMissionParticipation,
  deleteMissionParticipation,
  getMissionParticipationsByMissionId,
  getMissionParticipationStats,
  startMissionParticipation,
  testOtpGeneration,
  getUserEligibleMissions,
  completeMission,
  getMissionResetTimes,
} from '@/controllers/missionParticipation.controller';
import {
  createMissionRuleAssignment,
  getMissionRuleAssignments,
  getMissionRuleAssignmentById,
  updateMissionRuleAssignment,
  deleteMissionRuleAssignment,
  assignRuleToMission,
  getRulesForMission,
} from '@/controllers/missionRuleAssignment.controller';
import {
  createMissionObjectiveAssignment,
  getMissionObjectiveAssignments,
  getMissionObjectiveAssignmentById,
  updateMissionObjectiveAssignment,
  deleteMissionObjectiveAssignment,
  assignObjectiveToUser,
  getAssignmentsForObjective,
  getAssignmentsForMission,
  getMissionProgressStats,
} from '@/controllers/missionObjectiveAssignment.controller';
import {
  claimFinalMissionReward,
  createFinalMissionClaim,
  getFinalMissionClaims,
  getFinalMissionClaimById,
  updateFinalMissionClaim,
  deleteFinalMissionClaim,
  getFinalMissionClaimStatistics,
  getUserLatestFinalMissionClaims,
} from '@/controllers/finalMissionClaim.controller';
import {
  createMarketProduct,
  listMarketProducts,
  getMarketProductById,
  deleteMarketProduct,
  updateMarketProduct,
  listPublicMarketProducts,
} from '@/controllers/makroz/marketProduct.controller';
import {
  listUserMarketProductRequests,
  createMarketProductRequest,
  listAllMarketProductRequests,
  rejectMarketProductRequest,
  completeMarketProductRequest,
  refundMarketProductRequest,
  deleteMarketProductRequest,
} from '@/controllers/makroz/marketProductRequest.controller';
import { getProviders, getCurrencies } from '@/controllers/ebetlab/configuration.controller';
import {
  getTransactions,
  getTransactionStats,
  deleteTransaction,
  getUserTransactions,
} from '@/controllers/makroz/transaction.controller';
import { SlackBotController } from '@/controllers/makroz/slackBot.controller';
import { BetReportRecordController } from '@/controllers/betReportRecord.controller';
import { adminAuth } from '@/middleware/adminAuth';
import { fetchNexus } from '@/utils/fetchNexus';

const router = Router();

// Initialize Slack Bot System
console.log('🤖 Initializing Slack Bot System...');
try {
  // Register all Slack bots
  registerAllSlackBots();

  // Create and mount automatic bot routes
  const slackBotRoutes = createSlackBotRoutes();
  router.use('/', slackBotRoutes);

  // Add health check route for Slack bot system
  const healthCheckRoutes = createHealthCheckRoute();
  router.use('/', healthCheckRoutes);

  console.log('✅ Slack Bot System initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Slack Bot System:', error);
}

// Dashboard routes
router.get('/operator/dashboard/transactions/summary', DashboardController.getTransactionsSummary);
router.get('/operator/dashboard/debits/big-win-lose', DashboardController.getBigWinLoseDebits);
router.get('/operator/dashboard/debits/game-win-lose', DashboardController.getGameWinLoseDebits);
router.post('/operator/dashboard/widgets/stats', DashboardController.getWidgetStats);

// Bonuses routes
router.get('/bonuses', BonusController.listBonuses);
router.get('/bonuses/:id', BonusController.getBonusById);
router.get('/bonuses/:bonusId/freespins', BonusController.getBonusFreespins);
router.post('/bonuses', BonusController.createBonus);
router.post('/bonuses/:bonusId/cancellations', BonusController.cancelBonus);
router.patch('/bonuses/:id', BonusController.updateBonus);
router.delete('/bonuses/:bonusId', BonusController.deleteBonus);

// Games routes
router.get('/games', GameController.getGamesByProvider);

// Auto Bonus Rules routes
router.get('/auto-bonus-rules', AutoBonusRuleController.listAutoBonusRules);
router.get('/auto-bonus-rules/claims', AutoBonusRuleController.listAutoBonusRuleClaims);
router.get('/auto-bonus-rules/:id', AutoBonusRuleController.getAutoBonusRuleById);
router.get('/auto-bonus-rules/:id/claims', AutoBonusRuleController.getAutoBonusRuleClaims);
router.post('/auto-bonus-rules', AutoBonusRuleController.createAutoBonusRule);
router.post('/auto-bonus-rules/:id/cancellations', AutoBonusRuleController.cancelAutoBonusRule);
router.patch('/auto-bonus-rules/:id', AutoBonusRuleController.updateAutoBonusRule);
router.delete('/auto-bonus-rules/:id', AutoBonusRuleController.deleteAutoBonusRule);

// Balance Correction Rules routes
router.get('/balance-correction-rules', BalanceCorrectionRuleController.listBalanceCorrectionRules);
router.get('/balance-correction-rules/:id', BalanceCorrectionRuleController.getBalanceCorrectionRuleById);
router.get('/balance-correction-rules/:id/claims', BalanceCorrectionRuleController.getBalanceCorrectionRuleClaims);
router.post('/balance-correction-rules', BalanceCorrectionRuleController.createBalanceCorrectionRule);
router.post('/balance-correction-rules/:id/cancellations', BalanceCorrectionRuleController.cancelBalanceCorrectionRule);
router.post(
  '/balance-correction-rules/:id/claims/downloads',
  BalanceCorrectionRuleController.createBalanceCorrectionRuleDownload,
);
router.patch('/balance-correction-rules/:id', BalanceCorrectionRuleController.updateBalanceCorrectionRule);
router.delete('/balance-correction-rules/:id', BalanceCorrectionRuleController.deleteBalanceCorrectionRule);

// Balance Corrections routes
router.post('/operator/balance-corrections/summary', BalanceCorrectionsController.getBalanceCorrectionsSummary);
router.post('/operator/balance-corrections/index/:page/:limit', BalanceCorrectionsController.getBalanceCorrections);

// Bonus Request Content routes
router.get('/bonus-request-contents', BonusRequestContentController.listBonusRequestContents);
router.post('/bonus-request-contents', BonusRequestContentController.createBonusRequestContent);
router.post('/bonus-request-contents/sorts', BonusRequestContentController.createBonusRequestContentSort);
router.post('/bonus-request-contents/:id/states', BonusRequestContentController.toggleBonusRequestContentState);
router.delete('/bonus-request-contents/:id', BonusRequestContentController.deleteBonusRequestContent);

// Welcome Bonus routes
router.get('/welcome-bonuses', WelcomeBonusController.listWelcomeBonuses);
router.get('/welcome-bonuses/:id', WelcomeBonusController.getWelcomeBonusById);
router.get('/welcome-bonuses/:id/claims', WelcomeBonusController.getWelcomeBonusClaims);
router.post('/welcome-bonuses', WelcomeBonusController.createWelcomeBonus);
router.post('/welcome-bonuses/:id/cancellations', WelcomeBonusController.cancelWelcomeBonus);
router.patch('/welcome-bonuses/:id', WelcomeBonusController.updateWelcomeBonus);
router.delete('/welcome-bonuses/:id', WelcomeBonusController.deleteWelcomeBonus);

// Bonus Drop routes
router.get('/bonus-drops', BonusDropController.listBonusDrops);
router.get('/bonus-drops/redeems', BonusDropController.listBonusDropRedeems);
router.get('/bonus-drops/:id', BonusDropController.getBonusDropById);
router.get('/bonus-drops/:id/redeems', BonusDropController.getBonusDropRedeems);
router.post('/bonus-drops', BonusDropController.createBonusDrop);
router.post('/bonus-drops/redeems/downloads', BonusDropController.createBonusDropRedeemDownloadAll);
router.post('/bonus-drops/:id/cancellations', BonusDropController.cancelBonusDrop);
router.post('/bonus-drops/:id/downloads', BonusDropController.createBonusDropRedeemDownload);
router.patch('/bonus-drops/:id', BonusDropController.updateBonusDrop);
router.delete('/bonus-drops/:id', BonusDropController.deleteBonusDrop);

// User routes
router.get('/users/me', UserController.getAuthorizedUser);

// Currency routes
router.get('/currencies/rates/conversions/:currency', CurrencyController.getCurrencyRateConversion);

// Configuration routes
router.get('/configurations', ConfigurationController.getConfigurations);

// Customer routes
router.post('/operator/customers/index/:page/:limit', CustomersController.getCustomers);
router.post('/operator/customers/show/:id', CustomersController.getCustomerById);
router.post('/operator/customers/summary-range/:id', CustomersController.getCustomerSummaryRange);
router.post('/operator/customers/info/:id', CustomersController.getCustomerInfo);
router.post('/operator/customers/password/:id', CustomersController.updateCustomerPassword);

// Customer dashboard routes
router.post('/operator/customers/dashboard/debits/big-win-lose', CustomersController.getCustomerBigWinLoseDebits);
router.post('/operator/customers/dashboard/debits/game-win-lose', CustomersController.getCustomerGameWinLoseDebits);

// General limits routes
router.post('/operator/general-limits/show/:id', CustomersController.getGeneralLimits);
router.post('/operator/general-limits/apply/:id', CustomersController.applyGeneralLimits);

// Financial limits routes
router.post('/operator/customer-limits/show/:id', CustomersController.getFinancialLimits);
router.post('/operator/customer-limits/update/:id', CustomersController.updateFinancialLimits);

// Transactions routes
router.post('/operator/transactions/index/:page/:limit', CustomersController.getTransactions);
router.post('/operator/transactions/ftd/:page/:limit', CustomersController.getFtdTransactions);
router.post('/operator/transactions/ftw/:page/:limit', CustomersController.getFtwTransactions);
router.post('/operator/transactions/fiats/:page/:limit', CustomersController.getFiatTransactions);
router.post('/operator/transactions/summary', CustomersController.getTransactionsSummary);

// Casino debits routes
router.post('/operator/debits/index/:page/:limit', CustomersController.getCasinoDebits);

// Sportsbook debits routes
router.post('/operator/sportsbook-debits/index/:page/:limit', CustomersController.getSportsbookDebits);

// Trade debits routes
router.post('/operator/trade-debits/index/:page/:limit', CustomersController.getTradeDebits);

// Sessions routes
router.post('/operator/sessions/index/:page/:limit', CustomersController.getSessions);

// Bonus redeems routes
router.post('/operator/bonus-redeems/index/:page/:limit', CustomersController.getBonusRedeems);

// Discounts routes
router.post('/operator/discounts/summary', CustomersController.getDiscountsSummary);
router.post('/operator/discounts/index/:page/:limit', CustomersController.getDiscounts);

// Notifications routes
router.post('/operator/notifications/index/:page/:limit', CustomersController.getNotifications);

// Sportsbook players routes
router.post('/operator/players/sportsbook/:page/:limit', CustomersController.getSportsbookPlayers);

// Wallets routes
router.post('/operator/wallets/index/:page/:limit', CustomersController.getWallets);

// Vaults routes
router.post('/operator/vaults/index/:page/:limit', CustomersController.getVaults);

// VIP state changes routes
router.post('/operator/vip-state-changes/index/:page/:limit', CustomersController.getVipStateChanges);

// Player actions routes
router.post('/operator/player-actions/index/:page/:limit', CustomersController.getPlayerActions);

// Commits routes
router.post('/operator/commits/index/:page/:limit', CustomersController.getCommits);

// Sessions routes
router.post('/operator/sessions/check/:id', SessionsController.checkSessions);

// VIP rank routes
router.post('/operator/vip-state/rank/:slug', CustomersController.setVipRank);

// Profile update routes
router.post('/operator/profile/update/:id', CustomersController.updateProfile);

// Rakeback routes
router.post('/operator/rakeback-usages/index/:page/:limit', CustomersController.getRakebackUsages);
router.post('/operator/rakeback-availables/index/:page/:limit', CustomersController.getRakebackAvailables);

// Tips routes
router.post('/operator/tips/index/:page/:limit', CustomersController.getTips);

// Self-exclusion routes
router.post('/operator/self-exclusions/index/:page/:limit', SelfExclusionController.getSelfExclusions);

// Verification codes routes
router.post('/operator/verification-codes/index/:page/:limit', VerificationCodeController.getVerificationCodes);

// Chat blacklist routes
router.post('/operator/customers/chat/blacklist/:page/:limit', ChatBlacklistController.getChatBlacklistedCustomers);

// CSS settings routes
router.post('/operator/css-settings/index2/:page/:limit', CssSettingsController.getCssSettingsList);
router.post('/operator/css-settings/show/:id', CssSettingsController.getCssSettingById);

// Affiliate routes
router.post('/operator/affiliates/set/:id', CustomersController.setAffiliateCode);

// Makroz Public endpoints (JWT Bearer token authentication)
router.post('/makroz/extended-users/me', createExtendedUserByToken);
router.get('/makroz/extended-users/me', getExtendedUserByToken);
router.get('/makroz/extended-users/:externalId', getExtendedUserByExternalId);

// Public mission participation endpoint
router.post('/makroz/missions/:id/participations', startMissionParticipation);

// Public endpoint to complete a mission
router.post('/makroz/missions/:missionId/complete', completeMission);

// Public endpoint to get user's eligible missions
router.get('/makroz/extended-users/me/missions', getUserEligibleMissions);

// Public endpoint to get user's mission statistics
router.get('/makroz/extended-users/me/missions/stats', getUserMissionStats);

// Public endpoint to get mission reset times
router.get('/makroz/missions/reset-times', getMissionResetTimes);

// Public endpoint to claim final mission reward
router.post('/makroz/final-mission-claims', claimFinalMissionReward);

// Public endpoint to get user's latest final mission claims
router.get('/makroz/final-mission-claims/me', getUserLatestFinalMissionClaims);

// Debug endpoint for testing OTP generation
router.get('/debug/test-otp', testOtpGeneration);

// Makroz Admin endpoints (protected with admin authentication)
// Missions
router.post('/makroz/admin/missions', adminAuth, createMission);
router.get('/makroz/admin/missions', adminAuth, getMissions);
router.get('/makroz/admin/missions/:id', adminAuth, getMissionById);
router.patch('/makroz/admin/missions/:id', adminAuth, updateMission);
router.delete('/makroz/admin/missions/:id', adminAuth, deleteMission);

// Extended Users
router.post('/makroz/admin/extended-users', adminAuth, createExtendedUser);
router.get('/makroz/admin/extended-users', adminAuth, getExtendedUsers);

// Extended Users Statistics (must come before :id route)
router.get('/makroz/admin/extended-users/stats', adminAuth, getExtendedUserPointsStats);

router.get('/makroz/admin/extended-users/:id', adminAuth, getExtendedUserById);
router.patch('/makroz/admin/extended-users/:id', adminAuth, updateExtendedUser);
router.delete('/makroz/admin/extended-users/:id', adminAuth, deleteExtendedUser);
router.patch('/makroz/admin/extended-users/bulk-update', adminAuth, bulkUpdateExtendedUsers);

// Mission Rules
router.post('/makroz/admin/mission-rules', adminAuth, createMissionRule);
router.get('/makroz/admin/mission-rules', adminAuth, getMissionRules);
router.get('/makroz/admin/mission-rules/:id', adminAuth, getMissionRuleById);
router.patch('/makroz/admin/mission-rules/:id', adminAuth, updateMissionRule);
router.delete('/makroz/admin/mission-rules/:id', adminAuth, deleteMissionRule);

// Mission Objectives
router.post('/makroz/admin/mission-objectives', adminAuth, createMissionObjective);
router.get('/makroz/admin/mission-objectives', adminAuth, getMissionObjectives);
router.get('/makroz/admin/mission-objectives/:id', adminAuth, getMissionObjectiveById);
router.patch('/makroz/admin/mission-objectives/:id', adminAuth, updateMissionObjective);
router.delete('/makroz/admin/mission-objectives/:id', adminAuth, deleteMissionObjective);

// Mission-specific objective endpoints
router.get('/makroz/admin/mission-objectives/mission/:missionId', adminAuth, getMissionObjectivesByMissionId);

// Mission Participations
router.post('/makroz/admin/mission-participations', adminAuth, createMissionParticipation);
router.get('/makroz/admin/mission-participations', adminAuth, getMissionParticipations);
router.get('/makroz/admin/mission-participations/:id', adminAuth, getMissionParticipationById);
router.patch('/makroz/admin/mission-participations/:id', adminAuth, updateMissionParticipation);
router.delete('/makroz/admin/mission-participations/:id', adminAuth, deleteMissionParticipation);

// Mission-specific participation endpoints
router.get('/makroz/admin/mission-participations/mission/:missionId', adminAuth, getMissionParticipationsByMissionId);
router.get('/makroz/admin/mission-participations/mission/:missionId/stats', adminAuth, getMissionParticipationStats);

// Mission Rule Assignments
router.post('/makroz/admin/mission-rule-assignments', adminAuth, createMissionRuleAssignment);
router.get('/makroz/admin/mission-rule-assignments', adminAuth, getMissionRuleAssignments);
router.get('/makroz/admin/mission-rule-assignments/:id', adminAuth, getMissionRuleAssignmentById);
router.patch('/makroz/admin/mission-rule-assignments/:id', adminAuth, updateMissionRuleAssignment);
router.delete('/makroz/admin/mission-rule-assignments/:id', adminAuth, deleteMissionRuleAssignment);

// Mission-specific rule assignment endpoints
router.put('/makroz/admin/mission-rule-assignments/mission/:missionId/rules', adminAuth, assignRuleToMission);
router.get('/makroz/admin/mission-rule-assignments/mission/:missionId', adminAuth, getRulesForMission);

// Mission Objective Assignments
router.post('/makroz/admin/mission-objective-assignments', adminAuth, createMissionObjectiveAssignment);
router.get('/makroz/admin/mission-objective-assignments', adminAuth, getMissionObjectiveAssignments);
router.get('/makroz/admin/mission-objective-assignments/:id', adminAuth, getMissionObjectiveAssignmentById);
router.patch('/makroz/admin/mission-objective-assignments/:id', adminAuth, updateMissionObjectiveAssignment);
router.delete('/makroz/admin/mission-objective-assignments/:id', adminAuth, deleteMissionObjectiveAssignment);

// Mission-specific objective assignment endpoints
router.put(
  '/makroz/admin/mission-objective-assignments/objective/:missionObjectiveId/users',
  adminAuth,
  assignObjectiveToUser,
);
router.get(
  '/makroz/admin/mission-objective-assignments/objective/:missionObjectiveId',
  adminAuth,
  getAssignmentsForObjective,
);
router.get('/makroz/admin/mission-objective-assignments/mission/:missionId', adminAuth, getAssignmentsForMission);
router.get('/makroz/admin/mission-objective-assignments/mission/:missionId/stats', adminAuth, getMissionProgressStats);

// Final Mission Claims
router.post('/makroz/admin/final-mission-claims', adminAuth, createFinalMissionClaim);
router.get('/makroz/admin/final-mission-claims', adminAuth, getFinalMissionClaims);

// Final Mission Claim Statistics (must come before :id route)
router.get('/makroz/admin/final-mission-claims/stats', adminAuth, getFinalMissionClaimStatistics);

router.get('/makroz/admin/final-mission-claims/:id', adminAuth, getFinalMissionClaimById);
router.patch('/makroz/admin/final-mission-claims/:id', adminAuth, updateFinalMissionClaim);
router.delete('/makroz/admin/final-mission-claims/:id', adminAuth, deleteFinalMissionClaim);

// Market Products
router.post('/makroz/admin/market-products', adminAuth, createMarketProduct);
router.get('/makroz/admin/market-products', adminAuth, listMarketProducts);
router.get('/makroz/admin/market-products/:id', adminAuth, getMarketProductById);
router.patch('/makroz/admin/market-products/:id', adminAuth, updateMarketProduct);
router.delete('/makroz/admin/market-products/:id', adminAuth, deleteMarketProduct);

// Market Products - Public endpoints
router.get('/makroz/public/market-products', listPublicMarketProducts);

// Market Product Requests - Public endpoints
router.get('/makroz/public/market-product-requests', listUserMarketProductRequests);
router.post('/makroz/public/market-product-requests', createMarketProductRequest);

// Market Product Requests - Admin endpoints
router.get('/makroz/admin/market-product-requests', adminAuth, listAllMarketProductRequests);
router.post('/makroz/admin/market-product-requests/:id/rejections', adminAuth, rejectMarketProductRequest);
router.post('/makroz/admin/market-product-requests/:id/completions', adminAuth, completeMarketProductRequest);
router.post('/makroz/admin/market-product-requests/:id/refunds', adminAuth, refundMarketProductRequest);
router.delete('/makroz/admin/market-product-requests/:id', adminAuth, deleteMarketProductRequest);

// EbetLab Public Configuration endpoints
router.get('/ebetlab/public/providers', getProviders);
router.get('/ebetlab/public/currencies', getCurrencies);

// EbetLab Public Game endpoints
router.get('/ebetlab/public/games', GameController.listPublicGames);

// Transactions - Admin endpoints
router.get('/makroz/admin/transactions/stats', adminAuth, getTransactionStats);
router.get('/makroz/admin/transactions', adminAuth, getTransactions);
router.delete('/makroz/admin/transactions/:id', adminAuth, deleteTransaction);

// Transactions - Public endpoints
router.get('/makroz/extended-users/me/transactions', getUserTransactions);

// Slack Bots - Admin endpoints
router.get('/makroz/admin/slack-bots', adminAuth, SlackBotController.getAllBots);
router.get('/makroz/admin/slack-bots/:id', adminAuth, SlackBotController.getBotById);

// Bet Report Records - Admin endpoints (no auth protection)
router.post('/makroz/admin/bet-report-records/invoke', BetReportRecordController.invokeBetRecord);
router.get('/makroz/admin/bet-report-records', BetReportRecordController.getBetReportRecords);
router.get('/makroz/admin/bet-report-records/:id', BetReportRecordController.getBetReportRecordById);
router.delete('/makroz/admin/bet-report-records/:id', BetReportRecordController.deleteBetReportRecord);

// Pronet API endpoints
router.get('/pronet/v1/external-api/getBonusesAndFreeBets', PronetController.getBonusesAndFreeBets);

router.get('/', (_, res) => void res.sendStatus(200));

export default router;
