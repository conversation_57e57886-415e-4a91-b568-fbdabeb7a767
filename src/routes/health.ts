import { Router, Request, Response } from 'express';
import { HealthCheckResponse } from '@/types/api';
import { AppDataSource } from '@/database/connection';

const router = Router();

// Basic health check
router.get('/', (_req: Request, res: Response) => {
  const healthCheck: HealthCheckResponse = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env['npm_package_version'] || '1.0.0',
    environment: process.env['NODE_ENV'] || 'development',
  };

  res.status(200).json(healthCheck);
});

// Detailed health check
router.get('/detailed', async (_req: Request, res: Response) => {
  try {
    // Check database connection
    let dbStatus = 'disconnected';
    if (AppDataSource.isInitialized) {
      await AppDataSource.query('SELECT 1');
      dbStatus = 'connected';
    }

    const healthCheck: HealthCheckResponse = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['npm_package_version'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      services: {
        database: dbStatus,
      },
    };

    res.status(200).json(healthCheck);
  } catch (error) {
    const healthCheck: HealthCheckResponse = {
      status: 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['npm_package_version'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      services: {
        database: 'error',
      },
      error: error instanceof Error ? error.message : 'Unknown error',
    };

    res.status(500).json(healthCheck);
  }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (_req: Request, res: Response) => {
  try {
    // Check if database is ready
    if (AppDataSource.isInitialized) {
      await AppDataSource.query('SELECT 1');
      res.status(200).json({ status: 'ready' });
    } else {
      res.status(503).json({ status: 'not ready', reason: 'database not initialized' });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      reason: 'database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (_req: Request, res: Response) => {
  res.status(200).json({ status: 'alive' });
});

export default router;
