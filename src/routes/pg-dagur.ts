import { Router } from 'express';

import { CustomersController } from '@/controllers/pg-dagur/internal/customers.controller';
import { CustomerFreebetsController } from '@/controllers/pg-dagur/internal/customerFreebets.controller';

import { CustomerStatisticsController } from '@/controllers/pg-dagur/internal/customerStatistics.controller';
import { CustomerSummaryController } from '@/controllers/pg-dagur/internal/customerSummary.controller';
import { AccountingController } from '@/controllers/pg-dagur/internal/accounting.controller';
import { ReportsController } from '@/controllers/pg-dagur/internal/reports.controller';
import { BetReportsController } from '@/controllers/pg-dagur/internal/betReports.controller';
import { CustomerDiscountsController } from '@/controllers/pg-dagur/internal/customerDiscounts.controller';
import { PromotionsController } from '@/controllers/pg-dagur/internal/promotions.controller';

const router = Router();

// Internal routes (existing)
router.get('/v1/internal/customers/balances', CustomersController.getTotalBalances);
router.get('/v1/internal/customers/activity-reports', CustomersController.listActivityReports);
router.get('/v1/internal/customers/reports', CustomersController.listCustomerReports);
router.get('/v1/internal/customers/:customerId/freebets', CustomerFreebetsController.getCustomerFreebets);
router.post('/v1/internal/customers/:customerId/freebets', CustomerFreebetsController.assignCustomerFreebet);

router.get('/v1/internal/customers/:customerId/statistics', CustomerStatisticsController.getCustomerStatistics);
router.get('/v1/internal/customers/:customerId/summary', CustomerSummaryController.getCustomerSummary);

router.post('/v1/internal/customers/:customerId/discounts', CustomerDiscountsController.assignCustomerDiscount);
router.get('/v1/internal/customers/discount-types', CustomerDiscountsController.listDiscountTypes);
router.get('/v1/internal/customers/:customerId/discount-reasons', CustomerDiscountsController.listReasons);
router.get('/v1/internal/customers/:customerId/bets', CustomersController.listCustomerBets);
router.get('/v1/internal/customers/:customerId/details', CustomersController.getCustomerDetails);
router.get('/v1/internal/customers/:customerId/ip-conflicts', CustomersController.getCustomerIpConflicts);

router.get('/v1/internal/promotions/preload', PromotionsController.preloadPromotions);
router.get('/v1/internal/promotions/freebets', PromotionsController.getPromotionsFreebets);
router.post('/v1/internal/promotions/freebets/tab-change', PromotionsController.changeToFreebetsTab);

router.get('/v1/internal/accounting/transactions', AccountingController.listTransactions);

router.get('/v1/internal/bet-reports/daily', BetReportsController.getDailyBetReport);

router.post('/v1/internal/reports/daily-bet-report', ReportsController.getDailyBetReport);

// Public routes (duplicated from internal routes with identical functionality)
router.get('/v1/customers/balances', CustomersController.getTotalBalances);
router.get('/v1/customers/activity-reports', CustomersController.listActivityReports);
router.get('/v1/customers/reports', CustomersController.listCustomerReports);
router.get('/v1/customers/:customerId/freebets', CustomerFreebetsController.getCustomerFreebets);
router.post('/v1/customers/:customerId/freebets', CustomerFreebetsController.assignCustomerFreebet);

router.get('/v1/customers/:customerId/statistics', CustomerStatisticsController.getCustomerStatistics);
router.get('/v1/customers/:customerId/summary', CustomerSummaryController.getCustomerSummary);

router.get('/v1/promotions/preload', PromotionsController.preloadPromotions);
router.post('/v1/promotions/freebets/tab-change', PromotionsController.changeToFreebetsTab);

router.get('/v1/accounting/transactions', AccountingController.listTransactions);

router.get('/v1/bet-reports/daily', BetReportsController.getDailyBetReport);

router.post('/v1/reports/daily-bet-report', ReportsController.getDailyBetReport);

export default router;
