import { Router } from 'express';
import { ebetlabPanelController } from '@/controllers/internal/panels/ebetlab.controller';
import { internalAuthMiddleware } from '@/middleware/internal/auth.middleware';

const router = Router();

/**
 * Internal Ebetlab Panel Routes
 *
 * These routes handle Ebetlab panel operations for internal users
 * All routes are prefixed with /internal/panels/ebetlab
 * All routes require internal authentication
 */

// POST /internal/panels/ebetlab - Login to Ebetlab
router.post('/', internalAuthMiddleware.authenticate, ebetlabPanelController.login);

export default router;
