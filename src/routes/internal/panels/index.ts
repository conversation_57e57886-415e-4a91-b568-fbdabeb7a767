import { Router } from 'express';
import ebetlabRoutes from './ebetlab.routes';
import pgdagurRoutes from './pgdagur.routes';

const router = Router();

/**
 * Internal Panel Routes
 *
 * These routes handle various panel operations for internal users
 * All routes are prefixed with /internal/panels
 */

// Health check endpoint
router.get('/', (_, res) => void res.sendStatus(200));

// Ebetlab panel routes
router.use('/ebetlab', ebetlabRoutes);

// PG Dagur panel routes
router.use('/pgdagur', pgdagurRoutes);

export default router;
