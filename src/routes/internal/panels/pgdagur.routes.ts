import { Router } from 'express';
import { pgDagurPanelController } from '@/controllers/internal/panels/pgdagur.controller';
import { internalAuthMiddleware } from '@/middleware/internal/auth.middleware';

const router = Router();

/**
 * Internal PG Dagur Panel Routes
 *
 * These routes handle PG Dagur panel operations for internal users
 * All routes are prefixed with /internal/panels/pgdagur
 * All routes require internal authentication
 */

// POST /internal/panels/pgdagur - Login to PG Dagur
router.post('/', internalAuthMiddleware.authenticate, pgDagurPanelController.login);

export default router;
