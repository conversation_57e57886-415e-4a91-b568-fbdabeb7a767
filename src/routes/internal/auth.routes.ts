import { Router } from 'express';
import { internalAuthController } from '@/controllers/internal/auth.controller';
import { internalAuthMiddleware } from '@/middleware/internal/auth.middleware';

const router = Router();

/**
 * Internal Authentication Routes
 *
 * These routes handle JWT-based authentication for internal users
 * All routes are prefixed with /internal/auth
 */

// Public routes (no authentication required)
router.post('/logins', internalAuthController.signIn);
router.post('/refresh-token-exchanges', internalAuthController.refreshToken);

// Protected routes (authentication required)
router.post('/password-changes', internalAuthMiddleware.authenticate, internalAuthController.changePassword);

export default router;
