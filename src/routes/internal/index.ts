import { Router } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes';
import panelRoutes from './panels';

const router = Router();

// Health check endpoint
router.get('/', (_, res) => void res.sendStatus(200));

// Authentication routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);

// Panel routes
router.use('/panels', panelRoutes);

export default router;
