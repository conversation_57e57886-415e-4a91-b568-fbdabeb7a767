import { Router } from 'express';
import { internalAuthController } from '@/controllers/internal/auth.controller';
import { internalUserCredentialsController } from '@/controllers/internal/userCredentials.controller';
import { internalAuthMiddleware } from '@/middleware/internal/auth.middleware';

const router = Router();

router.get('/me', internalAuthMiddleware.authenticate, internalAuthController.getCurrentUser);

// Credentials routes
router.get('/credentials', internalAuthMiddleware.authenticate, internalUserCredentialsController.getCredentials);
router.post('/credentials', internalAuthMiddleware.authenticate, internalUserCredentialsController.createOrUpdateCredentials);

export default router;
