/**
 * Slack Bot Registration Configuration
 * 
 * This file contains all Slack bot registrations for the application.
 * Each bot is registered with a single line using the registerSlackBot function.
 * 
 * To add a new Slack bot:
 * 1. Create a validator function in src/validators/
 * 2. Create a handler function in src/handlers/
 * 3. Import them here
 * 4. Add a registerSlackBot() call
 * 
 * The system will automatically:
 * - Create POST endpoints for each registered route
 * - Handle request validation using the provided validator
 * - Process requests and send Slack messages using the provided handler
 */

import { registerSlackBot } from './services/slackBot.service';

// Import validators
import { callServiceValidator } from './validators/callServiceValidator';
import { bonusRequestValidator } from './validators/bonusRequestValidator';
import { marketRequestValidator } from './validators/marketRequestValidator';
import { dailyReportsValidator } from './validators/dailyReportsValidator';
import { hourlyReportsValidator } from './validators/hourlyReportsValidator';

// Import handlers
import { callServiceHandler } from './handlers/callServiceHandler';
import { bonusRequestHandler } from './handlers/bonusRequestHandler';
import { marketRequestHandler } from './handlers/marketRequestHandler';
import { dailyReportsHandler } from './handlers/dailyReportsHandler';
import { hourlyReportsHandler } from './handlers/hourlyReportsHandler';

/**
 * Register all Slack bots
 * 
 * This function is called during application startup to register all
 * configured Slack bots with their routes, channels, validators, and handlers.
 */
export function registerAllSlackBots(): void {
  console.log('🤖 Registering Slack bots...');

  try {
    // Register Call Service Demand Bot
    // This bot handles call service requests and sends notifications to the call service channel
    const callServiceBotId = registerSlackBot(
      '/pronet/v1/call-requests',           // Route that triggers the bot
      'makro-call-talep',                   // Slack channel to send messages to
      callServiceValidator,                 // Validator function for request validation
      callServiceHandler                    // Handler function for processing and message formatting
    );

    console.log(`✅ Call Service Bot registered with ID: ${callServiceBotId}`);

    // Register Bonus Requests Bot
    const bonusBotId = registerSlackBot(
      '/pronet/v1/bonus-requests',          // Route for bonus requests
      'makro-bonus-talep',                   // Same channel as call service (as requested)
      bonusRequestValidator,                // Bonus request validator
      bonusRequestHandler                   // Bonus request handler
    );

    console.log(`✅ Bonus Request Bot registered with ID: ${bonusBotId}`);

    // Register Market Requests Bot
    const marketBotId = registerSlackBot(
      '/pronet/v1/market-requests',         // Route for market requests
      'makro-market-talep',                   // Same channel as other bots (as requested)
      marketRequestValidator,               // Market request validator
      marketRequestHandler                  // Market request handler
    );

    console.log(`✅ Market Request Bot registered with ID: ${marketBotId}`);

    // Register Daily Reports Bot
    const dailyReportsBotId = registerSlackBot(
      '/makrobet/internal/reports/daily',   // Route for daily reports
      'makro-call-talep',                      // Reports channel
      dailyReportsValidator,                // Daily reports validator
      dailyReportsHandler                   // Daily reports handler
    );

    console.log(`✅ Daily Reports Bot registered with ID: ${dailyReportsBotId}`);

    // Register Hourly Reports Bot
    const hourlyReportsBotId = registerSlackBot(
      '/makrobet/internal/reports/hourly',  // Route for hourly reports
      'makro-call-talep',                      // Reports channel
      hourlyReportsValidator,               // Hourly reports validator
      hourlyReportsHandler                  // Hourly reports handler
    );

    console.log(`✅ Hourly Reports Bot registered with ID: ${hourlyReportsBotId}`);

    // Example: Register Support Requests Bot (commented out - implement when needed)
    /*
    const supportBotId = registerSlackBot(
      '/support/v1/tickets',                // Route for support tickets
      'makro-support',                      // Support channel
      supportValidator,                     // Support validator (to be implemented)
      supportHandler                        // Support handler (to be implemented)
    );

    console.log(`✅ Support Bot registered with ID: ${supportBotId}`);
    */

    console.log('🎉 All Slack bots registered successfully');

  } catch (error) {
    console.error('❌ Failed to register Slack bots:', error);
    throw error;
  }
}

/**
 * Get information about all registered bots
 * 
 * This function can be used for debugging or monitoring purposes
 */
export function getRegisteredBotsInfo(): Array<{
  route: string;
  channel: string;
  description: string;
}> {
  return [
    {
      route: '/pronet/v1/call-requests',
      channel: 'makro-call-talep',
      description: 'Handles call service demand requests and sends customer information to Slack',
    },
    {
      route: '/pronet/v1/bonus-requests',
      channel: 'makro-call-talep',
      description: 'Handles bonus requests and sends simple notification to Slack',
    },
    {
      route: '/pronet/v1/market-requests',
      channel: 'makro-call-talep',
      description: 'Handles market product requests and sends customer information with product details to Slack',
    },
    {
      route: '/makrobet/internal/reports/daily',
      channel: 'makro-reports',
      description: 'Handles daily report generation requests and sends report summaries to Slack',
    },
    {
      route: '/makrobet/internal/reports/hourly',
      channel: 'makro-reports',
      description: 'Handles hourly report generation requests and sends real-time report summaries to Slack',
    },
    // Add more bot descriptions here as they are implemented
  ];
}

/**
 * Validate bot registration configuration
 * 
 * This function checks that all required validators and handlers are properly imported
 */
export function validateBotConfiguration(): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Check that validators are properly imported
  if (typeof callServiceValidator !== 'function') {
    errors.push('callServiceValidator is not a function or not properly imported');
  }

  if (typeof bonusRequestValidator !== 'function') {
    errors.push('bonusRequestValidator is not a function or not properly imported');
  }

  if (typeof marketRequestValidator !== 'function') {
    errors.push('marketRequestValidator is not a function or not properly imported');
  }

  if (typeof dailyReportsValidator !== 'function') {
    errors.push('dailyReportsValidator is not a function or not properly imported');
  }

  if (typeof hourlyReportsValidator !== 'function') {
    errors.push('hourlyReportsValidator is not a function or not properly imported');
  }

  // Check that handlers are properly imported
  if (typeof callServiceHandler !== 'function') {
    errors.push('callServiceHandler is not a function or not properly imported');
  }

  if (typeof bonusRequestHandler !== 'function') {
    errors.push('bonusRequestHandler is not a function or not properly imported');
  }

  if (typeof marketRequestHandler !== 'function') {
    errors.push('marketRequestHandler is not a function or not properly imported');
  }

  if (typeof dailyReportsHandler !== 'function') {
    errors.push('dailyReportsHandler is not a function or not properly imported');
  }

  if (typeof hourlyReportsHandler !== 'function') {
    errors.push('hourlyReportsHandler is not a function or not properly imported');
  }

  // Add more validation checks as more bots are added

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Development helper: List all bot configurations
 * 
 * This function is useful during development to see what bots are configured
 */
export function listBotConfigurations(): void {
  console.log('📋 Configured Slack Bots:');
  console.log('');

  const botsInfo = getRegisteredBotsInfo();
  
  botsInfo.forEach((bot, index) => {
    console.log(`${index + 1}. ${bot.description}`);
    console.log(`   Route: POST ${bot.route}`);
    console.log(`   Channel: #${bot.channel}`);
    console.log('');
  });

  if (botsInfo.length === 0) {
    console.log('   No bots configured');
  }
}

// Export the main registration function as default
export default registerAllSlackBots;
