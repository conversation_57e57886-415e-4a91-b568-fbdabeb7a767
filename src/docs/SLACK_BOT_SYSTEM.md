# Slack Bot System Documentation

## Overview

The Slack Bot System is a modular, extensible framework for creating Slack notification bots with minimal code. It provides a one-line registration API that automatically creates POST endpoints, handles validation, processes requests, and sends formatted messages to Slack channels.

## Key Features

- **One-Line Registration**: Register new bots with a single function call
- **Automatic Route Creation**: POST endpoints are automatically generated
- **Modular Architecture**: Separate validators and handlers for each bot
- **Type Safety**: Full TypeScript support with proper interfaces
- **Error Handling**: Comprehensive error handling and logging
- **Health Monitoring**: Built-in health check endpoints
- **No Environment Variables**: Channels are passed as arguments, not env vars

## Architecture

### Core Components

1. **SlackBotService** (`src/services/slackBot.service.ts`)
   - Central registry for all bots
   - Handles Slack API communication
   - Processes bot requests and responses

2. **Validators** (`src/validators/`)
   - Validate incoming request data
   - Return structured validation results
   - Extend BaseValidator for common utilities

3. **Handlers** (`src/handlers/`)
   - Process validated requests
   - Generate Slack messages
   - Handle business logic and external API calls

4. **Route Middleware** (`src/middleware/slackBotRoutes.ts`)
   - Automatically creates Express routes
   - Integrates with bot registry
   - Provides health check endpoints

5. **Registration Config** (`src/registerSlackBots.ts`)
   - Central configuration for all bots
   - One-line bot registrations
   - Bot information and validation

## Quick Start

### 1. Create a Validator

```typescript
// src/validators/myBotValidator.ts
import { Request } from 'express';
import { BaseValidator, ValidatorResult } from './index';

export const myBotValidator = (req: Request): ValidatorResult => {
  const { requiredField } = req.body;
  
  if (!requiredField) {
    return {
      isValid: false,
      error: 'Missing required field: requiredField',
    };
  }
  
  return { isValid: true };
};
```

### 2. Create a Handler

```typescript
// src/handlers/myBotHandler.ts
import { Request, Response } from 'express';
import { BaseHandler, HandlerResult } from './index';

export const myBotHandler = async (req: Request, res: Response): Promise<HandlerResult> => {
  try {
    // Process the request
    const { requiredField } = req.body;
    
    // Generate Slack message
    const message = `🔔 *New Request*\n\n• Field: ${requiredField}`;
    
    return BaseHandler.success(message);
  } catch (error) {
    return BaseHandler.error(`Processing failed: ${error.message}`);
  }
};
```

### 3. Register the Bot

```typescript
// src/registerSlackBots.ts
import { myBotValidator } from './validators/myBotValidator';
import { myBotHandler } from './handlers/myBotHandler';

export function registerAllSlackBots(): void {
  // Register your bot
  registerSlackBot(
    '/api/v1/my-bot',        // Route
    'my-slack-channel',      // Channel
    myBotValidator,          // Validator
    myBotHandler             // Handler
  );
}
```

### 4. Done!

Your bot is now available at `POST /api/v1/my-bot` and will automatically:
- Validate incoming requests
- Process the data
- Send formatted messages to `#my-slack-channel`

## Environment Variables

Only one environment variable is required:

```env
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
```

**Note**: `SLACK_BOT_CHANNEL` is no longer used. Channels are specified during bot registration.

## API Reference

### registerSlackBot()

```typescript
registerSlackBot(
  route: string,           // POST route (e.g., '/api/v1/my-bot')
  channel: string,         // Slack channel (e.g., 'my-channel')
  validator: SlackBotValidator,  // Validation function
  handler: SlackBotHandler       // Processing function
): string                  // Returns bot ID
```

### Validator Interface

```typescript
type SlackBotValidator = (req: Request) => {
  isValid: boolean;
  error?: string;
};
```

### Handler Interface

```typescript
type SlackBotHandler = (req: Request, res: Response) => Promise<{
  shouldSend: boolean;
  message?: string;
  error?: string;
}>;
```

## Existing Bots

### Call Service Bot
- **Route**: `POST /api/v1/pronet/v1/call-requests`
- **Channel**: `makro-call-talep`
- **Purpose**: Handles call service demand requests
- **Features**:
  - Proxies to third-party endpoint (pn17.pfnow.net)
  - Dynamically detects origin from request headers (e.g., makrobet683.com, makrobet685.com)
  - Fetches customer data from detected origin
  - Sends formatted customer information
  - Skips notifications for error responses (pending_demand, not_allowed_action, etc.)

### Bonus Request Bot
- **Route**: `POST /api/v1/pronet/v1/bonus-requests`
- **Channel**: `makro-call-talep`
- **Purpose**: Handles bonus requests from users
- **Features**:
  - Proxies to third-party endpoint (pn17.pfnow.net/api/promoTracker/demand)
  - Validates bonus request structure (id, type, token, code)
  - Sends simple notification with user ID and bonus ID
  - Returns original response from endpoint

## Health Check

Monitor the system health at:
```
GET /api/v1/slack-bots/health
```

Returns:
```json
{
  "success": true,
  "timestamp": "2025-01-09T...",
  "slackBotSystem": {
    "isConfigured": true,
    "totalBots": 2,
    "totalRoutes": 2,
    "registeredRoutes": ["/pronet/v1/call-requests", "/pronet/v1/bonus-requests"],
    "validation": {
      "isValid": true,
      "errors": [],
      "warnings": []
    }
  }
}
```

## Best Practices

### Validator Best Practices
- Use `BaseValidator` utilities for common validations
- Return descriptive error messages
- Validate all required fields and types
- Use `combineValidations()` for multiple checks

### Handler Best Practices
- Use `BaseHandler` utilities for consistent responses
- Handle external API calls with proper error handling
- Use `HandlerUtils` for common operations
- Log activities with `HandlerUtils.logActivity()`
- Return `skip()` for conditions that shouldn't send messages

### Message Formatting
- Use `formatSectionedMessage()` for structured messages
- Use `formatKeyValueList()` for data lists
- Use `bold()` for emphasis
- Keep messages concise and informative

## Troubleshooting

### Bot Not Responding
1. Check if bot is registered: `GET /api/v1/slack-bots/health`
2. Verify SLACK_BOT_TOKEN is set
3. Check validator is returning `isValid: true`
4. Check handler is returning `shouldSend: true`

### Validation Errors
- Check request body structure matches validator expectations
- Verify all required fields are present
- Check field types match validator requirements

### Slack Messages Not Sending
- Verify SLACK_BOT_TOKEN has correct permissions
- Check channel name doesn't include '#' prefix
- Verify bot has access to the target channel

## Migration from Old System

The old system used:
- Environment variable `SLACK_BOT_CHANNEL`
- Direct Slack service integration in controllers
- Hardcoded message formatting

The new system provides:
- Channel specification in registration
- Modular validators and handlers
- Automatic route generation
- Better error handling and logging

## Contributing

To add a new bot:
1. Create validator in `src/validators/`
2. Create handler in `src/handlers/`
3. Add registration in `src/registerSlackBots.ts`
4. Update this documentation

The system will automatically handle the rest!
