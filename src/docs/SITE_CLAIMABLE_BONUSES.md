# Site Claimable Bonuses System

This document describes the complete backend implementation for managing claimable bonuses on the rewards page.

## Overview

The Site Claimable Bonuses system allows administrators to configure which bonuses are displayed and claimable on the rewards page. It provides a slot-based system where specific bonuses can be assigned to predefined slots (slot_1, slot_2, slot_3) and controlled via API endpoints.

## Database Schema

### Table: `site_claimable_bonuses`
Located in the `pronet` schema.

| Column | Type | Description |
|--------|------|-------------|
| `id` | integer (PK) | Auto-incrementing primary key |
| `slotName` | text (unique) | Identifies the position/slot on rewards page |
| `bonusId` | integer (FK) | References the bonus that should be displayed |
| `isActive` | boolean | Whether this slot is currently active (default: true) |
| `createdAt` | timestamptz | Creation timestamp |
| `updatedAt` | timestamptz | Last update timestamp |

**Indexes:**
- Unique index on `slotName`
- Index on `isActive`
- Foreign key constraint to `pronet.bonuses(id)` with CASCADE

**Initial Data:**
The migration creates 3 initial slots: `slot_1`, `slot_2`, `slot_3` (initially inactive).

## API Endpoints

All endpoints are prefixed with `/api/pronet/v1/site-claimable-bonuses`

### GET `/api/pronet/v1/site-claimable-bonuses`
Lists all claimable bonus configurations.

**Response:**
```json
{
  "success": true,
  "message": "Site claimable bonus configurations retrieved successfully",
  "data": [
    {
      "id": 1,
      "slotName": "slot_1",
      "bonusId": 123,
      "isActive": true,
      "createdAt": "2025-01-23T...",
      "updatedAt": "2025-01-23T...",
      "bonus": {
        "id": 123,
        "name": "Welcome Bonus",
        "type": "freespin",
        "isActive": true,
        "expiresAt": null,
        "rules": [...]
      }
    }
  ]
}
```

### PUT `/api/pronet/v1/site-claimable-bonuses/:slotName`
Updates which bonus is assigned to a specific slot.

**Request Body:**
```json
{
  "bonusId": 123,
  "isActive": true  // optional
}
```

**Validations:**
- Slot must exist
- Bonus must exist and be active
- Bonus must not be expired
- Bonus must not be soft-deleted

### GET `/api/pronet/v1/site-claimable-bonuses/check/:bonusId`
Verifies if a specific bonus is currently claimable.

**Response:**
```json
{
  "success": true,
  "message": "Bonus claimable status checked successfully",
  "data": {
    "bonusId": 123,
    "isClaimable": true
  }
}
```

### GET `/api/pronet/v1/site-claimable-bonuses/claimable`
Gets all currently claimable bonuses (active slots with active, non-expired bonuses).

### PATCH `/api/pronet/v1/site-claimable-bonuses/:slotName/toggle`
Toggles the active status of a slot.

**Request Body:**
```json
{
  "isActive": false
}
```

## Service Layer

### `SiteClaimableBonusService`

**Key Methods:**
- `list()` - Get all configurations
- `findBySlotName(slotName)` - Get specific slot configuration
- `update(slotName, data)` - Update slot configuration
- `isClaimable(bonusId)` - Check if bonus is claimable
- `getClaimableBonuses()` - Get all currently claimable bonuses
- `validateBonusClaimable(bonusId)` - Validate bonus for claiming
- `toggleActive(slotName, isActive)` - Toggle slot status

**Validation Logic:**
- Ensures bonuses are active and not deleted
- Checks expiration dates
- Validates slot existence
- Prevents assignment of invalid bonuses

## Security Integration

### Bonus Claiming Validation

The system integrates with the existing `BonusService.claimTx()` method to enforce that bonuses can only be claimed if they exist in `site_claimable_bonuses` with `isActive = true`.

**Integration Point:**
```typescript
// In BonusService.claimTx()
if (data.source === 'rewards_page' || data.source === 'site_claimable') {
  const siteClaimableBonusService = new SiteClaimableBonusService();
  await siteClaimableBonusService.validateBonusClaimable(data.bonusId);
}
```

This ensures that:
1. Only bonuses configured in active slots can be claimed from the rewards page
2. The validation is source-aware (only applies to rewards page claims)
3. Existing bonus claiming from other sources (promocodes, etc.) is unaffected

## Files Created/Modified

### New Files:
- `src/entities/pronet/SiteClaimableBonus.ts` - Entity definition
- `src/services/pronet/siteClaimableBonus.service.ts` - Service layer
- `src/controllers/pronet/siteClaimableBonus.controller.ts` - API controller
- `src/migrations/1753200000001-CreateSiteClaimableBonusesTable.ts` - Database migration
- `src/test-site-claimable-bonuses.ts` - Test script
- `src/docs/SITE_CLAIMABLE_BONUSES.md` - This documentation

### Modified Files:
- `src/routes/pronet/public/v1.ts` - Added API routes
- `src/services/pronet/bonus.service.ts` - Added validation integration

## Usage Examples

### Setting up a claimable bonus:
```bash
# Update slot_1 to show bonus ID 123
curl -X PUT "http://localhost:3000/api/pronet/v1/site-claimable-bonuses/slot_1" \
  -H "Content-Type: application/json" \
  -d '{"bonusId": 123, "isActive": true}'
```

### Checking if a bonus is claimable:
```bash
curl "http://localhost:3000/api/pronet/v1/site-claimable-bonuses/check/123"
```

### Getting all claimable bonuses:
```bash
curl "http://localhost:3000/api/pronet/v1/site-claimable-bonuses/claimable"
```

## Testing

Run the test script to verify the implementation:
```bash
npx ts-node src/test-site-claimable-bonuses.ts
```

## Migration

To set up the database table:
```bash
npm run migration:run
```

The migration will create the table and insert 3 initial slots (slot_1, slot_2, slot_3) in an inactive state.

## Error Handling

The system provides comprehensive error handling for:
- Invalid slot names
- Non-existent bonuses
- Inactive or expired bonuses
- Database connection issues
- Validation errors

All errors follow the project's standard error response format with appropriate HTTP status codes.
