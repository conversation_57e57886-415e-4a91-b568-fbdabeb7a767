# Dagur Auto-Refresh System

## Overview

The Dagur Auto-Refresh System provides automatic session management for the PG Dagur API client, ensuring continuous authentication without manual intervention. The system refreshes sessions every 5 minutes and includes error-based retry logic.

## Key Features

### 1. Time-Based Auto Refresh
- **Refresh Interval**: 5 minutes (300,000 milliseconds)
- **Proactive Refresh**: Sessions are refreshed before they expire
- **Logging**: Detailed logging shows remaining time and refresh schedules

### 2. Error-Based Recovery
- **Authentication Error Detection**: Detects "Unauthorized" and login redirect errors
- **Automatic Retry**: Up to 3 retries with forced re-authentication
- **Session Reset**: Clears invalid sessions and forces fresh login

### 3. Multi-Layer Authentication
- **Main Session**: Primary Dagur session with cookies
- **Accounting Token**: Separate token for accounting API calls
- **Bet Reports Token**: Separate bearer token for bet reports API

## Architecture

### Core Components

#### 1. PGDagurAdminHttpClient
```typescript
class PGDagurAdminHttpClient {
  private session: PronetDagurSession | null = null;
  private lastLoginTime: number = 0;
  private readonly LOGIN_INTERVAL = 5 * 60 * 1000; // 5 minutes
}
```

#### 2. AccountingApiClient
```typescript
class AccountingApiClient {
  private accessToken: string | null = null;
  private lastTokenTime: number = 0;
  private readonly TOKEN_INTERVAL = 5 * 60 * 1000; // 5 minutes
}
```

#### 3. BetReportsApiClient
```typescript
class BetReportsApiClient {
  private bearerToken: string | null = null;
  private lastTokenTime: number = 0;
  private readonly TOKEN_INTERVAL = 5 * 60 * 1000; // 5 minutes
}
```

## Configuration

### Environment Variables
```bash
PG_DAGUR_USERNAME=your_username
PG_DAGUR_PASSWORD=your_password
PG_DAGUR_OTP_SECRET=your_otp_secret
```

### TOTP Configuration
- **Period**: 30 seconds
- **Algorithm**: SHA-1 (default)
- **Digits**: 6 (default)

## Usage Examples

### Basic Usage
```typescript
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { AccountingLoginRequest } from '@/network/pg-dagur/requests/auth/AccountingLoginRequest';

// The client automatically handles authentication
const request = new AccountingLoginRequest();
const response = await pgDagurAdminHttpClient.makeRequest(request);
```

### Manual Refresh
```typescript
// Force refresh via API endpoint
POST /api/makroz/auth/refresh-dagur-session

// Or programmatically
await pgDagurAdminHttpClient.loginWithOtpSecret(username, password, otpSecret);
```

## Logging Output

### Successful Authentication
```
🔐 Starting Dagur admin login process...
🔐 Generating Dagur TOTP from secret: ABCD...
🔐 Dagur OTP Generated: 123456
✅ Dagur admin login successful, session stored
🕐 Next Dagur login required at: 2025-01-18T10:35:00.000Z
```

### Session Validation
```
🔐 Dagur session still valid for 3m 45s
```

### Auto Refresh
```
🔐 5 minutes since last Dagur login, re-login required
🔐 Starting Dagur admin login process...
✅ Dagur admin login successful, session stored
```

### Error Recovery
```
🔄 Dagur authentication error detected, forcing re-login...
🔐 Starting Dagur admin login process...
✅ Dagur admin login successful, session stored
```

## API Endpoints

### Manual Refresh Endpoint
- **URL**: `POST /api/makroz/auth/refresh-dagur-session`
- **Description**: Forces a fresh Dagur session refresh
- **Response**: Success confirmation with timestamp

```json
{
  "success": true,
  "message": "Dagur session refreshed successfully",
  "data": {
    "message": "Session refreshed and ready for use"
  },
  "timestamp": "2025-01-18T10:30:00.000Z"
}
```

## Testing

### Demonstration Script
```bash
# Run full demonstration
ts-node src/scripts/test-dagur-auto-refresh.ts

# Show system status
ts-node src/scripts/test-dagur-auto-refresh.ts --status

# Show help
ts-node src/scripts/test-dagur-auto-refresh.ts --help
```

### Test Scenarios
1. **Initial Login**: First authentication and session creation
2. **Cached Session**: Immediate requests using existing session
3. **Auto Refresh**: Forced expiry and automatic refresh
4. **Rapid Requests**: Multiple concurrent requests using same session
5. **Timing Information**: Session timing and refresh schedules

## Error Handling

### Common Errors
- **Missing Credentials**: Environment variables not set
- **Invalid OTP**: TOTP generation or timing issues
- **Network Errors**: Connection or timeout issues
- **Authentication Failures**: Invalid credentials or expired sessions

### Error Recovery
- **Automatic Retry**: Up to 3 attempts with exponential backoff
- **Session Reset**: Clears invalid sessions and forces fresh login
- **Detailed Logging**: Error messages with context and suggestions

## Monitoring

### Health Checks
- Session validity status
- Time until next refresh
- Environment configuration validation
- Authentication error tracking

### Metrics
- Login frequency
- Session duration
- Error rates
- Retry attempts

## Comparison with EbetLab System

| Feature | EbetLab | Dagur |
|---------|---------|-------|
| Refresh Interval | 6 hours | 5 minutes |
| Authentication Method | JWT Token | Session Cookies |
| Sub-tokens | None | Accounting + Bet Reports |
| Error Detection | 401 HTTP status | Redirect detection |
| TOTP Period | 30 seconds | 30 seconds |

## Best Practices

1. **Environment Security**: Store credentials securely
2. **Error Monitoring**: Monitor authentication failures
3. **Rate Limiting**: Respect API rate limits
4. **Logging**: Enable detailed logging for debugging
5. **Testing**: Regular testing of auto-refresh functionality

## Troubleshooting

### Common Issues
1. **OTP Sync Issues**: Check system time synchronization
2. **Credential Errors**: Verify environment variables
3. **Network Issues**: Check connectivity to dagur.pgbo.io
4. **Session Expiry**: Monitor refresh timing and intervals

### Debug Steps
1. Check environment variables
2. Verify OTP generation
3. Test manual login
4. Monitor session timing
5. Check error logs
