import app from './app';
import { initializeDatabase, closeDatabase } from './database/connection';

const PORT = parseInt(process.env['PORT'] || '3000');
const HOST = process.env['HOST'] || '0.0.0.0';

async function startServer() {
  try {
    // Initialize database connection
    await initializeDatabase();

    const server = app.listen(PORT, HOST, () => {
      console.log(`🚀 Server running on http://${HOST}:${PORT}`);
      console.log(`📝 Environment: ${process.env['NODE_ENV'] || 'development'}`);
      console.log(`🕐 Started at: ${new Date().toISOString()}`);
    });

    return server;
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

const serverPromise = startServer();

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  const server = await serverPromise;
  server.close(async () => {
    await closeDatabase();
    console.log('✅ Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  const server = await serverPromise;
  server.close(async () => {
    await closeDatabase();
    console.log('✅ Process terminated');
    process.exit(0);
  });
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  const server = await serverPromise;
  server.close(async () => {
    await closeDatabase();
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  console.error('❌ Uncaught Exception:', error);
  const server = await serverPromise;
  server.close(async () => {
    await closeDatabase();
    process.exit(1);
  });
});

export default serverPromise;
