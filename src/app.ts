import 'reflect-metadata';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import healthRoutes from '@/routes/health';
import apiRoutes from '@/routes/api';
import authRoutes from '@/routes/auth';
import debugRoutes from '@/routes/debug';
import pronetRoutes from '@/routes/pronet';
import pronetRoutesV1 from '@/routes/pronet/public/v1';
import pronetPrivateRoutesV1 from '@/routes/pronet/private/v1';
import pgDagurRoutes from '@/routes/pg-dagur';
import pgWebRoutes from '@/routes/pg-web';
import pgCasinoTraderRoutes from '@/routes/pg-ct';

// Load environment variables
dotenv.config();

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
if (process.env['ALLOWED_ORIGINS']) {
  // const allowedOrigins = process.env['ALLOWED_ORIGINS'].split(',');
  // app.use(
  //   cors({
  //     origin: allowedOrigins,
  //     credentials: true,
  //   }),
  // );
  app.use(
    cors({
      origin: '*',
      allowedHeaders: '*',
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    }),
  );
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use(requestLogger);

// Routes
app.use('/health', healthRoutes);
// Debug routes must be mounted before API routes to catch /debug/* patterns
app.use('/debug', debugRoutes);
// Pronet routes for Pronet API proxy
app.use('/api/pronet/v1', pronetRoutesV1);
app.use('/private/api/pronet/v1', pronetPrivateRoutesV1);
// @todo remove
app.use('/pronet', pronetRoutes);
app.use('/api/pg-dagur', pgDagurRoutes);
app.use('/api/pg-ct', pgCasinoTraderRoutes);
app.use('/api/pg-web', pgWebRoutes);
app.use('/api', apiRoutes);
app.use('/auth', authRoutes);

// 404 handler
app.use('/{*splat}', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl,
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

export default app;
