import { Request } from 'express';
import { BaseValidator, ValidatorResult } from './index';

/**
 * Hourly Report Request Interface
 *
 * Defines the expected structure of hourly report requests
 */
export interface HourlyReportRequest {
  baseDate?: string; // ISO date string for the hour to report on
  reportType?: string; // Legacy field, kept for compatibility
  hour?: number;
  date?: string;
  includeRealTime?: boolean;
  granularity?: 'basic' | 'detailed' | 'comprehensive';
  metadata?: {
    requestedBy?: string;
    department?: string;
    alertThreshold?: number;
    [key: string]: any;
  };
}

/**
 * Hourly Reports Validator
 *
 * Validates hourly report requests according to specification:
 * - reportType: Must be non-empty string (type of report requested)
 * - hour: Optional integer 0-23 (specific hour, defaults to current hour if not provided)
 * - date: Optional ISO date string (defaults to current date if not provided)
 * - includeRealTime: Optional boolean (whether to include real-time data)
 * - granularity: Optional enum ('basic' | 'detailed' | 'comprehensive', defaults to 'basic')
 * - metadata: Optional object containing additional request information
 */
export class HourlyReportsValidator extends BaseValidator {
  /**
   * Validate hourly report request
   *
   * @param req - Express request object
   * @returns Validation result
   */
  static validate(req: Request): ValidatorResult {
    console.log('🔍 Validating hourly report request...');
    console.log('📥 Request query:', JSON.stringify(req.query, null, 2));
    console.log('📥 Request body:', JSON.stringify(req.body, null, 2));

    const { baseDate } = req.query;
    const { reportType, hour, date, includeRealTime, granularity, metadata } = req.body || {};

    // Validate baseDate from query parameters (new required field)
    if (baseDate) {
      const baseDateValidation = this.validateType(baseDate, 'string', 'baseDate');
      if (!baseDateValidation.isValid) {
        return baseDateValidation;
      }

      // Validate baseDate format (ISO date check)
      if (!this.isValidDateString(baseDate as string)) {
        return {
          isValid: false,
          error: 'Field baseDate must be a valid ISO date string (YYYY-MM-DDTHH:mm:ss.sssZ)',
        };
      }
    }

    // Legacy validation for backward compatibility
    if (reportType) {
      const fieldValidation = this.validateFieldTypes({ reportType, hour, date, includeRealTime, granularity, metadata });
      if (!fieldValidation.isValid) {
        return fieldValidation;
      }
    }

    console.log('✅ Hourly report request validation passed');
    return { isValid: true };
  }

  /**
   * Validate required fields are present
   */
  private static validateRequiredFields(data: { reportType: any }): ValidatorResult {
    const requiredValidation = this.validateRequired(data.reportType, 'reportType');
    if (!requiredValidation.isValid) {
      return requiredValidation;
    }

    return { isValid: true };
  }

  /**
   * Validate field types and constraints
   */
  private static validateFieldTypes(data: {
    reportType: any;
    hour?: any;
    date?: any;
    includeRealTime?: any;
    granularity?: any;
    metadata?: any;
  }): ValidatorResult {
    // Validate reportType field
    const reportTypeValidation = this.combineValidations(
      this.validateType(data.reportType, 'string', 'reportType'),
      this.validateRequired(data.reportType.trim(), 'reportType')
    );
    if (!reportTypeValidation.isValid) {
      return reportTypeValidation;
    }

    // Validate optional hour field
    if (data.hour !== undefined) {
      const hourValidation = this.validateType(data.hour, 'number', 'hour');
      if (!hourValidation.isValid) {
        return hourValidation;
      }

      // Validate hour range (0-23)
      if (!Number.isInteger(data.hour) || data.hour < 0 || data.hour > 23) {
        return {
          isValid: false,
          error: 'Field hour must be an integer between 0 and 23',
        };
      }
    }

    // Validate optional date field
    if (data.date !== undefined) {
      const dateValidation = this.validateType(data.date, 'string', 'date');
      if (!dateValidation.isValid) {
        return dateValidation;
      }

      // Validate date format (basic ISO date check)
      if (data.date && !this.isValidDateString(data.date)) {
        return {
          isValid: false,
          error: 'Field date must be a valid ISO date string (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ)',
        };
      }
    }

    // Validate optional includeRealTime field
    if (data.includeRealTime !== undefined) {
      const realTimeValidation = this.validateType(data.includeRealTime, 'boolean', 'includeRealTime');
      if (!realTimeValidation.isValid) {
        return realTimeValidation;
      }
    }

    // Validate optional granularity field
    if (data.granularity !== undefined) {
      const granularityValidation = this.validateType(data.granularity, 'string', 'granularity');
      if (!granularityValidation.isValid) {
        return granularityValidation;
      }

      if (!['basic', 'detailed', 'comprehensive'].includes(data.granularity)) {
        return {
          isValid: false,
          error: 'Field granularity must be one of: "basic", "detailed", "comprehensive"',
        };
      }
    }

    // Validate optional metadata field
    if (data.metadata !== undefined) {
      const metadataValidation = this.validateType(data.metadata, 'object', 'metadata');
      if (!metadataValidation.isValid) {
        return metadataValidation;
      }

      // Validate alertThreshold if present in metadata
      if (data.metadata.alertThreshold !== undefined) {
        const thresholdValidation = this.validateType(data.metadata.alertThreshold, 'number', 'metadata.alertThreshold');
        if (!thresholdValidation.isValid) {
          return thresholdValidation;
        }
      }
    }

    return { isValid: true };
  }

  /**
   * Basic date string validation
   */
  private static isValidDateString(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && dateString.length >= 10;
  }

  /**
   * Extract validated hourly report data from request
   *
   * @param req - Express request object (assumed to be validated)
   * @returns Typed hourly report request data
   */
  static extractHourlyReportData(req: Request): HourlyReportRequest {
    const { baseDate } = req.query;
    const { reportType, hour, date, includeRealTime, granularity, metadata } = req.body || {};

    return {
      baseDate: baseDate as string || undefined,
      reportType: reportType || 'transactions',
      hour: hour !== undefined ? hour : undefined,
      date: date || undefined,
      includeRealTime: includeRealTime || false,
      granularity: granularity || 'basic',
      metadata: metadata || undefined,
    };
  }
}

/**
 * Exported validator function for use with registerSlackBot
 */
export const hourlyReportsValidator = (req: Request): ValidatorResult => {
  return HourlyReportsValidator.validate(req);
};
