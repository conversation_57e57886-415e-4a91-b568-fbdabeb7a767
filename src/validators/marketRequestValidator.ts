import { Request } from 'express';
import { BaseValidator, ValidatorResult } from './index';

/**
 * Market Request Interface
 * 
 * Defines the expected structure of market requests
 */
export interface MarketRequest {
  id: number;
  quantity: number;
  token: string;
  pft: string;
  cid: string;
}

/**
 * Market Request Response Interface
 * 
 * Defines the expected structure of market request responses from pn17.pfnow.net
 */
export interface MarketRequestResponse {
  result: string;
  [key: string]: any; // Allow for additional fields in response
}

/**
 * Market Request Validator
 * 
 * Validates market requests according to the specification:
 * - id: Must be a positive integer (market product identifier)
 * - quantity: Must be a positive integer (quantity to order)
 * - token: Must be non-empty string (user session authentication token)
 * - pft: Must be non-empty string (payment/transaction token)
 * - cid: Must be non-empty string (customer identification)
 */
export class MarketRequestValidator extends BaseValidator {
  /**
   * Validate market request
   * 
   * @param req - Express request object
   * @returns Validation result
   */
  static validate(req: Request): ValidatorResult {
    console.log('🔍 Validating market request...');
    console.log('📥 Request body:', JSON.stringify(req.body, null, 2));

    // Validate request body exists
    const bodyValidation = this.validateRequestBody(req);
    if (!bodyValidation.isValid) {
      return bodyValidation;
    }

    const { id, quantity, token, pft, cid } = req.body;

    // Validate all required fields are present
    const requiredFieldsValidation = this.validateRequiredFields({ id, quantity, token, pft, cid });
    if (!requiredFieldsValidation.isValid) {
      return requiredFieldsValidation;
    }

    // Validate field types and values
    const fieldValidation = this.validateFieldTypes({ id, quantity, token, pft, cid });
    if (!fieldValidation.isValid) {
      return fieldValidation;
    }

    console.log('✅ Market request validation passed');
    return { isValid: true };
  }

  /**
   * Validate that all required fields are present and not null/undefined
   */
  private static validateRequiredFields(data: Partial<MarketRequest>): ValidatorResult {
    const requiredFields = ['id', 'quantity', 'token', 'pft', 'cid'];
    
    for (const field of requiredFields) {
      const validation = this.validateRequired(data[field as keyof MarketRequest], field);
      if (!validation.isValid) {
        return validation;
      }
    }

    return { isValid: true };
  }

  /**
   * Validate field types and specific values
   */
  private static validateFieldTypes(data: MarketRequest): ValidatorResult {
    // Validate id field
    const idTypeValidation = this.validateType(data.id, 'number', 'id');
    if (!idTypeValidation.isValid) {
      return idTypeValidation;
    }

    if (!Number.isInteger(data.id) || data.id <= 0) {
      return {
        isValid: false,
        error: 'Field id must be a positive integer',
      };
    }

    // Validate quantity field
    const quantityTypeValidation = this.validateType(data.quantity, 'number', 'quantity');
    if (!quantityTypeValidation.isValid) {
      return quantityTypeValidation;
    }

    if (!Number.isInteger(data.quantity) || data.quantity <= 0) {
      return {
        isValid: false,
        error: 'Field quantity must be a positive integer',
      };
    }

    // Validate token field
    const tokenValidation = this.combineValidations(
      this.validateType(data.token, 'string', 'token'),
      this.validateRequired(data.token.trim(), 'token')
    );
    if (!tokenValidation.isValid) {
      return tokenValidation;
    }

    // Validate pft field
    const pftValidation = this.combineValidations(
      this.validateType(data.pft, 'string', 'pft'),
      this.validateRequired(data.pft.trim(), 'pft')
    );
    if (!pftValidation.isValid) {
      return pftValidation;
    }

    // Validate cid field
    const cidValidation = this.combineValidations(
      this.validateType(data.cid, 'string', 'cid'),
      this.validateRequired(data.cid.trim(), 'cid')
    );
    if (!cidValidation.isValid) {
      return cidValidation;
    }

    return { isValid: true };
  }

  /**
   * Extract validated market request data from request
   * 
   * @param req - Express request object (assumed to be validated)
   * @returns Typed market request data
   */
  static extractMarketRequestData(req: Request): MarketRequest {
    return {
      id: req.body.id,
      quantity: req.body.quantity,
      token: req.body.token,
      pft: req.body.pft,
      cid: req.body.cid,
    };
  }
}

/**
 * Exported validator function for use with registerSlackBot
 */
export const marketRequestValidator = (req: Request): ValidatorResult => {
  return MarketRequestValidator.validate(req);
};
