import { Request } from 'express';

/**
 * Slack Bot Validator Result
 * 
 * Standard return type for all validator functions
 */
export interface ValidatorResult {
  isValid: boolean;
  error?: string;
}

/**
 * Slack Bot Validator Function Type
 * 
 * All validator functions must implement this interface for consistency
 */
export type SlackBotValidator = (req: Request) => ValidatorResult;

/**
 * Base Validator Class
 * 
 * Provides common validation utilities that can be extended by specific validators
 */
export abstract class BaseValidator {
  /**
   * Validate that a field exists and is not empty
   */
  protected static validateRequired(value: any, fieldName: string): ValidatorResult {
    if (value === undefined || value === null) {
      return {
        isValid: false,
        error: `Missing required field: ${fieldName}`,
      };
    }

    if (typeof value === 'string' && value.trim() === '') {
      return {
        isValid: false,
        error: `Field ${fieldName} cannot be empty`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate that a field is of the expected type
   */
  protected static validateType(value: any, expectedType: string, fieldName: string): ValidatorResult {
    const actualType = typeof value;
    
    if (actualType !== expectedType) {
      return {
        isValid: false,
        error: `Field ${fieldName} must be of type ${expectedType}, got ${actualType}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate that a number field has a specific value
   */
  protected static validateExactValue(value: any, expectedValue: any, fieldName: string): ValidatorResult {
    if (value !== expectedValue) {
      return {
        isValid: false,
        error: `Field ${fieldName} must be exactly ${expectedValue}, got ${value}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate that a string matches a pattern
   */
  protected static validatePattern(value: string, pattern: RegExp, fieldName: string, description: string): ValidatorResult {
    if (!pattern.test(value)) {
      return {
        isValid: false,
        error: `Field ${fieldName} ${description}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Combine multiple validation results
   */
  protected static combineValidations(...results: ValidatorResult[]): ValidatorResult {
    for (const result of results) {
      if (!result.isValid) {
        return result;
      }
    }

    return { isValid: true };
  }

  /**
   * Validate request body exists
   */
  protected static validateRequestBody(req: Request): ValidatorResult {
    if (!req.body || typeof req.body !== 'object') {
      return {
        isValid: false,
        error: 'Request body is required and must be a valid JSON object',
      };
    }

    return { isValid: true };
  }

  /**
   * Validate request headers contain required header
   */
  protected static validateRequiredHeader(req: Request, headerName: string): ValidatorResult {
    const headerValue = req.headers[headerName.toLowerCase()];
    
    if (!headerValue) {
      return {
        isValid: false,
        error: `Missing required header: ${headerName}`,
      };
    }

    return { isValid: true };
  }
}

/**
 * Common validation utilities
 */
export class ValidationUtils {
  /**
   * Validate email format
   */
  static validateEmail(email: string): ValidatorResult {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailPattern.test(email)) {
      return {
        isValid: false,
        error: 'Invalid email format',
      };
    }

    return { isValid: true };
  }

  /**
   * Validate phone number format (basic validation)
   */
  static validatePhone(phone: string): ValidatorResult {
    const phonePattern = /^\+?[\d\s\-\(\)]{10,}$/;
    
    if (!phonePattern.test(phone)) {
      return {
        isValid: false,
        error: 'Invalid phone number format',
      };
    }

    return { isValid: true };
  }

  /**
   * Validate that a value is within a numeric range
   */
  static validateRange(value: number, min: number, max: number, fieldName: string): ValidatorResult {
    if (value < min || value > max) {
      return {
        isValid: false,
        error: `Field ${fieldName} must be between ${min} and ${max}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate that a string has minimum/maximum length
   */
  static validateLength(value: string, minLength: number, maxLength: number, fieldName: string): ValidatorResult {
    if (value.length < minLength) {
      return {
        isValid: false,
        error: `Field ${fieldName} must be at least ${minLength} characters long`,
      };
    }

    if (value.length > maxLength) {
      return {
        isValid: false,
        error: `Field ${fieldName} must be no more than ${maxLength} characters long`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate that a value is one of the allowed values
   */
  static validateEnum(value: any, allowedValues: any[], fieldName: string): ValidatorResult {
    if (!allowedValues.includes(value)) {
      return {
        isValid: false,
        error: `Field ${fieldName} must be one of: ${allowedValues.join(', ')}`,
      };
    }

    return { isValid: true };
  }
}

/**
 * Re-export types for convenience
 */
export type { SlackBotValidator } from '../services/slackBot.service';
