import { Request } from 'express';
import { BaseValidator, ValidatorResult } from './index';

/**
 * Call Service Request Interface
 * 
 * Defines the expected structure of call service demand requests
 */
export interface CallServiceRequest {
  id: number;
  type: number;
  token: string;
  code: string;
  time: string;
}

/**
 * Call Service Validator
 * 
 * Validates call service demand requests according to the specification:
 * - id: Must be a positive integer (time slot identifier)
 * - type: Must be exactly 3 (request type identifier for call service demands)
 * - token: Must be non-empty string (user session authentication token)
 * - code: Must be non-empty string (customer identification code)
 * - time: Must be non-empty string (human-readable time slot representation)
 */
export class CallServiceValidator extends BaseValidator {
  /**
   * Validate call service demand request
   * 
   * @param req - Express request object
   * @returns Validation result
   */
  static validate(req: Request): ValidatorResult {
    console.log('🔍 Validating call service request...');
    console.log('📥 Request body:', JSON.stringify(req.body, null, 2));

    // Validate request body exists
    const bodyValidation = this.validateRequestBody(req);
    if (!bodyValidation.isValid) {
      return bodyValidation;
    }

    const { id, type, token, code, time } = req.body;

    // Validate all required fields are present
    const requiredFieldsValidation = this.validateRequiredFields({ id, type, token, code, time });
    if (!requiredFieldsValidation.isValid) {
      return requiredFieldsValidation;
    }

    // Validate field types and values
    const fieldValidation = this.validateFieldTypes({ id, type, token, code, time });
    if (!fieldValidation.isValid) {
      return fieldValidation;
    }

    console.log('✅ Call service request validation passed');
    return { isValid: true };
  }

  /**
   * Validate that all required fields are present
   */
  private static validateRequiredFields(data: any): ValidatorResult {
    const requiredFields = ['id', 'type', 'token', 'code', 'time'];
    
    for (const field of requiredFields) {
      const validation = this.validateRequired(data[field], field);
      if (!validation.isValid) {
        return validation;
      }
    }

    return { isValid: true };
  }

  /**
   * Validate field types and specific values
   */
  private static validateFieldTypes(data: CallServiceRequest): ValidatorResult {
    // Validate id field
    const idTypeValidation = this.validateType(data.id, 'number', 'id');
    if (!idTypeValidation.isValid) {
      return idTypeValidation;
    }

    if (!Number.isInteger(data.id) || data.id <= 0) {
      return {
        isValid: false,
        error: 'Field id must be a positive integer',
      };
    }

    // Validate type field
    const typeValidation = this.combineValidations(
      this.validateType(data.type, 'number', 'type'),
      this.validateExactValue(data.type, 3, 'type')
    );
    if (!typeValidation.isValid) {
      return typeValidation;
    }

    // Validate token field
    const tokenValidation = this.combineValidations(
      this.validateType(data.token, 'string', 'token'),
      this.validateRequired(data.token.trim(), 'token')
    );
    if (!tokenValidation.isValid) {
      return tokenValidation;
    }

    // Validate code field
    const codeValidation = this.combineValidations(
      this.validateType(data.code, 'string', 'code'),
      this.validateRequired(data.code.trim(), 'code')
    );
    if (!codeValidation.isValid) {
      return codeValidation;
    }

    // Validate time field
    const timeValidation = this.combineValidations(
      this.validateType(data.time, 'string', 'time'),
      this.validateRequired(data.time.trim(), 'time')
    );
    if (!timeValidation.isValid) {
      return timeValidation;
    }

    // Optional: Validate time format (HH:MM - HH:MM)
    const timeFormatValidation = this.validateTimeFormat(data.time);
    if (!timeFormatValidation.isValid) {
      console.warn(`⚠️ Time format warning: ${timeFormatValidation.error}`);
      // Don't fail validation for time format, just warn
    }

    return { isValid: true };
  }

  /**
   * Validate time format (optional - warns but doesn't fail)
   */
  private static validateTimeFormat(time: string): ValidatorResult {
    // Expected format: "HH:MM - HH:MM" (e.g., "10:00 - 11:00")
    const timePattern = /^\d{1,2}:\d{2}\s*-\s*\d{1,2}:\d{2}$/;
    
    if (!timePattern.test(time)) {
      return {
        isValid: false,
        error: 'Time should be in format "HH:MM - HH:MM"',
      };
    }

    return { isValid: true };
  }

  /**
   * Extract validated call service data from request
   * 
   * @param req - Express request object (assumed to be validated)
   * @returns Typed call service request data
   */
  static extractCallServiceData(req: Request): CallServiceRequest {
    return {
      id: req.body.id,
      type: req.body.type,
      token: req.body.token,
      code: req.body.code,
      time: req.body.time,
    };
  }
}

/**
 * Exported validator function for use with registerSlackBot
 */
export const callServiceValidator = (req: Request): ValidatorResult => {
  return CallServiceValidator.validate(req);
};
