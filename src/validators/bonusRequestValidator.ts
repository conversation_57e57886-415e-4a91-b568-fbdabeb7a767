import { Request } from 'express';
import { BaseValidator, ValidatorResult } from './index';

/**
 * Bonus Request Interface
 *
 * Defines the expected structure of bonus requests
 */
export interface BonusRequest {
  id: number;
  type: number;
  token?: string;
  code: string;
  metadata?: {
    bonus_name: string;
  };
}

/**
 * Bonus Request Validator
 *
 * Validates bonus requests according to specification:
 * - id: Must be a positive integer (bonus identifier)
 * - type: Must be exactly 1 (request type identifier for bonus demands)
 * - token: Optional string (user session authentication token, can be empty)
 * - code: Must be non-empty string (customer identification code)
 * - metadata: Optional object containing bonus_name for display purposes
 */
export class BonusRequestValidator extends BaseValidator {
  /**
   * Validate bonus request
   * 
   * @param req - Express request object
   * @returns Validation result
   */
  static validate(req: Request): ValidatorResult {
    console.log('🔍 Validating bonus request...');
    console.log('📥 Request body:', JSON.stringify(req.body, null, 2));

    // Validate request body exists
    const bodyValidation = this.validateRequestBody(req);
    if (!bodyValidation.isValid) {
      return bodyValidation;
    }

    const { id, type, token, code, metadata } = req.body;

    // Validate all required fields are present
    const requiredFieldsValidation = this.validateRequiredFields({ id, type, code });
    if (!requiredFieldsValidation.isValid) {
      return requiredFieldsValidation;
    }

    // Validate field types and values
    const fieldValidation = this.validateFieldTypes({ id, type, token, code, metadata });
    if (!fieldValidation.isValid) {
      return fieldValidation;
    }

    console.log('✅ Bonus request validation passed');
    return { isValid: true };
  }

  /**
   * Validate that all required fields are present
   */
  private static validateRequiredFields(data: any): ValidatorResult {
    const requiredFields = ['id', 'type', 'code'];

    for (const field of requiredFields) {
      const validation = this.validateRequired(data[field], field);
      if (!validation.isValid) {
        return validation;
      }
    }

    return { isValid: true };
  }

  /**
   * Validate field types and specific values
   */
  private static validateFieldTypes(data: BonusRequest): ValidatorResult {
    // Validate id field
    const idTypeValidation = this.validateType(data.id, 'number', 'id');
    if (!idTypeValidation.isValid) {
      return idTypeValidation;
    }

    if (!Number.isInteger(data.id) || data.id <= 0) {
      return {
        isValid: false,
        error: 'Field id must be a positive integer',
      };
    }

    // Validate type field
    const typeValidation = this.combineValidations(
      this.validateType(data.type, 'number', 'type'),
      this.validateExactValue(data.type, 1, 'type')
    );
    if (!typeValidation.isValid) {
      return typeValidation;
    }

    // Validate token field (optional)
    if (data.token !== undefined) {
      const tokenValidation = this.validateType(data.token, 'string', 'token');
      if (!tokenValidation.isValid) {
        return tokenValidation;
      }
    }

    // Validate metadata field (optional)
    if (data.metadata !== undefined) {
      const metadataValidation = this.validateType(data.metadata, 'object', 'metadata');
      if (!metadataValidation.isValid) {
        return metadataValidation;
      }

      // Validate bonus_name within metadata if metadata exists
      if (data.metadata.bonus_name !== undefined) {
        const bonusNameValidation = this.combineValidations(
          this.validateType(data.metadata.bonus_name, 'string', 'metadata.bonus_name'),
          this.validateRequired(data.metadata.bonus_name.trim(), 'metadata.bonus_name')
        );
        if (!bonusNameValidation.isValid) {
          return bonusNameValidation;
        }
      }
    }

    // Validate code field
    const codeValidation = this.combineValidations(
      this.validateType(data.code, 'string', 'code'),
      this.validateRequired(data.code.trim(), 'code')
    );
    if (!codeValidation.isValid) {
      return codeValidation;
    }

    return { isValid: true };
  }

  /**
   * Extract validated bonus data from request
   * 
   * @param req - Express request object (assumed to be validated)
   * @returns Typed bonus request data
   */
  static extractBonusData(req: Request): BonusRequest {
    return {
      id: req.body.id,
      type: req.body.type,
      token: req.body.token || undefined,
      code: req.body.code,
      metadata: req.body.metadata || undefined,
    };
  }
}

/**
 * Exported validator function for use with registerSlackBot
 */
export const bonusRequestValidator = (req: Request): ValidatorResult => {
  return BonusRequestValidator.validate(req);
};
