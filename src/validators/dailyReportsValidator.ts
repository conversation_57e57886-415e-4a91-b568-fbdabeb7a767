import { Request } from 'express';
import { BaseValidator, ValidatorResult } from './index';

/**
 * Daily Report Request Interface
 *
 * Defines the expected structure of daily report requests
 */
export interface DailyReportRequest {
  startDate: string;
  endDate: string;
  reportType?: string;
  includeMetrics?: boolean;
  format?: 'summary' | 'detailed';
  metadata?: {
    requestedBy?: string;
    department?: string;
    [key: string]: any;
  };
}

/**
 * Daily Reports Validator
 *
 * Validates daily report requests according to specification:
 * - startDate: Required ISO date string (start date for the report)
 * - endDate: Required ISO date string (end date for the report)
 * - reportType: Optional string (type of report requested, defaults to 'activity')
 * - includeMetrics: Optional boolean (whether to include detailed metrics)
 * - format: Optional enum ('summary' | 'detailed', defaults to 'summary')
 * - metadata: Optional object containing additional request information
 */
export class DailyReportsValidator extends BaseValidator {
  /**
   * Validate daily report request
   *
   * @param req - Express request object
   * @returns Validation result
   */
  static validate(req: Request): ValidatorResult {
    console.log('🔍 Validating daily report request...');
    console.log('📥 Request query params:', JSON.stringify(req.query, null, 2));
    console.log('📥 Request body:', JSON.stringify(req.body, null, 2));

    const { startDate, endDate } = req.query;
    const { reportType, includeMetrics, format, metadata } = req.body || {};

    // Validate required query parameters
    const requiredFieldsValidation = this.validateRequiredFields({ startDate, endDate });
    if (!requiredFieldsValidation.isValid) {
      return requiredFieldsValidation;
    }

    // Validate field types and values
    const fieldValidation = this.validateFieldTypes({
      startDate,
      endDate,
      reportType,
      includeMetrics,
      format,
      metadata
    });
    if (!fieldValidation.isValid) {
      return fieldValidation;
    }

    console.log('✅ Daily report request validation passed');
    return { isValid: true };
  }

  /**
   * Validate required fields are present
   */
  private static validateRequiredFields(data: { startDate: any; endDate: any }): ValidatorResult {
    const startDateValidation = this.validateRequired(data.startDate, 'startDate');
    if (!startDateValidation.isValid) {
      return startDateValidation;
    }

    const endDateValidation = this.validateRequired(data.endDate, 'endDate');
    if (!endDateValidation.isValid) {
      return endDateValidation;
    }

    return { isValid: true };
  }

  /**
   * Validate field types and constraints
   */
  private static validateFieldTypes(data: {
    startDate: any;
    endDate: any;
    reportType?: any;
    includeMetrics?: any;
    format?: any;
    metadata?: any;
  }): ValidatorResult {
    // Validate required startDate field
    const startDateValidation = this.validateType(data.startDate, 'string', 'startDate');
    if (!startDateValidation.isValid) {
      return startDateValidation;
    }

    // Validate startDate format (basic ISO date check)
    if (data.startDate && !this.isValidDateString(data.startDate)) {
      return {
        isValid: false,
        error: 'Field startDate must be a valid ISO date string (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ)',
      };
    }

    // Validate required endDate field
    const endDateValidation = this.validateType(data.endDate, 'string', 'endDate');
    if (!endDateValidation.isValid) {
      return endDateValidation;
    }

    // Validate endDate format (basic ISO date check)
    if (data.endDate && !this.isValidDateString(data.endDate)) {
      return {
        isValid: false,
        error: 'Field endDate must be a valid ISO date string (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ)',
      };
    }

    // Validate optional reportType field
    if (data.reportType !== undefined) {
      const reportTypeValidation = this.validateType(data.reportType, 'string', 'reportType');
      if (!reportTypeValidation.isValid) {
        return reportTypeValidation;
      }
    }

    // Validate optional includeMetrics field
    if (data.includeMetrics !== undefined) {
      const metricsValidation = this.validateType(data.includeMetrics, 'boolean', 'includeMetrics');
      if (!metricsValidation.isValid) {
        return metricsValidation;
      }
    }

    // Validate optional format field
    if (data.format !== undefined) {
      const formatValidation = this.validateType(data.format, 'string', 'format');
      if (!formatValidation.isValid) {
        return formatValidation;
      }

      if (!['summary', 'detailed'].includes(data.format)) {
        return {
          isValid: false,
          error: 'Field format must be either "summary" or "detailed"',
        };
      }
    }

    // Validate optional metadata field
    if (data.metadata !== undefined) {
      const metadataValidation = this.validateType(data.metadata, 'object', 'metadata');
      if (!metadataValidation.isValid) {
        return metadataValidation;
      }
    }

    return { isValid: true };
  }

  /**
   * Basic date string validation
   */
  private static isValidDateString(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && dateString.length >= 10;
  }

  /**
   * Extract validated daily report data from request
   *
   * @param req - Express request object (assumed to be validated)
   * @returns Typed daily report request data
   */
  static extractDailyReportData(req: Request): DailyReportRequest {
    const { startDate, endDate } = req.query;
    const { reportType, includeMetrics, format, metadata } = req.body || {};

    return {
      startDate: startDate as string,
      endDate: endDate as string,
      reportType: reportType || 'activity',
      includeMetrics: includeMetrics || false,
      format: format || 'summary',
      metadata: metadata || undefined,
    };
  }
}

/**
 * Exported validator function for use with registerSlackBot
 */
export const dailyReportsValidator = (req: Request): ValidatorResult => {
  return DailyReportsValidator.validate(req);
};
