export type CustomerDTO = {
  customerId: number;
  traderId: number;
  languageId: number;
  currencyId: number;
  firstName: string;
  surname: string;
  username: string;
  code: string;
  email: string;
  phone: string;
  birthDate: string;
  address: string;
  kycVerified: boolean;
  kycStatus: number;
  type: number;
  gender: string;
  countryId: number;
  cityName: string;
  countryAbbrvt: string;
  balance: number;
  grossBalance: number;
  lockedBalance: number;
  isActiveOtp: number;
  vip: boolean;
  // @todo other fields
};

export type Customer = {
  customerId: number;
  traderId: number;
  languageId: number;
  currencyId: number;
  firstName: string;
  surname: string;
  username: string;
  code: string;
  email: string;
  phone: string;
  birthDate: Date;
  address: string;
  kycVerified: boolean;
  kycStatus: number;
  type: number;
  gender: string;
  countryId: number;
  cityName: string;
  countryAbbrvt: string;
  balance: number;
  grossBalance: number;
  lockedBalance: number;
  isActiveOtp: number;
  vip: boolean;
};

export const mapCustomer = (customer: CustomerDTO): Customer => {
  return {
    customerId: customer.customerId,
    traderId: customer.traderId,
    languageId: customer.languageId,
    currencyId: customer.currencyId,
    firstName: customer.firstName,
    surname: customer.surname,
    username: customer.username,
    code: customer.code,
    email: customer.email,
    phone: customer.phone,
    birthDate: new Date(customer.birthDate),
    address: customer.address,
    kycVerified: customer.kycVerified,
    kycStatus: customer.kycStatus,
    type: customer.type,
    gender: customer.gender,
    countryId: customer.countryId,
    cityName: customer.cityName,
    countryAbbrvt: customer.countryAbbrvt,
    balance: customer.balance,
    grossBalance: customer.grossBalance,
    lockedBalance: customer.lockedBalance,
    isActiveOtp: customer.isActiveOtp,
    vip: customer.vip,
  };
};
