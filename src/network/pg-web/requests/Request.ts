export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export abstract class PGWebRequest<ResponseModel = any, Model = any, Body = any> {
  abstract getPath(): string;

  abstract getMethod(): HttpMethod;

  abstract getBody(): Body;

  abstract getHeaders(): Record<string, string>;

  abstract validateResponse(json: PGWebRequestSuccessResponse<ResponseModel>): PGWebRequestResponse<Model>;
}

export type PGWebRequestSuccessResponse<T = any> = {
  data: T;
  success: true;
};

export type PGWebRequestErrorResponse = {
  success: false;
  error: string;
};

export type PGWebRequestResponse<T> = PGWebRequestSuccessResponse<T> | PGWebRequestErrorResponse;
