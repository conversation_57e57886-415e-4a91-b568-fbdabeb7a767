import { Customer, CustomerDTO, mapCustomer } from '../../dto/Customer';
import { HttpMethod, PGWebRequest, PGWebRequestResponse, PGWebRequestSuccessResponse } from '../Request';

export class CustomerGetRequest extends PGWebRequest<CustomerDTO, Customer> {
  getPath(): string {
    return `/odin/api/user/getCurrentCustomer`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(json: PGWebRequestSuccessResponse<CustomerDTO>): PGWebRequestResponse<Customer> {
    return {
      success: true,
      data: mapCustomer(json.data),
    };
  }
}
