import fetch from 'node-fetch';
import { PGWebRequest, PGWebRequestResponse, PGWebRequestSuccessResponse } from './requests/Request';
import { HttpMethod } from '../pronet/pronetApiClient';

export interface PronetWebCookies {
  __nxquid: string;
  __nxqsec: string;
}

export interface PronetWebSession {
  sessionId: string;
}

export class ApiError extends Error {
  constructor(message: string, public statusCode?: number, public response?: unknown) {
    super(message);
    this.name = 'ApiError';
  }
}

export class NetworkError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends Error {
  constructor(message = 'Request timeout') {
    super(message);
    this.name = 'TimeoutError';
  }
}

/**
 * Pronet API Client
 *
 * This client handles all communication with the Pronet API system.
 * It automatically handles authentication, checksum generation, and request formatting.
 */
export class PGWebHttpClient {
  private cookies: PronetWebCookies | null = null;

  /**
   * Makes an HTTP request using the provided PGWebRequest instance
   */
  async makeRequest<R, M>(
    request: PGWebRequest<R, M>,
    referer: string,
    additionalHeaders: Record<string, string> = {},
  ): Promise<PGWebRequestResponse<M>> {
    try {
      // let cookies: PronetWebCookies;
      // if (this.cookies === null) {
      //   cookies = await this.getCookies(referer);
      //   this.cookies = cookies;
      // } else {
      //   cookies = this.cookies;
      // }

      const url = this.buildUrl(referer, request.getPath());
      const method = request.getMethod();
      const headers: Record<string, string> = {
        ...request.getHeaders(),
        Referer: referer,
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        ...additionalHeaders,
      };
      const body = this.prepareBody(request, method);
      console.log('🚀 Making request to PG Web...');
      console.log(`📍 URL: ${method} ${url}`);
      console.log('📋 Request Headers:', JSON.stringify(headers, null, 2));
      if (body) {
        console.log('📋 Request Body:', body);
      }

      const response = await this.fetchWithTimeout(url, {
        method,
        headers,
        body,
      });

      const text = await response.text();
      let json: Record<string, unknown>;
      if (response.ok) {
        json = JSON.parse(text);
      } else {
        try {
          json = await JSON.parse(text);
        } catch (e) {
          return {
            success: false,
            error: (e as Error).message ?? 'Failed to parse error response',
          };
        }
      }

      if (typeof json !== 'object' || 'success' in json === false) {
        return {
          success: false,
          error: 'Invalid response format',
        };
      }

      if (json['success']) {
        if ('data' in json === false) {
          return {
            success: false,
            error: 'Invalid response format',
          };
        }

        return request.validateResponse(json as PGWebRequestSuccessResponse<R>);
      } else {
        const error = (json['responseCodes'] as any[] | undefined)?.map((c) => c.responseKey).join(',');

        return {
          success: false,
          error: error ?? 'Something went wrong',
        };
      }
    } catch (error) {
      return this.handleError(error);
    }
  }

  async makeAuthorizedRequest<R, M>(
    request: PGWebRequest<R, M>,
    referer: string,
    session: string,
    additionalHeaders: Record<string, string> = {},
  ): Promise<PGWebRequestResponse<M>> {
    let token = session;
    if (token.includes('Bearer')) {
      token = token.split('Bearer ')[1] || '';
    }

    return this.makeRequest(request, referer, {
      ...additionalHeaders,
      s7oryo9stv: token,
    });
  }

  /**
   * Builds the complete URL from the base URL and path
   */
  private buildUrl(referer: string, path: string): string {
    const baseUrl = referer.split('/').slice(0, 3).join('/').replace(/\/$/, '');
    const cleanPath = path.replace(/^\//, '');
    return `${baseUrl}/${cleanPath}`;
  }

  /**
   * Prepares the request body based on method and content
   */
  private prepareBody(request: PGWebRequest, method: HttpMethod): string | undefined {
    if (method === 'GET' || method === 'DELETE') {
      return undefined;
    }

    const body = request.getBody();
    if (body === null || body === undefined) {
      return undefined;
    }

    if (typeof body === 'string') {
      return body;
    }

    return JSON.stringify(body);
  }

  /**
   * Performs fetch with timeout support
   */
  private async fetchWithTimeout(url: string, options: fetch.RequestInit): Promise<fetch.Response> {
    try {
      const response = await fetch(url, {
        ...options,
        timeout: 60_000,
      });

      return response;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new TimeoutError();
      }

      throw new NetworkError(
        `Network request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Handles errors and converts them to PGWebRequestResponse format
   */
  private handleError<T>(error: unknown): PGWebRequestResponse<T> {
    if (error instanceof ApiError) {
      return {
        success: false,
        error: error.message,
      };
    }

    if (error instanceof NetworkError) {
      return {
        success: false,
        error: `Network error: ${error.message}`,
      };
    }

    if (error instanceof TimeoutError) {
      return {
        success: false,
        error: 'Request timeout. Please try again.',
      };
    }

    // Handle unknown errors
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }

  async getCookies(referer: string): Promise<PronetWebCookies> {
    let nxquid = null;
    let nxqsec = null;

    let response = await fetch(referer, {
      redirect: 'manual',
    });
    if (response.status === 302) {
      let setCookieHeaders = response.headers.raw()['set-cookie'];
      if (!setCookieHeaders || setCookieHeaders.length === 0) {
        throw new Error('No set-cookie headers found');
      }

      nxquid = setCookieHeaders.find((header) => header.includes('__nxquid'));
      if (!nxquid) {
        throw new Error('__nxquid cookie not found');
      }
      nxquid = nxquid.split(';')[0]?.split('=').slice(1).join('=') || '';

      response = await fetch(referer, {
        headers: {
          Cookie: `__nxquid=${nxquid}`,
        },
      });

      setCookieHeaders = response.headers.raw()['set-cookie'];
      if (!setCookieHeaders || setCookieHeaders.length === 0) {
        throw new Error('No set-cookie headers found');
      }

      nxqsec = setCookieHeaders.find((header) => header.includes('__nxqsec'));
      if (!nxqsec) {
        throw new Error('__nxqsec cookie not found');
      }
      nxqsec = nxqsec.split(';')[0]?.split('=').slice(1).join('=') || '';
    } else if (response.ok) {
      // do nothing
    } else {
      throw new Error(`Failed to prelogin: expected redirect to set __nxquid.`);
    }

    let data = await response.text();
    if (data.includes('nexusguard')) {
      const startIndex = data.indexOf(`${referer}?vcode=`);
      if (startIndex === -1) {
        throw new Error('Failed to find login url');
      }

      const endIndex = data.indexOf('"', startIndex);
      if (endIndex === -1) {
        throw new Error('Failed to find end of login url');
      }

      const code = data.substring(startIndex + `${referer}?vcode=`.length, endIndex);
      response = await fetch(`${referer}?vcode=${code}`, {
        headers: {
          Cookie: `__nxquid=${nxquid}; __nxqsec=${nxqsec}`,
        },
        redirect: 'manual',
      });
      if (response.status === 302) {
        const setCookieHeaders = response.headers.raw()['set-cookie'];
        if (!setCookieHeaders || setCookieHeaders.length === 0) {
          throw new Error('No set-cookie headers found');
        }

        nxqsec = setCookieHeaders.find((header) => header.includes('__nxqsec'));
        if (!nxqsec) {
          throw new Error('__nxqsec cookie not found');
        }
        nxqsec = nxqsec.split(';')[0]?.split('=').slice(1).join('=') || '';

        response = await fetch(referer, {
          headers: {
            Cookie: `__nxquid=${nxquid}; __nxqsec=${nxqsec}`,
          },
        });
      } else {
        throw new Error(`Failed to prelogin: expected redirect to set __nxqsec.`);
      }

      data = await response.text();
    }

    if (!nxquid || !nxqsec) {
      throw new Error('Failed to get cookies');
    }

    return {
      __nxquid: nxquid,
      __nxqsec: nxqsec,
    };
  }
}

// Export a singleton instance
export const pgWebHttpClient = new PGWebHttpClient();
