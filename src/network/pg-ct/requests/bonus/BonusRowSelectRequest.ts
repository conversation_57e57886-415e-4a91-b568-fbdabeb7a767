import { findJavaxViewState } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { BonusPreloadRequestResponse } from './BonusPreloadRequest';

export interface BonusRowSelectRequestOptions {
  javax: BonusPreloadRequestResponse;
  id: number;
}

export type BonusRowSelectResponse = null;

export class BonusRowSelectRequest extends PGCasinoTraderRequest<BonusRowSelectResponse, any> {
  constructor(private options: BonusRowSelectRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `${this.options.javax.tableId}:bonusesDt`,
      'javax.faces.partial.execute': `${this.options.javax.tableId}:bonusesDt`,
      'javax.faces.partial.render': `toolbarFrm bonusFrm`,
      'javax.faces.behavior.event': 'rowSelect',
      'javax.faces.partial.event': 'rowSelect',
      [`${this.options.javax.tableId}:bonusesDt_instantSelectedRowKey`]: this.options.id,
      frm: 'frm',
      [`${this.options.javax.tableId}:bonusesDt_selection`]: this.options.id,
      [`${this.options.javax.tableId}:bonusesDeactiveDt_selection`]: '',
      [`${this.options.javax.tableId}_activeIndex`]: '0',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<BonusRowSelectResponse> {
    try {
      const viewState = findJavaxViewState(xml);
      if (!viewState) {
        return {
          success: false,
          message: 'Failed to find view state',
        };
      }

      return {
        success: true,
        data: null,
        viewState,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse bonus list: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
