import { formatDate } from '@/network/pg-dagur/utils/time';
import { BONUS_STATUSES, BONUS_TYPES, BonusStatus, BonusType } from '../../dto/Bonus';
import { HttpMethod, PGCasinoTraderRequest } from '../Request';

export interface BonusCreateRequestOptions {
  type: BonusType;
  name: string;
  description?: string;
  code?: string;
  startDate: Date;
  endDate: Date;
  status?: BonusStatus;
  currencyId: number;
  shouldConfiscateOnExpiration?: boolean;
  shouldConfiscateOnForfeit?: boolean;
  isStickyBonus?: boolean;
  bonusAmount?: number;
  maxBonusWin?: number;
  turnoverRate: number;
  maxNumberOfClaims?: number;
  validityPeriodInDays?: number;
  bonusBudget?: number;
  minDepositAmount?: number;

  // @todo
  // excludedGames: any[];
  // focusedGames: any[];
  // excludedCustomers: any[];
  // focusedCustomers: any[];
  // excludedCountries: any[];
  // focusedCountries: any[];
  // termsAndConditions: any[];
}

export type BonusCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusCreateRequest extends PGCasinoTraderRequest<BonusCreateResponse, any> {
  constructor(private options: BonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const bonusType = BONUS_TYPES.indexOf(this.options.type);
    if (bonusType === -1) {
      throw new Error(`Invalid bonus type: ${this.options.type}`);
    }
    const bonusStatus = this.options.status || 'inactive';
    const bonusStatusIndex = BONUS_STATUSES.indexOf(bonusStatus);
    if (bonusStatusIndex === -1) {
      throw new Error(`Invalid bonus status: ${bonusStatus}`);
    }

    return {
      'javax.faces.partial.ajax': 'true',
      // @todo
      'javax.faces.source': 'bonusFrm:j_idt380',
      'javax.faces.partial.execute': 'bonusFrm',
      'javax.faces.partial.render': 'bonusFrm:bonusDlgPanel frm',
      // @todo
      'bonusFrm:j_idt380': 'bonusFrm:j_idt380',
      bonusFrm: 'bonusFrm',
      'bonusFrm:tabview:cmbType_focus': '',
      'bonusFrm:tabview:cmbType_input': bonusType.toString(),
      'bonusFrm:tabview:txtName': this.options.name,
      'bonusFrm:tabview:txtDescription': this.options.description ?? '',
      'bonusFrm:tabview:txtCode': this.options.code ?? '',
      'bonusFrm:tabview:calStartDate_input': formatDate(this.options.startDate),
      'bonusFrm:tabview:calEndDate_input': formatDate(this.options.endDate),
      'bonusFrm:tabview:cmbStatus_focus': '',
      'bonusFrm:tabview:cmbStatus_input': bonusStatus.toString(),
      'bonusFrm:tabview:cmbCurrency_focus': '',
      'bonusFrm:tabview:cmbCurrency_input': '1',
      'bonusFrm:tabview:chkConfiscateOnExpiration_input': 'on',
      'bonusFrm:tabview:chkConfiscateOnForfeit_input': 'on',
      'bonusFrm:tabview:chkStickyBonus_input': 'on',
      'bonusFrm:tabview:txtBonusAmount_input': '10.00',
      'bonusFrm:tabview:txtBonusAmount_hinput': '10',
      'bonusFrm:tabview:txtMaxBonusWin_input': '6.00',
      'bonusFrm:tabview:txtMaxBonusWin_hinput': '6',
      'bonusFrm:tabview:txtTurnoverRate_input': '3.00',
      'bonusFrm:tabview:txtTurnoverRate_hinput': '3',
      'bonusFrm:tabview:maxNumOfClaims_input': '15',
      'bonusFrm:tabview:maxNumOfClaims_hinput': '15',
      'bonusFrm:tabview:validityPeriodInDays_input': '9',
      'bonusFrm:tabview:validityPeriodInDays_hinput': '9',
      'bonusFrm:tabview:bonusBudget_input': '100.00',
      'bonusFrm:tabview:bonusBudget_hinput': '100',
      'bonusFrm:tabview:txtMinDepositAmountToClaimBonusWin_input': '300.00',
      'bonusFrm:tabview:txtMinDepositAmountToClaimBonusWin_hinput': '300',
      'bonusFrm:tabview:traderDynamicContentDataTable:j_idt374:filter': '',
      'bonusFrm:tabview:traderDynamicContentDataTable:j_idt378:filter': '',
      'bonusFrm:tabview:traderDynamicContentDataTable_selection': '0',
      'bonusFrm:tabview_activeIndex': '0',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
