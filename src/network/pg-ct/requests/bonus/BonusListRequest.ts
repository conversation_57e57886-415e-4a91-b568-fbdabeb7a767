import { findJavaxViewState } from '@/network/pg-dagur/utils/javax';
import { BonusListItem } from '../../dto/Bonus';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { parse } from 'node-html-parser';
import { XMLParser } from 'fast-xml-parser';
import { BonusPreloadRequestResponse } from './BonusPreloadRequest';

export interface BonusListRequestOptions {
  javax: BonusPreloadRequestResponse;
  status: string;

  cursor: string;
  page: number;
  limit: number;
}

export type BonusListResponse = {
  // total: number;
  cursor: string;
  page: number;
  items: BonusListItem[];
};

export class BonusListRequest extends PGCasinoTraderRequest<BonusListResponse, any> {
  constructor(private options: BonusListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const tableName = this.options.status === 'active' ? 'bonusesDt' : 'bonusesDeactiveDt';

    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `${this.options.javax.tableId}:${tableName}`,
      'javax.faces.partial.execute': `${this.options.javax.tableId}:${tableName}`,
      'javax.faces.partial.render': `${this.options.javax.tableId}:${tableName}`,
      [`${this.options.javax.tableId}:${tableName}`]: `${this.options.javax.tableId}:${tableName}`,
      [`${this.options.javax.tableId}:${tableName}_pagination`]: 'true',
      [`${this.options.javax.tableId}:${tableName}_first`]: (this.options.page - 1) * this.options.limit,
      [`${this.options.javax.tableId}:${tableName}_rows`]: this.options.limit,
      [`${this.options.javax.tableId}:${tableName}_encodeFeature`]: 'true',
      frm: 'frm',
      [`${this.options.javax.tableId}:bonusesDt_selection`]: '',
      [`${this.options.javax.tableId}:${tableName}_selection`]: '',
      [`${this.options.javax.tableId}_activeIndex`]: '1',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<BonusListResponse> {
    try {
      // Check if this is a JSF partial response
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Determine table name based on status
        const tableName = this.options.status === 'active' ? 'bonusesDt' : 'bonusesDeactiveDt';
        const expectedUpdateId = `${this.options.javax.tableId}:${tableName}`;

        // Find the update containing the table data
        let tableUpdate = update;
        if (Array.isArray(update)) {
          tableUpdate = update.find((u: any) => u?.['@id'] === expectedUpdateId);
        } else if (tableUpdate?.['@id'] !== expectedUpdateId) {
          tableUpdate = null;
        }

        if (!tableUpdate) {
          return {
            success: false,
            message: `No table data found for ${expectedUpdateId} in response`,
          };
        }

        // Extract HTML content from the update (CDATA section)
        const htmlContent = tableUpdate['#text'] || tableUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in table update',
          };
        }

        // Find ViewState update
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        // Initialize response structure
        const response: BonusListResponse = {
          items: [],
          cursor: viewState || '',
          page: this.options.page,
        };

        // Parse the HTML content which starts directly with <tr> tags
        this.parseBonusHTML(htmlContent, response);

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML response (fallback)
        const viewState = findJavaxViewState(xml);
        if (!viewState) {
          return {
            success: false,
            message: 'Failed to find view state',
          };
        }

        const response: BonusListResponse = {
          items: [],
          cursor: viewState,
          page: this.options.page,
        };

        this.parseBonusHTML(xml, response);

        return {
          success: true,
          data: response,
          viewState,
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse bonus list: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseBonusHTML(html: string, response: BonusListResponse): void {
    // Parse HTML using node-html-parser
    const root = parse(html);

    // Find all table rows with data-ri attribute (data rows)
    // The HTML content starts directly with <tr> tags in partial responses
    let rows: any[] = [];
    if (html.trim().startsWith('<tr data-ri')) {
      // Direct <tr> tags from partial response
      rows = root.querySelectorAll('tr[data-ri]');
    } else {
      // Full HTML with table structure
      const tableName = this.options.status === 'active' ? 'bonusesDt' : 'bonusesDeactiveDt';
      const tableSelector = `[id$=":${tableName}"]`;
      const table = root.querySelector(tableSelector) || root.querySelector('.ui-datatable');
      if (table) {
        rows = table.querySelectorAll('tbody tr[data-ri]');
      }
    }

    if (!rows || rows.length === 0) {
      // No data rows found, but this might be valid (empty result)
      return;
    }

    // Parse each row
    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length < 8) continue; // Ensure we have all required columns

      // Extract data from cells based on the table structure
      // Columns: ID, Type, Name, Description, Code, Start Date, End Date, Status
      const bonus: BonusListItem = {
        id: this.parseNumericValue(this.extractCellText(cells[0])),
        type: this.extractCellText(cells[1]) || '',
        name: this.extractCellText(cells[2]) || '',
        description: this.extractCellText(cells[3]) || '',
        code: this.extractCellText(cells[4]) || '',
        startDate: this.parseDate(this.extractCellText(cells[5])),
        endDate: this.parseDate(this.extractCellText(cells[6])),
        status: this.extractCellText(cells[7]) || '',
      };

      response.items.push(bonus);
    }
  }

  private extractCellText(cell: any): string {
    if (!cell) return '';

    // Try to get text from label first, then from cell directly
    const label = cell.querySelector('label');
    if (label && label.text) {
      return label.text.trim();
    }

    return cell.text?.trim() || '';
  }

  private parseNumericValue(text: string): number {
    if (!text) return 0;

    // Remove commas and parse as float
    const cleaned = text.replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }

  private parseDate(dateStr: string): Date {
    if (!dateStr) return new Date();

    // Parse date in DD.MM.YYYY format (as shown in the example HTML)
    const parts = dateStr.split('.');
    if (parts.length === 3 && parts[0] && parts[1] && parts[2]) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed in JavaScript Date
      const year = parseInt(parts[2], 10);

      if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
        return new Date(year, month, day);
      }
    }

    // Fallback: try to parse as-is
    const fallbackDate = new Date(dateStr);
    return isNaN(fallbackDate.getTime()) ? new Date() : fallbackDate;
  }
}
