import { findJavaxElementId, findJavaxElementIdBefore, findJavaxViewState } from '@/network/pg-dagur/utils/javax';
import { Bonus, BONUS_STATUSES, BONUS_TYPE, BonusStatus, BonusType } from '../../dto/Bonus';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { parse } from 'node-html-parser';
import { XMLParser } from 'fast-xml-parser';
import { BonusPreloadRequestResponse } from './BonusPreloadRequest';

export interface BonusGetRequestOptions {
  javax: BonusPreloadRequestResponse;
  id: number;
}

export type BonusGetResponse = Bonus;

export class BonusGetPreloadRequest extends PGCasinoTraderRequest<BonusPreloadRequestResponse, any> {
  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<BonusPreloadRequestResponse> {
    try {
      const viewState = findJavaxViewState(xml);
      if (!viewState) {
        return {
          success: false,
          message: 'Failed to find view state',
        };
      }

      const tableId = findJavaxElementIdBefore(xml, '', 'ui-tabs-top');
      if (!tableId) {
        return {
          success: false,
          message: 'Failed to find table id',
        };
      }

      const bonusFormId = findJavaxElementId(xml, 'bonusFrm:', 'id="bonusFrm"');
      if (!bonusFormId) {
        return {
          success: false,
          message: 'Failed to find bonus form id',
        };
      }

      return {
        success: true,
        data: {
          tableId: tableId,
          bonusFormId: bonusFormId,
        },
        viewState,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse bonus list: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}

export class BonusGetRequest extends PGCasinoTraderRequest<BonusGetResponse, any> {
  constructor(private options: BonusGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `bonusFrm:${this.options.javax.bonusFormId}`,
      'javax.faces.partial.execute': `bonusFrm:${this.options.javax.bonusFormId}`,
      'javax.faces.partial.render': `bonusFrm:${this.options.javax.bonusFormId}`,
      [`bonusFrm:${this.options.javax.bonusFormId}`]: `bonusFrm:${this.options.javax.bonusFormId}`,
      [`bonusFrm:${this.options.javax.bonusFormId}_contentLoad`]: 'true',
      bonusFrm: 'bonusFrm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<BonusGetResponse> {
    try {
      // Check if this is a JSF partial response
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the bonus form data
        const expectedUpdateId = `bonusFrm:${this.options.javax.bonusFormId}`;
        let formUpdate = update;
        if (Array.isArray(update)) {
          formUpdate = update.find((u: any) => u?.['@id'] === expectedUpdateId);
        } else if (formUpdate?.['@id'] !== expectedUpdateId) {
          formUpdate = null;
        }

        if (!formUpdate) {
          return {
            success: false,
            message: `No form data found for ${expectedUpdateId} in response`,
          };
        }

        // Extract HTML content from the update (CDATA section)
        const htmlContent = formUpdate['#text'] || formUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in form update',
          };
        }

        // Find ViewState update
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        // Parse the bonus data from HTML
        const bonusData = this.parseBonusData(htmlContent);

        return {
          success: true,
          data: bonusData,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML response (fallback)
        const viewState = findJavaxViewState(xml);
        if (!viewState) {
          return {
            success: false,
            message: 'Failed to find view state',
          };
        }

        const bonusData = this.parseBonusData(xml);

        return {
          success: true,
          data: bonusData,
          viewState,
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse bonus data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseBonusData(html: string): Bonus {
    // Parse HTML using node-html-parser
    const root = parse(html);

    // Find the container with the bonus form data
    const container = root.querySelector('#bonusFrm\\:tabview\\:panelgrid') || root;

    // Extract all the required fields according to the specifications
    const bonus: Bonus = {
      id: this.options.id,

      // 1) bonus type is the selected option value from select #bonusFrm:tabview:cmbType_input
      type: this.extractBonusType(container),

      // 2) bonus name is value of input #bonusFrm:tabview:txtName
      name: this.extractInputValue(container, '#bonusFrm\\:tabview\\:txtName'),

      // 3) description is value of input #bonusFrm:tabview:txtDescription
      description: this.extractInputValue(container, '#bonusFrm\\:tabview\\:txtDescription'),

      // 4) code is value of input #bonusFrm:tabview:txtCode
      code: this.extractInputValue(container, '#bonusFrm\\:tabview\\:txtCode'),

      // 5) startDate is value of input #bonusFrm:tabview:calStartDate_input
      startDate: this.parseDateTime(this.extractInputValue(container, '#bonusFrm\\:tabview\\:calStartDate_input')),

      // 6) end date is value of input #bonusFrm:tabview:calEndDate_input
      endDate: this.parseDateTime(this.extractInputValue(container, '#bonusFrm\\:tabview\\:calEndDate_input')),

      // 7) status is the selected option value from select #bonusFrm:tabview:cmbStatus_input
      status: this.extractBonusStatus(container),

      // 8) currencyId is the selected option value from select #bonusFrm:tabview:cmbCurrency_input
      currencyId: this.extractSelectedOptionValueAsNumber(container, '#bonusFrm\\:tabview\\:cmbCurrency_input'),

      // 9) currencyCode is the first part of the selected option text from select #bonusFrm:tabview:cmbCurrency_input
      currencyCode: this.extractSelectedOptionText(container, '#bonusFrm\\:tabview\\:cmbCurrency_input'),

      // 10) confiscate on expiration is value of checkbox #bonusFrm:tabview:chkConfiscateOnExpiration_input
      shouldConfiscateOnExpiration: this.extractCheckboxValue(
        container,
        '#bonusFrm\\:tabview\\:chkConfiscateOnExpiration_input',
      ),

      // 11) confiscate on forfeit is value of checkbox #bonusFrm:tabview:chkConfiscateOnForfeit_input
      shouldConfiscateOnForfeit: this.extractCheckboxValue(
        container,
        '#bonusFrm\\:tabview\\:chkConfiscateOnForfeit_input',
      ),

      // 12) sticky bonus is value from checkbox #bonusFrm:tabview:chkStickyBonus_hinput
      isStickyBonus: this.extractCheckboxValue(container, '#bonusFrm\\:tabview\\:chkStickyBonus_input'),

      // 13) bonus value is value from input #bonusFrm:tabview:txtBonusAmount_hinput
      bonusAmount: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:txtBonusAmount_hinput'),
      ),

      // 14) max bonus win is value from input #bonusFrm:tabview:txtMaxBonusWin_hinput
      maxBonusWin: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:txtMaxBonusWin_hinput'),
      ),

      // 15) turnover rate is value from input #bonusFrm:tabview:txtTurnoverRate_hinput
      turnoverRate: this.extractTurnoverRate(html) || 0,

      // 16) max number of claims is value from input #bonusFrm:tabview:maxNumOfClaims_hinput
      maxNumberOfClaims: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:maxNumOfClaims_hinput'),
      ),

      // 17) validity period in days is value from input #bonusFrm:tabview:validityPeriodInDays_hinput
      validityPeriodInDays: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:validityPeriodInDays_hinput'),
      ),

      // 18) bonus budget is value from input #bonusFrm:tabview:bonusBudget_hinput
      bonusBudget: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:bonusBudget_hinput'),
      ),

      // 19) Remaining bonus budget is value from input #bonusFrm:tabview:bonusBudgetRemaining_hinput
      remainingBonusBudget: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:bonusBudgetRemaining_hinput'),
      ),

      // 20) min deposit amount is value from input #bonusFrm:tabview:txtMinDepositAmountToClaimBonusWin_hinput
      minDepositAmount: this.parseNullableNumericValue(
        this.extractInputValue(container, '#bonusFrm\\:tabview\\:txtMinDepositAmountToClaimBonusWin_hinput'),
      ),

      // TODO: These will be implemented later
      excludedGames: [],
      focusedGames: [],
      excludedCustomers: [],
      focusedCustomers: [],
      excludedCountries: [],
      focusedCountries: [],
      termsAndConditions: [],
    };

    return bonus;
  }

  private extractInputValue(container: any, selector: string): string {
    const element = container.querySelector(selector);
    return element?.getAttribute('value') || '';
  }

  private extractCheckboxValue(container: any, selector: string): boolean {
    const element = container.querySelector(selector);
    return element?.hasAttribute('checked') || false;
  }

  private extractSelectedOptionValue(container: any, selector: string): string {
    const select = container.querySelector(selector);
    if (!select) return '';

    // Find the selected option
    const selectedOption = select.querySelector('option[selected]');
    if (selectedOption) {
      return selectedOption.getAttribute('value') || '';
    }

    return '';
  }

  private extractSelectedOptionValueAsNumber(container: any, selector: string): number {
    const value = this.extractSelectedOptionValue(container, selector);
    return value ? parseInt(value, 10) : 0;
  }

  private extractSelectedOptionText(container: any, selector: string): string {
    const select = container.querySelector(selector);
    if (!select) return '';

    // Find the selected option
    const selectedOption = select.querySelector('option[selected]');
    if (selectedOption) {
      const optionText = selectedOption.text?.trim() || '';
      // Extract the first part before the '-' character for currency code
      // Example: "TRY - Turkish Lira" -> "TRY"
      const parts = optionText.split('-');
      return parts[0]?.trim() || '';
    }

    return '';
  }

  private parseNullableNumericValue(text: string): number | null {
    if (!text || text.trim() === '') return null;

    // Remove commas and parse as float
    const cleaned = text.replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? null : parsed;
  }

  private parseDateTime(dateStr: string): Date {
    if (!dateStr) return new Date();

    // Parse date in DD.MM.YYYY HH:mm:ss format (as shown in the example HTML)
    // Example: "13.06.2025 00:00:00"
    const parts = dateStr.split(' ');
    if (parts.length >= 1 && parts[0]) {
      const datePart = parts[0];
      const timePart = parts[1] || '00:00:00';

      const dateComponents = datePart.split('.');
      const timeComponents = timePart.split(':');

      if (
        dateComponents.length === 3 &&
        timeComponents.length >= 2 &&
        dateComponents[0] &&
        dateComponents[1] &&
        dateComponents[2] &&
        timeComponents[0] &&
        timeComponents[1]
      ) {
        const day = parseInt(dateComponents[0], 10);
        const month = parseInt(dateComponents[1], 10) - 1; // Month is 0-indexed in JavaScript Date
        const year = parseInt(dateComponents[2], 10);
        const hour = parseInt(timeComponents[0], 10);
        const minute = parseInt(timeComponents[1], 10);
        const second = timeComponents[2] ? parseInt(timeComponents[2], 10) : 0;

        if (!isNaN(day) && !isNaN(month) && !isNaN(year) && !isNaN(hour) && !isNaN(minute)) {
          return new Date(year, month, day, hour, minute, second);
        }
      }
    }

    // Fallback: try to parse as-is
    const fallbackDate = new Date(dateStr);
    return isNaN(fallbackDate.getTime()) ? new Date() : fallbackDate;
  }

  private extractBonusType(container: any): BonusType {
    const optionValue = this.extractSelectedOptionValue(container, '#bonusFrm\\:tabview\\:cmbType_input');

    // If no value is present, return fallback
    if (!optionValue || optionValue.trim() === '') {
      return BONUS_TYPE[0] as BonusType; // ''
    }

    const index = parseInt(optionValue, 10);

    // Use the numeric option value as index in the BONUS_TYPE array
    if (index >= 0 && index < BONUS_TYPE.length) {
      return BONUS_TYPE[index] as BonusType;
    }

    // Fallback to empty string if index is out of bounds
    return BONUS_TYPE[0] as BonusType; // ''
  }

  private extractBonusStatus(container: any): BonusStatus {
    const optionValue = this.extractSelectedOptionValue(container, '#bonusFrm\\:tabview\\:cmbStatus_input');

    // If no value is present, return fallback
    if (!optionValue || optionValue.trim() === '') {
      return BONUS_STATUSES[0] as BonusStatus; // 'inactive'
    }

    const index = parseInt(optionValue, 10);

    // Use the numeric option value as index in the BONUS_STATUSES array
    if (index >= 0 && index < BONUS_STATUSES.length) {
      return BONUS_STATUSES[index] as BonusStatus;
    }

    // Fallback to inactive if index is out of bounds
    return BONUS_STATUSES[0] as BonusStatus; // 'inactive'
  }

  private extractTurnoverRate(html: string): number | null {
    // Look for the pattern: widget_bonusFrm_tabview_txtTurnoverRate followed by valueToRender
    const pattern = /widget_bonusFrm_tabview_txtTurnoverRate[\s\S]*?valueToRender:\s*'([^']+)'/;
    const match = html.match(pattern);

    if (match && match[1]) {
      const value = parseFloat(match[1]);
      return isNaN(value) ? null : value;
    }

    return null;
  }
}
