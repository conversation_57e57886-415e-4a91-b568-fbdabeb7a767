import { findJavaxElementId, findJavaxElementIdBefore, findJavaxViewState } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';

export interface BonusPreloadRequestResponse {
  tableId: string;
  bonusFormId: string;
}

export class BonusPreloadRequest extends PGCasinoTraderRequest<BonusPreloadRequestResponse, any> {
  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<BonusPreloadRequestResponse> {
    try {
      const viewState = findJavaxViewState(xml);
      if (!viewState) {
        return {
          success: false,
          message: 'Failed to find view state',
        };
      }

      const tableId = findJavaxElementIdBefore(xml, '', 'ui-tabs-top');
      if (!tableId) {
        return {
          success: false,
          message: 'Failed to find table id',
        };
      }

      const bonusFormId = findJavaxElementId(xml, 'bonusFrm:', 'id="bonusFrm"');
      if (!bonusFormId) {
        return {
          success: false,
          message: 'Failed to find bonus form id',
        };
      }

      return {
        success: true,
        data: {
          tableId: tableId,
          bonusFormId: bonusFormId,
        },
        viewState,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse bonus list: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
