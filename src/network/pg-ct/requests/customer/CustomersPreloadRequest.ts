import { findJavaxElementId, findJavaxElementIdBefore, findJavaxViewState } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';

export interface CustomersPreloadRequestResponse {
  searchButtonId: string;
  editButtonId: string;
  customerFormId: string;
  bonusFormId: string;
  bonusFormSaveButtonId: string;
}

export class CustomersPreloadRequest extends PGCasinoTraderRequest<CustomersPreloadRequestResponse, any> {
  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<CustomersPreloadRequestResponse> {
    try {
      const viewState = findJavaxViewState(xml);
      if (!viewState) {
        return {
          success: false,
          message: 'Failed to find view state',
        };
      }

      const searchButtonId = findJavaxElementIdBefore(xml, '', '<span class="ui-button-text ui-c">Ara</span>');
      if (!searchButtonId) {
        return {
          success: false,
          message: 'Failed to find search button id',
        };
      }

      const editButtonId = findJavaxElementIdBefore(xml, '', '<span class="ui-button-text ui-c">Düzenle</span>');
      if (!editButtonId) {
        return {
          success: false,
          message: 'Failed to find edit button id',
        };
      }

      const customerFormId = findJavaxElementId(xml, 'recordFrm:', 'id="recordFrm"');
      if (!customerFormId) {
        return {
          success: false,
          message: 'Failed to find customer form id',
        };
      }

      const bonusFormId = findJavaxElementId(xml, 'bonusFrm:', 'id="bonusFrm"');
      if (!bonusFormId) {
        return {
          success: false,
          message: 'Failed to find bonus form id',
        };
      }

      const bonusFormSaveButtonId = findJavaxElementId(xml, 'bonusFrm:', 'id="bonusFrm"', 1);
      if (!bonusFormSaveButtonId) {
        return {
          success: false,
          message: 'Failed to find bonus form save button id',
        };
      }

      return {
        success: true,
        data: {
          searchButtonId,
          editButtonId,
          customerFormId,
          bonusFormId,
          bonusFormSaveButtonId,
        },
        viewState,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse customers preload: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
