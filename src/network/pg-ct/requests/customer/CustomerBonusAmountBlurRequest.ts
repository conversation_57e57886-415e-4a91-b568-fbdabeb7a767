import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerBonusAmountBlurRequestOptions {
  javax: CustomersPreloadRequestResponse;

  bonusId: number;
  amount: number;
}

export type CustomerBonusAmountBlurResponse = {};

export class CustomerBonusAmountBlurRequest extends PGCasinoTraderRequest<CustomerBonusAmountBlurResponse, any> {
  constructor(private options: CustomerBonusAmountBlurRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'bonusFrm:inscBonusAmount',
      'javax.faces.partial.execute': 'bonusFrm:inscBonusAmount',
      'javax.faces.partial.render': 'bonusFrm:inscInitialAmount bonusFrm:inscTurnoverAmount bonusFrm:inscDepositAmount',
      'javax.faces.behavior.event': 'blur',
      'javax.faces.partial.event': 'blur',
      bonusFrm: 'bonusFrm',
      'bonusFrm:inscTraderBonusId_focus': '',
      'bonusFrm:inscTraderBonusId_input': this.options.bonusId,
      'bonusFrm:inscBonusAmount_input': this.options.amount,
      'bonusFrm:inscBonusAmount_hinput': this.options.amount,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
