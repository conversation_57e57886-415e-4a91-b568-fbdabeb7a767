import { HttpMethod, PGCasinoTraderRequest } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerEnterEditModeRequestOptions {
  javax: CustomersPreloadRequestResponse;

  id: number;
}

export type CustomerEnterEditModeResponse = {};

export class CustomerEnterEditModeRequest extends PGCasinoTraderRequest<CustomerEnterEditModeResponse, any> {
  constructor(private options: CustomerEnterEditModeRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.editButtonId,
      'javax.faces.partial.execute': this.options.javax.editButtonId,
      'javax.faces.partial.render': 'recordFrm:recordDlgPanel',
      [this.options.javax.editButtonId]: this.options.javax.editButtonId,
      frm: 'frm',
      dtcustomers_selection: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
