import { Customer } from '../../dto/Customer';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';
import { parse } from 'node-html-parser';

export interface CustomersSearchRequestOptions {
  javax: CustomersPreloadRequestResponse;
  customerCode?: string;
}

export type CustomersSearchResponse = Customer[];

export class CustomersSearchRequest extends PGCasinoTraderRequest<CustomersSearchResponse, any> {
  constructor(private options: CustomersSearchRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.searchButtonId,
      'javax.faces.partial.execute': 'searchFrm',
      'javax.faces.partial.render': 'dtcustomers',
      [this.options.javax.searchButtonId]: this.options.javax.searchButtonId,
      searchFrm: 'searchFrm',
      crId: '',
      crName: '',
      crSurname: '',
      crUsername: '',
      crCustomerCode: this.options.customerCode,
      crRegisterIp: '',
      crLastLoginIp: '',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<CustomersSearchResponse> {
    try {
      if (xml.includes('Empty List')) {
        return {
          success: true,
          data: [],
        };
      }

      const customers = this.extractCustomers(xml);

      return {
        success: true,
        data: customers,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse customers search: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private extractCustomers(xml: string): Customer[] {
    const doc = parse(xml);
    const customers: Customer[] = [];

    // Find the table body with customer data
    const tbody = doc.querySelector('#dtcustomers_data');
    if (!tbody) {
      return customers;
    }

    // Find all data rows
    const rows = tbody.querySelectorAll('tr[data-ri]');

    for (const row of rows) {
      const cells = row.querySelectorAll('td[role="gridcell"]');
      if (cells.length >= 14) {
        try {
          const customer = this.parseCustomerRow(cells);
          customers.push(customer);
        } catch (error) {
          console.warn('Failed to parse customer row:', error);
          // Continue parsing other rows even if one fails
        }
      }
    }

    return customers;
  }

  private parseCustomerRow(cells: any[]): Customer {
    // Helper function to parse date from format "Mon Jul 28 11:53:25 EET 2025" as UTC
    const parseDate = (dateStr: string): Date => {
      try {
        // Parse the date string and convert to UTC
        const date = new Date(dateStr);
        return new Date(
          Date.UTC(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds(),
          ),
        );
      } catch {
        return new Date();
      }
    };

    // Extract data from cells based on EXAMPLE_HTML structure
    const id = parseInt(cells[0].text.trim(), 10) || 0;
    const code = cells[1].text.trim();
    const country = cells[2].text.trim();
    const name = cells[3].text.trim();
    const surname = cells[4].text.trim();
    const username = cells[5].text.trim();
    const registerIp = cells[6].text.trim();
    const lastLoginIp = cells[7].text.trim(); // Using first "Last login IP" column
    const currency = cells[9].text.trim();
    const balance = parseFloat(cells[10].text.trim()) || 0;
    const bonusBalance = parseFloat(cells[11].text.trim()) || 0;
    const lastLoginDate = parseDate(cells[12].text.trim());
    const registerDate = parseDate(cells[13].text.trim());

    return {
      id,
      code,
      country,
      name,
      surname,
      username,
      registerIp,
      lastLoginIp,
      currency,
      balance,
      bonusBalance,
      lastLoginDate,
      registerDate,
    };
  }
}
