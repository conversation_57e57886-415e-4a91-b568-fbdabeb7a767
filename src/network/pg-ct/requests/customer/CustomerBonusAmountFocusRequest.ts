import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerBonusAmountFocusRequestOptions {
  javax: CustomersPreloadRequestResponse;

  bonusId: number;
  amount: number;
}

export type CustomerBonusAmountFocusResponse = {};

export class CustomerBonusAmountFocusRequest extends PGCasinoTraderRequest<CustomerBonusAmountFocusResponse, any> {
  constructor(private options: CustomerBonusAmountFocusRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'bonusFrm:inscBonusAmount',
      'javax.faces.partial.execute': 'bonusFrm:inscBonusAmount',
      'javax.faces.behavior.event': 'focus',
      'javax.faces.partial.event': 'focus',
      bonusFrm: 'bonusFrm',
      'bonusFrm:inscTraderBonusId_focus': '',
      'bonusFrm:inscTraderBonusId_input': this.options.bonusId,
      'bonusFrm:inscBonusAmount_input': this.options.amount,
      'bonusFrm:inscBonusAmount_hinput': this.options.amount,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
