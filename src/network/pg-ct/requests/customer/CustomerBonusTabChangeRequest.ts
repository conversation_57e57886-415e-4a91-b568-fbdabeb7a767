import { findJavaxElementIdBefore } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerBonusTabChangeRequestOptions {
  javax: CustomersPreloadRequestResponse;
}

export type CustomerBonusTabChangeResponse = {
  newButtonId: string;
};

export class CustomerBonusTabChangeRequest extends PGCasinoTraderRequest<CustomerBonusTabChangeResponse, any> {
  constructor(private options: CustomerBonusTabChangeRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'recordFrm:tabview',
      'javax.faces.partial.execute': 'recordFrm:tabview',
      'javax.faces.partial.render': 'recordFrm:tabview',
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      'recordFrm:tabview_contentLoad': 'true',
      'recordFrm:tabview_newTab': 'recordFrm:tabview:tabBonus',
      'recordFrm:tabview_tabindex': '2',
      recordFrm: 'recordFrm',
      // 'recordFrm:tabview:j_idt171': '<name>',
      // 'recordFrm:tabview:j_idt174': '<surname>',
      'recordFrm:tabview:customerStatusTypes_focus': '',
      'recordFrm:tabview:customerStatusTypes_input': '1',
      'recordFrm:tabview:activeNotesDt_selection': '',
      'recordFrm:tabview:activeNotesDt_scrollState': '0,0',
      'recordFrm:tabview_activeIndex': '2',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<CustomerBonusTabChangeResponse> {
    try {
      const newButtonId = findJavaxElementIdBefore(
        xml,
        'recordFrm:tabview:',
        '<span class="ui-button-text ui-c">Yeni</span>',
      );
      if (!newButtonId) {
        return {
          success: false,
          message: 'Failed to find new button id',
        };
      }

      return {
        success: true,
        data: {
          newButtonId,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse customer bonus tab change: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }
}
