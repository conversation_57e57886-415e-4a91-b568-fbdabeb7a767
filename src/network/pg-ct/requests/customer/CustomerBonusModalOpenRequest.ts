import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomerBonusTabChangeResponse } from './CustomerBonusTabChangeRequest';

export interface CustomerBonusModalOpenRequestOptions {
  javax: CustomerBonusTabChangeResponse;
}

export type CustomerBonusModalOpenResponse = {};

export class CustomerBonusModalOpenRequest extends PGCasinoTraderRequest<CustomerBonusModalOpenResponse, any> {
  constructor(private options: CustomerBonusModalOpenRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `recordFrm:tabview:${this.options.javax.newButtonId}`,
      'javax.faces.partial.execute': `recordFrm:tabview:${this.options.javax.newButtonId}`,
      'javax.faces.partial.render': 'bonusFrm:bonusDlgPanel recordFrm:tabview:bonusesToolbar',
      [`recordFrm:tabview:${this.options.javax.newButtonId}`]: `recordFrm:tabview:${this.options.javax.newButtonId}`,
      recordFrm: 'recordFrm',
      // 'recordFrm:tabview:j_idt171': 'bobby',
      // 'recordFrm:tabview:j_idt174': 'futuro',
      'recordFrm:tabview:customerStatusTypes_focus': '',
      'recordFrm:tabview:customerStatusTypes_input': '1',
      'recordFrm:tabview:activeNotesDt_selection': '',
      'recordFrm:tabview:activeNotesDt_scrollState': '0,0',
      'recordFrm:tabview:bonusesShowType_focus': '',
      'recordFrm:tabview:bonusesShowType_input': '',
      // 'recordFrm:tabview:bonusesDt:j_idt536:filter': '',
      // 'recordFrm:tabview:bonusesDt:0:j_idt510': 'on',
      'recordFrm:tabview:bonusesDt_selection': '',
      'recordFrm:tabview:bonusesDt_scrollState': '0,0',
      'recordFrm:tabview_activeIndex': '2',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
