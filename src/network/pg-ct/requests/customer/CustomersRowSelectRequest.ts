import { HttpMethod, PGCasinoTraderRequest } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomersRowSelectRequestOptions {
  javax: CustomersPreloadRequestResponse;
  id: number;
}

export type CustomersRowSelectResponse = null;

export class CustomersRowSelectRequest extends PGCasinoTraderRequest<CustomersRowSelectResponse, any> {
  constructor(private options: CustomersRowSelectRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'dtcustomers',
      'javax.faces.partial.execute': 'dtcustomers',
      'javax.faces.partial.render': 'toolbar',
      'javax.faces.behavior.event': 'rowSelect',
      'javax.faces.partial.event': 'rowSelect',
      dtcustomers_instantSelectedRowKey: this.options.id,
      frm: 'frm',
      dtcustomers_selection: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
