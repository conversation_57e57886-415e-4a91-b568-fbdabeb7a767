import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerBonusCreateRequestOptions {
  javax: CustomersPreloadRequestResponse;
  bonusId: number;
  amount: number;
}

export type CustomerBonusCreateResponse = null;

export class CustomerBonusCreateRequest extends PGCasinoTraderRequest<CustomerBonusCreateResponse, any> {
  constructor(private options: CustomerBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `bonusFrm:${this.options.javax.bonusFormSaveButtonId}`,
      'javax.faces.partial.execute': 'bonusFrm',
      [`bonusFrm:${this.options.javax.bonusFormSaveButtonId}`]: `bonusFrm:${this.options.javax.bonusFormSaveButtonId}`,
      bonusFrm: 'bonusFrm',
      'bonusFrm:inscTraderBonusId_focus': '',
      'bonusFrm:inscTraderBonusId_input': this.options.bonusId,
      'bonusFrm:inscBonusAmount_input': this.options.amount,
      'bonusFrm:inscBonusAmount_hinput': this.options.amount,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<CustomerBonusCreateResponse> {
    if (xml.includes('has an active bonus')) {
      return {
        success: false,
        message: 'Customer has an active bonus',
      };
    }

    return {
      success: true,
      data: null,
    };
  }
}
