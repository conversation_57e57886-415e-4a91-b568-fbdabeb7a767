import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerBonusSelectRequestOptions {
  javax: CustomersPreloadRequestResponse;
  bonusId: number;
}

export type CustomerBonusSelectResponse = {};

export class CustomerBonusSelectRequest extends PGCasinoTraderRequest<CustomerBonusSelectResponse, any> {
  constructor(private options: CustomerBonusSelectRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'bonusFrm:inscTraderBonusId',
      'javax.faces.partial.execute': 'bonusFrm:inscTraderBonusId',
      'javax.faces.partial.render':
        'bonusFrm:inscInitialAmount bonusFrm:inscTurnoverAmount bonusFrm:inscDepositAmount bonusFrm:inscBonusAmount bonusFrm:inscStartDate bonusFrm:inscExpiryDate bonusFrm:inscMaxBonusWin bonusFrm:inscMaxBonusAmount',
      'javax.faces.behavior.event': 'change',
      'javax.faces.partial.event': 'change',
      'bonusFrm:inscTraderBonusId_focus': '',
      'bonusFrm:inscTraderBonusId_input': this.options.bonusId,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
