import { HttpMethod, PGCasinoTraderRequest } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerEditModalShowRequestOptions {
  javax: CustomersPreloadRequestResponse;
}

export type CustomerEditModalShowResponse = null;

export class CustomerEditModalShowRequest extends PGCasinoTraderRequest<CustomerEditModalShowResponse, any> {
  constructor(private options: CustomerEditModalShowRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `recordFrm:${this.options.javax.customerFormId}`,
      'javax.faces.partial.execute': `recordFrm:${this.options.javax.customerFormId}`,
      'javax.faces.partial.render': `recordFrm:${this.options.javax.customerFormId}`,
      [`recordFrm:${this.options.javax.customerFormId}`]: `recordFrm:${this.options.javax.customerFormId}`,
      [`recordFrm:${this.options.javax.customerFormId}_contentLoad`]: 'true',
      recordFrm: 'recordFrm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
