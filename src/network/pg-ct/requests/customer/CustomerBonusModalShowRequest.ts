import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { CustomersPreloadRequestResponse } from './CustomersPreloadRequest';

export interface CustomerBonusModalShowRequestOptions {
  javax: CustomersPreloadRequestResponse;
}

export type CustomerBonusModalShowResponse = {};

export class CustomerBonusModalShowRequest extends PGCasinoTraderRequest<CustomerBonusModalShowResponse, any> {
  constructor(private options: CustomerBonusModalShowRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/customers.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `bonusFrm:${this.options.javax.bonusFormId}`,
      'javax.faces.partial.execute': `bonusFrm:${this.options.javax.bonusFormId}`,
      'javax.faces.partial.render': `bonusFrm:${this.options.javax.bonusFormId}`,
      [`bonusFrm:${this.options.javax.bonusFormId}`]: `bonusFrm:${this.options.javax.bonusFormId}`,
      [`bonusFrm:${this.options.javax.bonusFormId}_contentLoad`]: 'true',
      bonusFrm: 'bonusFrm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
