import { XMLParser } from 'fast-xml-parser';

import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { findJavaxElementId, findJavaxElementIdBefore, findJavaxViewState } from '@/network/pg-dagur/utils/javax';

export interface LoginRequestOptions {
  loginButtonId: string;

  username: string;
  password: string;
  traderCode: string;
}

export type LoginResponse = {
  loginButtonId: string;
  otpInputId: string;
};

export class LoginRequest extends PGCasinoTraderRequest<LoginResponse, any> {
  constructor(private options: LoginRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/login.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      frm: 'frm',
      txtUsername: this.options.username,
      txtPassword: this.options.password,
      txtTraderCode: this.options.traderCode,
      [this.options.loginButtonId]: '',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<LoginResponse> {
    const viewState = findJavaxViewState(xml);
    if (!viewState) {
      return {
        success: false,
        message: 'Failed to find view state',
      };
    }
    console.log(xml);
    const loginButtonId = findJavaxElementIdBefore(xml, '', '<span class="ui-button-text ui-c">Giriş</span>');
    if (!loginButtonId) {
      return {
        success: false,
        message: 'Failed to find login button id',
      };
    }

    const otpInputId = findJavaxElementId(xml, '', 'Tek kullanımlık şifre');
    if (!otpInputId) {
      return {
        success: false,
        message: 'Failed to find otp input id',
      };
    }

    return {
      success: true,
      data: {
        loginButtonId,
        otpInputId,
      },
      viewState,
    };
  }
}
