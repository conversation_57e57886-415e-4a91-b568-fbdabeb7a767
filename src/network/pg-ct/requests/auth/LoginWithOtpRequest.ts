import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';

export interface LoginWithOtpRequestOptions {
  otp: string;
  otpInputId: string;
  loginButtonId: string;
}

export type LoginWithOtpResponse = null;

export class LoginWithOtpRequest extends PGCasinoTraderRequest<LoginWithOtpResponse, any> {
  constructor(private options: LoginWithOtpRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/login.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      frm: 'frm',
      [this.options.otpInputId]: this.options.otp,
      [this.options.loginButtonId]: this.options.loginButtonId,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<LoginWithOtpResponse> {
    if (xml.includes('Casino Administration') === false) {
      return {
        success: false,
        message: 'Failed to login',
      };
    }

    return {
      success: true,
      data: null,
    };
  }
}
