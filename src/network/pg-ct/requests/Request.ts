export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export abstract class PGCasinoTraderRequest<ResponseModel = any, Body = any> {
  abstract getPath(): string;

  getPreloadRequest(): PGCasinoTraderRequest | null {
    return null;
  }

  abstract getMethod(): HttpMethod;

  abstract getBody(): Body;

  abstract getHeaders(): Record<string, string>;

  validateResponse(xml: string): PGCasinoTraderRequestResponse<ResponseModel> {
    if (1 == 1) {
      return {
        data: xml as any,
        success: true,
      };
    } else {
      return {
        message: 'Something went wrong',
        success: false,
      };
    }
  }
}

export type PGCasinoTraderRequestSuccessResponse<T = any> = {
  data: T;
  viewState?: string;
  success: true;
};

export type PGCasinoTraderRequestErrorResponse = {
  success: false;
  viewState?: string;
  message: string;
};

export type PGCasinoTraderRequestResponse<T> =
  | PGCasinoTraderRequestSuccessResponse<T>
  | PGCasinoTraderRequestErrorResponse;
