import { findJavaxElementId, findJavaxElementIdBefore, findJavaxViewState } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';

export interface FreespinBonusPreloadRequestResponse {
  addBonusFormId: string;
  addButtonId: string;
  cancelButtonId: string;
}

export class FreespinBonusPreloadRequest extends PGCasinoTraderRequest<FreespinBonusPreloadRequestResponse, any> {
  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusPreloadRequestResponse> {
    try {
      const viewState = findJavaxViewState(xml);
      if (!viewState) {
        return {
          success: false,
          message: 'Failed to find view state',
        };
      }

      const addButtonId = findJavaxElementIdBefore(xml, '', 'Ekle');
      if (!addButtonId) {
        return {
          success: false,
          message: 'Failed to find add button id',
        };
      }

      const cancelButtonId = findJavaxElementIdBefore(xml, '', 'İptal et');
      if (!cancelButtonId) {
        return {
          success: false,
          message: 'Failed to find cancel button id',
        };
      }

      const addBonusFormId = findJavaxElementId(xml, 'addFreeSpinFrm:', 'form id="addFreeSpinFrm"');
      if (!addBonusFormId) {
        return {
          success: false,
          message: 'Failed to find freespin bonus form id',
        };
      }

      return {
        success: true,
        data: {
          addBonusFormId,
          addButtonId,
          cancelButtonId,
        },
        viewState,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus preload: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
