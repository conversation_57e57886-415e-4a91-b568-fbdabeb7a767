import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';
import { parse } from 'node-html-parser';

export interface FreespinBonusSelectGameRequestOptions {
  javax: FreespinBonusPreloadRequestResponse & FreespinBonusEnterCreateModeResponse;

  gameId: number;
  gameIds: number[];
}

export type FreespinBonusSelectGameResponse = number[];

export class FreespinBonusSelectGameRequest extends PGCasinoTraderRequest<FreespinBonusSelectGameResponse, any> {
  constructor(private options: FreespinBonusSelectGameRequestOptions) {
    super();
    this.options.javax = options.javax;
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:tabview:selectedGames`,
      'javax.faces.partial.execute': `addFreeSpinFrm:tabview:selectedGames`,
      'javax.faces.behavior.event': 'rowSelectCheckbox',
      'javax.faces.partial.event': 'rowSelectCheckbox',
      [`addFreeSpinFrm:tabview:selectedGames_instantSelectedRowKey`]: this.options.gameId,
      addFreeSpinFrm: 'addFreeSpinFrm',

      'addFreeSpinFrm:tabview:selectedGames_selection': this.options.gameIds.join(','),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusSelectGameResponse> {
    try {
      // Parse HTML and extract bet amounts from select element
      const betAmounts = this.extractBetAmounts(xml);

      return {
        success: true,
        data: betAmounts,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus select game: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }

  private extractBetAmounts(html: string): number[] {
    try {
      // Parse HTML using node-html-parser
      const root = parse(html);
      console.log(html);

      // Find the select element with id "addFreeSpinFrm:betAmountsMenu_input"
      const select = root.querySelector('#addFreeSpinFrm\\:betAmountsMenu_input');
      if (!select) {
        return [];
      }

      // Extract all option values and parse them as floats
      const options = select.querySelectorAll('option');
      const betAmounts: number[] = [];

      for (const option of options) {
        const value = option.getAttribute('value');
        if (value) {
          const floatValue = parseFloat(value);
          if (!isNaN(floatValue)) {
            betAmounts.push(floatValue);
          }
        }
      }

      return betAmounts;
    } catch (error) {
      // If parsing fails, return empty array as specified
      return [];
    }
  }
}
