import { findJavaxElementIdBefore } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';

export interface FreespinBonusTabChangeRequestOptions {
  javax: FreespinBonusPreloadRequestResponse & FreespinBonusEnterCreateModeResponse;

  tab: 'customers' | 'games';
}

export type FreespinBonusTabChangeResponse = {
  searchButtonId: string;
};

export class FreespinBonusTabChangeRequest extends PGCasinoTraderRequest<FreespinBonusTabChangeResponse, any> {
  constructor(private options: FreespinBonusTabChangeRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:tabview`,
      'javax.faces.partial.execute': `addFreeSpinFrm:tabview`,
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      'addFreeSpinFrm:tabview_newTab': `addFreeSpinFrm:tabview:tabSelect${
        this.options.tab[0]?.toUpperCase() + this.options.tab.slice(1)
      }`,
      'addFreeSpinFrm:tabview_tabindex': this.options.tab === 'customers' ? 1 : 0,
      addFreeSpinFrm: 'addFreeSpinFrm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusTabChangeResponse> {
    try {
      const searchButtonId = findJavaxElementIdBefore(
        xml,
        'addFreeSpinFrm:tabview:',
        '<span class="ui-button-text ui-c">Ara</span>',
      );
      if (!searchButtonId) {
        return {
          success: false,
          message: 'Failed to find search button id',
        };
      }

      return {
        success: true,
        data: {
          searchButtonId,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus tab change: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }
}
