import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';
import parse from 'node-html-parser';

export interface FreespinBonusChangeCreateFormRequestOptions {
  javax: FreespinBonusPreloadRequestResponse & FreespinBonusEnterCreateModeResponse;

  field: 'vendor' | 'cmbCurrency';
  value: string;
}

export type FreespinBonusChangeCreateFormResponse = {
  currencies: { name: string; id: number }[];
};

export class FreespinBonusChangeCreateFormRequest extends PGCasinoTraderRequest<
  FreespinBonusChangeCreateFormResponse,
  any
> {
  constructor(private options: FreespinBonusChangeCreateFormRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:${this.options.field}`,
      'javax.faces.partial.execute': `addFreeSpinFrm:${this.options.field}`,
      'javax.faces.partial.render': 'addFreeSpinFrm:addFreeSpinDlgPanel',
      'javax.faces.behavior.event': 'change',
      'javax.faces.partial.event': 'change',
      addFreeSpinFrm: 'addFreeSpinFrm',
      [`addFreeSpinFrm:${this.options.field}_focus`]: '',
      [`addFreeSpinFrm:${this.options.field}_input`]: this.options.value,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusChangeCreateFormResponse> {
    const currencies = this.extractCurrencies(xml);

    return {
      success: true,
      data: {
        currencies,
      },
    };
  }

  private extractCurrencies(xml: string): { name: string; id: number }[] {
    try {
      // Parse HTML and extract bet amounts from select element
      const currencies = this.extractCurrenciesFromSelect(xml);

      return currencies;
    } catch (error) {
      return [];
    }
  }

  private extractCurrenciesFromSelect(html: string): { name: string; id: number }[] {
    try {
      // Parse HTML using node-html-parser
      const root = parse(html);

      // Find the select element with id "addFreeSpinFrm:cmbCurrency_input"
      const select = root.querySelector('#addFreeSpinFrm\\:cmbCurrency_input');
      if (!select) {
        return [];
      }

      // Extract all option elements
      const options = select.querySelectorAll('option');
      const currencies: { name: string; id: number }[] = [];

      for (const option of options) {
        const value = option.getAttribute('value');
        const name = option.text?.trim();

        if (value && name) {
          const id = parseInt(value, 10);
          if (!isNaN(id)) {
            currencies.push({
              id,
              name,
            });
          }
        }
      }

      return currencies;
    } catch (error) {
      // If parsing fails, return empty array as specified
      return [];
    }
  }
}
