import { findJavaxElementIdBefore } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';

export interface FreespinBonusEnterCreateModeRequestOptions {
  javax: FreespinBonusPreloadRequestResponse;
}

export type FreespinBonusEnterCreateModeResponse = {
  createButtonId: string;
  nameFilterId: string;
};

export class FreespinBonusEnterCreateModeRequest extends PGCasinoTraderRequest<
  FreespinBonusEnterCreateModeResponse,
  any
> {
  constructor(private options: FreespinBonusEnterCreateModeRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:${this.options.javax.addBonusFormId}`,
      'javax.faces.partial.execute': `addFreeSpinFrm:${this.options.javax.addBonusFormId}`,
      'javax.faces.partial.render': `addFreeSpinFrm:${this.options.javax.addBonusFormId}`,
      [`addFreeSpinFrm:${this.options.javax.addBonusFormId}`]: `addFreeSpinFrm:${this.options.javax.addBonusFormId}`,
      [`addFreeSpinFrm:${this.options.javax.addBonusFormId}_contentLoad`]: 'true',
      addFreeSpinFrm: 'addFreeSpinFrm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusEnterCreateModeResponse> {
    try {
      const createButtonId = findJavaxElementIdBefore(xml, 'addFreeSpinFrm:', 'Kaydet');
      if (!createButtonId) {
        return {
          success: false,
          message: 'Failed to find create button id',
        };
      }

      const nameFilterId = findJavaxElementIdBefore(
        xml,
        'addFreeSpinFrm:tabview:selectedGames:',
        '<span class="ui-column-title">Adı</span>',
      );
      if (!nameFilterId) {
        return {
          success: false,
          message: 'Failed to find name filter id',
        };
      }

      return {
        success: true,
        data: {
          createButtonId,
          nameFilterId,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus enter create mode: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }
}
