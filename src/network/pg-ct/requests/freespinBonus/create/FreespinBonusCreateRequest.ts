import { formatDate } from '@/network/pg-dagur/utils/time';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';

export interface FreespinBonusCreateRequestOptions {
  javax: FreespinBonusPreloadRequestResponse & FreespinBonusEnterCreateModeResponse;

  providerId: number;
  customerId: number;

  currencyId?: number;
  nOfFreespins?: number;
  betAmount?: number;
  betPerLine?: number;
  maxWin?: number;
  minWinAmountRequired?: number;
  freeRoundBalance?: number;
  lines?: number;
  coins?: number;
  denomination?: number;

  gameIds: number[];
  expiresAt: Date;
}

export type FreespinBonusCreateResponse = null;

export class FreespinBonusCreateRequest extends PGCasinoTraderRequest<FreespinBonusCreateResponse, any> {
  constructor(private options: FreespinBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:${this.options.javax.createButtonId}`,
      'javax.faces.partial.execute': '@all',
      'javax.faces.partial.render': 'addFreeSpinFrm:freespinDetails',
      [`addFreeSpinFrm:${this.options.javax.createButtonId}`]: `addFreeSpinFrm:${this.options.javax.createButtonId}`,
      addFreeSpinFrm: 'addFreeSpinFrm',
      'addFreeSpinFrm:vendor_focus': '',
      'addFreeSpinFrm:vendor_input': this.options.providerId,
      'addFreeSpinFrm:tabview:dtcustomers_selection': this.options.customerId,
      'addFreeSpinFrm:tabview:selectedGames_selection': this.options.gameIds.join(','),
      'addFreeSpinFrm:freeRoundValidity_input': formatDate(this.options.expiresAt),

      'addFreeSpinFrm:numberOfFreeRounds_input': this.options.nOfFreespins,
      'addFreeSpinFrm:numberOfFreeRounds_hinput': this.options.nOfFreespins,
      'addFreeSpinFrm:freeSpinWinMax_input': this.options.maxWin,
      'addFreeSpinFrm:freeSpinWinMax_hinput': this.options.maxWin,

      'addFreeSpinFrm:cmbCurrency_input': this.options.currencyId,
      'addFreeSpinFrm:betPerLine_input': this.options.betPerLine,
      'addFreeSpinFrm:betPerLine_hinput': this.options.betPerLine,
      'addFreeSpinFrm:minWinAmountRequired_input': this.options.minWinAmountRequired,
      'addFreeSpinFrm:minWinAmountRequired_hinput': this.options.minWinAmountRequired,
      'addFreeSpinFrm:betAmountsMenu_input': this.options.betAmount,
      'addFreeSpinFrm:initialBalance_input': this.options.freeRoundBalance,
      'addFreeSpinFrm:initialBalance_hinput': this.options.freeRoundBalance,
      'addFreeSpinFrm:lines_input': this.options.lines,
      'addFreeSpinFrm:lines_hinput': this.options.lines,
      'addFreeSpinFrm:coins_input': this.options.coins,
      'addFreeSpinFrm:coins_hinput': this.options.coins,
      'addFreeSpinFrm:denomination_input': this.options.denomination,
      'addFreeSpinFrm:denomination_hinput': this.options.denomination,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusCreateResponse> {
    try {
      if (xml.includes('error')) {
        return {
          success: false,
          message: 'Failed to create freespin bonus: validation error',
        };
      }

      return {
        success: true,
        data: null,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus create: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
