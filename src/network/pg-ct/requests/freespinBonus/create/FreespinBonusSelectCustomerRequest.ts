import { HttpMethod, PGCasinoTraderRequest } from '../../Request';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';

export interface FreespinBonusSelectCustomerRequestOptions {
  javax: FreespinBonusPreloadRequestResponse & FreespinBonusEnterCreateModeResponse;

  customerId: number;
}

export type FreespinBonusSelectCustomerResponse = null;

export class FreespinBonusSelectCustomerRequest extends PGCasinoTraderRequest<
  FreespinBonusSelectCustomerResponse,
  any
> {
  constructor(private options: FreespinBonusSelectCustomerRequestOptions) {
    super();
    this.options.javax = options.javax;
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:tabview:dtcustomers`,
      'javax.faces.partial.execute': `addFreeSpinFrm:tabview:dtcustomers`,
      'javax.faces.behavior.event': 'rowSelectCheckbox',
      'javax.faces.partial.event': 'rowSelectCheckbox',
      [`addFreeSpinFrm:tabview:dtcustomers_instantSelectedRowKey`]: this.options.customerId,
      addFreeSpinFrm: 'addFreeSpinFrm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
