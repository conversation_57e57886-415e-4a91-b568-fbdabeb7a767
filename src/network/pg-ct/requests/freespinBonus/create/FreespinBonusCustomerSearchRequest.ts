import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { FreespinBonusPreloadRequestResponse } from '../FreespinBonusPreloadRequest';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';
import { FreespinBonusTabChangeResponse } from './FreespinBonusTabChangeRequest';
import { parse } from 'node-html-parser';

export interface FreespinBonusCustomerSearchRequestOptions {
  javax: FreespinBonusPreloadRequestResponse & FreespinBonusEnterCreateModeResponse & FreespinBonusTabChangeResponse;

  customerId?: number;
  customerCode?: string;
}

export type FreespinBonusCustomerSearchResponse = { customerId: number; customerCode: string }[];

export class FreespinBonusCustomerSearchRequest extends PGCasinoTraderRequest<
  FreespinBonusCustomerSearchResponse,
  any
> {
  constructor(private options: FreespinBonusCustomerSearchRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:tabview:${this.options.javax.searchButtonId}`,
      'javax.faces.partial.execute': `addFreeSpinFrm:tabview:searchFrm`,
      'javax.faces.partial.render': `addFreeSpinFrm:tabview:dtcustomers`,
      [`addFreeSpinFrm:tabview:${this.options.javax.searchButtonId}`]: `addFreeSpinFrm:tabview:${this.options.javax.searchButtonId}`,
      'addFreeSpinFrm:tabview:searchFrm': 'addFreeSpinFrm:tabview:searchFrm',
      'addFreeSpinFrm:tabview:crId': this.options.customerId ?? '',
      'addFreeSpinFrm:tabview:crName': '',
      'addFreeSpinFrm:tabview:crSurname': '',
      'addFreeSpinFrm:tabview:crUsername': '',
      'addFreeSpinFrm:tabview:crCustomerCode': this.options.customerCode ?? '',
      'addFreeSpinFrm:tabview:crRegisterIp': '',
      'addFreeSpinFrm:tabview:crLastLoginIp': '',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusCustomerSearchResponse> {
    try {
      const customers = this.extractCustomers(xml);

      return {
        success: true,
        data: customers,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus customer search: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }

  private extractCustomers(xml: string): FreespinBonusCustomerSearchResponse {
    const doc = parse(xml);

    const table = doc.getElementById('addFreeSpinFrm:tabview:dtcustomers_data');
    if (!table) {
      throw new Error('Failed to find customers table');
    }

    const rows = table.querySelectorAll('tr');
    const customers: Array<{ customerId: number; customerCode: string }> = [];

    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 3) {
        // Second column (index 1) contains customer ID
        const customerIdText = cells[1]?.text?.trim();
        // Third column (index 2) contains customer code
        const customerCodeText = cells[2]?.text?.trim();

        if (customerIdText && customerCodeText) {
          const customerId = parseInt(customerIdText, 10);
          if (!isNaN(customerId)) {
            customers.push({
              customerId,
              customerCode: customerCodeText,
            });
          }
        }
      }
    }

    if (customers.length === 0) {
      throw new Error('No customers found in table');
    }

    return customers;
  }
}
