import { Game } from '@/network/pg-ct/dto/Game';
import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../../Request';
import { parse } from 'node-html-parser';
import { FreespinBonusEnterCreateModeResponse } from './FreespinBonusEnterCreateModeRequest';

export interface FreespinBonusGameListRequestOptions {
  javax: FreespinBonusEnterCreateModeResponse;

  providerId: number;
  name?: string;

  page: number;
  limit: number;
}

export type FreespinBonusGameListResponse = Game[];

export class FreespinBonusGameListRequest extends PGCasinoTraderRequest<FreespinBonusGameListResponse, any> {
  constructor(private options: FreespinBonusGameListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const isFiltering = !!this.options.name;

    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `addFreeSpinFrm:tabview:selectedGames`,
      'javax.faces.partial.execute': `addFreeSpinFrm:tabview:selectedGames`,
      'javax.faces.partial.render': `addFreeSpinFrm:tabview:selectedGames`,
      'addFreeSpinFrm:tabview:selectedGames': 'addFreeSpinFrm:tabview:selectedGames',
      'addFreeSpinFrm:tabview:selectedGames_pagination': 'true',
      'addFreeSpinFrm:tabview:selectedGames_first': (this.options.page - 1) * this.options.limit,
      'addFreeSpinFrm:tabview:selectedGames_rows': this.options.limit,
      'addFreeSpinFrm:tabview:selectedGames_filtering': isFiltering.toString(),
      'addFreeSpinFrm:tabview:selectedGames_encodeFeature': 'true',
      [`addFreeSpinFrm:tabview:selectedGames:${this.options.javax.nameFilterId}:filter`]: this.options.name || '',
      addFreeSpinFrm: 'addFreeSpinFrm',
      'addFreeSpinFrm:vendor_input': this.options.providerId,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusGameListResponse> {
    try {
      // Parse HTML and extract games data from the table
      const games = this.extractGamesFromTable(xml);

      return {
        success: true,
        data: games,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus game list: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }

  private extractGamesFromTable(html: string): Game[] {
    try {
      // Parse HTML using node-html-parser
      const root = parse(html);

      // Extract all table rows with data-ri attribute (data rows)
      const rows = root.querySelectorAll('tr[data-ri]');
      const games: Game[] = [];

      for (const row of rows) {
        const cells = row.querySelectorAll('td[role="gridcell"]');

        // Skip the first cell (checkbox column) and extract the data from remaining cells
        // Expected columns: [checkbox], ID, Vendor Game Id, Name, Game Type
        if (cells.length >= 5) {
          const idText = cells[1]?.text?.trim();
          const providerGameId = cells[2]?.text?.trim();
          const name = cells[3]?.text?.trim();
          const gameType = cells[4]?.text?.trim();

          // Parse ID as number
          const id = idText ? parseInt(idText, 10) : 0;

          if (id && providerGameId && name && gameType) {
            games.push({
              id,
              providerGameId,
              name,
              gameType,
            });
          }
        }
      }

      return games;
    } catch (error) {
      // If parsing fails, return empty array
      return [];
    }
  }
}
