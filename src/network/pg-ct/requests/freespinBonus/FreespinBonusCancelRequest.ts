import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { FreespinBonusPreloadRequestResponse } from './FreespinBonusPreloadRequest';

export interface FreespinBonusCancelRequestOptions {
  javax: FreespinBonusPreloadRequestResponse;
  id: number;
}

export type FreespinBonusCancelResponse = null;

export class FreespinBonusCancelRequest extends PGCasinoTraderRequest<FreespinBonusCancelResponse, any> {
  constructor(private options: FreespinBonusCancelRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.cancelButtonId,
      'javax.faces.partial.execute': '@all',
      [this.options.javax.cancelButtonId]: this.options.javax.cancelButtonId,
      formFreeSpinDt: 'formFreeSpinDt',
      dtFreeSpins_selection: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusCancelResponse> {
    try {
      if (xml.includes('redirect url="/casino-trader/freespins.xhtml"') === false) {
        return {
          success: false,
          message: 'Failed to find redirect url',
        };
      }

      return {
        success: true,
        data: null,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus cancel: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
