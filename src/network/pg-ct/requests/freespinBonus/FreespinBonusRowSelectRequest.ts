import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from '../Request';
import { FreespinBonusPreloadRequestResponse } from './FreespinBonusPreloadRequest';

export interface FreespinBonusRowSelectRequestOptions {
  javax: FreespinBonusPreloadRequestResponse;
  id: number;
}

export type FreespinBonusRowSelectResponse = null;

export class FreespinBonusRowSelectRequest extends PGCasinoTraderRequest<FreespinBonusRowSelectResponse, any> {
  constructor(private options: FreespinBonusRowSelectRequestOptions) {
    super();
  }

  getPath(): string {
    return `/casino-trader/freespins.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `dtFreeSpins`,
      'javax.faces.partial.execute': `dtFreeSpins`,
      'javax.faces.partial.render': `formFreeSpinDt formSearch`,
      'javax.faces.behavior.event': 'rowSelect',
      'javax.faces.partial.event': 'rowSelect',
      dtFreeSpins_instantSelectedRowKey: this.options.id,
      formFreeSpinDt: 'formFreeSpinDt',
      dtFreeSpins_selection: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<FreespinBonusRowSelectResponse> {
    try {
      if (xml.includes('formSearch') === false) {
        return {
          success: false,
          message: 'Failed to find formSearch in response',
        };
      }

      return {
        success: true,
        data: null,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse freespin bonus row select: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }
}
