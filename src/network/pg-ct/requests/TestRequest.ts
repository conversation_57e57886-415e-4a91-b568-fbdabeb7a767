import { HttpMethod, PGCasinoTraderRequest, PGCasinoTraderRequestResponse } from './Request';

export class TestRequest extends PGCasinoTraderRequest<string, any> {
  getPath(): string {
    return `/casino-trader/trader-bonuses.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGCasinoTraderRequestResponse<string> {
    if (xml.includes('Manual Bonus') === false) {
      return {
        success: false,
        message: 'Failed to load bonuses',
      };
    }

    return {
      success: true,
      data: xml,
    };
  }
}
