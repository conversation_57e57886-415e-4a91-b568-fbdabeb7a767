export type BonusListItem = {
  id: number;
  // @ todo enum: manual bonus, etc
  type: string;
  name: string;
  description: string;
  code: string;
  startDate: Date;
  endDate: Date;
  // active or passive
  status: string;
};

export const BONUS_STATUSES = ['inactive', 'active'] as const;
export type BonusStatus = (typeof BONUS_STATUSES)[number];

export const BONUS_TYPES = [
  '',
  'deposit_bonus',
  'manual_bonus',
  'reward_bonus',
  '@TODO:1',
  'automatic_deposit_bonus',
  'automatic_registration_bonus',
] as const;
export type BonusType = (typeof BONUS_TYPES)[number];

export type Bonus = {
  id: number;
  type: BonusType;
  name: string;
  description: string;
  code: string;
  startDate: Date;
  endDate: Date;
  status: BonusStatus;
  currencyId: number;
  currencyCode: string;
  shouldConfiscateOnExpiration: boolean;
  shouldConfiscateOnForfeit: boolean;
  isStickyBonus: boolean;
  bonusAmount: number | null;
  maxBonusWin: number | null;
  turnoverRate: number;
  maxNumberOfClaims: number | null;
  validityPeriodInDays: number | null;
  bonusBudget: number | null;
  remainingBonusBudget: number | null;
  minDepositAmount: number | null;
  // @todo
  excludedGames: any[];
  focusedGames: any[];
  excludedCustomers: any[];
  focusedCustomers: any[];
  excludedCountries: any[];
  focusedCountries: any[];
  termsAndConditions: any[];
};
