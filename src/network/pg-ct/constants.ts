export const FREESPIN_BONUS_SCHEMAS: { providerName: string; providerId: number; steps: Record<string, any>[] }[] = [
  {
    providerName: 'Big Time Gaming',
    providerId: 138,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            required: true,
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Blueprint Gaming',
    providerId: 26,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputType: 'select',
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            required: true,
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'EGT Digital',
    providerId: 156,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            required: true,
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Evolution',
    providerId: 130,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 2 } },
          freeRoundBalance: { type: 'float', required: true, inputOptions: { name: 'Free Round Balance', order: 3 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 5 },
          },
        },
      },
    ],
  },
  {
    providerName: 'NetEnt',
    providerId: 133,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            required: true,
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: "Play'n GO",
    providerId: 61,
    steps: [
      {
        schema: {
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          lines: { type: 'integer', inputOptions: { name: 'Lines', order: 6 } },
          coins: { type: 'float', inputOptions: { name: 'Coins', order: 7 } },
          denomination: { type: 'float', inputOptions: { name: 'Denomination', order: 8 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 9 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Pragmatic Play',
    providerId: 80,
    steps: [
      {
        schema: {
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          betPerLine: { type: 'float', required: true, inputOptions: { name: 'Bet Per Line', order: 6 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 7 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Red Tiger',
    providerId: 134,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            required: true,
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'VoltEnt',
    providerId: 3,
    steps: [
      {
        schema: {
          currencyId: {
            type: 'integer',
            required: true,
            inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 },
          },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            required: true,
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          minWinAmountRequired: {
            type: 'float',
            inputOptions: { name: 'Min Win Amount Required', order: 6 },
          },
        },
      },
    ],
  },
];
