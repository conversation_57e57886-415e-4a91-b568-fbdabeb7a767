import { client as WebSocket } from 'websocket';
import { IPGDagurHttpClient } from './pg-dagur';
import { AccountingLoginRequest } from './requests/auth/AccountingLoginRequest';

export interface ListTransactionsOptions {
  startDate: Date;
  endDate: Date;
  /**
   * N - new
   * P - pending
   * Z - reserved
   * V - valid
   * C - confirmed
   * R - rejected
   */
  status: string[];
  /**
   * D - deposit
   * W - withdrawal
   */
  masterType?: string;
  username?: string;
  page: number;
  limit: number;
  /**
   * Whether to load subtotals along with transactions
   * Used internally by our backend to determine if subtotals should be fetched
   */
  loadSubtotals?: boolean;
}

export interface ListTransactionsResponse {
  transactions: {
    total: number;
    pageSize: number;
    offset: number;
    items: TransactionRecord[];
  };
  subtotals: TransactionSubtotal | null;
}

export interface TransactionRecord {
  username: string;
  fullName: string;
  identity: string;
  country: string;
  customerId: number;
  currentBalance: number;
  traderId: number;
  // @todo other fields
}

export interface TransactionSubtotal {
  appUserId: number;
  cacheTime: number;
  endDate: number;
  startDate: number;
  totalConfirmedDeposits: number;
  totalConfirmedWithdrawals: number;
  totalDeposits: number;
  totalRejectedDeposits: number;
  totalRejectedWithdrawals: number;
  totalWaitingDeposits: number;
  totalWaitingWithdrawals: number;
  totalWithdrawals: number;
  traderId: number;
  visibleCurrency: {
    currencyId: number;
    currencyCode: string;
    currencyName: string;
    orderBy: number;
    currencySymbol: string;
    webMultiType: string;
    webMultiKey: string;
    currencyType: string;
    decimals: number;
    decimalInfo: string;
  };
}

export interface TransactionListEvent {
  event: 'TRANSACTION_RESPONSE';
  offset: number;
  pageSize: number;
  total: number;
  messages: TransactionRecord[];
}

export interface TransactionSubtotalsEvent {
  event: 'SUB_TOTALS';
  subTotals: TransactionSubtotal;
}

export interface TransactionAuthSuccessEvent {
  event: 'AUTHENTICATION_SUCCESS';
}

export interface TransactionPongEvent {
  event: 'PONG';
}

export interface TransactionErrorEvent {
  event: 'ERROR';
  errorCode: string;
  paramMap: Record<string, any>;
}

export interface TransactionTraderLoginNotFoundEvent {
  event: 'TRADER_LOGIN_NOT_FOUND_IN_SESSION';
}

export type TransactionEvent =
  | TransactionListEvent
  | TransactionSubtotalsEvent
  | TransactionAuthSuccessEvent
  | TransactionPongEvent
  | TransactionErrorEvent
  | TransactionTraderLoginNotFoundEvent;

const WS_URL = `wss://jord.pronetgaming.eu/accounting`;

export class AccountingApiClient {
  private readonly httpClient: IPGDagurHttpClient;
  private accessToken: string | null = null;
  private lastTokenTime: number = 0;
  private readonly TOKEN_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor(httpClient: IPGDagurHttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * Check if access token needs refresh (5 minutes have passed or no token exists)
   */
  private needsTokenRefresh(): boolean {
    const now = Date.now();
    const timeSinceLastToken = now - this.lastTokenTime;

    if (!this.accessToken) {
      console.log('🔐 No Dagur accounting access token found, refresh required');
      return true;
    }

    if (timeSinceLastToken >= this.TOKEN_INTERVAL) {
      console.log(
        `🔐 ${Math.floor(
          timeSinceLastToken / (60 * 1000),
        )} minutes since last Dagur accounting token, refresh required`,
      );
      return true;
    }

    const minutesRemaining = Math.floor((this.TOKEN_INTERVAL - timeSinceLastToken) / (60 * 1000));
    const secondsRemaining = Math.floor(((this.TOKEN_INTERVAL - timeSinceLastToken) % (60 * 1000)) / 1000);
    console.log(`🔐 Dagur accounting token still valid for ${minutesRemaining}m ${secondsRemaining}s`);
    return false;
  }

  private async getAccessToken(): Promise<string> {
    if (!this.needsTokenRefresh()) {
      return this.accessToken!;
    }

    console.log('🔑 Refreshing Dagur accounting access token...');
    const response = await this.httpClient.makeRequest(new AccountingLoginRequest());
    if (!response.success) {
      throw new Error(`Failed to get access token: ${response.message}`);
    }

    this.accessToken = response.data;
    this.lastTokenTime = Date.now();

    console.log('✅ Dagur accounting access token refreshed successfully');
    console.log(
      `🕐 Next Dagur accounting token refresh at: ${new Date(this.lastTokenTime + this.TOKEN_INTERVAL).toISOString()}`,
    );

    return response.data;
  }

  async listTransactions(options: ListTransactionsOptions): Promise<ListTransactionsResponse> {
    const payload = new URLSearchParams({
      event: 'TRANSACTION_REQUEST',
      startDate: options.startDate.getTime().toString(),
      endDate: options.endDate.getTime().toString(),
      status: options.status.join(','),
      offset: options.page.toString(),
      pageSize: options.limit.toString(),
    });
    if (options.masterType) {
      payload.append('masterType', options.masterType);
    }
    if (options.username) {
      payload.append('username', options.username);
    }

    const accessToken = await this.getAccessToken();

    return new Promise((resolve, reject) => {
      let transactions: ListTransactionsResponse['transactions'] | null = null;
      let subtotals: TransactionSubtotal | null = null;
      let collected = false;

      const ws = new WebSocket();
      ws.connect(`${WS_URL}/Bearer%20${accessToken}`);
      ws.on('connect', (connection) => {
        const timeout = setTimeout(() => {
          connection.close();
          reject('WS connection timeout');
        }, 60_000);

        connection.on('message', (message) => {
          if (message.type !== 'utf8') {
            reject('Invalid message type');
          } else {
            try {
              console.log(`Received message: ${message.utf8Data}`);
              const data = JSON.parse(message.utf8Data) as TransactionEvent;
              switch (data.event) {
                case 'SUB_TOTALS':
                  subtotals = data.subTotals;
                  break;
                case 'TRANSACTION_RESPONSE':
                  transactions = {
                    total: data.total,
                    pageSize: data.pageSize,
                    offset: data.offset,
                    items: data.messages ?? [],
                  };
                  if (data.offset > 1) {
                    collected = true;
                  }
                  break;
                case 'TRADER_LOGIN_NOT_FOUND_IN_SESSION':
                  reject(data.event);
                  break;
                case 'ERROR':
                  if (data.errorCode === 'NO_RECORD_FOUND') {
                    transactions = {
                      total: 0,
                      pageSize: 0,
                      offset: 0,
                      items: [],
                    };
                    collected = true;
                  } else {
                    reject(data.errorCode);
                  }

                  break;
                case 'AUTHENTICATION_SUCCESS':
                case 'PONG':
                default:
                  break;
              }

              // Determine if we should wait for subtotals based on loadSubtotals option
              const shouldWaitForSubtotals = options.loadSubtotals !== false; // Default to true if not specified
              const readyToResolve = transactions && (collected || (shouldWaitForSubtotals ? subtotals : true));

              if (readyToResolve) {
                clearTimeout(timeout);
                connection.close();
                resolve({
                  transactions: transactions!,
                  subtotals: shouldWaitForSubtotals ? subtotals : null,
                });
              }
            } catch (e) {
              reject((e as Error).message);
            }
          }
        });

        connection.on('error', () => {
          reject('WS connection error');
        });

        connection.sendUTF(payload.toString());
        connection.sendUTF('event=PING');
      });
    });
  }
}
