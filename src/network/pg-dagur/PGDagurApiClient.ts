import fetch from 'node-fetch';
import { PGDagurRequest } from './requests/Request';
import { TOTP } from 'totp-generator';
import { LoginRequest } from './requests/auth/LoginRequest';
import { LoginWithOtpRequest } from './requests/auth/LoginWithOtpRequest';
import { XMLParser } from 'fast-xml-parser';
import { IPGDagurHttpClient } from './pg-dagur';
import { AccountingApiClient } from './AccountingApiClient';
import { BetReportsApiClient } from './BetReportsApiClient';
import { TimezoneChangePreloadRequest, TimezoneChangeRequest } from './requests/configuration/TimezoneChangeRequest';

const BASE_URL = 'https://dagur.pgbo.io';

export interface PronetDagurSession {
  cookies: {
    DAGUR_CLIENT_TIMEZONE: string;
    JSESSIONID: string;
    __nxquid: string;
  };
}

/**
 * Pronet API Client
 *
 * This client handles all communication with the Pronet API system.
 * It automatically handles authentication, checksum generation, and request formatting.
 */
export class PGDagurHttpClient implements IPGDagurHttpClient {
  // accounting:

  async makeRequest<ResponseModel extends any, Request extends PGDagurRequest<ResponseModel>>(
    request: Request,
    viewState: string = '',
    additionalHeaders: Record<string, any> = {},
  ): Promise<ReturnType<Request['validateResponse']>> {
    try {
      const url = `${BASE_URL}${request.getPath()}`;

      console.log('🚀 Making request...');
      console.log(`📍 URL: ${request.getMethod()} ${url}`);

      let body: URLSearchParams | undefined = undefined;
      if (request.getMethod() !== 'GET') {
        const bodyData = {
          'javax.faces.ViewState': viewState,
          ...request.getBody(),
        };
        body = new URLSearchParams(bodyData);

        console.log('📋 Request Body Data (before URLSearchParams):');
        console.log(JSON.stringify(bodyData, null, 2));
        console.log('📋 Request Body (URLSearchParams string):');
        console.log(body.toString());
        console.log('📋 ViewState length:', viewState.length);
      }

      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        Origin: BASE_URL,
        ...request.getHeaders(BASE_URL),
        ...additionalHeaders,
      };

      console.log('📋 Request Headers:');
      console.log(JSON.stringify(headers, null, 2));

      console.log('🌐 Making fetch request to Dagur...');
      const response = await fetch(url, {
        method: request.getMethod(),
        headers: headers,
        body: body,
      });

      console.log('📥 Response received from Dagur:');
      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      console.log('📊 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

      const data = await response.text();
      console.log('📄 Response body length:', data.length, 'characters');

      if (data.includes('redirect url="/login.xhtml"')) {
        console.error('❌ Authentication failed - redirected to login');
        throw new Error('Unauthorized');
      }

      if (data.includes('action="/login.xhtml') && request.getPath() !== '/login.xhtml') {
        console.error('❌ Authentication failed - login form detected in response');
        throw new Error('Unauthorized');
      }

      if (!response.ok) {
        console.error('❌ HTTP Error Response:');
        console.error('📊 Status:', response.status, response.statusText);
        console.error('📄 Response Body:', data);
        throw new Error(`Request failed: ${response.status} ${response.statusText} - ${data}`);
      }

      console.log('✅ Request successful');

      // Print first 1000 characters of response body for debugging
      console.log('📥 Response Body Preview (first 1000 chars):', data.slice(0, 1000));

      // Print full response body for debugging
      // console.log('📄 Full Response Body:', data);

      // For debugging: check if response contains error indicators
      if (data.includes('error') || data.includes('Error') || data.includes('hata') || data.includes('Hata')) {
        console.warn('⚠️ Response may contain error messages');
      }

      // For debugging: check if response contains success indicators
      if (data.includes('success') || data.includes('Success') || data.includes('başarı') || data.includes('Başarı')) {
        console.log('✅ Response may contain success messages');
      }

      const validationResult = request.validateResponse(data) as ReturnType<Request['validateResponse']>;

      return validationResult;
    } catch (error) {
      console.error('❌ Request failed:', error);
      throw error;
    }
  }

  async getCookies(): Promise<{ cookies: PronetDagurSession['cookies']; initialViewState: string }> {
    const response = await fetch(`${BASE_URL}/login.xhtml`);
    if (!response.ok) {
      throw new Error(`Failed to prelogin: ${await response.text()}`);
    }

    const setCookieHeaders = response.headers.raw()['set-cookie'];
    if (!setCookieHeaders || setCookieHeaders.length === 0) {
      throw new Error('No set-cookie headers found');
    }

    const jsessionid = setCookieHeaders.find((header) => header.includes('JSESSIONID'));
    if (!jsessionid) {
      throw new Error('JSESSIONID cookie not found');
    }

    const nxquid = setCookieHeaders.find((header) => header.includes('__nxquid'));
    if (!nxquid) {
      throw new Error('__nxquid cookie not found');
    }

    const xmlParser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@',
      unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
      stopNodes: ['*.pre', '*.script'],
      processEntities: true,
      htmlEntities: true,
    });

    const obj = xmlParser.parse(await response.text());
    const viewState = obj['html']['body']['form']['input']?.find((i: any) => i['@name'] === 'javax.faces.ViewState')?.[
      '@value'
    ];
    if (!viewState) {
      throw new Error('Failed to find view state');
    }

    return {
      cookies: {
        DAGUR_CLIENT_TIMEZONE: '0',
        JSESSIONID: jsessionid.split(';')[0]?.split('=')[1] || '',
        __nxquid: nxquid.split(';')[0]?.split('=')[1] || '',
      },
      initialViewState: viewState,
    };
  }

  async loginWithOtp(username: string, password: string, otp: string): Promise<PronetDagurSession> {
    const { cookies, initialViewState } = await this.getCookies();

    const response = await this.makeRequest(new LoginRequest({ username, password }), initialViewState, {
      Cookie: `DAGUR_CLIENT_TIMEZONE=${cookies.DAGUR_CLIENT_TIMEZONE}; JSESSIONID=${cookies.JSESSIONID}; __nxquid=${cookies.__nxquid}`,
    });

    if (response.success) {
      const response2 = await this.makeRequest(
        new LoginWithOtpRequest({ otp, buttonId: response.data.buttonId }),
        response.viewState,
        {
          Cookie: `DAGUR_CLIENT_TIMEZONE=${cookies.DAGUR_CLIENT_TIMEZONE}; JSESSIONID=${cookies.JSESSIONID}; __nxquid=${cookies.__nxquid}`,
        },
      );

      if (response2.success) {
        const timezonePreloadRequest = new TimezoneChangePreloadRequest();
        const timezonePreloadResponse = await this.makeRequest(timezonePreloadRequest, response2.viewState, {
          Cookie: `DAGUR_CLIENT_TIMEZONE=${cookies.DAGUR_CLIENT_TIMEZONE}; JSESSIONID=${cookies.JSESSIONID}; __nxquid=${cookies.__nxquid}`,
        });
        if (!timezonePreloadResponse.success) {
          throw new Error(`Failed to preload timezone change: ${timezonePreloadResponse.message}`);
        }

        const timezoneChangeRequest = new TimezoneChangeRequest({
          javax: timezonePreloadResponse.data,
          timezone: 'GMT',
        });
        const timezoneChangeResponse = await this.makeRequest(
          timezoneChangeRequest,
          timezonePreloadResponse.viewState,
          {
            Cookie: `DAGUR_CLIENT_TIMEZONE=${cookies.DAGUR_CLIENT_TIMEZONE}; JSESSIONID=${cookies.JSESSIONID}; __nxquid=${cookies.__nxquid}`,
          },
        );
        if (!timezoneChangeResponse.success) {
          throw new Error(`Failed to change timezone: ${timezoneChangeResponse.message}`);
        }

        return {
          cookies: cookies,
        };
      } else {
        throw new Error(`Failed to login: ${response2.message}`);
      }
    } else {
      throw new Error(`Failed to login: ${response.message}`);
    }
  }
}

export class PGDagurAdminHttpClient implements IPGDagurHttpClient {
  private client: PGDagurHttpClient;
  private session: PronetDagurSession | null = null;
  private lastLoginTime: number = 0;
  private isLoggingIn = false;
  private readonly LOGIN_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor(client: PGDagurHttpClient) {
    this.client = client;
  }

  /**
   * Check if login is needed (5 minutes have passed or no session exists)
   */
  private needsLogin(): boolean {
    const now = Date.now();
    const timeSinceLastLogin = now - this.lastLoginTime;

    if (!this.session) {
      console.log('🔐 No Dagur session found, login required');
      return true;
    }

    if (timeSinceLastLogin >= this.LOGIN_INTERVAL) {
      console.log(
        `🔐 ${Math.floor(timeSinceLastLogin / (60 * 1000))} minutes since last Dagur login, re-login required`,
      );
      return true;
    }

    const minutesRemaining = Math.floor((this.LOGIN_INTERVAL - timeSinceLastLogin) / (60 * 1000));
    const secondsRemaining = Math.floor(((this.LOGIN_INTERVAL - timeSinceLastLogin) % (60 * 1000)) / 1000);
    console.log(`🔐 Dagur session still valid for ${minutesRemaining}m ${secondsRemaining}s`);
    return false;
  }

  /**
   * Ensure valid login before making requests
   */
  private async ensureValidLogin(): Promise<void> {
    if (this.needsLogin()) {
      const username = process.env['PG_DAGUR_USERNAME'] || '';
      const password = process.env['PG_DAGUR_PASSWORD'] || '';
      const otpSecret = process.env['PG_DAGUR_OTP_SECRET'] || '';

      if (!username || !password || !otpSecret) {
        throw new Error(
          'Missing Dagur credentials in environment variables (PG_DAGUR_USERNAME, PG_DAGUR_PASSWORD, PG_DAGUR_OTP_SECRET)',
        );
      }

      await this.loginWithOtpSecret(username, password, otpSecret);
    }
  }

  async makeRequest<ResponseModel extends any, Request extends PGDagurRequest<ResponseModel>>(
    request: Request,
    viewState: string = '',
    additionalHeaders: Record<string, any> = {},
    retries = 0,
  ): Promise<ReturnType<Request['validateResponse']>> {
    try {
      // Ensure we have a valid login before making the request
      await this.ensureValidLogin();

      if (!this.session) {
        throw new Error('Failed to obtain Dagur session');
      }

      const cookieHeader = `DAGUR_CLIENT_TIMEZONE=${this.session.cookies.DAGUR_CLIENT_TIMEZONE}; JSESSIONID=${this.session.cookies.JSESSIONID}; __nxquid=${this.session.cookies.__nxquid}`;

      const result = await this.client.makeRequest(request, viewState, {
        ...additionalHeaders,
        Cookie: cookieHeader,
      });

      return result;
    } catch (error: any) {
      // Handle authentication errors by forcing re-login
      if (
        (error.message.includes('Unauthorized') || error.message.includes('redirect url="/login.xhtml"')) &&
        retries < 3
      ) {
        console.log('🔄 Dagur authentication error detected, forcing re-login...');
        this.session = null;
        this.lastLoginTime = 0;

        return this.makeRequest(request, viewState, additionalHeaders, retries + 1);
      }

      throw error;
    }
  }

  /**
   * Generate TOTP from secret with 30-second interval
   */
  private generateTOTP(secret: string): string {
    try {
      console.log(`🔐 Generating Dagur TOTP from secret: ${secret.substring(0, 4)}...`);

      const otpCode = TOTP.generate(secret, {
        period: 30,
      }).otp;

      console.log(`🔐 Dagur OTP Generated: ${otpCode}`);
      return otpCode;
    } catch (error) {
      console.error(`❌ Dagur TOTP generation failed:`, error);
      throw error;
    }
  }

  /**
   * Perform login with OTP secret and store session with timestamp
   */
  async loginWithOtpSecret(username: string, password: string, otpSecret: string) {
    if (this.isLoggingIn) {
      for (let i = 0; i < 60; i++) {
        if (this.needsLogin() === false) {
          return;
        }

        if (!this.isLoggingIn) {
          break;
        }

        await new Promise((res) => setTimeout(res, 1000));
      }
    }

    this.isLoggingIn = true;

    console.log('🔐 Starting Dagur admin login process...');

    // Generate OTP from secret
    const otp = this.generateTOTP(otpSecret);
    console.log(`🔑 Using OTP: ${otp} for Dagur admin login`);

    try {
      this.session = await this.client.loginWithOtp(username, password, otp);
      this.lastLoginTime = Date.now();
      console.log('✅ Dagur admin login successful, session stored');
      console.log(
        `🕐 Next Dagur login required at: ${new Date(this.lastLoginTime + this.LOGIN_INTERVAL).toISOString()}`,
      );
    } finally {
      this.isLoggingIn = false;
    }
  }
}

export class PGDagurApiClient {
  readonly accounting: AccountingApiClient;
  readonly betReports: BetReportsApiClient;

  constructor(httpClient: IPGDagurHttpClient) {
    this.accounting = new AccountingApiClient(httpClient);
    this.betReports = new BetReportsApiClient(httpClient);
  }
}

// Export a singleton instance
export const pgDagurHttpClient = new PGDagurHttpClient();
export const pgDagurAdminHttpClient = new PGDagurAdminHttpClient(pgDagurHttpClient);
export const pgDagurApiClient = new PGDagurApiClient(pgDagurAdminHttpClient);
