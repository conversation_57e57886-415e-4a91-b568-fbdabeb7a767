import { IPGDagurHttpClient } from './pg-dagur';
import { BetReportLoginRequest } from './requests/auth/BetReportLoginRequest';

export interface DailyBetReportOptions {
  startDate: Date;
  endDate: Date;
  page: number;
  limit: number;
}

export interface DailyBetReportResponse {
  reports: {
    total: number;
    pageSize: number;
    offset: number;
    items: BetReportRecord[];
  };
  summary: BetReportSummary | null;
}

export interface BetReportRecord {
  id: string;
  date: string;
  totalBets: number;
  totalAmount: number;
  totalWinnings: number;
  netProfit: number;
  // Add other fields as needed based on actual response structure
}

export interface BetReportSummary {
  totalBets: number;
  totalAmount: number;
  totalWinnings: number;
  netProfit: number;
  // Add other summary fields as needed
}

export class BetReportsApiClient {
  private readonly httpClient: IPGDagurHttpClient;
  private bearerToken: string | null = null;
  private lastTokenTime: number = 0;
  private readonly TOKEN_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor(httpClient: IPGDagurHttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * Check if bearer token needs refresh (5 minutes have passed or no token exists)
   */
  private needsTokenRefresh(): boolean {
    const now = Date.now();
    const timeSinceLastToken = now - this.lastTokenTime;

    if (!this.bearerToken) {
      console.log('🔐 No Dagur bet reports bearer token found, refresh required');
      return true;
    }

    if (timeSinceLastToken >= this.TOKEN_INTERVAL) {
      console.log(`🔐 ${Math.floor(timeSinceLastToken / (60 * 1000))} minutes since last Dagur bet reports token, refresh required`);
      return true;
    }

    const minutesRemaining = Math.floor((this.TOKEN_INTERVAL - timeSinceLastToken) / (60 * 1000));
    const secondsRemaining = Math.floor(((this.TOKEN_INTERVAL - timeSinceLastToken) % (60 * 1000)) / 1000);
    console.log(`🔐 Dagur bet reports token still valid for ${minutesRemaining}m ${secondsRemaining}s`);
    return false;
  }

  /**
   * Get bearer token for bet reports API
   */
  private async getBearerToken(): Promise<string> {
    if (!this.needsTokenRefresh()) {
      return this.bearerToken!;
    }

    console.log('🔑 Refreshing Dagur bet reports bearer token...');
    const response = await this.httpClient.makeRequest(new BetReportLoginRequest());

    if (!response.success) {
      throw new Error(`Failed to get bearer token: ${response.message}`);
    }

    this.bearerToken = response.data;
    this.lastTokenTime = Date.now();

    console.log('✅ Dagur bet reports bearer token refreshed successfully');
    console.log(`🕐 Next Dagur bet reports token refresh at: ${new Date(this.lastTokenTime + this.TOKEN_INTERVAL).toISOString()}`);

    return response.data;
  }

  /**
   * Get daily bet report
   */
  async getDailyBetReport(options: DailyBetReportOptions): Promise<DailyBetReportResponse> {
    console.log('📊 Fetching daily bet report...');
    console.log('📋 Request options:', JSON.stringify(options, null, 2));

    // This method would implement the logic to fetch daily bet reports
    // For now, it's a placeholder that matches the interface expected by the controller
    
    // Get bearer token
    const bearerToken = await this.getBearerToken();

    // TODO: Implement the actual API call logic here
    // This would make a request to the appropriate endpoint using the bearer token
    
    // Placeholder response structure
    const response: DailyBetReportResponse = {
      reports: {
        total: 0,
        pageSize: options.limit,
        offset: options.page,
        items: [],
      },
      summary: null,
    };

    console.log('✅ Daily bet report retrieved successfully');
    return response;
  }

  /**
   * Clear cached bearer token (useful for token refresh)
   */
  clearToken(): void {
    this.bearerToken = null;
  }
}
