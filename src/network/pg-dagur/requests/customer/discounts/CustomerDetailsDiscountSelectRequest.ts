import { findJavaxElementId } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';

export interface CustomerDetailsDiscountSelectRequestOptions {
  discountType: string;
}

export type CustomerDetailsDiscountSelectRequestResponse = {
  currencyInputId: string;
};

export class CustomerDetailsDiscountSelectRequest extends PGDagurRequest<
  CustomerDetailsDiscountSelectRequestResponse,
  any
> {
  constructor(private options: CustomerDetailsDiscountSelectRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'discountType',
      'javax.faces.partial.execute': 'discountType',
      'javax.faces.partial.render': 'otherFieldsPanel',
      'javax.faces.behavior.event': 'valueChange',
      'javax.faces.partial.event': 'change',
      addDiscountForm: 'addDiscountForm',
      discountType: this.options.discountType,

      discountConsole_input: 1953,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsDiscountSelectRequestResponse> {
    const currencyInputId = findJavaxElementId(xml, '', 'Player Wallet Currency', 1);
    if (!currencyInputId) {
      return {
        success: false,
        message: 'Failed to find currency input id',
      };
    }

    return {
      success: true,
      data: {
        currencyInputId,
      },
    };
  }
}
