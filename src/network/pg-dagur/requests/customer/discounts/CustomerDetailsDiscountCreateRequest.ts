import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsDiscountDialogGetRequestResponse } from './CustomerDetailsDiscountDialogGetRequest';
import { CustomerDetailsDiscountSelectRequestResponse } from './CustomerDetailsDiscountSelectRequest';

export interface CustomerDetailsDiscountCreateRequestOptions {
  javax: CustomerDetailsDiscountSelectRequestResponse & CustomerDetailsDiscountDialogGetRequestResponse;

  discountType: string;
  customerId: number;
  reasonId: number;
  amount: number;
  note: string;
}

export type CustomerDetailsDiscountCreateRequestResponse = null;

export class CustomerDetailsDiscountCreateRequest extends PGDagurRequest<
  CustomerDetailsDiscountCreateRequestResponse,
  any
> {
  constructor(private options: CustomerDetailsDiscountCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.saveButtonId,
      'javax.faces.partial.execute': 'addDiscountPanel',
      'javax.faces.partial.render': 'growl addDiscountDialog',
      [this.options.javax.saveButtonId]: this.options.javax.saveButtonId,
      addDiscountForm: 'addDiscountForm',
      discountType: this.options.discountType,
      [`${this.options.javax.currencyInputId}_focus`]: '',
      [`${this.options.javax.currencyInputId}_input`]: this.options.customerId,
      discountConsole_focus: '',
      discountConsole_input: this.options.reasonId,
      discountAmount_input: this.options.amount,
      discountAmount_hinput: this.options.amount,
      discountAdminNote: this.options.note,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsDiscountCreateRequestResponse> {
    if (xml.includes('validationFailed":true')) {
      return {
        success: false,
        message: 'Failed to create customer discount: validation error',
      };
    }

    return {
      success: true,
      data: null,
    };
  }
}
