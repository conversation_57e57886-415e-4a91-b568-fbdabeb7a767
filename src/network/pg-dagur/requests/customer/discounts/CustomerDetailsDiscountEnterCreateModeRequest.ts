import { HttpMethod, PGDagurRequest } from '../../Request';

export interface CustomerDetailsDiscountEnterCreateModeRequestOptions {}

export class CustomerDetailsDiscountEnterCreateModeRequest extends PGDagurRequest<null, any> {
  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:tabView:showDiscountDialogBtn',
      'javax.faces.partial.execute': 'form:tabView:showDiscountDialogBtn',
      'javax.faces.partial.render': 'addDiscountDialog form:mainGrowl',
      'javax.faces.behavior.event': 'click',
      'javax.faces.partial.event': 'click',
      form: 'form',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
