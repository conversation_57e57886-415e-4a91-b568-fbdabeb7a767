import { HttpMethod, PGDagurRequest } from '../../Request';

export interface CustomerDetailsDiscountTabChangeRequestOptions {}

export class CustomerDetailsDiscountTabChangeRequest extends PGDagurRequest<null, any> {
  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:tabView',
      'javax.faces.partial.execute': 'form:tabView',
      'javax.faces.partial.render': 'form:tabView',
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      'form:tabView_contentLoad': 'true',
      'form:tabView_newTab': `form:tabView:customerDiscountTab`,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
