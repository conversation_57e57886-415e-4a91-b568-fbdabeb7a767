import { findJavaxElementIdBefore } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { parse } from 'node-html-parser';

export interface CustomerDetailsDiscountDialogGetRequestOptions {}

export type CustomerDetailsDiscountDialogGetRequestResponse = {
  saveButtonId: string;
  reasons: { id: number; name: string }[];
};

export class CustomerDetailsDiscountDialogGetRequest extends PGDagurRequest<
  CustomerDetailsDiscountDialogGetRequestResponse,
  any
> {
  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'addDiscountDialog',
      'javax.faces.partial.execute': 'addDiscountDialog',
      'javax.faces.partial.render': 'addDiscountDialog',
      addDiscountDialog: 'addDiscountDialog',
      addDiscountDialog_contentLoad: 'true',
      addDiscountForm: 'addDiscountForm',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsDiscountDialogGetRequestResponse> {
    const saveButtonId = findJavaxElementIdBefore(xml, '', "'saveDiscountBtn'");
    if (!saveButtonId) {
      return {
        success: false,
        message: 'Failed to find save button id',
      };
    }

    // Parse HTML and extract reasons from select element
    const reasons = this.extractReasonsFromSelect(xml);

    return {
      success: true,
      data: {
        saveButtonId,
        reasons,
      },
    };
  }

  private extractReasonsFromSelect(html: string): { id: number; name: string }[] {
    try {
      // Parse HTML using node-html-parser
      const root = parse(html);

      // Find the select element with id "discountConsole_input"
      const select = root.querySelector('#discountConsole_input');
      if (!select) {
        return [];
      }

      // Extract all option elements
      const options = select.querySelectorAll('option');
      const reasons: { id: number; name: string }[] = [];

      for (const option of options) {
        const value = option.getAttribute('value');
        const name = option.text?.trim();

        if (value && name) {
          const id = parseInt(value, 10);
          if (!isNaN(id)) {
            reasons.push({
              id,
              name,
            });
          }
        }
      }

      return reasons;
    } catch (error) {
      // If parsing fails, return empty array
      return [];
    }
  }
}
