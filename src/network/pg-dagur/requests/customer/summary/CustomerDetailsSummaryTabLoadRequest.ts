import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

/**
 * Options for the customer details summary tab load request
 * @interface CustomerDetailsSummaryTabLoadRequestOptions
 */
export interface CustomerDetailsSummaryTabLoadRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** JSF element data from the preload request */
  javax: CustomerDetailsPreloadRequestResponse;
  /** ViewState from the preload request */
  viewState: string;
}

/**
 * Response structure for customer summary data
 * @interface CustomerSummaryResponse
 */
export interface CustomerSummaryResponse {
  /** Customer basic data extracted from the response */
  customer: {
    /** First Name */
    firstName?: string;
    /** Surname */
    surname?: string;
    /** Customer Code */
    customerCode?: string;
    /** Username */
    username?: string;
    /** Currency */
    currency?: string;
    /** Last Login Date */
    lastLoginDate?: string;
  };
  /** Customer detail data extracted from the response */
  customerDetail: {
    /** Customer ID */
    customerId?: string;
    /** Trader */
    trader?: string;
    /** Promotion Code */
    promotionCode?: string;
    /** Gender */
    gender?: string;
    /** Login Status */
    loginStatus?: string;
    /** Register Date */
    registerDate?: string;
    /** Profile Age */
    profileAge?: string;
    /** Register IP */
    registerIp?: string;
    /** Last Login IP */
    lastLoginIp?: string;
  };
  /** Balance data extracted from the response */
  balances: {
    /** Sport Real Balance */
    sportRealBalance?: number;
    /** Sport Real Locked Balance */
    sportRealLockedBalance?: number;
    /** Sport Real Credit Balance */
    sportRealCreditBalance?: number;
    /** Sport Bonus Balance */
    sportBonusBalance?: number;
    /** Freeze Bonus */
    freezeBonus?: number;
    /** Sport Bonus Credit Balance */
    sportBonusCreditBalance?: number;
    /** Sport Discount Balance */
    sportDiscountBalance?: number;
    /** Sport Freebet Balance */
    sportFreebetBalance?: number;
  };
  /** Calculated data derived from existing balance fields */
  calculatedData: {
    /** Sport Full Balance (Sport Real Balance + Sport Real Locked Balance + Sport Real Credit Balance) */
    sportsFullBalance: number;
    /** Sports Bonus Balance (Sport Bonus Balance + Sport Bonus Credit Balance + Sport Discount Balance + Sport Freebet Balance) */
    sportsBonusBalance: number;
  };
}

/**
 * Tab load request to get customer summary balance data
 */
export class CustomerDetailsSummaryTabLoadRequest extends PGDagurRequest<CustomerSummaryResponse, any> {
  constructor(private options: CustomerDetailsSummaryTabLoadRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerDetailsSummaryTabLoadRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for CustomerDetailsSummaryTabLoadRequest');
    }

    if (!this.options.javax.tabViewId) {
      throw new Error('tabViewId is required in javax data');
    }

    if (!this.options.viewState || this.options.viewState.trim() === '') {
      throw new Error('viewState is required for CustomerDetailsSummaryTabLoadRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:landingPageLoaderPoll',
      'javax.faces.partial.execute': 'form',
      'javax.faces.partial.render': 'form:customerDetailsTabPanel',
      'form:landingPageLoaderPoll': 'form:landingPageLoaderPoll',
      'form': 'form',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt627}_input`]: 'on',
      [`${this.options.javax.tabViewId}:customerNotesDt_rppDD`]: '10',
      [`${this.options.javax.tabViewId}_activeIndex`]: '0',
      'javax.faces.ViewState': this.options.viewState,
    };

    console.log('📋 CustomerDetailsSummaryTabLoadRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerSummaryResponse> {
    try {
      console.log('🔍 Parsing customer summary tab load response...');

      // Initialize response structure
      const response: CustomerSummaryResponse = {
        customer: {},
        customerDetail: {},
        balances: {},
        calculatedData: {
          sportsFullBalance: 0,
          sportsBonusBalance: 0,
        },
      };

      // Parse XML response
      const parser = new XMLParser({
        ignoreAttributes: false,
        attributeNamePrefix: '@',
        unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
        stopNodes: ['*.pre', '*.script'],
        processEntities: true,
        htmlEntities: true,
      });

      const obj = parser.parse(xml);
      console.log('🔍 Parsed XML structure:', JSON.stringify(obj, null, 2));

      // Handle JSF partial response structure
      const partialResponse = obj['partial-response'];
      if (partialResponse && partialResponse['changes']) {
        const changes = partialResponse['changes'];
        console.log('🔍 Found JSF partial response with changes');

        const update = changes['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the customer details tab panel data
        let tabPanelUpdate = update;
        if (Array.isArray(update)) {
          tabPanelUpdate = update.find(
            (u: any) => {
              // Skip ViewState updates
              if (u?.['@id']?.includes('javax.faces.ViewState')) {
                return false;
              }
              // Check if the HTML content contains form:customerDetailsTabPanel
              const htmlContent = u?.['#text'] || u;
              return typeof htmlContent === 'string' && htmlContent.includes('form:customerDetailsTabPanel');
            }
          );
        } else {
          // Single update - check if it contains the tab panel
          const htmlContent = update?.['#text'] || update;
          if (typeof htmlContent !== 'string' || !htmlContent.includes('form:customerDetailsTabPanel')) {
            tabPanelUpdate = null;
          }
        }

        if (!tabPanelUpdate) {
          return {
            success: false,
            message: 'No customer details tab panel data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tabPanelUpdate['#text'] || tabPanelUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in customer details tab panel update',
          };
        }

        // Parse the HTML content and extract customer detail and balance data
        console.log('🔍 Parsing HTML content for customer detail and balance data...');
        this.parseSummaryHTML(htmlContent, response);

        // Find ViewState update for next request
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        console.log('� Summary parsing complete - Response data:', JSON.stringify({
          customerKeys: Object.keys(response.customer),
          customerDetailKeys: Object.keys(response.customerDetail),
          balanceKeys: Object.keys(response.balances),
          hasViewState: !!viewState
        }, null, 2));

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML content (fallback)
        this.parseSummaryHTML(xml, response);

        return {
          success: true,
          data: response,
        };
      }
    } catch (error) {
      console.error('❌ Error parsing customer summary tab load response:', error);
      return {
        success: false,
        message: `Failed to parse summary response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Parse HTML content to extract customer detail and balance data
   */
  private parseSummaryHTML(html: string, response: CustomerSummaryResponse): void {
    try {
      console.log('🔍 Parsing summary HTML content...');

      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Initialize response objects
      response.customer = {};
      response.customerDetail = {};
      response.balances = {};

      // Define the customer basic fields to extract
      const customerFieldsToExtract = [
        { displayName: 'First Name', propertyName: 'firstName', isString: true },
        { displayName: 'Surname', propertyName: 'surname', isString: true },
        { displayName: 'Customer Code', propertyName: 'customerCode', isString: true },
        { displayName: 'Username', propertyName: 'username', isString: true },
        { displayName: 'Currency', propertyName: 'currency', isString: true },
        { displayName: 'Last Login Date', propertyName: 'lastLoginDate', isString: true },
      ];

      // Define the customer detail fields to extract
      const customerDetailFieldsToExtract = [
        { displayName: 'Customer ID', propertyName: 'customerId', isString: true },
        { displayName: 'Trader', propertyName: 'trader', isString: true },
        { displayName: 'Promotion Code', propertyName: 'promotionCode', isString: true },
        { displayName: 'Gender', propertyName: 'gender', isString: true },
        { displayName: 'Login Status', propertyName: 'loginStatus', isString: true },
        { displayName: 'Register Date', propertyName: 'registerDate', isString: true },
        { displayName: 'Profile Age', propertyName: 'profileAge', isString: true },
        { displayName: 'Register IP', propertyName: 'registerIp', isString: true },
        { displayName: 'Last Login IP', propertyName: 'lastLoginIp', isString: true },
      ];

      // Define the balance fields to extract
      const balanceFieldsToExtract = [
        { displayName: 'Sport Real Balance', propertyName: 'sportRealBalance', isString: false },
        { displayName: 'Sport Real Locked Balance', propertyName: 'sportRealLockedBalance', isString: false },
        { displayName: 'Sport Real Credit Balance', propertyName: 'sportRealCreditBalance', isString: false },
        { displayName: 'Sport Bonus Balance', propertyName: 'sportBonusBalance', isString: false },
        { displayName: 'Freeze Bonus', propertyName: 'freezeBonus', isString: false },
        { displayName: 'Sport Bonus Credit Balance', propertyName: 'sportBonusCreditBalance', isString: false },
        { displayName: 'Sport Discount Balance', propertyName: 'sportDiscountBalance', isString: false },
        { displayName: 'Sport Freebet Balance', propertyName: 'sportFreebetBalance', isString: false },
      ];

      // Extract customer basic fields using the smart parser
      for (const field of customerFieldsToExtract) {
        const value = this.extractStatisticValue(root, field.displayName, field.isString);
        // Always include the field in response, even if null
        (response.customer as any)[field.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted customer ${field.displayName}:`, value);
        } else {
          console.log(`⚠️ Customer ${field.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract customer detail fields using the smart parser
      for (const field of customerDetailFieldsToExtract) {
        const value = this.extractStatisticValue(root, field.displayName, field.isString);
        // Always include the field in response, even if null
        (response.customerDetail as any)[field.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted customer detail ${field.displayName}:`, value);
        } else {
          console.log(`⚠️ Customer detail ${field.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract balance fields using the smart parser
      for (const field of balanceFieldsToExtract) {
        const value = this.extractStatisticValue(root, field.displayName, field.isString);
        // Always include the field in response, even if null
        (response.balances as any)[field.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted balance ${field.displayName}:`, value);
        } else {
          console.log(`⚠️ Balance ${field.displayName} not found in HTML (setting to null)`);
        }
      }

      // Calculate derived balance fields
      console.log('🧮 Calculating derived balance fields...');

      // Calculate sportsFullBalance (Sport Real Balance + Sport Real Locked Balance + Sport Real Credit Balance)
      const sportRealBalance = response.balances.sportRealBalance || 0;
      const sportRealLockedBalance = response.balances.sportRealLockedBalance || 0;
      const sportRealCreditBalance = response.balances.sportRealCreditBalance || 0;
      response.calculatedData.sportsFullBalance = sportRealBalance + sportRealLockedBalance + sportRealCreditBalance;

      // Calculate sportsBonusBalance (Sport Bonus Balance + Sport Bonus Credit Balance + Sport Discount Balance + Sport Freebet Balance)
      const sportBonusBalance = response.balances.sportBonusBalance || 0;
      const sportBonusCreditBalance = response.balances.sportBonusCreditBalance || 0;
      const sportDiscountBalance = response.balances.sportDiscountBalance || 0;
      const sportFreebetBalance = response.balances.sportFreebetBalance || 0;
      response.calculatedData.sportsBonusBalance = sportBonusBalance + sportBonusCreditBalance + sportDiscountBalance + sportFreebetBalance;

      console.log(`✅ Calculated sportsFullBalance: ${response.calculatedData.sportsFullBalance} (${sportRealBalance} + ${sportRealLockedBalance} + ${sportRealCreditBalance})`);
      console.log(`✅ Calculated sportsBonusBalance: ${response.calculatedData.sportsBonusBalance} (${sportBonusBalance} + ${sportBonusCreditBalance} + ${sportDiscountBalance} + ${sportFreebetBalance})`);

      // Log the HTML structure for debugging (first 2000 chars)
      console.log('📋 Summary HTML structure (first 2000 chars):', html.substring(0, 2000));

      console.log('👤 Final parsed customer:', JSON.stringify(response.customer, null, 2));
      console.log('📋 Final parsed customer details:', JSON.stringify(response.customerDetail, null, 2));
      console.log('💰 Final parsed balances:', JSON.stringify(response.balances, null, 2));
      console.log('🧮 Final calculated data:', JSON.stringify(response.calculatedData, null, 2));

    } catch (error) {
      console.error('❌ Error parsing summary HTML:', error);
      // Initialize empty objects on error
      response.customer = {};
      response.customerDetail = {};
      response.balances = {};
    }
  }

  /**
   * Extract a specific statistic value from the HTML table structure
   * Supports two patterns:
   * 1. <tr><td><label>StatisticName</label></td><td>:</td><td><label>Value</label></td></tr>
   * 2. <tr><td><label>StatisticName</label></td><td>:</td><td><span id="...tooltipWrapper"><span id="...text">Value</span></span></td></tr>
   * Handles both numeric and string values, empty labels, and decimal numbers
   */
  private extractStatisticValue(root: any, statisticName: string, isString: boolean = false): number | string | null {
    try {
      // Find all table rows
      const rows = root.querySelectorAll('tr');

      for (const row of rows) {
        // Get all cells in this row
        const cells = row.querySelectorAll('td');

        if (cells.length >= 3) {
          // Check if first cell contains the statistic name
          const firstCellLabel = cells[0].querySelector('label');
          if (firstCellLabel && firstCellLabel.text && firstCellLabel.text.trim() === statisticName) {
            // Check if second cell contains ":"
            const secondCellLabel = cells[1].querySelector('label');
            if (secondCellLabel && secondCellLabel.text && secondCellLabel.text.trim() === ':') {
              // Extract value from third cell - handle both label and span structures
              let valueText = '';

              // Try to find label first (original structure)
              const thirdCellLabel = cells[2].querySelector('label');
              if (thirdCellLabel && thirdCellLabel.text) {
                valueText = thirdCellLabel.text.trim();
              } else {
                // Try to find span with tooltip wrapper structure
                // Pattern: <span id="...tooltipWrapper"><span id="...text">VALUE</span></span>
                const tooltipWrapper = cells[2].querySelector('span[id*="tooltipWrapper"]');
                if (tooltipWrapper) {
                  const textSpan = tooltipWrapper.querySelector('span[id*="text"]');
                  if (textSpan && textSpan.text) {
                    valueText = textSpan.text.trim();
                  }
                } else {
                  // Fallback: try any span in the third cell
                  const anySpan = cells[2].querySelector('span');
                  if (anySpan && anySpan.text) {
                    valueText = anySpan.text.trim();
                  }
                }
              }

              // Handle empty values (null values)
              if (valueText === '') {
                console.log(`🔍 Found ${statisticName}: empty (null)`);
                return null;
              }

              // Handle string values
              if (isString) {
                console.log(`🔍 Found ${statisticName}: "${valueText}"`);
                return valueText;
              }

              // Handle numeric values (including decimals)
              // Remove commas from numbers like "234,300.00" before parsing
              const cleanedValueText = valueText.replace(/,/g, '');
              const numericValue = parseFloat(cleanedValueText);
              if (!isNaN(numericValue)) {
                console.log(`🔍 Found ${statisticName}: ${numericValue} (original: "${valueText}")`);
                return numericValue;
              } else {
                console.log(`⚠️ Could not parse numeric value for ${statisticName}: "${valueText}"`);
                // For numeric fields, if we can't parse, return the string value as fallback
                console.log(`🔍 Returning string value as fallback: "${valueText}"`);
                return valueText;
              }
            }
          }
        }
      }

      console.log(`⚠️ ${statisticName} not found in table structure`);
      return null;

    } catch (error) {
      console.error(`❌ Error extracting ${statisticName}:`, error);
      return null;
    }
  }
}
