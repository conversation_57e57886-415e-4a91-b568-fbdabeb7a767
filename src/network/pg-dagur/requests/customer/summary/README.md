# Customer Summary Pipeline

This directory contains the Dagur pipeline implementation for accessing customer summary balance data.

## Overview

The customer summary pipeline follows the standard Dagur 2-step sequence:
1. **Preload** - Uses the shared `CustomerDetailsPreloadRequest` to get initial page state and dynamic JSF element IDs
2. **Summary Tab Load** - Load customer balance data from the summary tab

The controller automatically processes the response and extracts balance information.

The response is structured with customer data, customer detail data and balance data:

**Customer Fields:**
- `firstName`: First Name
- `surname`: Surname
- `customerCode`: Customer Code
- `username`: Username
- `currency`: Currency
- `lastLoginDate`: Last Login Date

**Customer Detail Fields:**
- `customerId`: Customer ID
- `trader`: Trader information
- `promotionCode`: Promotion Code
- `gender`: Gender information
- `loginStatus`: Login Status
- `registerDate`: Register Date
- `profileAge`: Profile Age
- `registerIp`: Register IP address
- `lastLoginIp`: Last Login IP address

**Balance Fields:**
- `sportRealBalance`: Sport Real Balance amount
- `sportRealLockedBalance`: Sport Real Locked Balance amount
- `sportRealCreditBalance`: Sport Real Credit Balance amount
- `sportBonusBalance`: Sport Bonus Balance amount
- `freezeBonus`: Freeze Bonus amount
- `sportBonusCreditBalance`: Sport Bonus Credit Balance amount
- `sportDiscountBalance`: Sport Discount Balance amount
- `sportFreebetBalance`: Sport Freebet Balance amount

**Calculated Data Fields:**
- `sportsFullBalance`: Total sports balance (Sport Real Balance + Sport Real Locked Balance + Sport Real Credit Balance)
- `sportsBonusBalance`: Total sports bonus balance (Sport Bonus Balance + Sport Bonus Credit Balance + Sport Discount Balance + Sport Freebet Balance)

## API Endpoint

**GET** `/api/pg-dagur/v1/internal/customers/:customerId/summary`

Returns customer summary data including customer information, customer details and balance information.

## Files

### CustomerDetailsSummaryTabLoadRequest.ts
- **Purpose**: Tab load request to retrieve customer balance data from the summary tab
- **Method**: POST
- **Path**: `/restricted/customer-details.xhtml`
- **Form Data**: Uses landingPageLoaderPoll source with dynamic JSF elements
- **Returns**: Customer balance data and updated ViewState

## Usage Example

### API Call
```bash
GET /api/pg-dagur/v1/internal/customers/12345/summary
```

### Response
```json
{
  "success": true,
  "data": {
    "customer": {
      "firstName": "John",
      "surname": "Titor",
      "customerCode": "CUST12345",
      "username": "johntitor",
      "currency": "TRY",
      "lastLoginDate": "2024-01-20"
    },
    "customerDetail": {
      "customerId": "12345",
      "trader": "John Smith",
      "promotionCode": "PROMO123",
      "gender": "Male",
      "loginStatus": "Active",
      "registerDate": "2024-01-15",
      "profileAge": "25",
      "registerIp": "*************",
      "lastLoginIp": "*************"
    },
    "balances": {
      "sportRealBalance": 1250.50,
      "sportRealLockedBalance": 0,
      "sportRealCreditBalance": 100.00,
      "sportBonusBalance": 75.25,
      "freezeBonus": 0,
      "sportBonusCreditBalance": 25.00,
      "sportDiscountBalance": 50.00,
      "sportFreebetBalance": 10.00
    },
    "calculatedData": {
      "sportsFullBalance": 1350.50,
      "sportsBonusBalance": 160.25
    },
    "customerId": "12345"
  },
  "message": "Customer summary retrieved successfully"
}
```

### Programmatic Usage
```typescript
import { CustomerDetailsPreloadRequest } from '../CustomerDetailsPreloadRequest';
import {
  CustomerDetailsSummaryTabLoadRequest,
  CustomerSummaryResponse
} from './summary';

// Step 1: Preload to get dynamic element IDs (shared preload request)
const preloadRequest = new CustomerDetailsPreloadRequest({
  customerId: '12345'
});

const preloadResponse = await dagurClient.execute(preloadRequest);
if (!preloadResponse.success) {
  throw new Error('Preload failed');
}

// Step 2: Summary tab load to get balance data
const summaryTabLoadRequest = new CustomerDetailsSummaryTabLoadRequest({
  customerId: '12345',
  javax: preloadResponse.data,
  viewState: preloadResponse.viewState!
});

const summaryResponse = await dagurClient.execute(summaryTabLoadRequest);
if (summaryResponse.success) {
  console.log('Customer info:', summaryResponse.data.customer);
  console.log('Customer details:', summaryResponse.data.customerDetail);
  console.log('Customer balances:', summaryResponse.data.balances);
}
```

## Error Handling

The pipeline includes comprehensive error handling:
- Validates required parameters (customerId, javax data, viewState)
- Handles JSF partial response parsing errors
- Provides detailed error messages for debugging
- Logs all request/response data for troubleshooting

## HTML Parsing

The pipeline uses the same HTML parsing logic as other customer pipelines:
- Parses HTML table structures to extract balance values
- Supports both label and span-based value structures
- Handles numeric values with comma formatting
- Returns null for missing or empty values
- Provides detailed logging for each extraction attempt

## Response Structure

All balance fields are always included in the response, even if null:
- Numeric values are parsed as numbers
- Missing values are returned as null
- Empty values are returned as null
- Invalid numeric values fall back to string representation

## Dependencies

- `fast-xml-parser`: For parsing JSF XML responses
- `node-html-parser`: For parsing HTML content and extracting balance data
- Shared `CustomerDetailsPreloadRequest`: For initial page state
- PGDagurApiClient: For authenticated requests to Dagur API
