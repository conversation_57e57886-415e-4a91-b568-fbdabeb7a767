import { findJavaxElementId, findJavaxElementIdBefore } from '@/network/pg-dagur/utils/javax';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';

export interface CustomerDetailsBetsTabChangeRequestOptions {}

export interface CustomerDetailsBetsTabChangeRequestResponse {
  searchButtonId: string;
  currencyInputId: string;
  providerInputId: string;
}

export class CustomerDetailsBetsTabChangeRequest extends PGDagurRequest<
  CustomerDetailsBetsTabChangeRequestResponse,
  any
> {
  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:tabView',
      'javax.faces.partial.execute': 'form:tabView',
      'javax.faces.partial.render': 'form:tabView',
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      'form:tabView_contentLoad': 'true',
      'form:tabView_newTab': `form:tabView:customerBetReports`,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsBetsTabChangeRequestResponse> {
    const searchButtonId = findJavaxElementIdBefore(
      xml,
      'form:tabView:',
      '<span class="ui-button-text ui-c">Search</span>',
    );
    if (!searchButtonId) {
      return {
        success: false,
        message: 'Failed to find search button id',
      };
    }

    const currencyInputId = findJavaxElementId(xml, 'form:tabView:', 'Display Currency');
    if (!currencyInputId) {
      return {
        success: false,
        message: 'Failed to find currency input id',
      };
    }

    const providerInputId = findJavaxElementId(xml, 'form:tabView:', 'Provider');
    if (!providerInputId) {
      return {
        success: false,
        message: 'Failed to find provider input id',
      };
    }

    return {
      success: true,
      data: {
        searchButtonId,
        currencyInputId,
        providerInputId,
      },
    };
  }
}
