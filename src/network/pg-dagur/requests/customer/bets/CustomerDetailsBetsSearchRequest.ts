import { formatDate } from '@/network/pg-dagur/utils/time';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsBetsTabChangeRequestResponse } from './CustomerDetailsBetsTabChangeRequest';
import { parse } from 'node-html-parser';
import { findJavaxElementIdBefore } from '@/network/pg-dagur/utils/javax';

export interface CustomerDetailsBetsSearchRequestOptions {
  javax: CustomerDetailsBetsTabChangeRequestResponse;

  currencyId?: number;
  providerId?: number;
  startDate: Date;
  endDate: Date;
}

export interface CustomerDetailsBetsSearchRequestResponse {
  items: {
    playedAmount: number;
    remainingPlayedAmount: number;
    possibleWinAmount: number;
    multibetBonusAmount: number;
    totalAmountToBeWon: number;
    winAmount: number;
    bonusAmount: number;
    transactionCurrency: string;
    date: Date;
    status: string; // L, O, W
    hasEp: boolean;
    settledEp: boolean;
    isLiveBet: boolean;
    couponType: string; // single, combo
  }[];

  statusFilterId: string;
  couponTypeId: string;

  table1: {
    masterIdColumnId: string;
    codeColumnId: string;
    playedAmountColumnId: string;
    remaniningPlayedAmountColumnId: string;
    possibleWinAmountColumnId: string;
    multibetBonusAmountColumnId: string;
    totalAmountToBeWonColumnId: string;
    winAmountColumnId: string;
    // bonusAmountColumnId: string;
    transactionCurrencyColumnId: string;
    dateColumnId: string;
    statusColumnId: string;
    hasEpColumnId: string;
    settledEpColumnId: string;
    // isLiveBetColumnId: string;
    // betBuilderColumnId: string;
    // customEventColumnId: string;
    couponTypeColumnId: string;
  };
  table2: {};
  table3: {};
}

export class CustomerDetailsBetsSearchRequest extends PGDagurRequest<CustomerDetailsBetsSearchRequestResponse, any> {
  constructor(private options: CustomerDetailsBetsSearchRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `form:tabView:${this.options.javax.searchButtonId}`,
      'javax.faces.partial.execute': 'form',
      'javax.faces.partial.render':
        'form:tabView:customerSportBetReportsLeftPanel form:tabView:customerSportBetReportsRightPanel',
      'javax.faces.behavior.event': 'click',
      'javax.faces.partial.event': 'click',
      form: 'form',
      'form:tabView:customerBetReportCriteriaStartDate_input': formatDate(this.options.startDate),
      'form:tabView:customerBetReportCriteriaEndDate_input': formatDate(this.options.endDate),
      [`form:tabView:${this.options.javax.currencyInputId}_input`]: this.options.currencyId ?? 1,
      [`form:tabView:${this.options.javax.providerInputId}_input`]: this.options.providerId ?? -1,
      'form:tabView:betBuilderRadioButton': -1,
      'form:tabView:customRadioButton': -1,
      'form:tabView:earlyPayoutButton': -1,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsBetsSearchRequestResponse> {
    try {
      const items = this.parseBetRows(xml);

      const statusFilterId = findJavaxElementIdBefore(
        xml,
        'form:tabView:sportBetMasterDataTable:',
        '<span class="ui-column-title">Status</span>',
      );
      if (!statusFilterId) {
        return {
          success: false,
          message: 'Failed to find status filter id',
        };
      }

      const couponTypeId = findJavaxElementIdBefore(
        xml,
        'form:tabView:sportBetMasterDataTable:',
        '<span class="ui-column-title">Coupon Type</span>',
      );
      if (!couponTypeId) {
        return {
          success: false,
          message: 'Failed to find coupon type id',
        };
      }

      const table1 = this.parseTable1Columns(xml);
      // const table2 = this.parseTable2Columns(xml, table2ColumnId);
      // const table3 = this.parseTable3Columns(xml, table3ColumnId);

      return {
        success: true,
        data: { items, statusFilterId, couponTypeId, table1, table2: {}, table3: {} },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse customer bets search: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseTable1Columns(xml: string): CustomerDetailsBetsSearchRequestResponse['table1'] {
    const masterId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Master ID</span>',
    );
    if (!masterId) {
      throw new Error('Failed to find masterId column id');
    }

    const codeId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Code</span>',
    );
    if (!codeId) {
      throw new Error('Failed to find code column id');
    }

    const playedAmountId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Played Amount</span>',
    );
    if (!playedAmountId) {
      throw new Error('Failed to find playedAmount column id');
    }

    const remainingPlayedAmountId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Remaining Played Amount</span>',
    );
    if (!remainingPlayedAmountId) {
      throw new Error('Failed to find remainingPlayedAmount column id');
    }

    const possibleWinAmountId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Possible Win Amount</span>',
    );
    if (!possibleWinAmountId) {
      throw new Error('Failed to find possibleWinAmount column id');
    }

    const multibetBonusAmountId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Multibet Bonus Amount</span>',
    );
    if (!multibetBonusAmountId) {
      throw new Error('Failed to find multibetBonusAmount column id');
    }

    const totalAmountToBeWonId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Total Amount To Be Won</span>',
    );
    if (!totalAmountToBeWonId) {
      throw new Error('Failed to find totalAmountToBeWon column id');
    }

    const winAmountId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Win Amount</span>',
    );
    if (!winAmountId) {
      throw new Error('Failed to find winAmount column id');
    }

    // const bonusAmountId = findJavaxElementIdBefore(
    //   xml,
    //   'form:tabView:sportBetMasterDataTable:',
    //   '<span class="ui-column-title">Bonus Amount</span>',
    // );
    // if (!bonusAmountId) {
    //   throw new Error('Failed to find bonusAmount column id');
    // }

    const transactionCurrencyId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Transaction Currency</span>',
    );
    if (!transactionCurrencyId) {
      throw new Error('Failed to find transactionCurrency column id');
    }

    const dateId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Date</span>',
    );
    if (!dateId) {
      throw new Error('Failed to find date column id');
    }

    const statusId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Status</span>',
    );
    if (!statusId) {
      throw new Error('Failed to find status column id');
    }

    const hasEpId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Has EP</span>',
    );
    if (!hasEpId) {
      throw new Error('Failed to find hasEp column id');
    }

    const settledEpId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Settled EP</span>',
    );
    if (!settledEpId) {
      throw new Error('Failed to find settledEp column id');
    }

    // const isLiveBetId = findJavaxElementIdBefore(
    //   xml,
    //   'form:tabView:sportBetMasterDataTable:',
    //   '<span class="ui-column-title">Is Live Bet</span>',
    // );
    // if (!isLiveBetId) {
    //   throw new Error('Failed to find isLiveBet column id');
    // }

    // const betBuilderId = findJavaxElementIdBefore(
    //   xml,
    //   'form:tabView:sportBetMasterDataTable:',
    //   '<span class="ui-column-title">Bet Builder</span>',
    // );
    // if (!betBuilderId) {
    //   throw new Error('Failed to find betBuilder column id');
    // }

    // const customEventId = findJavaxElementIdBefore(
    //   xml,
    //   'form:tabView:sportBetMasterDataTable:',
    //   '<span class="ui-column-title">Custom Event</span>',
    // );
    // if (!customEventId) {
    //   throw new Error('Failed to find customEvent column id');
    // }

    const couponTypeId = findJavaxElementIdBefore(
      xml,
      'form:tabView:sportBetMasterDataTable:',
      '<span class="ui-column-title">Coupon Type</span>',
    );
    if (!couponTypeId) {
      throw new Error('Failed to find couponType column id');
    }

    return {
      masterIdColumnId: masterId,
      codeColumnId: codeId,
      playedAmountColumnId: playedAmountId,
      remaniningPlayedAmountColumnId: remainingPlayedAmountId,
      possibleWinAmountColumnId: possibleWinAmountId,
      multibetBonusAmountColumnId: multibetBonusAmountId,
      totalAmountToBeWonColumnId: totalAmountToBeWonId,
      winAmountColumnId: winAmountId,
      // bonusAmountColumnId: bonusAmountId,
      transactionCurrencyColumnId: transactionCurrencyId,
      dateColumnId: dateId,
      statusColumnId: statusId,
      hasEpColumnId: hasEpId,
      settledEpColumnId: settledEpId,
      // isLiveBetColumnId: isLiveBetId,
      // betBuilderColumnId: betBuilderId,
      // customEventColumnId: customEventId,
      couponTypeColumnId: couponTypeId,
    };
  }

  private parseBetRows(xml: string): CustomerDetailsBetsSearchRequestResponse['items'] {
    const doc = parse(xml);

    const tbody = doc.getElementById('form:tabView:sportBetMasterDataTable_data');
    if (!tbody) {
      throw new Error('Failed to find bet table body');
    }

    const rows = tbody.querySelectorAll('tr');
    const items: CustomerDetailsBetsSearchRequestResponse['items'] = [];

    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 18) {
        try {
          const item = this.parseRowData(cells);
          items.push(item);
        } catch (error) {
          console.warn('Failed to parse bet row:', error);
          // Continue parsing other rows even if one fails
        }
      }
    }

    return items;
  }

  private parseRowData(cells: any[]): CustomerDetailsBetsSearchRequestResponse['items'][0] {
    // Extract numeric values from tooltip wrappers or direct text
    const getNumericValue = (cell: any): number => {
      const tooltipText = cell.querySelector('[id*=":text"]')?.text?.trim();
      if (tooltipText) {
        return parseFloat(tooltipText) || 0;
      }
      const directText = cell.text?.replace(/[^\d.-]/g, '');
      return parseFloat(directText) || 0;
    };

    // Extract bonus amount and parse it
    const getBonusAmount = (cell: any): number => {
      const tooltipText = cell.querySelector('[id*=":text"]')?.text?.trim();
      if (tooltipText) {
        // Extract number from text like "7.00 (SBB)"
        const match = tooltipText.match(/^([\d.]+)/);
        return match ? parseFloat(match[1]) : 0;
      }
      return 0;
    };

    // Extract checkbox state
    const getCheckboxState = (cell: any): boolean => {
      const checkbox = cell.querySelector('input[type="checkbox"]');
      return checkbox?.getAttribute('checked') === 'checked' || checkbox?.getAttribute('aria-checked') === 'true';
    };

    // Parse date from format "23.07.2025 22:25:45" as UTC
    const parseDate = (dateStr: string): Date => {
      const match = dateStr.match(/(\d{2})\.(\d{2})\.(\d{4}) (\d{2}):(\d{2}):(\d{2})/);
      if (match) {
        const [, day, month, year, hour, minute, second] = match;
        return new Date(
          Date.UTC(
            parseInt(year!),
            parseInt(month!) - 1,
            parseInt(day!),
            parseInt(hour!),
            parseInt(minute!),
            parseInt(second!),
          ),
        );
      }
      return new Date();
    };

    return {
      playedAmount: getNumericValue(cells[2]),
      remainingPlayedAmount: getNumericValue(cells[3]),
      possibleWinAmount: getNumericValue(cells[4]),
      multibetBonusAmount: getNumericValue(cells[5]),
      totalAmountToBeWon: getNumericValue(cells[6]),
      winAmount: getNumericValue(cells[7]),
      bonusAmount: getBonusAmount(cells[8]),
      transactionCurrency: cells[9]?.text?.trim() || '',
      date: parseDate(cells[10]?.text?.trim() || ''),
      status: cells[11]?.text?.trim() || '',
      hasEp: cells[12]?.text?.trim() === '1',
      settledEp: cells[13]?.text?.trim() === '1',
      isLiveBet: getCheckboxState(cells[14]),
      couponType: cells[17]?.text?.trim().toLowerCase() || 'single',
    };
  }
}
