import { formatDate } from '@/network/pg-dagur/utils/time';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsBetsTabChangeRequestResponse } from './CustomerDetailsBetsTabChangeRequest';
import parse from 'node-html-parser';
import { CustomerDetailsBetsSearchRequestResponse } from './CustomerDetailsBetsSearchRequest';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

export interface CustomerDetailsBetsFilterRequestOptions {
  javax: CustomerDetailsPreloadRequestResponse['formElementIds'] &
    CustomerDetailsBetsTabChangeRequestResponse &
    CustomerDetailsBetsSearchRequestResponse;

  status: string;

  customerId: number;
  currencyId?: number;
  providerId?: number;
  startDate: Date;
  endDate: Date;
}

export interface CustomerDetailsBetsFilterRequestResponse {
  items: {
    playedAmount: number;
    remainingPlayedAmount: number;
    possibleWinAmount: number;
    multibetBonusAmount: number;
    totalAmountToBeWon: number;
    winAmount: number;
    bonusAmount: number;
    transactionCurrency: string;
    date: Date;
    status: string; // L, O, W
    hasEp: boolean;
    settledEp: boolean;
    isLiveBet: boolean;
    couponType: string; // single, combo
  }[];
}

export class CustomerDetailsBetsFilterRequest extends PGDagurRequest<any, any> {
  constructor(private options: CustomerDetailsBetsFilterRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:tabView:sportBetMasterDataTable',
      'javax.faces.partial.execute': 'form:tabView:sportBetMasterDataTable',
      'javax.faces.partial.render': 'form:tabView:sportBetMasterDataTable',
      'form:tabView:sportBetMasterDataTable': 'form:tabView:sportBetMasterDataTable',
      'form:tabView:sportBetMasterDataTable_filtering': 'true',
      'form:tabView:sportBetMasterDataTable_encodeFeature': 'true',
      form: 'form',
      'form:tabView:customerNotesDt_rppDD': '10',
      'form:tabView:customerBetReportCriteriaStartDate_input': formatDate(this.options.startDate),
      'form:tabView:customerBetReportCriteriaEndDate_input': formatDate(this.options.endDate),
      'form:tabView:betBuilderRadioButton': '-1',
      'form:tabView:customRadioButton': '-1',
      'form:tabView:earlyPayoutButton': '-1',

      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.masterIdColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.codeColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.playedAmountColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.remaniningPlayedAmountColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.possibleWinAmountColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.multibetBonusAmountColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.totalAmountToBeWonColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.winAmountColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.transactionCurrencyColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.dateColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.statusColumnId}:filter`]: this.options.status,
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.hasEpColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.settledEpColumnId}:filter`]: '',
      [`form:tabView:sportBetMasterDataTable:${this.options.javax.table1.couponTypeColumnId}:filter`]: 'X',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsBetsFilterRequestResponse> {
    try {
      if (xml.includes('/error')) {
        return {
          success: false,
          message: 'Failed to filter customer bets: validation error',
        };
      }

      if (xml.includes('No records found')) {
        return {
          success: true,
          data: { items: [] },
        };
      }

      const items = this.parseBetRows(xml);

      return {
        success: true,
        data: { items },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse customer bets search: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseBetRows(xml: string): CustomerDetailsBetsFilterRequestResponse['items'] {
    const doc = parse(xml);

    const rows = doc.querySelectorAll('tr');
    const items: CustomerDetailsBetsFilterRequestResponse['items'] = [];

    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 18) {
        try {
          const item = this.parseRowData(cells);
          items.push(item);
        } catch (error) {
          console.warn('Failed to parse bet row:', error);
          // Continue parsing other rows even if one fails
        }
      }
    }

    return items;
  }

  private parseRowData(cells: any[]): CustomerDetailsBetsFilterRequestResponse['items'][0] {
    // Extract numeric values from tooltip wrappers or direct text
    const getNumericValue = (cell: any): number => {
      const tooltipText = cell.querySelector('[id*=":text"]')?.text?.trim();
      if (tooltipText) {
        return parseFloat(tooltipText) || 0;
      }
      const directText = cell.text?.replace(/[^\d.-]/g, '');
      return parseFloat(directText) || 0;
    };

    // Extract bonus amount and parse it
    const getBonusAmount = (cell: any): number => {
      const tooltipText = cell.querySelector('[id*=":text"]')?.text?.trim();
      if (tooltipText) {
        // Extract number from text like "7.00 (SBB)"
        const match = tooltipText.match(/^([\d.]+)/);
        return match ? parseFloat(match[1]) : 0;
      }
      return 0;
    };

    // Extract checkbox state
    const getCheckboxState = (cell: any): boolean => {
      const checkbox = cell.querySelector('input[type="checkbox"]');
      return checkbox?.getAttribute('checked') === 'checked' || checkbox?.getAttribute('aria-checked') === 'true';
    };

    // Parse date from format "23.07.2025 22:25:45" as UTC
    const parseDate = (dateStr: string): Date => {
      const match = dateStr.match(/(\d{2})\.(\d{2})\.(\d{4}) (\d{2}):(\d{2}):(\d{2})/);
      if (match) {
        const [, day, month, year, hour, minute, second] = match;
        return new Date(
          Date.UTC(
            parseInt(year!),
            parseInt(month!) - 1,
            parseInt(day!),
            parseInt(hour!),
            parseInt(minute!),
            parseInt(second!),
          ),
        );
      }
      return new Date();
    };

    return {
      playedAmount: getNumericValue(cells[2]),
      remainingPlayedAmount: getNumericValue(cells[3]),
      possibleWinAmount: getNumericValue(cells[4]),
      multibetBonusAmount: getNumericValue(cells[5]),
      totalAmountToBeWon: getNumericValue(cells[6]),
      winAmount: getNumericValue(cells[7]),
      bonusAmount: getBonusAmount(cells[8]),
      transactionCurrency: cells[9]?.text?.trim() || '',
      date: parseDate(cells[10]?.text?.trim() || ''),
      status: cells[11]?.text?.trim() || '',
      hasEp: cells[12]?.text?.trim() === '1',
      settledEp: cells[13]?.text?.trim() === '1',
      isLiveBet: getCheckboxState(cells[14]),
      couponType: cells[17]?.text?.trim().toLowerCase() || 'single',
    };
  }
}
