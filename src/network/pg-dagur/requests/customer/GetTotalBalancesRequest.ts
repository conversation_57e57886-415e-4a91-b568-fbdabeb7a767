import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';
import { findJavaxElementId } from '../../utils/javax';
import { formatDate } from '../../utils/time';

export interface GetTotalBalancesRequestOptions {
  testPlayerInputId: string;
  endDate: Date;
}

interface PreloadRequestResponse {
  testPlayerInputId: string;
}

export type GetTotalBalancesResponse = {
  mainBalance: { [currency: string]: number };
  sportBonusBalance: { [currency: string]: number };
  sportFreebetBalance: { [currency: string]: number };
  casinoBonusBalance: { [currency: string]: number };
};

export class GetTotalBalancesPreloadRequest extends PGDagurRequest<PreloadRequestResponse> {
  getPath(): string {
    return `/restricted/customer-total-balance.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<PreloadRequestResponse> {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@',
      unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
      stopNodes: ['*.pre', '*.script'],
      processEntities: true,
      htmlEntities: true,
    });
    const obj = parser.parse(xml);

    const html = obj['html'];
    if (!html) {
      return {
        success: false,
        message: 'Missing html tag',
      };
    }

    const body = html['body'];
    if (!body) {
      return {
        success: false,
        message: 'Missing body tag',
      };
    }

    const form = body['form'];
    if (!form) {
      return {
        success: false,
        message: 'Missing form tag',
      };
    }

    const input = form['input']?.find((i: any) => i['@name'] === 'javax.faces.ViewState');
    if (!input) {
      return {
        success: false,
        message: 'Missing view state input',
      };
    }

    const viewState = input['@value'];
    if (!viewState) {
      return {
        success: false,
        message: 'Missing view state value',
      };
    }

    const testPlayerInputId = findJavaxElementId(xml, 'form:', 'Test Player');
    if (!testPlayerInputId) {
      return {
        success: false,
        message: 'Failed to find test player input id',
      };
    }

    return {
      success: true,
      data: {
        testPlayerInputId,
      },
      viewState,
    };
  }
}

export class GetTotalBalancesRequest extends PGDagurRequest<GetTotalBalancesResponse, any> {
  constructor(private options: GetTotalBalancesRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-total-balance.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.source': 'form:searchButton',
      'javax.faces.partial.execute': 'form',
      'javax.faces.partial.render':
        'form:custTotalBalanceSearchPanel form:custTotalBalanceRealDateDt form:custTotalBalanceBonusDateDt form:custTotalBalanceFreebetDateDt form:custTotalBalanceCasinoBonusDateDt form:custTotalBalanceCasinoSportDateDt growl',
      'form:searchButton': 'form:searchButton',
      form: 'form',
      'form:endDate_input': formatDate(this.options.endDate, 'datetime'),
      [`form:${this.options.testPlayerInputId}_focus`]: '',
      [`form:${this.options.testPlayerInputId}_input`]: '-99',
      'form:status_focus': '',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<GetTotalBalancesResponse> {
    try {
      // Initialize response structure
      const response: GetTotalBalancesResponse = {
        mainBalance: {},
        sportBonusBalance: {},
        sportFreebetBalance: {},
        casinoBonusBalance: {},
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the balance data
        let balanceUpdate = update;
        if (Array.isArray(update)) {
          balanceUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('custTotalBalanceRealDateDt') ||
              u?.['#text']?.includes('custTotalBalanceRealDateDt'),
          );
        }

        if (!balanceUpdate) {
          return {
            success: false,
            message: 'No balance data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = balanceUpdate['#text'] || balanceUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in balance update',
          };
        }

        // Parse the HTML content
        this.parseBalanceHTML(htmlContent, response);
      } else {
        // Handle direct HTML content
        this.parseBalanceHTML(xml, response);
      }

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error parsing balance data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseBalanceHTML(html: string, response: GetTotalBalancesResponse): void {
    // Parse HTML using node-html-parser
    const root = parse(html);

    // Find all balance tables by their IDs or classes
    const tables = [
      { element: root.querySelector('#form\\:custTotalBalanceRealDateDt'), type: 'main' },
      { element: root.querySelector('#form\\:custTotalBalanceBonusDateDt'), type: 'sportBonus' },
      { element: root.querySelector('#form\\:custTotalBalanceFreebetDateDt'), type: 'sportFreebet' },
      { element: root.querySelector('#form\\:custTotalBalanceCasinoSportDateDt'), type: 'casinoBonus' },
    ];

    // If no specific tables found, try to find any datatable
    if (tables.every((t) => !t.element)) {
      const anyTable = root.querySelector('.ui-datatable');
      if (anyTable) {
        tables.push({ element: anyTable, type: 'main' });
      }
    }

    for (const { element: table, type } of tables) {
      if (!table) continue;

      // Find all table rows with data-ri attribute (balance rows)
      const rows = table.querySelectorAll('tr[data-ri]');

      if (!rows || rows.length === 0) continue;

      for (const row of rows) {
        // Find all labels in the row
        const labels = row.querySelectorAll('label');

        if (labels.length < 2) continue;

        // First label should contain the balance
        const balanceLabel = labels[0];
        const balanceText = balanceLabel?.text?.trim();

        // Second label should contain the currency
        const currencyLabel = labels[1];
        const currencyText = currencyLabel?.text?.trim();

        if (!balanceText || !currencyText) continue;

        // Clean and parse the values
        const balance = parseFloat(balanceText.replace(/,/g, ''));
        const currency = currencyText.trim();

        if (!isNaN(balance) && currency) {
          // Assign to appropriate balance type
          switch (type) {
            case 'main':
              response.mainBalance[currency] = balance;
              break;
            case 'sportBonus':
              response.sportBonusBalance[currency] = balance;
              break;
            case 'sportFreebet':
              response.sportFreebetBalance[currency] = balance;
              break;
            case 'casinoBonus':
              response.casinoBonusBalance[currency] = balance;
              break;
            default:
              response.mainBalance[currency] = balance;
              break;
          }
        }
      }
    }

    // If no data was found, throw an error
    const hasData =
      Object.keys(response.mainBalance).length > 0 ||
      Object.keys(response.sportBonusBalance).length > 0 ||
      Object.keys(response.sportFreebetBalance).length > 0 ||
      Object.keys(response.casinoBonusBalance).length > 0;

    if (!hasData) {
      throw new Error('No balance data found in HTML');
    }
  }
}
