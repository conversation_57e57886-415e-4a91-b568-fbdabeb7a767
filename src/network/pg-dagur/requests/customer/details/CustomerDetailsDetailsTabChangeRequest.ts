import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { parse } from 'node-html-parser';

export interface CustomerDetailsDetailsTabChangeRequestOptions {}

export interface CustomerDetailsDetailsTabChangeRequestResponse {
  basic: {
    customerId: number;
    customerCode: string;
    username: string;
    firstName: string;
    secondName: string | null;
    surname: string;
    loginStatus: string;
    selfExclusionStatus: string | null;
    telephone: string;
    birthDate: string;
    identity: string | null;
    cpfNumber: string | null;
    email: string;
    currency: string;
    language: string;
  };
  contacts: {
    telegramUsername: string;
    address: string;
    stateName: string;
    city: string;
    postalCode: string;
    street: string;
    houseName: string;
    country: string;
  };
  sessions: {
    registerDate: Date;
    registerIp: string;
    lastLoginDate: Date;
    lastLoginIp: string;
    lastSessionId: string;
  };
  '2fa': {
    otpType: string;
    otpStatus: boolean;
    gaKey: string | null;
    gaUrl: string | null;
  };
  kyc: {
    kycEmail: boolean;
    kycPhone: boolean;
    kycIdentity: boolean;
    kycAddress: boolean;
  };
  affiliate: {
    incomeAffiliateCode: string | null;
    netreferAffiliateCode: string | null;
    netreferOfflineAffiliateCode: string | null;
    retailAffiliateCode: string | null;
    voluumAffiliateCode: string | null;
    trackboxAffiliateCode: string | null;
    trackboxAffiliateId: string | null;
  };
  other: {
    tosDate: Date | null;
    secretQuestion: number;
    secretAnswer: string;
    nickname: string;
    klasPokerUsername: string;
    favouriteTeam: string;
    safeWord: string;
    // usedBonus: boolean;
    // testUser: boolean;
    // bonusSeeker: boolean;
    // vipCustomer: boolean;
    // potentialVipCustomer: boolean;
    howDidYouHearAboutUs: string;
    accountClosedLevel: string;
    totalLoginErrorCounter: number;
    totalVerifyIdentityErrorCounter: number;
    referenceCode: string;
    registerCode: string;
  };
}

export class CustomerDetailsDetailsTabChangeRequest extends PGDagurRequest<
  CustomerDetailsDetailsTabChangeRequestResponse,
  any
> {
  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:tabView',
      'javax.faces.partial.execute': 'form:tabView',
      'javax.faces.partial.render': 'form:tabView',
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      'form:tabView_contentLoad': 'true',
      'form:tabView_newTab': `form:tabView:customerInfo`,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsDetailsTabChangeRequestResponse> {
    try {
      const data = this.parseCustomerDetails(xml);

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse customer details: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseCustomerDetails(xml: string): CustomerDetailsDetailsTabChangeRequestResponse {
    const doc = parse(xml);

    // Parse basic customer information
    const basic = this.parseBasicInfo(doc);

    // Parse contact information
    const contacts = this.parseContactInfo(doc);

    // Return parsed data with placeholder values for sections not yet implemented
    return {
      basic,
      contacts,
      sessions: this.parseSessionsInfo(doc),
      '2fa': this.parse2FAInfo(doc),
      kyc: this.parseKYCInfo(doc),
      affiliate: this.parseAffiliateInfo(doc),
      other: this.parseOtherInfo(doc),
    };
  }

  private parseBasicInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['basic'] {
    // Helper function to find value by label text
    const findValueByLabel = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Remove script tags before extracting text
            const scripts = valueCell.querySelectorAll('script');
            scripts.forEach((script: any) => script.remove());

            // Try different ways to extract value
            let value = valueCell.text.trim().replace(/ui-button/g, '');

            // If empty, try to find nested elements
            if (!value) {
              const nestedElements = valueCell.querySelectorAll('span, label, input');
              for (const element of nestedElements) {
                const elementText = element.text.trim();
                if (elementText && elementText !== '-') {
                  value = elementText.replace(/ui-button/g, '');
                  break;
                }
              }
            }

            return value === '-' ? '' : value;
          }
        }
      }
      return '';
    };

    // Helper function to extract selected option value
    const findSelectedOption = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim();
          if (labelContent.includes(labelText)) {
            // Look for selected option in select element
            const select = valueCell.querySelector('select');
            if (select) {
              const selectedOption = select.querySelector('option[selected]');
              if (selectedOption) {
                return selectedOption.text.trim();
              }
            }

            // Fallback to label text in dropdown
            const dropdownLabel = valueCell.querySelector('.ui-selectonemenu-label');
            if (dropdownLabel) {
              return dropdownLabel.text.trim();
            }
          }
        }
      }
      return '';
    };

    const customerId = parseInt(findValueByLabel('Customer ID') || '0', 10);
    const customerCode = findValueByLabel('Customer Code');
    const username = findValueByLabel('Username');
    const firstName = findValueByLabel('First Name');
    const secondName = findValueByLabel('Second Name');
    const surname = findValueByLabel('Surname');
    const loginStatus = findSelectedOption('Login Status');
    const selfExclusionStatus = findValueByLabel('Self Exclusion Status');
    const telephone = findValueByLabel('Telephone');
    const birthDate = findValueByLabel('Birth Date');
    const identity = findValueByLabel('Identity');
    const cpfNumber = findValueByLabel('CPF Number');
    const email = findValueByLabel('E-Mail');
    const currency = findValueByLabel('Currency');
    const language = findValueByLabel('Language');

    return {
      customerId,
      customerCode,
      username,
      firstName,
      secondName: secondName || null,
      surname,
      loginStatus,
      selfExclusionStatus: selfExclusionStatus || null,
      telephone,
      birthDate,
      identity: identity || null,
      cpfNumber: cpfNumber || null,
      email,
      currency: currency || 'TRY',
      language: language || 'English',
    };
  }

  private parseContactInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['contacts'] {
    // Helper function to find value by label text
    const findValueByLabel = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Remove script tags before extracting text
            const scripts = valueCell.querySelectorAll('script');
            scripts.forEach((script: any) => script.remove());

            // Try different ways to extract value
            let value = valueCell.text.trim().replace(/ui-button/g, '');

            // If empty, try to find nested elements
            if (!value) {
              const nestedElements = valueCell.querySelectorAll('span, label, input');
              for (const element of nestedElements) {
                const elementText = element.text.trim();
                if (elementText && elementText !== '-') {
                  value = elementText.replace(/ui-button/g, '');
                  break;
                }
              }
            }

            return value === '-' ? '' : value;
          }
        }
      }
      return '';
    };

    return {
      telegramUsername: findValueByLabel('Telegram Username'),
      address: findValueByLabel('Address'),
      stateName: findValueByLabel('State Name'),
      city: findValueByLabel('City'),
      postalCode: findValueByLabel('Post Code'),
      street: findValueByLabel('Street'),
      houseName: findValueByLabel('House Name'),
      country: findValueByLabel('Country'),
    };
  }

  private parseSessionsInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['sessions'] {
    // Helper function to find value by label text
    const findValueByLabel = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Remove script tags before extracting text
            const scripts = valueCell.querySelectorAll('script');
            scripts.forEach((script: any) => script.remove());

            let value = valueCell.text.trim().replace(/ui-button/g, '');

            // If empty, try to find nested elements
            if (!value) {
              const nestedElements = valueCell.querySelectorAll('span, label, input');
              for (const element of nestedElements) {
                const elementText = element.text.trim();
                if (elementText && elementText !== '-') {
                  value = elementText.replace(/ui-button/g, '');
                  break;
                }
              }
            }

            return value === '-' ? '' : value;
          }
        }
      }
      return '';
    };

    // Helper function to parse date strings as UTC
    const parseDate = (dateStr: string): Date => {
      if (!dateStr) return new Date();
      try {
        // Parse format: "09.06.2025 15:40:59"
        const [datePart, timePart] = dateStr.split(' ');
        if (!datePart || !timePart) return new Date();

        const [day, month, year] = datePart.split('.');
        const [hour, minute, second] = timePart.split(':');

        if (!day || !month || !year || !hour || !minute || !second) return new Date();

        return new Date(
          Date.UTC(
            parseInt(year, 10),
            parseInt(month, 10) - 1,
            parseInt(day, 10),
            parseInt(hour, 10),
            parseInt(minute, 10),
            parseInt(second, 10),
          ),
        );
      } catch {
        return new Date();
      }
    };

    const registerDateStr = findValueByLabel('Register Date');
    const lastLoginDateStr = findValueByLabel('Last Login Date');

    return {
      registerDate: parseDate(registerDateStr),
      registerIp: findValueByLabel('Register IP'),
      lastLoginDate: parseDate(lastLoginDateStr),
      lastLoginIp: findValueByLabel('Last Login IP'),
      lastSessionId: findValueByLabel('Last Session ID'),
    };
  }

  private parse2FAInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['2fa'] {
    // Helper function to find value by label text
    const findValueByLabel = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[2];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Remove script tags before extracting text
            const scripts = valueCell.querySelectorAll('script');
            scripts.forEach((script: any) => script.remove());

            let value = valueCell.text.trim().replace(/ui-button/g, '');

            // If empty, try to find nested elements
            if (!value) {
              const nestedElements = valueCell.querySelectorAll('span, label, input, select');
              for (const element of nestedElements) {
                const elementText = element.text.trim();
                if (elementText && elementText !== '-') {
                  value = elementText.replace(/ui-button/g, '');
                  break;
                }
              }
            }

            return value === '-' ? '' : value;
          }
        }
      }
      return '';
    };

    // Helper function to check checkbox status
    const isCheckboxChecked = (labelText: string): boolean => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[2];
          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Look for checked checkbox
            const checkbox = valueCell.querySelector('input[type="checkbox"]');
            if (checkbox) {
              return checkbox.hasAttribute('checked') || checkbox.getAttribute('aria-checked') === 'true';
            }
          }
        }
      }
      return false;
    };

    // Helper function to extract selected option from OTP Type dropdown
    const findOtpTypeSelected = (): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 3) {
          const labelCell = cells[0];
          const valueCell = cells[2]; // OTP Type is in the 3rd column

          const labelContent = labelCell.text.trim();
          if (labelContent.includes('OTP Type')) {
            // Look for selected option in select element
            const select = valueCell.querySelector('select');
            if (select) {
              const selectedOption = select.querySelector('option[selected]');
              if (selectedOption) {
                return selectedOption.getAttribute('value') || selectedOption.text.trim();
              }
            }

            // Fallback to dropdown label
            const dropdownLabel = valueCell.querySelector('.ui-selectonemenu-label');
            if (dropdownLabel) {
              return dropdownLabel.text.trim();
            }
          }
        }
      }
      return '';
    };

    const otpType = findOtpTypeSelected();
    const otpStatus = isCheckboxChecked('OTP Status');
    const gaKey = findValueByLabel('GA Key');
    const gaUrl = findValueByLabel('GA Url');

    return {
      otpType,
      otpStatus,
      gaKey: gaKey === '--' ? null : gaKey,
      gaUrl: gaUrl === '--' ? null : gaUrl,
    };
  }

  private parseKYCInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['kyc'] {
    // Helper function to check switch status
    const isSwitchEnabled = (labelText: string): boolean => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Fallback to checkbox
            const checkbox = valueCell.querySelector('input[type="checkbox"]');
            if (checkbox) {
              return checkbox.hasAttribute('checked') || checkbox.getAttribute('aria-checked') === 'true';
            }
          }
        }
      }
      return false;
    };

    return {
      kycEmail: isSwitchEnabled('KYC Email'),
      kycPhone: isSwitchEnabled('KYC Phone'),
      kycIdentity: isSwitchEnabled('KYC Identity'),
      kycAddress: isSwitchEnabled('KYC Address'),
    };
  }

  private parseAffiliateInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['affiliate'] {
    // Helper function to find value by label text
    const findValueByLabel = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Remove script tags before extracting text
            const scripts = valueCell.querySelectorAll('script');
            scripts.forEach((script: any) => script.remove());

            let value = valueCell.text.trim().replace(/ui-button/g, '');

            // If empty, try to find nested elements
            if (!value) {
              const nestedElements = valueCell.querySelectorAll('span, label, input');
              for (const element of nestedElements) {
                const elementText = element.text.trim();
                if (elementText && elementText !== '-') {
                  value = elementText.replace(/ui-button/g, '');
                  break;
                }
              }
            }

            return value === '-' ? '' : value;
          }
        }
      }
      return '';
    };

    const incomeAffiliateCode = findValueByLabel('Income Affiliate Code');
    const netreferAffiliateCode = findValueByLabel('Netrefer Affiliate Code');
    const netreferOfflineAffiliateCode = findValueByLabel('Netrefer Offline Affiliate Code');
    const retailAffiliateCode = findValueByLabel('Retail Affiliate Code');
    const voluumAffiliateCode = findValueByLabel('Voluum Affiliate Code');
    const trackboxAffiliateCode = findValueByLabel('Trackbox Affiliate Code');
    const trackboxAffiliateId = findValueByLabel('Trackbox Aiffliate Id'); // Note: typo in HTML

    return {
      incomeAffiliateCode: incomeAffiliateCode || null,
      netreferAffiliateCode: netreferAffiliateCode || null,
      netreferOfflineAffiliateCode: netreferOfflineAffiliateCode || null,
      retailAffiliateCode: retailAffiliateCode || null,
      voluumAffiliateCode: voluumAffiliateCode || null,
      trackboxAffiliateCode: trackboxAffiliateCode || null,
      trackboxAffiliateId: trackboxAffiliateId || null,
    };
  }

  private parseOtherInfo(doc: any): CustomerDetailsDetailsTabChangeRequestResponse['other'] {
    // Helper function to find value by label text
    const findValueByLabel = (labelText: string): string => {
      const rows = doc.querySelectorAll('tr');
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          const labelCell = cells[0];
          const valueCell = cells[1];

          const labelContent = labelCell.text.trim().replace(/ui-button/g, '');
          if (labelContent.includes(labelText)) {
            // Remove script tags before extracting text
            const scripts = valueCell.querySelectorAll('script');
            scripts.forEach((script: any) => script.remove());

            let value = valueCell.text.trim().replace(/ui-button/g, '');

            // If empty, try to find nested elements
            if (!value) {
              const nestedElements = valueCell.querySelectorAll('span, label, input');
              for (const element of nestedElements) {
                const elementText = element.text.trim();
                if (elementText && elementText !== '-') {
                  value = elementText.replace(/ui-button/g, '');
                  break;
                }
              }
            }

            return value === '-' ? '' : value;
          }
        }
      }
      return '';
    };

    // Helper function to parse date strings as UTC (for TOS date)
    const parseDate = (dateStr: string): Date | null => {
      if (!dateStr) return null;
      try {
        // Parse format: "09.06.2025 15:40:59" or other formats as UTC
        const match = dateStr.match(/(\d{2})\.(\d{2})\.(\d{4}) (\d{2}):(\d{2}):(\d{2})/);
        if (match) {
          const [, day, month, year, hour, minute, second] = match;
          if (!day || !month || !year || !hour || !minute || !second) return null;

          return new Date(
            Date.UTC(
              parseInt(year, 10),
              parseInt(month, 10) - 1,
              parseInt(day, 10),
              parseInt(hour, 10),
              parseInt(minute, 10),
              parseInt(second, 10),
            ),
          );
        }
        // Fallback for other date formats, parse as UTC
        const date = new Date(dateStr);
        return new Date(
          Date.UTC(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds(),
          ),
        );
      } catch {
        return null;
      }
    };

    const tosDateStr = findValueByLabel('Date of Acceptance of the Terms and Conditions and the Privacy Policy');
    const secretQuestionStr = findValueByLabel('Secret Question');
    const secretAnswer = findValueByLabel('Secret Answer');
    const nickname = findValueByLabel('Nickname');
    const klasPokerUsername = findValueByLabel('Klas Poker Username');
    const favouriteTeam = findValueByLabel('Favourite Team');
    const safeWord = findValueByLabel('Safe word');
    const howDidYouHearAboutUs = findValueByLabel('How Did You Hear About Us');
    const accountClosedLevel = findValueByLabel('Account Closed Level');
    const totalLoginErrorCounterStr = findValueByLabel('Total Login Error Counter');
    const totalVerifyIdentityErrorCounterStr = findValueByLabel('Total Verify Identity Error Counter');
    const referenceCode = findValueByLabel('Reference Code');
    const registerCode = findValueByLabel('Register Code');

    return {
      tosDate: parseDate(tosDateStr),
      secretQuestion: parseInt(secretQuestionStr, 10) || 0,
      secretAnswer,
      nickname,
      klasPokerUsername,
      favouriteTeam,
      safeWord,
      // usedBonus: isSwitchEnabled('Used Bonus'),
      // testUser: isSwitchEnabled('Test User'),
      // bonusSeeker: isSwitchEnabled('Bonus Seeker'),
      // vipCustomer: isSwitchEnabled('VIP Customer'),
      // potentialVipCustomer: isSwitchEnabled('Potential VIP Customer'),
      howDidYouHearAboutUs,
      accountClosedLevel,
      totalLoginErrorCounter: parseInt(totalLoginErrorCounterStr, 10) || 0,
      totalVerifyIdentityErrorCounter: parseInt(totalVerifyIdentityErrorCounterStr, 10) || 0,
      referenceCode,
      registerCode,
    };
  }
}
