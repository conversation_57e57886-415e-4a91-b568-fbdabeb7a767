import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

/**
 * Options for the customer details statistics tab change request
 * @interface CustomerDetailsStatisticsTabChangeRequestOptions
 */
export interface CustomerDetailsStatisticsTabChangeRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** JSF element data from the preload request */
  javax: CustomerDetailsPreloadRequestResponse;
  /** ViewState from the preload request */
  viewState: string;
}

/**
 * Response structure for customer statistics data
 * @interface CustomerStatisticsResponse
 */
export interface CustomerStatisticsResponse {
  /** Statistics data extracted from the response */
  statistics: {
    /** Game statistics from the main statistics tab */
    gameStatistics: {
      /** Last bet date */
      lastBetDate?: string;
      /** Income affiliate code */
      incomeAffiliateCode?: string;
      /** Trackbox affiliate ID */
      trackboxAffiliateId?: string;
      /** Klas Poker username */
      klasPokerUsername?: string;
      /** Total bonus win amount */
      totalBonusWin?: number;
      /** Total assigned bonus amount */
      totalAssignedBonus?: number;
      /** Total activated bonus amount */
      totalActivatedBonusAmount?: number;
      /** Total freebet count */
      totalFreebet?: number;
      /** Total used freebet count */
      totalUsedFreebet?: number;
      /** Total unused freebet count */
      totalUnusedFreebet?: number;
      /** Total jackpot joinings count */
      totalJackpotJoinings?: number;
      /** Total jackpot winning count */
      totalJackpotWinningCount?: number;
      /** Total jackpot winning amount */
      totalJackpotWinningAmount?: number;
      /** Total payout amount */
      totalPayout?: number;
      /** Total discount count */
      totalDiscount?: number;
      /** Total discount amount */
      totalDiscountAmount?: number;
      /** Total rakeback count */
      totalRakeback?: number;
      /** Total rakeback amount */
      totalRakebackAmount?: number;
      /** Total deposit of Klas Poker */
      totalDepositOfKlasPoker?: number;
      /** Total deposit number of Klas Poker */
      totalDepositNumberOfKlasPoker?: number;
      /** Total withdraw of Klas Poker */
      totalWithdrawOfKlasPoker?: number;
      /** Total withdraw number of Klas Poker */
      totalWithdrawNumberOfKlasPoker?: number;
      /** Average stake amount */
      averageStake?: number;
      /** Lifetime turnover amount */
      lifetimeTurnover?: number;
    };
    /** Deposit and withdraw statistics from the main statistics tab */
    depositWithdrawStatistics: {
      /** Total deposit amount */
      totalDepositAmount?: number;
      /** Total deposit number */
      totalDepositNumber?: number;
      /** Total withdraw amount */
      totalWithdrawAmount?: number;
      /** Total withdraw number */
      totalWithdrawNumber?: number;
      /** Total ATM outage amount */
      totalAtmOutageAmount?: number;
      /** Total ATM outage number */
      totalAtmOutageNumber?: number;
    };
    /** QR/Reference statistics from the main statistics tab */
    qrReferenceStatistics: {
      /** Total QR/Reference deposit amount */
      totalQrReferenceDepositAmount?: number;
      /** Total QR/Reference deposit number */
      totalQrReferenceDepositNumber?: number;
    };
    /** Profit statistics from the main statistics tab */
    profitStatistics: {
      /** Total profit amount */
      totalProfitAmount?: number;
      /** Profit percentage */
      profitPercentage?: number;
    };
    /** Casino statistics from the main statistics tab */
    casinoStatistics: {
      /** Casino turnover */
      casinoTurnover?: number;
      /** Casino payout */
      casinoPayout?: number;
      /** Casino P/L */
      casinoPL?: number;
      /** Casino P/L percentage */
      casinoPLPercentage?: number;
    };
    /** Manual deposit and withdraw statistics from the main statistics tab */
    manualDepositWithdrawStatistics: {
      /** Manual total deposit amount */
      manualTotalDepositAmount?: number;
      /** Manual total deposit number */
      manualTotalDepositNumber?: number;
      /** Manual total withdraw amount */
      manualTotalWithdrawAmount?: number;
      /** Manual total withdraw number */
      manualTotalWithdrawNumber?: number;
    };
  };
  /** Customer ID for reference */
  customerId?: string;
}

/**
 * Tab change request to get customer statistics data
 */
export class CustomerDetailsStatisticsTabChangeRequest extends PGDagurRequest<CustomerStatisticsResponse, any> {
  constructor(private options: CustomerDetailsStatisticsTabChangeRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerDetailsStatisticsTabChangeRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for CustomerDetailsStatisticsTabChangeRequest');
    }

    if (!this.options.javax.tabViewId) {
      throw new Error('tabViewId is required in javax data');
    }

    if (!this.options.viewState || this.options.viewState.trim() === '') {
      throw new Error('viewState is required for CustomerDetailsStatisticsTabChangeRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.tabViewId,
      'javax.faces.partial.execute': this.options.javax.tabViewId,
      'javax.faces.partial.render': this.options.javax.tabViewId,
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      [`${this.options.javax.tabViewId}_contentLoad`]: 'true',
      [`${this.options.javax.tabViewId}_newTab`]: `${this.options.javax.tabViewId}:statistics`,
      [`${this.options.javax.tabViewId}_tabindex`]: '1',
      'form': 'form',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt627}_input`]: 'on',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt629}:0:j_idt630_collapsed`]: 'false',
      [`${this.options.javax.tabViewId}:customerNotesDt_rppDD`]: '10',
      [`${this.options.javax.tabViewId}_activeIndex`]: '1',
      'javax.faces.ViewState': this.options.viewState,
    };

    console.log('📋 CustomerDetailsStatisticsTabChangeRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerStatisticsResponse> {
    try {
      console.log('🔍 Parsing customer statistics tab change response...');

      // Initialize response structure
      const response: CustomerStatisticsResponse = {
        statistics: {
          gameStatistics: {},
        },
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the tab view data
        let tabViewUpdate = update;
        if (Array.isArray(update)) {
          tabViewUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('form:tabView') &&
              !u?.['@id']?.includes('javax.faces.ViewState'),
          );
        }

        if (!tabViewUpdate) {
          return {
            success: false,
            message: 'No tab view data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tabViewUpdate['#text'] || tabViewUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in tab view update',
          };
        }

        // Parse the HTML content and extract statistics data
        console.log('🔍 Parsing HTML content for statistics data...');
        this.parseStatisticsHTML(htmlContent, response);

        // Find ViewState update for next request
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        console.log('📊 Statistics parsing complete - Response data:', JSON.stringify({
          gameStatisticsKeys: Object.keys(response.statistics.gameStatistics),
          hasViewState: !!viewState
        }, null, 2));

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML content (fallback)
        this.parseStatisticsHTML(xml, response);

        return {
          success: true,
          data: response,
        };
      }
    } catch (error) {
      console.error('❌ Error parsing customer statistics tab change response:', error);
      return {
        success: false,
        message: `Failed to parse statistics response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Parse HTML content to extract customer statistics data
   */
  private parseStatisticsHTML(html: string, response: CustomerStatisticsResponse): void {
    try {
      console.log('🔍 Parsing statistics HTML content...');

      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Initialize statistics object
      response.statistics = {
        gameStatistics: {},
        depositWithdrawStatistics: {},
        qrReferenceStatistics: {},
        profitStatistics: {},
        casinoStatistics: {},
        manualDepositWithdrawStatistics: {},
      };

      // Define game statistics to extract with their display names and property names
      const gameStatisticsToExtract = [
        { displayName: 'Last Bet Date', propertyName: 'lastBetDate', isString: true },
        { displayName: 'Income Affiliate Code', propertyName: 'incomeAffiliateCode', isString: true },
        { displayName: 'Trackbox Aiffliate Id', propertyName: 'trackboxAffiliateId', isString: true },
        { displayName: 'Klas Poker Username', propertyName: 'klasPokerUsername', isString: true },
        { displayName: 'Total Bonus Win', propertyName: 'totalBonusWin' },
        { displayName: 'Total Assigned Bonus', propertyName: 'totalAssignedBonus' },
        { displayName: 'Total Activated Bonus Amount', propertyName: 'totalActivatedBonusAmount' },
        { displayName: 'Total Freebet', propertyName: 'totalFreebet' },
        { displayName: 'Total Used Freebet', propertyName: 'totalUsedFreebet' },
        { displayName: 'Total Unused Freebet', propertyName: 'totalUnusedFreebet' },
        { displayName: 'Total Jackpot Joinings', propertyName: 'totalJackpotJoinings' },
        { displayName: 'Total Jackpot Winning Count', propertyName: 'totalJackpotWinningCount' },
        { displayName: 'Total Jackpot Winning Amount', propertyName: 'totalJackpotWinningAmount' },
        { displayName: 'Total Payout', propertyName: 'totalPayout' },
        { displayName: 'Total Discount', propertyName: 'totalDiscount' },
        { displayName: 'Total Discount Amount', propertyName: 'totalDiscountAmount' },
        { displayName: 'Total Rakeback', propertyName: 'totalRakeback' },
        { displayName: 'Total Rakeback Amount', propertyName: 'totalRakebackAmount' },
        { displayName: 'Total Deposit of Klas Poker', propertyName: 'totalDepositOfKlasPoker' },
        { displayName: 'Total Deposit Number of Klas Poker', propertyName: 'totalDepositNumberOfKlasPoker' },
        { displayName: 'Total Withdraw of Klas Poker', propertyName: 'totalWithdrawOfKlasPoker' },
        { displayName: 'Total Withdraw Number of Klas Poker', propertyName: 'totalWithdrawNumberOfKlasPoker' },
        { displayName: 'Average Stake', propertyName: 'averageStake' },
        { displayName: 'Lifetime Turnover', propertyName: 'lifetimeTurnover' },
      ];

      // Define deposit/withdraw statistics to extract with their display names and property names
      const depositWithdrawStatisticsToExtract = [
        { displayName: 'Total Deposit Amount', propertyName: 'totalDepositAmount' },
        { displayName: 'Total Deposit Number', propertyName: 'totalDepositNumber' },
        { displayName: 'Total Withdraw Amount', propertyName: 'totalWithdrawAmount' },
        { displayName: 'Total Withdraw Number', propertyName: 'totalWithdrawNumber' },
        { displayName: 'Total Atm Outage Amount', propertyName: 'totalAtmOutageAmount' },
        { displayName: 'Total Atm Outage Number', propertyName: 'totalAtmOutageNumber' },
      ];

      // Define QR/Reference statistics to extract with their display names and property names
      const qrReferenceStatisticsToExtract = [
        { displayName: 'Total QR/Reference Deposit Amount', propertyName: 'totalQrReferenceDepositAmount' },
        { displayName: 'Total QR/Reference Deposit Number', propertyName: 'totalQrReferenceDepositNumber' },
      ];

      // Define profit statistics to extract with their display names and property names
      const profitStatisticsToExtract = [
        { displayName: 'Total Profit Amount', propertyName: 'totalProfitAmount' },
        { displayName: 'Profit %', propertyName: 'profitPercentage' },
      ];

      // Define casino statistics to extract with their display names and property names
      const casinoStatisticsToExtract = [
        { displayName: 'Casino Turnover', propertyName: 'casinoTurnover' },
        { displayName: 'Casino Payout', propertyName: 'casinoPayout' },
        { displayName: 'Casino P/L', propertyName: 'casinoPL' },
        { displayName: 'Casino P/L %', propertyName: 'casinoPLPercentage' },
      ];

      // Define manual deposit/withdraw statistics to extract with their display names and property names
      const manualDepositWithdrawStatisticsToExtract = [
        { displayName: 'Manual Total Deposit Amount', propertyName: 'manualTotalDepositAmount' },
        { displayName: 'Manual Total Deposit Number', propertyName: 'manualTotalDepositNumber' },
        { displayName: 'Manual Total Withdraw Amount', propertyName: 'manualTotalWithdrawAmount' },
        { displayName: 'Manual Total Withdraw Number', propertyName: 'manualTotalWithdrawNumber' },
      ];

      // Extract game statistics using the smart parser
      for (const stat of gameStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.gameStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted game statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ Game statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract deposit/withdraw statistics using the smart parser
      for (const stat of depositWithdrawStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.depositWithdrawStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted deposit/withdraw statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ Deposit/withdraw statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract QR/Reference statistics using the smart parser
      for (const stat of qrReferenceStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.qrReferenceStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted QR/Reference statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ QR/Reference statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract profit statistics using the smart parser
      for (const stat of profitStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.profitStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted profit statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ Profit statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract casino statistics using the smart parser
      for (const stat of casinoStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.casinoStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted casino statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ Casino statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Extract manual deposit/withdraw statistics using the smart parser
      for (const stat of manualDepositWithdrawStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.manualDepositWithdrawStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted manual deposit/withdraw statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ Manual deposit/withdraw statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Log the HTML structure for debugging (first 2000 chars)
      console.log('📋 Statistics HTML structure (first 2000 chars):', html.substring(0, 2000));

      console.log('📊 Final parsed statistics:', JSON.stringify(response.statistics, null, 2));

    } catch (error) {
      console.error('❌ Error parsing statistics HTML:', error);
      // Initialize empty statistics object on error
      response.statistics = {
        gameStatistics: {},
        depositWithdrawStatistics: {},
        qrReferenceStatistics: {},
        profitStatistics: {},
        casinoStatistics: {},
        manualDepositWithdrawStatistics: {},
      };
    }
  }

  /**
   * Extract a specific statistic value from the HTML table structure
   * Supports two patterns:
   * 1. <tr><td><label>StatisticName</label></td><td>:</td><td><label>Value</label></td></tr>
   * 2. <tr><td><label>StatisticName</label></td><td>:</td><td><span id="...tooltipWrapper"><span id="...text">Value</span></span></td></tr>
   * Handles both numeric and string values, empty labels, and decimal numbers
   */
  private extractStatisticValue(root: any, statisticName: string, isString: boolean = false): number | string | null {
    try {
      // Find all table rows
      const rows = root.querySelectorAll('tr');

      for (const row of rows) {
        // Get all cells in this row
        const cells = row.querySelectorAll('td');

        if (cells.length >= 3) {
          // Check if first cell contains the statistic name
          const firstCellLabel = cells[0].querySelector('label');
          if (firstCellLabel && firstCellLabel.text && firstCellLabel.text.trim() === statisticName) {
            // Check if second cell contains ":"
            const secondCellLabel = cells[1].querySelector('label');
            if (secondCellLabel && secondCellLabel.text && secondCellLabel.text.trim() === ':') {
              // Extract value from third cell - handle both label and span structures
              let valueText = '';

              // Try to find label first (original structure)
              const thirdCellLabel = cells[2].querySelector('label');
              if (thirdCellLabel && thirdCellLabel.text) {
                valueText = thirdCellLabel.text.trim();
              } else {
                // Try to find span with tooltip wrapper structure
                // Pattern: <span id="...tooltipWrapper"><span id="...text">VALUE</span></span>
                const tooltipWrapper = cells[2].querySelector('span[id*="tooltipWrapper"]');
                if (tooltipWrapper) {
                  const textSpan = tooltipWrapper.querySelector('span[id*="text"]');
                  if (textSpan && textSpan.text) {
                    valueText = textSpan.text.trim();
                  }
                } else {
                  // Fallback: try any span in the third cell
                  const anySpan = cells[2].querySelector('span');
                  if (anySpan && anySpan.text) {
                    valueText = anySpan.text.trim();
                  }
                }
              }

              // Handle empty values (null values)
              if (valueText === '') {
                console.log(`🔍 Found ${statisticName}: empty (null)`);
                return null;
              }

              // Handle string values
              if (isString) {
                console.log(`🔍 Found ${statisticName}: "${valueText}"`);
                return valueText;
              }

              // Handle numeric values (including decimals)
              // Remove commas from numbers like "234,300.00" before parsing
              const cleanedValueText = valueText.replace(/,/g, '');
              const numericValue = parseFloat(cleanedValueText);
              if (!isNaN(numericValue)) {
                console.log(`🔍 Found ${statisticName}: ${numericValue} (original: "${valueText}")`);
                return numericValue;
              } else {
                console.log(`⚠️ Could not parse numeric value for ${statisticName}: "${valueText}"`);
                // For numeric fields, if we can't parse, return the string value as fallback
                console.log(`🔍 Returning string value as fallback: "${valueText}"`);
                return valueText;
              }
            }
          }
        }
      }

      console.log(`⚠️ ${statisticName} not found in table structure`);
      return null;

    } catch (error) {
      console.error(`❌ Error extracting ${statisticName}:`, error);
      return null;
    }
  }
}
