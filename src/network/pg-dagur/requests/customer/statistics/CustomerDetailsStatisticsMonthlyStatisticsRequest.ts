import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

/**
 * Options for the customer details statistics monthly statistics request
 * @interface CustomerDetailsStatisticsMonthlyStatisticsRequestOptions
 */
export interface CustomerDetailsStatisticsMonthlyStatisticsRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** JSF element data from the preload request */
  javax: CustomerDetailsPreloadRequestResponse;
  /** ViewState from the preload request */
  viewState: string;
}

/**
 * Response structure for customer monthly statistics data
 * @interface CustomerMonthlyStatisticsResponse
 */
export interface CustomerMonthlyStatisticsResponse {
  /** Monthly statistics data extracted from the response */
  statistics: {
    /** Monthly statistics from the monthly statistics section */
    monthlyStatistics: {
      /** Total deposit amount */
      totalDepositAmount?: number;
      /** Total deposit number */
      totalDepositNumber?: number;
      /** Total withdraw amount */
      totalWithdrawAmount?: number;
      /** Total withdraw number */
      totalWithdrawNumber?: number;
      /** Max deposit amount */
      maxDepositAmount?: number;
      /** Max withdraw amount */
      maxWithdrawAmount?: number;
      /** Manual total deposit amount */
      manualTotalDepositAmount?: number;
      /** Manual total deposit number */
      manualTotalDepositNumber?: number;
      /** Manual total withdraw amount */
      manualTotalWithdrawAmount?: number;
      /** Total QR/Reference deposit amount */
      totalQrReferenceDepositAmount?: number;
      /** Total QR/Reference deposit number */
      totalQrReferenceDepositNumber?: number;
      /** Manual total withdraw number */
      manualTotalWithdrawNumber?: number;
      /** Total bonus win */
      totalBonusWin?: number;
      /** Total assigned bonus */
      totalAssignedBonus?: number;
      /** Total activated bonus amount */
      totalActivatedBonusAmount?: number;
      /** Total discount */
      totalDiscount?: number;
      /** Total discount amount */
      totalDiscountAmount?: number;
      /** Total rakeback */
      totalRakeback?: number;
      /** Total rakeback amount */
      totalRakebackAmount?: number;
      /** Total ATM outage amount */
      totalAtmOutageAmount?: number;
      /** Total ATM outage number */
      totalAtmOutageNumber?: number;
      /** Last transaction date */
      lastTransactionDate?: string;
      /** First deposit date */
      firstDepositDate?: string;
      /** First withdrawal request date */
      firstWithdrawalRequestDate?: string;
      /** Last withdrawal request date */
      lastWithdrawalRequestDate?: string;
      /** Last withdrawal process date */
      lastWithdrawalProcessDate?: string;
      /** Last deposit date */
      lastDepositDate?: string;
    };
  };
  /** Customer ID for reference */
  customerId?: string;
}

/**
 * Request to load monthly statistics data for a customer
 */
export class CustomerDetailsStatisticsMonthlyStatisticsRequest extends PGDagurRequest<CustomerMonthlyStatisticsResponse, any> {
  constructor(private options: CustomerDetailsStatisticsMonthlyStatisticsRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerDetailsStatisticsMonthlyStatisticsRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for CustomerDetailsStatisticsMonthlyStatisticsRequest');
    }

    if (!this.options.javax.tabViewId) {
      throw new Error('tabViewId is required in javax data');
    }

    if (!this.options.viewState || this.options.viewState.trim() === '') {
      throw new Error('viewState is required for CustomerDetailsStatisticsMonthlyStatisticsRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `${this.options.javax.tabViewId}:loadMonthlyStat`,
      'javax.faces.partial.execute': `${this.options.javax.tabViewId}:loadMonthlyStat`,
      'javax.faces.partial.render': `${this.options.javax.tabViewId}:lastMonthlyStatistics`,
      [`${this.options.javax.tabViewId}:loadMonthlyStat`]: `${this.options.javax.tabViewId}:loadMonthlyStat`,
      'form': 'form',
      [`${this.options.javax.tabViewId}:reasonId_focus`]: '',
      [`${this.options.javax.tabViewId}:reasonId_input`]: '',
      [`${this.options.javax.tabViewId}:group_focus`]: '',
      [`${this.options.javax.tabViewId}:group_input`]: '-1',
      [`${this.options.javax.tabViewId}_activeIndex`]: '1',
      'javax.faces.ViewState': this.options.viewState,
    };

    console.log('📋 CustomerDetailsStatisticsMonthlyStatisticsRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerMonthlyStatisticsResponse> {
    try {
      console.log('🔍 Parsing customer monthly statistics response...');

      // Initialize response structure
      const response: CustomerMonthlyStatisticsResponse = {
        statistics: {
          monthlyStatistics: {},
        },
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the monthly statistics data
        let monthlyStatsUpdate = update;
        if (Array.isArray(update)) {
          monthlyStatsUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('lastMonthlyStatistics') &&
              !u?.['@id']?.includes('javax.faces.ViewState'),
          );
        }

        if (!monthlyStatsUpdate) {
          return {
            success: false,
            message: 'No monthly statistics data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = monthlyStatsUpdate['#text'] || monthlyStatsUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in monthly statistics update',
          };
        }

        // Parse the HTML content and extract monthly statistics data
        console.log('🔍 Parsing HTML content for monthly statistics data...');
        this.parseMonthlyStatisticsHTML(htmlContent, response);

        // Find ViewState update for next request
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        console.log('📊 Monthly statistics parsing complete - Response data:', JSON.stringify({
          monthlyStatisticsKeys: Object.keys(response.statistics.monthlyStatistics),
          hasViewState: !!viewState
        }, null, 2));

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML content (fallback)
        this.parseMonthlyStatisticsHTML(xml, response);

        return {
          success: true,
          data: response,
        };
      }
    } catch (error) {
      console.error('❌ Error parsing customer monthly statistics response:', error);
      return {
        success: false,
        message: `Failed to parse monthly statistics response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Parse HTML content to extract customer monthly statistics data
   */
  private parseMonthlyStatisticsHTML(html: string, response: CustomerMonthlyStatisticsResponse): void {
    try {
      console.log('🔍 Parsing monthly statistics HTML content...');

      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Initialize statistics object
      response.statistics = {
        monthlyStatistics: {},
      };

      // Define monthly statistics to extract with their display names and property names
      const monthlyStatisticsToExtract = [
        { displayName: 'Total Deposit Amount', propertyName: 'totalDepositAmount' },
        { displayName: 'Total Deposit Number', propertyName: 'totalDepositNumber' },
        { displayName: 'Total Withdraw Amount', propertyName: 'totalWithdrawAmount' },
        { displayName: 'Total Withdraw Number', propertyName: 'totalWithdrawNumber' },
        { displayName: 'Max Deposit Amount', propertyName: 'maxDepositAmount' },
        { displayName: 'Max Withdraw Amount', propertyName: 'maxWithdrawAmount' },
        { displayName: 'Manual Total Deposit Amount', propertyName: 'manualTotalDepositAmount' },
        { displayName: 'Manual Total Deposit Number', propertyName: 'manualTotalDepositNumber' },
        { displayName: 'Manual Total Withdraw Amount', propertyName: 'manualTotalWithdrawAmount' },
        { displayName: 'Total QR/Reference Deposit Amount', propertyName: 'totalQrReferenceDepositAmount' },
        { displayName: 'Total QR/Reference Deposit Number', propertyName: 'totalQrReferenceDepositNumber' },
        { displayName: 'Manual Total Withdraw Number', propertyName: 'manualTotalWithdrawNumber' },
        { displayName: 'Total Bonus Win', propertyName: 'totalBonusWin' },
        { displayName: 'Total Assigned Bonus', propertyName: 'totalAssignedBonus' },
        { displayName: 'Total Activated Bonus Amount', propertyName: 'totalActivatedBonusAmount' },
        { displayName: 'Total Discount', propertyName: 'totalDiscount' },
        { displayName: 'Total Discount Amount', propertyName: 'totalDiscountAmount' },
        { displayName: 'Total Rakeback', propertyName: 'totalRakeback' },
        { displayName: 'Total Rakeback Amount', propertyName: 'totalRakebackAmount' },
        { displayName: 'Total Atm Outage Amount', propertyName: 'totalAtmOutageAmount' },
        { displayName: 'Total Atm Outage Number', propertyName: 'totalAtmOutageNumber' },
        { displayName: 'Last Transaction Date', propertyName: 'lastTransactionDate', isString: true },
        { displayName: 'First Deposit Date', propertyName: 'firstDepositDate', isString: true },
        { displayName: 'First Withdrawal Request Date', propertyName: 'firstWithdrawalRequestDate', isString: true },
        { displayName: 'Last Withdrawal Request Date', propertyName: 'lastWithdrawalRequestDate', isString: true },
        { displayName: 'Last Withdrawal Process Date', propertyName: 'lastWithdrawalProcessDate', isString: true },
        { displayName: 'Last Deposit Date', propertyName: 'lastDepositDate', isString: true },
      ];

      // Extract all monthly statistics using the enhanced parser
      for (const stat of monthlyStatisticsToExtract) {
        const value = this.extractMonthlyStatisticValue(root, stat.displayName, stat.isString);
        // Always include the field in response, even if null
        (response.statistics.monthlyStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Log the HTML structure for debugging (first 2000 chars)
      console.log('📋 Monthly statistics HTML structure (first 2000 chars):', html.substring(0, 2000));

      console.log('📊 Final parsed monthly statistics:', JSON.stringify(response.statistics, null, 2));

    } catch (error) {
      console.error('❌ Error parsing monthly statistics HTML:', error);
      // Initialize empty statistics object on error
      response.statistics = {
        monthlyStatistics: {},
      };
    }
  }

  /**
   * Extract a specific monthly statistic value from the HTML table structure
   * Supports multiple patterns:
   * 1. <tr><td><label>StatisticName</label></td><td><label>:</label></td><td><span id="...tooltipWrapper"><span id="...text">Value</span></span></td></tr>
   * 2. <tr><td><label>StatisticName</label></td><td><label>:</label></td><td>Value</td></tr>
   * 3. <tr><td><label>StatisticName</label></td><td><label>:</label></td><td></td></tr> (empty value)
   * Handles both numeric and string values, empty cells, and decimal numbers
   */
  private extractMonthlyStatisticValue(root: any, statisticName: string, isString: boolean = false): number | string | null {
    try {
      // Find all table rows
      const rows = root.querySelectorAll('tr');

      for (const row of rows) {
        // Get all cells in this row
        const cells = row.querySelectorAll('td');

        if (cells.length >= 3) {
          // Check if first cell contains the statistic name
          const firstCellLabel = cells[0].querySelector('label');
          if (firstCellLabel && firstCellLabel.text && firstCellLabel.text.trim() === statisticName) {
            // Check if second cell contains ":"
            const secondCellLabel = cells[1].querySelector('label');
            if (secondCellLabel && secondCellLabel.text && secondCellLabel.text.trim() === ':') {
              // Extract value from third cell - handle multiple structures
              let valueText = '';

              // Try to find span with tooltip wrapper structure first
              // Pattern: <span id="...tooltipWrapper"><span id="...text">VALUE</span></span>
              const tooltipWrapper = cells[2].querySelector('span[id*="tooltipWrapper"]');
              if (tooltipWrapper) {
                const textSpan = tooltipWrapper.querySelector('span[id*="text"]');
                if (textSpan && textSpan.text) {
                  valueText = textSpan.text.trim();
                }
              } else {
                // Try to find any span in the third cell
                const anySpan = cells[2].querySelector('span');
                if (anySpan && anySpan.text) {
                  valueText = anySpan.text.trim();
                } else {
                  // Try to find label in the third cell
                  const thirdCellLabel = cells[2].querySelector('label');
                  if (thirdCellLabel && thirdCellLabel.text) {
                    valueText = thirdCellLabel.text.trim();
                  } else {
                    // Fallback: get direct text content from the cell
                    const cellText = cells[2].text;
                    if (cellText) {
                      valueText = cellText.trim();
                    }
                  }
                }
              }

              // Handle empty values (null values)
              if (valueText === '') {
                console.log(`🔍 Found ${statisticName}: empty (null)`);
                return null;
              }

              // Handle string values (dates, etc.)
              if (isString) {
                console.log(`🔍 Found ${statisticName}: "${valueText}"`);
                return valueText;
              }

              // Handle numeric values (including decimals)
              // Remove commas from numbers like "234,300.00" before parsing
              const cleanedValueText = valueText.replace(/,/g, '');
              const numericValue = parseFloat(cleanedValueText);
              if (!isNaN(numericValue)) {
                console.log(`🔍 Found ${statisticName}: ${numericValue} (original: "${valueText}")`);
                return numericValue;
              } else {
                console.log(`⚠️ Could not parse numeric value for ${statisticName}: "${valueText}"`);
                // For monthly statistics, if we can't parse as number, return the string value as fallback
                console.log(`🔍 Returning string value as fallback: "${valueText}"`);
                return valueText;
              }
            }
          }
        }
      }

      console.log(`⚠️ ${statisticName} not found in table structure`);
      return null;

    } catch (error) {
      console.error(`❌ Error extracting ${statisticName}:`, error);
      return null;
    }
  }
}
