import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

/**
 * Options for the customer details statistics lifetime turnover request
 * @interface CustomerDetailsStatisticsLifetimeTurnoverRequestOptions
 */
export interface CustomerDetailsStatisticsLifetimeTurnoverRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** JSF element data from the preload request */
  javax: CustomerDetailsPreloadRequestResponse;
  /** ViewState from the preload request */
  viewState: string;
}

/**
 * Response structure for customer lifetime turnover statistics data
 * @interface CustomerLifetimeTurnoverStatisticsResponse
 */
export interface CustomerLifetimeTurnoverStatisticsResponse {
  /** Statistics data extracted from the response */
  statistics: {
    /** Game statistics from the lifetime turnover request */
    gameStatistics: {
      /** Lifetime turnover amount */
      lifetimeTurnover?: number;
      /** Total deposit of Klas Poker */
      totalDepositOfKlasPoker?: number;
      /** Total deposit number of Klas Poker */
      totalDepositNumberOfKlasPoker?: number;
      /** Total withdraw of Klas Poker */
      totalWithdrawOfKlasPoker?: number;
      /** Total withdraw number of Klas Poker */
      totalWithdrawNumberOfKlasPoker?: number;
    };
  };
  /** Customer ID for reference */
  customerId?: string;
}

/**
 * Request to load lifetime turnover statistics data for a customer
 */
export class CustomerDetailsStatisticsLifetimeTurnoverRequest extends PGDagurRequest<CustomerLifetimeTurnoverStatisticsResponse, any> {
  constructor(private options: CustomerDetailsStatisticsLifetimeTurnoverRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerDetailsStatisticsLifetimeTurnoverRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for CustomerDetailsStatisticsLifetimeTurnoverRequest');
    }

    if (!this.options.javax.tabViewId) {
      throw new Error('tabViewId is required in javax data');
    }

    if (!this.options.viewState || this.options.viewState.trim() === '') {
      throw new Error('viewState is required for CustomerDetailsStatisticsLifetimeTurnoverRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `${this.options.javax.tabViewId}:loadLifetimeTurnover`,
      'javax.faces.partial.execute': `${this.options.javax.tabViewId}:loadLifetimeTurnover`,
      'javax.faces.partial.render': `${this.options.javax.tabViewId}:gameStatistics`,
      [`${this.options.javax.tabViewId}:loadLifetimeTurnover`]: `${this.options.javax.tabViewId}:loadLifetimeTurnover`,
      'form': 'form',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt653}_input`]: 'on',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt655}:0:j_idt656_collapsed`]: 'false',
      [`${this.options.javax.tabViewId}:customerNotesDt_rppDD`]: '10',
      [`${this.options.javax.tabViewId}:reasonId_focus`]: '',
      [`${this.options.javax.tabViewId}:reasonId_input`]: '',
      [`${this.options.javax.tabViewId}_activeIndex`]: '1',
      'javax.faces.ViewState': this.options.viewState,
    };

    console.log('📋 CustomerDetailsStatisticsLifetimeTurnoverRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerLifetimeTurnoverStatisticsResponse> {
    try {
      console.log('🔍 Parsing customer lifetime turnover statistics response...');

      // Initialize response structure
      const response: CustomerLifetimeTurnoverStatisticsResponse = {
        statistics: {
          gameStatistics: {},
        },
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the game statistics data
        let gameStatisticsUpdate = update;
        if (Array.isArray(update)) {
          gameStatisticsUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('gameStatistics') &&
              !u?.['@id']?.includes('javax.faces.ViewState'),
          );
        }

        if (!gameStatisticsUpdate) {
          return {
            success: false,
            message: 'No game statistics data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = gameStatisticsUpdate['#text'] || gameStatisticsUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in game statistics update',
          };
        }

        // Parse the HTML content and extract lifetime turnover statistics data
        console.log('🔍 Parsing HTML content for lifetime turnover statistics data...');
        this.parseLifetimeTurnoverStatisticsHTML(htmlContent, response);

        // Find ViewState update for next request
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        console.log('📊 Lifetime turnover statistics parsing complete - Response data:', JSON.stringify({
          gameStatisticsKeys: Object.keys(response.statistics.gameStatistics),
          hasViewState: !!viewState
        }, null, 2));

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML content (fallback)
        this.parseLifetimeTurnoverStatisticsHTML(xml, response);

        return {
          success: true,
          data: response,
        };
      }
    } catch (error) {
      console.error('❌ Error parsing customer lifetime turnover statistics response:', error);
      return {
        success: false,
        message: `Failed to parse lifetime turnover statistics response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Parse HTML content to extract customer lifetime turnover statistics data
   */
  private parseLifetimeTurnoverStatisticsHTML(html: string, response: CustomerLifetimeTurnoverStatisticsResponse): void {
    try {
      console.log('🔍 Parsing lifetime turnover statistics HTML content...');

      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Initialize statistics object
      response.statistics = {
        gameStatistics: {},
      };

      // Define lifetime turnover statistics to extract with their display names and property names
      const lifetimeTurnoverStatisticsToExtract = [
        { displayName: 'Lifetime Turnover', propertyName: 'lifetimeTurnover' },
        { displayName: 'Total Deposit of Klas Poker', propertyName: 'totalDepositOfKlasPoker' },
        { displayName: 'Total Deposit Number of Klas Poker', propertyName: 'totalDepositNumberOfKlasPoker' },
        { displayName: 'Total Withdraw of Klas Poker', propertyName: 'totalWithdrawOfKlasPoker' },
        { displayName: 'Total Withdraw Number of Klas Poker', propertyName: 'totalWithdrawNumberOfKlasPoker' },
      ];

      // Extract lifetime turnover statistics using the smart parser
      for (const stat of lifetimeTurnoverStatisticsToExtract) {
        const value = this.extractStatisticValue(root, stat.displayName, false);
        // Always include the field in response, even if null
        (response.statistics.gameStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted lifetime turnover statistic ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ Lifetime turnover statistic ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Log the HTML structure for debugging (first 2000 chars)
      console.log('📋 Lifetime turnover statistics HTML structure (first 2000 chars):', html.substring(0, 2000));

      console.log('📊 Final parsed lifetime turnover statistics:', JSON.stringify(response.statistics, null, 2));

    } catch (error) {
      console.error('❌ Error parsing lifetime turnover statistics HTML:', error);
      // Initialize empty statistics object on error
      response.statistics = {
        gameStatistics: {},
      };
    }
  }

  /**
   * Extract a specific statistic value from the HTML table structure
   * Supports two patterns:
   * 1. <tr><td><label>StatisticName</label></td><td>:</td><td><label>Value</label></td></tr>
   * 2. <tr><td><label>StatisticName</label></td><td>:</td><td><span id="...tooltipWrapper"><span id="...text">Value</span></span></td></tr>
   * Handles both numeric and string values, empty labels, and decimal numbers
   */
  private extractStatisticValue(root: any, statisticName: string, isString: boolean = false): number | string | null {
    try {
      // Find all table rows
      const rows = root.querySelectorAll('tr');

      for (const row of rows) {
        // Get all cells in this row
        const cells = row.querySelectorAll('td');

        if (cells.length >= 3) {
          // Check if first cell contains the statistic name
          const firstCellLabel = cells[0].querySelector('label');
          if (firstCellLabel && firstCellLabel.text && firstCellLabel.text.trim() === statisticName) {
            // Check if second cell contains ":"
            const secondCellLabel = cells[1].querySelector('label');
            if (secondCellLabel && secondCellLabel.text && secondCellLabel.text.trim() === ':') {
              // Extract value from third cell - handle both label and span structures
              let valueText = '';

              // Try to find label first (original structure)
              const thirdCellLabel = cells[2].querySelector('label');
              if (thirdCellLabel && thirdCellLabel.text) {
                valueText = thirdCellLabel.text.trim();
              } else {
                // Try to find span with tooltip wrapper structure
                // Pattern: <span id="...tooltipWrapper"><span id="...text">VALUE</span></span>
                const tooltipWrapper = cells[2].querySelector('span[id*="tooltipWrapper"]');
                if (tooltipWrapper) {
                  const textSpan = tooltipWrapper.querySelector('span[id*="text"]');
                  if (textSpan && textSpan.text) {
                    valueText = textSpan.text.trim();
                  }
                } else {
                  // Fallback: try any span in the third cell
                  const anySpan = cells[2].querySelector('span');
                  if (anySpan && anySpan.text) {
                    valueText = anySpan.text.trim();
                  }
                }
              }

              // Handle empty values (null values)
              if (valueText === '') {
                console.log(`🔍 Found ${statisticName}: empty (null)`);
                return null;
              }

              // Handle string values
              if (isString) {
                console.log(`🔍 Found ${statisticName}: "${valueText}"`);
                return valueText;
              }

              // Handle numeric values (including decimals)
              // Remove commas from numbers like "234,300.00" before parsing
              const cleanedValueText = valueText.replace(/,/g, '');
              const numericValue = parseFloat(cleanedValueText);
              if (!isNaN(numericValue)) {
                console.log(`🔍 Found ${statisticName}: ${numericValue} (original: "${valueText}")`);
                return numericValue;
              } else {
                console.log(`⚠️ Could not parse numeric value for ${statisticName}: "${valueText}"`);
                // For numeric fields, if we can't parse, return the string value as fallback
                console.log(`🔍 Returning string value as fallback: "${valueText}"`);
                return valueText;
              }
            }
          }
        }
      }

      console.log(`⚠️ ${statisticName} not found in table structure using standard method`);

      // Fallback: Try a more aggressive search for the specific pattern
      return this.extractStatisticValueFallback(root, statisticName, isString);

    } catch (error) {
      console.error(`❌ Error extracting ${statisticName}:`, error);
      return null;
    }
  }

  /**
   * Fallback method to extract statistic values using a more aggressive search
   * This handles cases where the standard parser might miss certain structures
   */
  private extractStatisticValueFallback(root: any, statisticName: string, isString: boolean = false): number | string | null {
    try {
      // Find all labels that contain the statistic name
      const allLabels = root.querySelectorAll('label');

      for (let i = 0; i < allLabels.length; i++) {
        const label = allLabels[i];
        const labelText = label.text?.trim() || '';

        if (labelText === statisticName) {
          // Find the parent row
          let currentElement = label.parentNode;
          while (currentElement && currentElement.tagName !== 'TR') {
            currentElement = currentElement.parentNode;
          }

          if (currentElement && currentElement.tagName === 'TR') {
            const cells = currentElement.querySelectorAll('td');

            if (cells.length >= 3) {
              // Look for value in the third cell
              const thirdCell = cells[2];
              let valueText = '';

              // Try tooltip wrapper pattern first
              const tooltipWrapper = thirdCell.querySelector('span[id*="tooltipWrapper"]');
              if (tooltipWrapper) {
                const textSpan = tooltipWrapper.querySelector('span[id*="text"]');
                if (textSpan && textSpan.text) {
                  valueText = textSpan.text.trim();
                }
              }

              // Try any span if tooltip wrapper didn't work
              if (!valueText) {
                const anySpan = thirdCell.querySelector('span');
                if (anySpan && anySpan.text) {
                  valueText = anySpan.text.trim();
                }
              }

              // Try label as last resort
              if (!valueText) {
                const thirdCellLabel = thirdCell.querySelector('label');
                if (thirdCellLabel && thirdCellLabel.text) {
                  valueText = thirdCellLabel.text.trim();
                }
              }

              if (valueText) {
                // Handle empty values (null values)
                if (valueText === '') {
                  return null;
                }

                // Handle string values
                if (isString) {
                  return valueText;
                }

                // Handle numeric values (including decimals)
                // Remove commas from numbers like "234,300.00" before parsing
                const cleanedValueText = valueText.replace(/,/g, '');
                const numericValue = parseFloat(cleanedValueText);
                if (!isNaN(numericValue)) {
                  return numericValue;
                } else {
                  // For numeric fields, if we can't parse, return the string value as fallback
                  return valueText;
                }
              }
            }
          }
        }
      }

      return null;

    } catch (error) {
      console.error(`❌ Error in fallback extraction for ${statisticName}:`, error);
      return null;
    }
  }
}
