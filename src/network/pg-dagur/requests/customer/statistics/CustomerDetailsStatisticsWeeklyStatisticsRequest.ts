import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

/**
 * Options for the customer details statistics weekly statistics request
 * @interface CustomerDetailsStatisticsWeeklyStatisticsRequestOptions
 */
export interface CustomerDetailsStatisticsWeeklyStatisticsRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** JSF element data from the preload request */
  javax: CustomerDetailsPreloadRequestResponse;
  /** ViewState from the preload request */
  viewState: string;
}

/**
 * Response structure for customer weekly statistics data
 * @interface CustomerWeeklyStatisticsResponse
 */
export interface CustomerWeeklyStatisticsResponse {
  /** Weekly statistics data extracted from the response */
  statistics: {
    /** Weekly statistics from the weekly statistics section */
    weeklyStatistics: {
      /** Total deposit amount */
      totalDepositAmount?: number;
      /** Total deposit number */
      totalDepositNumber?: number;
      /** Total withdraw amount */
      totalWithdrawAmount?: number;
      /** Total withdraw number */
      totalWithdrawNumber?: number;
      /** Total bonus win */
      totalBonusWin?: number;
      /** Total assigned bonus */
      totalAssignedBonus?: number;
      /** Total activated bonus amount */
      totalActivatedBonusAmount?: number;
      /** Total deposit of Klas Poker */
      totalDepositOfKlasPoker?: number;
      /** Total deposit number of Klas Poker */
      totalDepositNumberOfKlasPoker?: number;
      /** Total withdraw of Klas Poker */
      totalWithdrawOfKlasPoker?: number;
      /** Total withdraw number of Klas Poker */
      totalWithdrawNumberOfKlasPoker?: number;
    };
  };
  /** Customer ID for reference */
  customerId?: string;
}

/**
 * Request to load weekly statistics data for a customer
 */
export class CustomerDetailsStatisticsWeeklyStatisticsRequest extends PGDagurRequest<CustomerWeeklyStatisticsResponse, any> {
  constructor(private options: CustomerDetailsStatisticsWeeklyStatisticsRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerDetailsStatisticsWeeklyStatisticsRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for CustomerDetailsStatisticsWeeklyStatisticsRequest');
    }

    if (!this.options.javax.tabViewId) {
      throw new Error('tabViewId is required in javax data');
    }

    if (!this.options.viewState || this.options.viewState.trim() === '') {
      throw new Error('viewState is required for CustomerDetailsStatisticsWeeklyStatisticsRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `${this.options.javax.tabViewId}:loadWeeklyStat`,
      'javax.faces.partial.execute': `${this.options.javax.tabViewId}:loadWeeklyStat`,
      'javax.faces.partial.render': `${this.options.javax.tabViewId}:lastWeeklyStatistics`,
      [`${this.options.javax.tabViewId}:loadWeeklyStat`]: `${this.options.javax.tabViewId}:loadWeeklyStat`,
      'form': 'form',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt627}_input`]: 'on',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt629}:0:j_idt630_collapsed`]: 'false',
      [`${this.options.javax.tabViewId}:customerNotesDt_rppDD`]: '10',
      [`${this.options.javax.tabViewId}:reasonId_focus`]: '',
      [`${this.options.javax.tabViewId}:reasonId_input`]: '',
      [`${this.options.javax.tabViewId}:group_focus`]: '',
      [`${this.options.javax.tabViewId}:group_input`]: '0',
      [`${this.options.javax.tabViewId}_activeIndex`]: '1',
      'javax.faces.ViewState': this.options.viewState,
    };

    console.log('📋 CustomerDetailsStatisticsWeeklyStatisticsRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerWeeklyStatisticsResponse> {
    try {
      console.log('🔍 Parsing customer weekly statistics response...');

      // Initialize response structure
      const response: CustomerWeeklyStatisticsResponse = {
        statistics: {
          weeklyStatistics: {},
        },
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the weekly statistics data
        let weeklyStatsUpdate = update;
        if (Array.isArray(update)) {
          weeklyStatsUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('lastWeeklyStatistics') &&
              !u?.['@id']?.includes('javax.faces.ViewState'),
          );
        }

        if (!weeklyStatsUpdate) {
          return {
            success: false,
            message: 'No weekly statistics data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = weeklyStatsUpdate['#text'] || weeklyStatsUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in weekly statistics update',
          };
        }

        // Parse the HTML content and extract weekly statistics data
        console.log('🔍 Parsing HTML content for weekly statistics data...');
        this.parseWeeklyStatisticsHTML(htmlContent, response);

        // Find ViewState update for next request
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        console.log('📊 Weekly statistics parsing complete - Response data:', JSON.stringify({
          weeklyStatisticsKeys: Object.keys(response.statistics.weeklyStatistics),
          hasViewState: !!viewState
        }, null, 2));

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML content (fallback)
        this.parseWeeklyStatisticsHTML(xml, response);

        return {
          success: true,
          data: response,
        };
      }
    } catch (error) {
      console.error('❌ Error parsing customer weekly statistics response:', error);
      return {
        success: false,
        message: `Failed to parse weekly statistics response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Parse HTML content to extract customer weekly statistics data
   */
  private parseWeeklyStatisticsHTML(html: string, response: CustomerWeeklyStatisticsResponse): void {
    try {
      console.log('🔍 Parsing weekly statistics HTML content...');

      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Initialize statistics object
      response.statistics = {
        weeklyStatistics: {},
      };

      // Define weekly statistics to extract with their display names and property names
      const weeklyStatisticsToExtract = [
        { displayName: 'Total Deposit Amount', propertyName: 'totalDepositAmount' },
        { displayName: 'Total Deposit Number', propertyName: 'totalDepositNumber' },
        { displayName: 'Total Withdraw Amount', propertyName: 'totalWithdrawAmount' },
        { displayName: 'Total Withdraw Number', propertyName: 'totalWithdrawNumber' },
        { displayName: 'Total Bonus Win', propertyName: 'totalBonusWin' },
        { displayName: 'Total Assigned Bonus', propertyName: 'totalAssignedBonus' },
        { displayName: 'Total Activated Bonus Amount', propertyName: 'totalActivatedBonusAmount' },
        { displayName: 'Total Deposit of Klas Poker', propertyName: 'totalDepositOfKlasPoker' },
        { displayName: 'Total Deposit Number of Klas Poker', propertyName: 'totalDepositNumberOfKlasPoker' },
        { displayName: 'Total Withdraw of Klas Poker', propertyName: 'totalWithdrawOfKlasPoker' },
        { displayName: 'Total Withdraw Number of Klas Poker', propertyName: 'totalWithdrawNumberOfKlasPoker' },
      ];

      // Extract all weekly statistics using the enhanced parser
      for (const stat of weeklyStatisticsToExtract) {
        const value = this.extractWeeklyStatisticValue(root, stat.displayName);
        // Always include the field in response, even if null
        (response.statistics.weeklyStatistics as any)[stat.propertyName] = value;
        if (value !== null) {
          console.log(`✅ Extracted ${stat.displayName}:`, value);
        } else {
          console.log(`⚠️ ${stat.displayName} not found in HTML (setting to null)`);
        }
      }

      // Log the HTML structure for debugging (first 2000 chars)
      console.log('📋 Weekly statistics HTML structure (first 2000 chars):', html.substring(0, 2000));

      console.log('📊 Final parsed weekly statistics:', JSON.stringify(response.statistics, null, 2));

    } catch (error) {
      console.error('❌ Error parsing weekly statistics HTML:', error);
      // Initialize empty statistics object on error
      response.statistics = {
        weeklyStatistics: {},
      };
    }
  }

  /**
   * Extract a specific weekly statistic value from the HTML table structure
   * Supports multiple patterns:
   * 1. <tr><td><label>StatisticName</label></td><td><label>:</label></td><td><span id="...tooltipWrapper"><span id="...text">Value</span></span></td></tr>
   * 2. <tr><td><label>StatisticName</label></td><td><label>:</label></td><td>Value</td></tr>
   * 3. <tr><td><label>StatisticName</label></td><td><label>:</label></td><td></td></tr> (empty value)
   * Handles both numeric and string values, empty cells, and decimal numbers
   */
  private extractWeeklyStatisticValue(root: any, statisticName: string): number | string | null {
    try {
      // Find all table rows
      const rows = root.querySelectorAll('tr');

      for (const row of rows) {
        // Get all cells in this row
        const cells = row.querySelectorAll('td');

        if (cells.length >= 3) {
          // Check if first cell contains the statistic name
          const firstCellLabel = cells[0].querySelector('label');
          if (firstCellLabel && firstCellLabel.text && firstCellLabel.text.trim() === statisticName) {
            // Check if second cell contains ":"
            const secondCellLabel = cells[1].querySelector('label');
            if (secondCellLabel && secondCellLabel.text && secondCellLabel.text.trim() === ':') {
              // Extract value from third cell - handle multiple structures
              let valueText = '';

              // Try to find span with tooltip wrapper structure first
              // Pattern: <span id="...tooltipWrapper"><span id="...text">VALUE</span></span>
              const tooltipWrapper = cells[2].querySelector('span[id*="tooltipWrapper"]');
              if (tooltipWrapper) {
                const textSpan = tooltipWrapper.querySelector('span[id*="text"]');
                if (textSpan && textSpan.text) {
                  valueText = textSpan.text.trim();
                }
              } else {
                // Try to find any span in the third cell
                const anySpan = cells[2].querySelector('span');
                if (anySpan && anySpan.text) {
                  valueText = anySpan.text.trim();
                } else {
                  // Try to find label in the third cell
                  const thirdCellLabel = cells[2].querySelector('label');
                  if (thirdCellLabel && thirdCellLabel.text) {
                    valueText = thirdCellLabel.text.trim();
                  } else {
                    // Fallback: get direct text content from the cell
                    const cellText = cells[2].text;
                    if (cellText) {
                      valueText = cellText.trim();
                    }
                  }
                }
              }

              // Handle empty values (null values)
              if (valueText === '') {
                console.log(`🔍 Found ${statisticName}: empty (null)`);
                return null;
              }

              // Handle numeric values (including decimals)
              // Remove commas from numbers like "234,300.00" before parsing
              const cleanedValueText = valueText.replace(/,/g, '');
              const numericValue = parseFloat(cleanedValueText);
              if (!isNaN(numericValue)) {
                console.log(`🔍 Found ${statisticName}: ${numericValue} (original: "${valueText}")`);
                return numericValue;
              } else {
                console.log(`⚠️ Could not parse numeric value for ${statisticName}: "${valueText}"`);
                // For weekly statistics, if we can't parse as number, return the string value as fallback
                console.log(`🔍 Returning string value as fallback: "${valueText}"`);
                return valueText;
              }
            }
          }
        }
      }

      console.log(`⚠️ ${statisticName} not found in table structure`);
      return null;

    } catch (error) {
      console.error(`❌ Error extracting ${statisticName}:`, error);
      return null;
    }
  }
}
