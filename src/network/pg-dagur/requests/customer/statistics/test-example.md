# Test Example for Customer Statistics

## Sample HTML Structure

The following HTML structure should be parsed correctly by the `extractStatisticValue` method:

```html
<table>
  <tr>
    <td><label id="form:tabView:j_idt842" class="ui-outputlabel ui-widget">Total Freebet</label></td>
    <td><label id="form:tabView:j_idt843" class="ui-outputlabel ui-widget">:</label></td>
    <td><label id="form:tabView:j_idt844" class="ui-outputlabel ui-widget">9</label></td>
  </tr>
  <tr>
    <td><label id="form:tabView:j_idt845" class="ui-outputlabel ui-widget">Total Deposits</label></td>
    <td><label id="form:tabView:j_idt846" class="ui-outputlabel ui-widget">:</label></td>
    <td><label id="form:tabView:j_idt847" class="ui-outputlabel ui-widget">1250.50</label></td>
  </tr>
</table>
```

## Expected Parsing Results

- **Total Freebet**: Should extract `9` as an integer
- **Total Deposits**: Would extract `1250` (parseInt ignores decimal part)

## Key Features

1. **Dynamic ID Agnostic**: The parser ignores the specific j_idt IDs and focuses on content
2. **Structure-Based**: Looks for the 3-column table structure with specific content pattern
3. **Text Content Matching**: Matches exact text content for statistic names
4. **Numeric Parsing**: Converts text values to integers using parseInt()

## Testing the Endpoint

```bash
# Test with a real customer ID
curl -X GET "http://localhost:3000/api/pg-dagur/v1/internal/customers/12345/statistics"
```

Expected response:
```json
{
  "success": true,
  "data": {
    "statistics": {
      "totalFreebet": 9
    },
    "customerId": "12345"
  },
  "message": "Customer statistics retrieved successfully"
}
```
