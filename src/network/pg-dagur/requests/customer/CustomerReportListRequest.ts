import { XMLParser } from 'fast-xml-parser';
import { HTMLElement, parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';
import { findJavaxElementId } from '../../utils/javax';
import { formatDate } from '../../utils/time';

export interface CustomerReportListPreloadRequestResponse {
  registrationStartDateInputId: string;
  registrationEndDateInputId: string;
  lastBoUpdateInputId: string;
  callCenterInfoInputId: string;
  affiliateCodeInputId: string;
  affiliateIdInputId: string;
  birthDateStartInputId: string;
  birthDateEndInputId: string;
  testPlayerInputId: string;
  firstDepositStartDateInputId: string;
  firstDepositEndDateInputId: string;
  lastDepositStartDateInputId: string;
  lastDepositEndDateInputId: string;
  lastLoginStartDateInputId: string;
  lastLoginEndDateInputId: string;
  searchButtonId: string;
}

export interface CustomerReportListRequestOptions {
  javax: CustomerReportListPreloadRequestResponse;

  registrationStartDate?: Date | undefined;
  registrationEndDate?: Date | undefined;
  firstDepositStartDate?: Date | undefined;
  firstDepositEndDate?: Date | undefined;
  lastDepositStartDate?: Date | undefined;
  lastDepositEndDate?: Date | undefined;
  lastLoginStartDate?: Date | undefined;
  lastLoginEndDate?: Date | undefined;

  cursor: string;
  page?: number | undefined;
  limit?: number | undefined;
}

export type CustomerReportListResponse = {
  total: number;
  cursor: string;
  items: CustomerReportEntry[];
};

export type CustomerReportEntry = {
  customerId: number;
  operatorCustomLabels: string;
  firstName: string;
  secondName: string;
  surname: string;
  username: string;
  customerCode: string;
  loginStatus: string;
  country: string;
  city: string;
  twoFAMethod: string;
  registerDate: string;
  registerCode: string;
  registrationDevice: string;
  lastLoginDate: string;
  updateDate: string;
  currentTotalBalance: number;
  currency: string;
  hasDeposit: string;
  registerWithDeposit: string;
  totalDepositAmount: number;
  totalDepositNumber: number;
  totalWithdrawAmount: number;
  totalWithdrawNumber: number;
  firstDepositAmount: number;
  firstDepositTransactionDate: string;
  firstWithdrawTransactionDate: string;
  lastDepositTransactionDate: string;
  lastWithdrawTransactionDate: string;
  incomeAffiliateCode: string;
  netreferAffiliateCode: string;
  voluumAffiliateCode: string;
  trackBoxAffiliateCode: string;
  trackBoxAffiliateId: string;
  incomeAffiliateId: string;
  netreferAffiliateId: string;
  callCenterInfo: string;
  registerIp: string;
  lastLoginIp: string;
  birthdate: string;
  kycStatus: string;
  kycEmail: string;
  kycPhone: string;
  kycIdentity: string;
  kycAddress: string;
  selfExcluded: string;
  smsPromotions: string;
  emailPromotions: string;
  identity: string;
  phone: string;
  email: string;
  favouriteTeam: string;
  channel: string;
  whereDidYouHearUs: string;
  vipCustomer: string;
  potentialVipCustomer: string;
  bonusSeeker: string;
  riskLimitRate: number;
  manualTotalDepositAmount: number;
  manualTotalDepositNumber: number;
  manualTotalWithdrawAmount: number;
  manualTotalWithdrawNumber: number;
  totalQRReferenceDepositAmount: number;
  totalQRReferenceDepositNumber: number;
  totalProfitAmount: number;
  profitPercentage: number;
  totalDiscountAmount: number;
  lastBetDate: string;
  cpfNumber: string;
  gender: string;
  testPlayer: string;
};

export class CustomerReportListPreloadRequest extends PGDagurRequest<CustomerReportListPreloadRequestResponse> {
  getPath(): string {
    return `/restricted/customer-reports.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerReportListPreloadRequestResponse> {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@',
      unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
      stopNodes: ['*.pre', '*.script'],
      processEntities: true,
      htmlEntities: true,
    });
    const obj = parser.parse(xml);

    const html = obj['html'];
    if (!html) {
      return {
        success: false,
        message: 'Missing html tag',
      };
    }

    const body = html['body'];
    if (!body) {
      return {
        success: false,
        message: 'Missing body tag',
      };
    }

    const form = body['form'];
    if (!form) {
      return {
        success: false,
        message: 'Missing form tag',
      };
    }

    const input = form['input']?.find((i: any) => i['@name'] === 'javax.faces.ViewState');
    if (!input) {
      return {
        success: false,
        message: 'Missing view state input',
      };
    }

    const viewState = input['@value'];
    if (!viewState) {
      return {
        success: false,
        message: 'Missing view state value',
      };
    }

    const registrationStartDateInputId = findJavaxElementId(xml, 'form:tabView:', 'Registration Start Date');
    if (!registrationStartDateInputId) {
      return {
        success: false,
        message: 'Failed to find registration start date input id',
      };
    }

    const registrationEndDateInputId = findJavaxElementId(xml, 'form:tabView:', 'Registration End Date');
    if (!registrationEndDateInputId) {
      return {
        success: false,
        message: 'Failed to find registration end date input id',
      };
    }

    const lastBoUpdateInputId = findJavaxElementId(xml, 'form:tabView:', 'Last BO Update Date');
    if (!lastBoUpdateInputId) {
      return {
        success: false,
        message: 'Failed to find last bo update input id',
      };
    }

    const callCenterInfoInputId = findJavaxElementId(xml, 'form:tabView:', 'Call Center Info');
    if (!callCenterInfoInputId) {
      return {
        success: false,
        message: 'Failed to find call center info input id',
      };
    }

    const affiliateCodeInputId = findJavaxElementId(xml, 'form:tabView:', 'Affiliate Code');
    if (!affiliateCodeInputId) {
      return {
        success: false,
        message: 'Failed to find affiliate code input id',
      };
    }

    const affiliateIdInputId = findJavaxElementId(xml, 'form:tabView:', 'Affiliate Id');
    if (!affiliateIdInputId) {
      return {
        success: false,
        message: 'Failed to find affiliate id input id',
      };
    }

    const birthDateStartInputId = findJavaxElementId(xml, 'form:tabView:', 'Birth date start');
    if (!birthDateStartInputId) {
      return {
        success: false,
        message: 'Failed to find birth date start input id',
      };
    }

    const birthDateEndInputId = findJavaxElementId(xml, 'form:tabView:', 'Birth date end');
    if (!birthDateEndInputId) {
      return {
        success: false,
        message: 'Failed to find birth date end input id',
      };
    }

    const testPlayerInputId = findJavaxElementId(xml, 'form:tabView:', 'Test Player');
    if (!testPlayerInputId) {
      return {
        success: false,
        message: 'Failed to find test player input id',
      };
    }

    const firstDepositStartDateInputId = findJavaxElementId(xml, 'form:tabView:', 'First Deposit Start Date');
    if (!firstDepositStartDateInputId) {
      return {
        success: false,
        message: 'Failed to find first deposit start date input id',
      };
    }

    const firstDepositEndDateInputId = findJavaxElementId(xml, 'form:tabView:', 'First Deposit End Date');
    if (!firstDepositEndDateInputId) {
      return {
        success: false,
        message: 'Failed to find first deposit end date input id',
      };
    }

    const lastDepositStartDateInputId = findJavaxElementId(xml, 'form:tabView:', 'Last Deposit Start Date');
    if (!lastDepositStartDateInputId) {
      return {
        success: false,
        message: 'Failed to find last deposit start date input id',
      };
    }

    const lastDepositEndDateInputId = findJavaxElementId(xml, 'form:tabView:', 'Last Deposit End Date');
    if (!lastDepositEndDateInputId) {
      return {
        success: false,
        message: 'Failed to find last deposit end date input id',
      };
    }

    const lastLoginStartDateInputId = findJavaxElementId(xml, 'form:tabView:', 'Last Login Start Date');
    if (!lastLoginStartDateInputId) {
      return {
        success: false,
        message: 'Failed to find last login start date input id',
      };
    }

    const lastLoginEndDateInputId = findJavaxElementId(xml, 'form:tabView:', 'Last Login End Date');
    if (!lastLoginEndDateInputId) {
      return {
        success: false,
        message: 'Failed to find last login end date input id',
      };
    }

    const searchButtonId = findJavaxElementId(xml, 'form:tabView:', `form:tabView:${lastLoginEndDateInputId}_s`);
    if (!searchButtonId) {
      return {
        success: false,
        message: 'Failed to find search button id',
      };
    }

    return {
      success: true,
      data: {
        registrationStartDateInputId,
        registrationEndDateInputId,
        lastBoUpdateInputId,
        callCenterInfoInputId,
        affiliateCodeInputId,
        affiliateIdInputId,
        birthDateStartInputId,
        birthDateEndInputId,
        testPlayerInputId,
        firstDepositStartDateInputId,
        firstDepositEndDateInputId,
        lastDepositStartDateInputId,
        lastDepositEndDateInputId,
        lastLoginStartDateInputId,
        lastLoginEndDateInputId,
        searchButtonId,
      },
      viewState,
    };
  }
}

export class CustomerReportListRequest extends PGDagurRequest<CustomerReportListResponse, any> {
  constructor(private options: CustomerReportListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-reports.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const limit = this.options.limit ?? 100;

    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `form:tabView:${this.options.javax.searchButtonId}`,
      'javax.faces.partial.execute': 'form:tabView:searchPanel form:tabView:subTotalPanel',
      'javax.faces.partial.render': 'form:tabView:subTotalPanel form:tabView:compositeId:innerTableId',
      'javax.faces.behavior.event': 'click',
      'javax.faces.partial.event': 'click',
      form: 'form',
      [`form:tabView:${this.options.javax.registrationStartDateInputId}_input`]: formatDate(
        this.options.registrationStartDate,
      ),
      [`form:tabView:${this.options.javax.registrationEndDateInputId}_input`]: formatDate(
        this.options.registrationEndDate,
      ),
      [`form:tabView:${this.options.javax.lastBoUpdateInputId}_input`]: '',
      'form:tabView:favoriteTeam_focus': '',
      'form:tabView:favoriteTeam_input': '-1',
      'form:tabView:currencId_focus': '',
      'form:tabView:countryId_focus': '',
      'form:tabView:deviceTypes_focus': '',
      'form:tabView:customerKycStatus_focus': '',
      'form:tabView:status_focus': '',
      'form:tabView:radioButton4': '-1',
      'form:tabView:radioButton6': '-99',
      'form:tabView:radioButton7': '-99',
      'form:tabView:customerId_input': '',
      'form:tabView:customerId_hinput': '',
      'form:tabView:radioButton1': '-99',
      'form:tabView:radioButton5': '-99',
      'form:tabView:radioButton': '-99',
      [`form:tabView:${this.options.javax.callCenterInfoInputId}`]: '',
      [`form:tabView:${this.options.javax.affiliateCodeInputId}`]: '',
      [`form:tabView:${this.options.javax.affiliateIdInputId}`]: '',
      'form:tabView:radioButton2': '-99',
      'form:tabView:radioButton3': '-99',
      'form:tabView:registerIp': '',
      'form:tabView:lastLoginIp': '',
      'form:tabView:limitRateStart_input': '0.00',
      'form:tabView:limitRateStart_hinput': '0',
      'form:tabView:limitRateEnd_input': '500.00',
      'form:tabView:limitRateEnd_hinput': '500',
      'form:tabView:referredCustomers': '-1',
      'form:tabView:referenceCode': '',
      'form:tabView:genderId_focus': '',
      'form:tabView:faMethod': 'NONE',
      'form:tabView:birthMonth_focus': '',
      'form:tabView:birthMonth_input': '',
      'form:tabView:birthDay_focus': '',
      'form:tabView:birthDay_input': '',
      [`form:tabView:${this.options.javax.birthDateStartInputId}_input`]: '',
      [`form:tabView:${this.options.javax.birthDateEndInputId}_input`]: '',
      [`form:tabView:${this.options.javax.testPlayerInputId}_focus`]: '',
      [`form:tabView:${this.options.javax.testPlayerInputId}_input`]: '-99',
      'form:tabView:traderLabels_focus': '',
      [`form:tabView:${this.options.javax.birthDateStartInputId}_input`]: '',
      [`form:tabView:${this.options.javax.firstDepositStartDateInputId}_input`]: formatDate(
        this.options.firstDepositStartDate,
      ),
      [`form:tabView:${this.options.javax.firstDepositEndDateInputId}_input`]: formatDate(
        this.options.firstDepositEndDate,
      ),
      [`form:tabView:${this.options.javax.lastDepositStartDateInputId}_input`]: formatDate(
        this.options.lastDepositStartDate,
      ),
      [`form:tabView:${this.options.javax.lastDepositEndDateInputId}_input`]: formatDate(
        this.options.lastDepositEndDate,
      ),
      [`form:tabView:${this.options.javax.lastLoginStartDateInputId}_input`]: formatDate(
        this.options.lastLoginStartDate,
      ),
      [`form:tabView:${this.options.javax.lastLoginEndDateInputId}_input`]: formatDate(this.options.lastLoginEndDate),
      'form:tabView:compositeId:innerTableId_reflowDD': '0_0',
      'form:tabView:compositeId:innerTableId_rppDD': limit.toString(),
      'form:tabView:compositeId:innerTableId_scrollState': '0,0',
      'form:tabView_activeIndex': '0',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerReportListResponse> {
    try {
      if (xml.includes('run register report after')) {
        return {
          success: false,
          message: 'Failed to load customer report. Please try again in few minutes.',
        };
      }

      // Initialize response structure
      const response: CustomerReportListResponse = {
        total: 0,
        cursor: this.options.cursor,
        items: [],
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the table data
        let tableUpdate = update;
        if (Array.isArray(update)) {
          tableUpdate = update.find((u: any) => u?.['@id']?.includes('form:tabView:compositeId:innerTableId'));
        }

        if (!tableUpdate) {
          return {
            success: false,
            message: 'No table data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tableUpdate['#text'] || tableUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in table update',
          };
        }

        // Parse the HTML content
        this.parseCustomerReportHTML(htmlContent, response);
      } else {
        // Handle direct HTML content
        this.parseCustomerReportHTML(xml, response);
      }

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error parsing customer report data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseCustomerReportHTML(html: string, response: CustomerReportListResponse): void {
    // Parse HTML using node-html-parser
    const root = parse(html);

    // Find all table rows with data-ri attribute (data rows)
    let rows: HTMLElement[] = [];
    if (html.includes('<![CDATA[<tr data-ri')) {
      rows = root.querySelectorAll('tr[data-ri]');
    } else {
      // Find the data table
      const table =
        root.querySelector('#form\\:tabView\\:compositeId\\:innerTableId') || root.querySelector('.ui-datatable');
      if (!table) {
        throw new Error('Customer report table not found');
      }
      rows = table.querySelectorAll('tbody tr[data-ri]');
    }

    // const rows = table.querySelectorAll('tbody tr[data-ri]');

    if (!rows || rows.length === 0) {
      // No data rows found, but this might be valid (empty result)
      response.total = 0;
      response.items = [];
      return;
    }

    // Parse each row
    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length === 0) continue;

      // Extract data from cells based on the table structure (71 columns total)
      const entry: CustomerReportEntry = {
        customerId: this.parseNumericValue(this.extractCellText(cells[0])),
        operatorCustomLabels: this.extractCellText(cells[1]) || '',
        firstName: this.extractCellText(cells[2]) || '',
        secondName: this.extractCellText(cells[3]) || '',
        surname: this.extractCellText(cells[4]) || '',
        username: this.extractCellText(cells[5]) || '',
        customerCode: this.extractCellText(cells[6]) || '',
        loginStatus: this.extractCellText(cells[7]) || '',
        country: this.extractCellText(cells[8]) || '',
        city: this.extractCellText(cells[9]) || '',
        twoFAMethod: this.extractCellText(cells[10]) || '',
        registerDate: this.extractCellText(cells[11]) || '',
        registerCode: this.extractCellText(cells[12]) || '',
        registrationDevice: this.extractCellText(cells[13]) || '',
        lastLoginDate: this.extractCellText(cells[14]) || '',
        updateDate: this.extractCellText(cells[15]) || '',
        currentTotalBalance: this.parseNumericValue(this.extractCellText(cells[16])),
        currency: this.extractCellText(cells[17]) || '',
        hasDeposit: this.extractCellText(cells[18]) || '',
        registerWithDeposit: this.extractCellText(cells[19]) || '',
        totalDepositAmount: this.parseNumericValue(this.extractCellText(cells[20])),
        totalDepositNumber: this.parseNumericValue(this.extractCellText(cells[21])),
        totalWithdrawAmount: this.parseNumericValue(this.extractCellText(cells[22])),
        totalWithdrawNumber: this.parseNumericValue(this.extractCellText(cells[23])),
        firstDepositAmount: this.parseNumericValue(this.extractCellText(cells[24])),
        firstDepositTransactionDate: this.extractCellText(cells[25]) || '',
        firstWithdrawTransactionDate: this.extractCellText(cells[26]) || '',
        lastDepositTransactionDate: this.extractCellText(cells[27]) || '',
        lastWithdrawTransactionDate: this.extractCellText(cells[28]) || '',
        incomeAffiliateCode: this.extractCellText(cells[29]) || '',
        netreferAffiliateCode: this.extractCellText(cells[30]) || '',
        voluumAffiliateCode: this.extractCellText(cells[31]) || '',
        trackBoxAffiliateCode: this.extractCellText(cells[32]) || '',
        trackBoxAffiliateId: this.extractCellText(cells[33]) || '',
        incomeAffiliateId: this.extractCellText(cells[34]) || '',
        netreferAffiliateId: this.extractCellText(cells[35]) || '',
        callCenterInfo: this.extractCellText(cells[36]) || '',
        registerIp: this.extractCellText(cells[37]) || '',
        lastLoginIp: this.extractCellText(cells[38]) || '',
        birthdate: this.extractCellText(cells[39]) || '',
        kycStatus: this.extractCellText(cells[40]) || '',
        kycEmail: this.extractCellText(cells[41]) || '',
        kycPhone: this.extractCellText(cells[42]) || '',
        kycIdentity: this.extractCellText(cells[43]) || '',
        kycAddress: this.extractCellText(cells[44]) || '',
        selfExcluded: this.extractCellText(cells[45]) || '',
        smsPromotions: this.extractCellText(cells[46]) || '',
        emailPromotions: this.extractCellText(cells[47]) || '',
        identity: this.extractCellText(cells[48]) || '',
        phone: this.extractCellText(cells[49]) || '',
        email: this.extractCellText(cells[50]) || '',
        favouriteTeam: this.extractCellText(cells[51]) || '',
        channel: this.extractCellText(cells[52]) || '',
        whereDidYouHearUs: this.extractCellText(cells[53]) || '',
        vipCustomer: this.extractCellText(cells[54]) || '',
        potentialVipCustomer: this.extractCellText(cells[55]) || '',
        bonusSeeker: this.extractCellText(cells[56]) || '',
        riskLimitRate: this.parseNumericValue(this.extractCellText(cells[57])),
        manualTotalDepositAmount: this.parseNumericValue(this.extractCellText(cells[58])),
        manualTotalDepositNumber: this.parseNumericValue(this.extractCellText(cells[59])),
        manualTotalWithdrawAmount: this.parseNumericValue(this.extractCellText(cells[60])),
        manualTotalWithdrawNumber: this.parseNumericValue(this.extractCellText(cells[61])),
        totalQRReferenceDepositAmount: this.parseNumericValue(this.extractCellText(cells[62])),
        totalQRReferenceDepositNumber: this.parseNumericValue(this.extractCellText(cells[63])),
        totalProfitAmount: this.parseNumericValue(this.extractCellText(cells[64])),
        profitPercentage: this.parseNumericValue(this.extractCellText(cells[65])),
        totalDiscountAmount: this.parseNumericValue(this.extractCellText(cells[66])),
        lastBetDate: this.extractCellText(cells[67]) || '',
        cpfNumber: this.extractCellText(cells[68]) || '',
        gender: this.extractCellText(cells[69]) || '',
        testPlayer: this.extractCellText(cells[70]) || '',
      };

      response.items.push(entry);
    }

    response.total = response.items.length;
  }

  private extractCellText(cell: any): string {
    if (!cell) return '';

    // Try to get text from label first, then from cell directly
    const label = cell.querySelector('label');
    if (label && label.text) {
      return label.text.trim();
    }

    return cell.text?.trim() || '';
  }

  private parseNumericValue(text: string): number {
    if (!text) return 0;

    // Remove commas and parse as float
    const cleaned = text.replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
}

export class CustomerReportListWithCursorRequest extends PGDagurRequest<CustomerReportListResponse, any> {
  constructor(private options: CustomerReportListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-reports.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const page = this.options.page ?? 1;
    const limit = this.options.limit ?? 100;

    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `form:tabView:compositeId:innerTableId`,
      'javax.faces.partial.execute': 'form:tabView:compositeId:innerTableId',
      'javax.faces.partial.render': 'form:tabView:compositeId:innerTableId',
      'form:tabView:compositeId:innerTableId': 'form:tabView:compositeId:innerTableId',
      'form:tabView:compositeId:innerTableId_pagination': 'true',
      'form:tabView:compositeId:innerTableId_first': ((page - 1) * limit).toString(),
      'form:tabView:compositeId:innerTableId_rows': limit.toString(),
      'form:tabView:compositeId:innerTableId_skipChildren': 'true',
      'form:tabView:compositeId:innerTableId_encodeFeature': 'true',
      form: 'form',
      [`form:tabView:${this.options.javax.registrationStartDateInputId}_input`]: formatDate(
        this.options.registrationStartDate,
      ),
      [`form:tabView:${this.options.javax.registrationEndDateInputId}_input`]: formatDate(
        this.options.registrationEndDate,
      ),
      [`form:tabView:${this.options.javax.lastBoUpdateInputId}_input`]: '',
      'form:tabView:favoriteTeam_focus': '',
      'form:tabView:favoriteTeam_input': '-1',
      'form:tabView:currencId_focus': '',
      'form:tabView:countryId_focus': '',
      'form:tabView:deviceTypes_focus': '',
      'form:tabView:customerKycStatus_focus': '',
      'form:tabView:status_focus': '',
      'form:tabView:radioButton4': '-1',
      'form:tabView:radioButton6': '-99',
      'form:tabView:radioButton7': '-99',
      'form:tabView:customerId_input': '',
      'form:tabView:customerId_hinput': '',
      'form:tabView:radioButton1': '-99',
      'form:tabView:radioButton5': '-99',
      'form:tabView:radioButton': '-99',
      [`form:tabView:${this.options.javax.callCenterInfoInputId}`]: '',
      [`form:tabView:${this.options.javax.affiliateCodeInputId}`]: '',
      [`form:tabView:${this.options.javax.affiliateIdInputId}`]: '',
      'form:tabView:radioButton2': '-99',
      'form:tabView:radioButton3': '-99',
      'form:tabView:registerIp': '',
      'form:tabView:lastLoginIp': '',
      'form:tabView:limitRateStart_input': '0.00',
      'form:tabView:limitRateStart_hinput': '0',
      'form:tabView:limitRateEnd_input': '500.00',
      'form:tabView:limitRateEnd_hinput': '500',
      'form:tabView:referredCustomers': '-1',
      'form:tabView:referenceCode': '',
      'form:tabView:genderId_focus': '',
      'form:tabView:faMethod': 'NONE',
      'form:tabView:birthMonth_focus': '',
      'form:tabView:birthMonth_input': '',
      'form:tabView:birthDay_focus': '',
      'form:tabView:birthDay_input': '',
      [`form:tabView:${this.options.javax.birthDateStartInputId}_input`]: '',
      [`form:tabView:${this.options.javax.birthDateEndInputId}_input`]: '',
      [`form:tabView:${this.options.javax.testPlayerInputId}_focus`]: '',
      [`form:tabView:${this.options.javax.testPlayerInputId}_input`]: '-99',
      'form:tabView:traderLabels_focus': '',
      [`form:tabView:${this.options.javax.birthDateStartInputId}_input`]: '',
      [`form:tabView:${this.options.javax.firstDepositStartDateInputId}_input`]: formatDate(
        this.options.firstDepositStartDate,
      ),
      [`form:tabView:${this.options.javax.firstDepositEndDateInputId}_input`]: formatDate(
        this.options.firstDepositEndDate,
      ),
      [`form:tabView:${this.options.javax.lastDepositStartDateInputId}_input`]: formatDate(
        this.options.lastDepositStartDate,
      ),
      [`form:tabView:${this.options.javax.lastDepositEndDateInputId}_input`]: formatDate(
        this.options.lastDepositEndDate,
      ),
      [`form:tabView:${this.options.javax.lastLoginStartDateInputId}_input`]: formatDate(
        this.options.lastLoginStartDate,
      ),
      [`form:tabView:${this.options.javax.lastLoginEndDateInputId}_input`]: formatDate(this.options.lastLoginEndDate),
      'form:tabView:compositeId:innerTableId_reflowDD': '0_0',
      'form:tabView:compositeId:innerTableId_rppDD': limit.toString(),
      'form:tabView:compositeId:innerTableId_scrollState': '0,0',
      'form:tabView_activeIndex': '0',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerReportListResponse> {
    try {
      if (xml.includes('run register report after')) {
        return {
          success: false,
          message: 'Failed to load customer report. Please try again in few minutes.',
        };
      }

      // Initialize response structure
      const response: CustomerReportListResponse = {
        total: 0,
        cursor: this.options.cursor,
        items: [],
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the table data
        let tableUpdate = update;
        if (Array.isArray(update)) {
          tableUpdate = update.find((u: any) => u?.['@id']?.includes('form:tabView:compositeId:innerTableId'));
        }

        if (!tableUpdate) {
          return {
            success: false,
            message: 'No table data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tableUpdate['#text'] || tableUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in table update',
          };
        }

        // Parse the HTML content
        this.parseCustomerReportHTML(htmlContent, response);
      } else {
        // Handle direct HTML content
        this.parseCustomerReportHTML(xml, response);
      }

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error parsing customer report data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseCustomerReportHTML(html: string, response: CustomerReportListResponse): void {
    // Parse HTML using node-html-parser
    const root = parse(html);

    // Find all table rows with data-ri attribute (data rows)
    let rows: HTMLElement[] = [];
    if (html.trim().startsWith('<tr data-ri')) {
      rows = root.querySelectorAll('tr[data-ri]');
    } else {
      // Find the data table
      const table =
        root.querySelector('#form\\:tabView\\:compositeId\\:innerTableId') || root.querySelector('.ui-datatable');
      if (!table) {
        throw new Error('Customer report table not found');
      }
      rows = table.querySelectorAll('tbody tr[data-ri]');
    }

    // const rows = table.querySelectorAll('tbody tr[data-ri]');

    if (!rows || rows.length === 0) {
      // No data rows found, but this might be valid (empty result)
      response.total = 0;
      response.items = [];
      return;
    }

    // Parse each row
    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length === 0) continue;

      // Extract data from cells based on the table structure (71 columns total)
      const entry: CustomerReportEntry = {
        customerId: this.parseNumericValue(this.extractCellText(cells[0])),
        operatorCustomLabels: this.extractCellText(cells[1]) || '',
        firstName: this.extractCellText(cells[2]) || '',
        secondName: this.extractCellText(cells[3]) || '',
        surname: this.extractCellText(cells[4]) || '',
        username: this.extractCellText(cells[5]) || '',
        customerCode: this.extractCellText(cells[6]) || '',
        loginStatus: this.extractCellText(cells[7]) || '',
        country: this.extractCellText(cells[8]) || '',
        city: this.extractCellText(cells[9]) || '',
        twoFAMethod: this.extractCellText(cells[10]) || '',
        registerDate: this.extractCellText(cells[11]) || '',
        registerCode: this.extractCellText(cells[12]) || '',
        registrationDevice: this.extractCellText(cells[13]) || '',
        lastLoginDate: this.extractCellText(cells[14]) || '',
        updateDate: this.extractCellText(cells[15]) || '',
        currentTotalBalance: this.parseNumericValue(this.extractCellText(cells[16])),
        currency: this.extractCellText(cells[17]) || '',
        hasDeposit: this.extractCellText(cells[18]) || '',
        registerWithDeposit: this.extractCellText(cells[19]) || '',
        totalDepositAmount: this.parseNumericValue(this.extractCellText(cells[20])),
        totalDepositNumber: this.parseNumericValue(this.extractCellText(cells[21])),
        totalWithdrawAmount: this.parseNumericValue(this.extractCellText(cells[22])),
        totalWithdrawNumber: this.parseNumericValue(this.extractCellText(cells[23])),
        firstDepositAmount: this.parseNumericValue(this.extractCellText(cells[24])),
        firstDepositTransactionDate: this.extractCellText(cells[25]) || '',
        firstWithdrawTransactionDate: this.extractCellText(cells[26]) || '',
        lastDepositTransactionDate: this.extractCellText(cells[27]) || '',
        lastWithdrawTransactionDate: this.extractCellText(cells[28]) || '',
        incomeAffiliateCode: this.extractCellText(cells[29]) || '',
        netreferAffiliateCode: this.extractCellText(cells[30]) || '',
        voluumAffiliateCode: this.extractCellText(cells[31]) || '',
        trackBoxAffiliateCode: this.extractCellText(cells[32]) || '',
        trackBoxAffiliateId: this.extractCellText(cells[33]) || '',
        incomeAffiliateId: this.extractCellText(cells[34]) || '',
        netreferAffiliateId: this.extractCellText(cells[35]) || '',
        callCenterInfo: this.extractCellText(cells[36]) || '',
        registerIp: this.extractCellText(cells[37]) || '',
        lastLoginIp: this.extractCellText(cells[38]) || '',
        birthdate: this.extractCellText(cells[39]) || '',
        kycStatus: this.extractCellText(cells[40]) || '',
        kycEmail: this.extractCellText(cells[41]) || '',
        kycPhone: this.extractCellText(cells[42]) || '',
        kycIdentity: this.extractCellText(cells[43]) || '',
        kycAddress: this.extractCellText(cells[44]) || '',
        selfExcluded: this.extractCellText(cells[45]) || '',
        smsPromotions: this.extractCellText(cells[46]) || '',
        emailPromotions: this.extractCellText(cells[47]) || '',
        identity: this.extractCellText(cells[48]) || '',
        phone: this.extractCellText(cells[49]) || '',
        email: this.extractCellText(cells[50]) || '',
        favouriteTeam: this.extractCellText(cells[51]) || '',
        channel: this.extractCellText(cells[52]) || '',
        whereDidYouHearUs: this.extractCellText(cells[53]) || '',
        vipCustomer: this.extractCellText(cells[54]) || '',
        potentialVipCustomer: this.extractCellText(cells[55]) || '',
        bonusSeeker: this.extractCellText(cells[56]) || '',
        riskLimitRate: this.parseNumericValue(this.extractCellText(cells[57])),
        manualTotalDepositAmount: this.parseNumericValue(this.extractCellText(cells[58])),
        manualTotalDepositNumber: this.parseNumericValue(this.extractCellText(cells[59])),
        manualTotalWithdrawAmount: this.parseNumericValue(this.extractCellText(cells[60])),
        manualTotalWithdrawNumber: this.parseNumericValue(this.extractCellText(cells[61])),
        totalQRReferenceDepositAmount: this.parseNumericValue(this.extractCellText(cells[62])),
        totalQRReferenceDepositNumber: this.parseNumericValue(this.extractCellText(cells[63])),
        totalProfitAmount: this.parseNumericValue(this.extractCellText(cells[64])),
        profitPercentage: this.parseNumericValue(this.extractCellText(cells[65])),
        totalDiscountAmount: this.parseNumericValue(this.extractCellText(cells[66])),
        lastBetDate: this.extractCellText(cells[67]) || '',
        cpfNumber: this.extractCellText(cells[68]) || '',
        gender: this.extractCellText(cells[69]) || '',
        testPlayer: this.extractCellText(cells[70]) || '',
      };

      response.items.push(entry);
    }

    response.total = response.items.length;
  }

  private extractCellText(cell: any): string {
    if (!cell) return '';

    // Try to get text from label first, then from cell directly
    const label = cell.querySelector('label');
    if (label && label.text) {
      return label.text.trim();
    }

    return cell.text?.trim() || '';
  }

  private parseNumericValue(text: string): number {
    if (!text) return 0;

    // Remove commas and parse as float
    const cleaned = text.replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
}
