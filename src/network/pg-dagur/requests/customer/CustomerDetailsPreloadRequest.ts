import { XMLParser } from 'fast-xml-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';
import {
  findCustomerDetailsTabViewId,
  findCustomerDetailsFormElements,
  findJavaxElementId,
  findJavaxElementIdWithPrefix,
} from '../../utils/javax';

/**
 * Options for the customer details preload request
 * @interface CustomerDetailsPreloadRequestOptions
 */
export interface CustomerDetailsPreloadRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
}

/**
 * Response from the preload request containing dynamic JSF element IDs
 * @interface CustomerDetailsPreloadRequestResponse
 */
export interface CustomerDetailsPreloadRequestResponse {
  /** The main tab view component ID */
  tabViewId: string;
  /** Dynamic form element IDs extracted from the page */
  formElementIds: {
    /** Dynamic ID for checkbox/toggle input */
    j_idt627: string;
    /** Dynamic ID for collapsible panel */
    j_idt629: string;
    /** Dynamic ID for close button */
    closeButtonId: string;
    /** Dynamic ID for balances collapsed section */
    balancesCollapsedSectionId: string;
    ipConflictsModalWindowId: string;
  };
}

/**
 * Preload request to get initial page state and dynamic element IDs
 */
export class CustomerDetailsPreloadRequest extends PGDagurRequest<CustomerDetailsPreloadRequestResponse> {
  constructor(private options: CustomerDetailsPreloadRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerDetailsPreloadRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml?faces-redirect=true&customerId=${this.options.customerId}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsPreloadRequestResponse> {
    try {
      const parser = new XMLParser({
        ignoreAttributes: false,
        attributeNamePrefix: '@',
        unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
        stopNodes: ['*.pre', '*.script'],
        processEntities: true,
        htmlEntities: true,
      });
      const obj = parser.parse(xml);

      const html = obj['html'];
      if (!html) {
        return {
          success: false,
          message: 'Missing html tag',
        };
      }

      const body = html['body'];
      if (!body) {
        return {
          success: false,
          message: 'Missing body tag',
        };
      }

      const form = body['form'];
      if (!form) {
        return {
          success: false,
          message: 'Missing form tag',
        };
      }

      // Extract ViewState
      const input = form['input']?.find((i: any) => i['@name'] === 'javax.faces.ViewState');
      if (!input) {
        return {
          success: false,
          message: 'Missing view state input',
        };
      }

      const viewState = input['@value'];
      if (!viewState) {
        return {
          success: false,
          message: 'Missing view state value',
        };
      }

      // Extract dynamic element IDs using utility functions
      const tabViewId = findCustomerDetailsTabViewId(xml) || 'form:tabView';
      const formElementIds = findCustomerDetailsFormElements(xml) || {
        j_idt627: 'j_idt627',
        j_idt629: 'j_idt629',
      };

      const closeButtonId = findJavaxElementId(xml, 'form:tabView:', 'form:tabView:summaryBalance_content');
      if (!closeButtonId) {
        return {
          success: false,
          message: 'Failed to find close button id',
        };
      }

      const balancesCollapsedSectionId = findJavaxElementIdWithPrefix(xml, 'form:tabView:', 'Hide zero balances');
      if (!balancesCollapsedSectionId) {
        return {
          success: false,
          message: 'Failed to find balances collapsed section id',
        };
      }

      const ipConflictsModalWindowId = findJavaxElementId(
        xml,
        '',
        '<span class="ui-button-text ui-c">Show Linked Customers</span>',
      );
      if (!ipConflictsModalWindowId) {
        return {
          success: false,
          message: 'Failed to find IP conflicts modal window id',
        };
      }

      return {
        success: true,
        data: {
          tabViewId,
          formElementIds: {
            ...formElementIds,
            closeButtonId,
            balancesCollapsedSectionId,
            ipConflictsModalWindowId,
          },
        },
        viewState,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error parsing preload response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
