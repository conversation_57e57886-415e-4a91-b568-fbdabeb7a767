import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';
import { findJavaxElementId } from '../../utils/javax';

export interface CustomerActivityReportListRequestOptions {
  currencyInputId: string;
  startDate: Date;
  endDate: Date;
}

interface PreloadRequestResponse {
  currencyInputId: string;
}

export type CustomerActivityReportListResponse = {
  total: number;
  items: CustomerActivityReportEntry[];
};

export type CustomerActivityReportEntry = {
  transactionDate: string;
  totalDepositAmount: number;
  totalWithdrawAmount: number;
  adjustmentTopUpAmount: number;
  numberOfSportBets: number;
  numberOfVirtualBets: number;
  totalSportPlayAmount: number;
  sportBonusAmount: number;
  sportSpentBonusAmount: number;
  casinoBonusAmount: number;
  casinoSpentBonusAmount: number;
  totalBonusAmount: number;
  totalSpentBonusAmount: number;
  totalDiscountAmount: number;
  totalSportOpenAmount: number;
  totalGivenFreebetAmount: number;
  sportGGR: number;
  totalAmountTransferredFromCasino: number;
  totalAmountTransferredToCasino: number;
  totalAmountTransferredFromKlasPoker: number;
  totalAmountTransferredToKlasPoker: number;
  numberOfCasinoBets: number;
  totalCasinoPlayAmount: number;
  casinoGGR: number;
  bonusSeeker: string;
};

export class CustomerActivityReportListPreloadRequest extends PGDagurRequest<PreloadRequestResponse> {
  getPath(): string {
    return `/restricted/player-activity-report.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<PreloadRequestResponse> {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@',
      unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
      stopNodes: ['*.pre', '*.script'],
      processEntities: true,
      htmlEntities: true,
    });
    const obj = parser.parse(xml);

    const html = obj['html'];
    if (!html) {
      return {
        success: false,
        message: 'Missing html tag',
      };
    }

    const body = html['body'];
    if (!body) {
      return {
        success: false,
        message: 'Missing body tag',
      };
    }

    const form = body['form'];
    if (!form) {
      return {
        success: false,
        message: 'Missing form tag',
      };
    }

    const input = form['input']?.find((i: any) => i['@name'] === 'javax.faces.ViewState');
    if (!input) {
      return {
        success: false,
        message: 'Missing view state input',
      };
    }

    const viewState = input['@value'];
    if (!viewState) {
      return {
        success: false,
        message: 'Missing view state value',
      };
    }

    const currencyInputId = findJavaxElementId(xml, 'form:', 'Display Currency');
    if (!currencyInputId) {
      return {
        success: false,
        message: 'Failed to find currency input id',
      };
    }

    return {
      success: true,
      data: {
        currencyInputId: currencyInputId,
      },
      viewState,
    };
  }
}

function formatUTCDate(date: Date) {
  const pad = (num: number) => String(num).padStart(2, '0');

  const day = pad(date.getUTCDate());
  const month = pad(date.getUTCMonth() + 1); // Months are 0-based
  const year = date.getUTCFullYear();

  const hours = pad(date.getUTCHours());
  const minutes = pad(date.getUTCMinutes());
  const seconds = pad(date.getUTCSeconds());

  return `${day}.${month}.${year} ${hours}:${minutes}:${seconds}`;
}

export class CustomerActivityReportListRequest extends PGDagurRequest<CustomerActivityReportListResponse, any> {
  constructor(private options: CustomerActivityReportListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/player-activity-report.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:searchButton',
      'javax.faces.partial.execute': 'form:searchPanel form:listPanel',
      'javax.faces.partial.render': 'form:dtPanel form:searchPanel form:listPanel',
      'form:searchButton': 'form:searchButton',
      'javax.faces.behavior.event': 'click',
      'javax.faces.partial.event': 'click',
      form: 'form',
      'form:radioButton': '1',
      [`form:${this.options.currencyInputId}_focus`]: '',
      [`form:${this.options.currencyInputId}_input`]: '1',
      'form:transDate_focus': '',
      'form:transDate_input': '8',
      'form:startDate_input': formatUTCDate(this.options.startDate),
      'form:endDate_input': formatUTCDate(this.options.endDate),
      'form:currencyId_focus': '',
      'form:countryId_focus': '',
      'form:radioButton6': '-99',
      'form:traderLabels_focus': '',
      'form:playerActivitytDt_reflowDD': '0_0',
      'form:playerActivitytDt_rppDD': '100',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerActivityReportListResponse> {
    try {
      // Initialize response structure
      const response: CustomerActivityReportListResponse = {
        total: 0,
        items: [],
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the table data
        let tableUpdate = update;
        if (Array.isArray(update)) {
          tableUpdate = update.find((u: any) => u?.['@id']?.includes('form:dtPanel'));
        }

        if (!tableUpdate) {
          return {
            success: false,
            message: 'No table data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tableUpdate['#text'] || tableUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in table update',
          };
        }

        // Parse the HTML content
        this.parseActivityReportHTML(htmlContent, response);
      } else {
        // Handle direct HTML content
        this.parseActivityReportHTML(xml, response);
      }

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error parsing activity report data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseActivityReportHTML(html: string, response: CustomerActivityReportListResponse): void {
    // Parse HTML using node-html-parser
    const root = parse(html);

    // Find the data table
    const table = root.querySelector('#form\\:dtPanel') || root.querySelector('.ui-datatable');
    if (!table) {
      throw new Error('Activity report table not found');
    }

    // Find all table rows with data-ri attribute (data rows)
    const rows = table.querySelectorAll('tbody tr[data-ri]');

    if (!rows || rows.length === 0) {
      // No data rows found, but this might be valid (empty result)
      response.total = 0;
      response.items = [];
      return;
    }

    // Parse each row
    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length === 0) continue;

      // Extract data from cells based on the table structure
      // Map each cell to the corresponding field in the interface
      const entry: CustomerActivityReportEntry = {
        transactionDate: this.extractCellText(cells[0]) || '',
        totalDepositAmount: this.parseNumericValue(this.extractCellText(cells[1])),
        totalWithdrawAmount: this.parseNumericValue(this.extractCellText(cells[2])),
        adjustmentTopUpAmount: this.parseNumericValue(this.extractCellText(cells[3])),
        numberOfSportBets: this.parseNumericValue(this.extractCellText(cells[4])),
        numberOfVirtualBets: this.parseNumericValue(this.extractCellText(cells[5])),
        totalSportPlayAmount: this.parseNumericValue(this.extractCellText(cells[6])),
        sportBonusAmount: this.parseNumericValue(this.extractCellText(cells[7])),
        sportSpentBonusAmount: this.parseNumericValue(this.extractCellText(cells[8])),
        casinoBonusAmount: this.parseNumericValue(this.extractCellText(cells[9])),
        casinoSpentBonusAmount: this.parseNumericValue(this.extractCellText(cells[10])),
        totalBonusAmount: this.parseNumericValue(this.extractCellText(cells[11])),
        totalSpentBonusAmount: this.parseNumericValue(this.extractCellText(cells[12])),
        totalDiscountAmount: this.parseNumericValue(this.extractCellText(cells[13])),
        totalSportOpenAmount: this.parseNumericValue(this.extractCellText(cells[14])),
        totalGivenFreebetAmount: this.parseNumericValue(this.extractCellText(cells[15])),
        sportGGR: this.parseNumericValue(this.extractCellText(cells[16])),
        totalAmountTransferredFromCasino: this.parseNumericValue(this.extractCellText(cells[17])),
        totalAmountTransferredToCasino: this.parseNumericValue(this.extractCellText(cells[18])),
        totalAmountTransferredFromKlasPoker: this.parseNumericValue(this.extractCellText(cells[19])),
        totalAmountTransferredToKlasPoker: this.parseNumericValue(this.extractCellText(cells[20])),
        numberOfCasinoBets: this.parseNumericValue(this.extractCellText(cells[21])),
        totalCasinoPlayAmount: this.parseNumericValue(this.extractCellText(cells[22])),
        casinoGGR: this.parseNumericValue(this.extractCellText(cells[23])),
        bonusSeeker: this.extractCellText(cells[24]) || '',
      };

      response.items.push(entry);
    }

    response.total = response.items.length;
  }

  private extractCellText(cell: any): string {
    if (!cell) return '';

    // Try to get text from label first, then from cell directly
    const label = cell.querySelector('label');
    if (label && label.text) {
      return label.text.trim();
    }

    return cell.text?.trim() || '';
  }

  private parseNumericValue(text: string): number {
    if (!text) return 0;

    // Remove commas and parse as float
    const cleaned = text.replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
}
