import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { AddFreebetButtonPressResponse } from './AddFreebetButtonPressRequest';

/**
 * Options for the assign customer freebet request
 * @interface AssignCustomerFreebetRequestOptions
 */
export interface AssignCustomerFreebetRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** The freebet ID to assign */
  freebetId: number;
  /** The amount with currency (e.g., "250.00 TRY") */
  amount: string;
  /** The numeric amount value */
  numericAmount: number;
  /** Optional note for the freebet assignment */
  note?: string;
  /** JSF element data from the add button press request */
  javax: AddFreebetButtonPressResponse;
}

/**
 * Response from freebet assignment operation
 * @interface AssignCustomerFreebetResponse
 */
export interface AssignCustomerFreebetResponse {
  /** Whether the assignment was successful */
  success: boolean;
  /** Success or error message */
  message: string;
  /** Assigned freebet details (if successful) */
  assignedFreebet?: {
    freebetId: number;
    customerId: string;
    amount: string;
    note?: string;
  };
}

/**
 * Request to assign a freebet to a customer
 */
export class AssignCustomerFreebetRequest extends PGDagurRequest<AssignCustomerFreebetResponse, any> {
  constructor(private options: AssignCustomerFreebetRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for AssignCustomerFreebetRequest');
    }

    if (!this.options.freebetId || this.options.freebetId <= 0) {
      throw new Error('freebetId is required and must be a positive number');
    }

    if (!this.options.amount || this.options.amount.trim() === '') {
      throw new Error('amount is required for AssignCustomerFreebetRequest');
    }

    if (!this.options.numericAmount || this.options.numericAmount <= 0) {
      throw new Error('numericAmount is required and must be a positive number');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for AssignCustomerFreebetRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    console.log('🔧 Building freebet assignment form body...');
    console.log('🔧 Using submit button ID:', this.options.javax.formElementIds.submitButtonId);
    console.log('🔧 Using freebet input ID:', this.options.javax.formElementIds.freebetIdInputId);
    console.log('🔧 Assignment parameters:', {
      freebetId: this.options.freebetId,
      amount: this.options.amount,
      numericAmount: this.options.numericAmount,
      note: this.options.note
    });

    // The freebet dropdown container ID and input ID
    const freebetContainerId = this.options.javax.formElementIds.freebetIdInputId;
    const freebetInputId = `${freebetContainerId}_input`;

    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.formElementIds.submitButtonId,
      'javax.faces.partial.execute': 'addFreebetDialogForm',
      'javax.faces.partial.render': 'dlgFreebetMessages+form:tabView:freebetsTabView:activeFreebetsListDataTable+addFreebetDialogPanel',
      'javax.faces.behavior.event': 'click',
      'javax.faces.partial.event': 'click',
      'addFreebetDialogForm': 'addFreebetDialogForm',
      [`${freebetContainerId}_focus`]: '',
      [freebetInputId]: this.options.freebetId.toString(),
      'txtCustomerFreebetAmount_input': this.options.amount,
      'txtCustomerFreebetAmount_hinput': this.options.numericAmount.toString(),
      'txtCustomerFreebetNote': this.options.note || '',
    };

    console.log('📋 Complete AssignCustomerFreebetRequest form body:');
    console.log(JSON.stringify(body, null, 2));

    // Log the exact form data that will be sent
    console.log('📋 Form data as it will be sent (key=value format):');
    Object.entries(body).forEach(([key, value]) => {
      console.log(`${key}=${value}`);
    });

    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<AssignCustomerFreebetResponse> {
    console.log('🔍 Processing freebet assignment response...');
    console.log('📄 Response length:', xml.length, 'characters');

    // Print first 500 characters of response body for debugging
    // console.log('📄 Response preview (first 500 chars):', xml.substring(0, 500));

    // Print full response body for debugging
    console.log('📄 Full Response Body:', xml);

    // For freebet assignment, we don't need to parse the response body
    // The response only contains updated HTML which we don't need
    // We assume success if we got a response (Dagur would return an error page if it failed)

    const response: AssignCustomerFreebetResponse = {
      success: true,
      message: 'Freebet assignment request completed successfully',
      assignedFreebet: {
        freebetId: this.options.freebetId,
        customerId: this.options.customerId,
        amount: this.options.amount,
        note: this.options.note || undefined,
      },
    };

    console.log('✅ Assignment response processed - assuming success');
    console.log('📊 Assignment details:', JSON.stringify(response.assignedFreebet, null, 2));

    return {
      success: true,
      data: response,
    };
  }

  /**
   * Parse the assignment result from message content
   */
  private parseAssignmentResult(messageContent: string, response: AssignCustomerFreebetResponse): boolean {
    console.log('🔍 Parsing assignment result from message content...');

    // Look for success indicators
    if (messageContent.includes('success') ||
        messageContent.includes('assigned') ||
        messageContent.includes('completed') ||
        messageContent.includes('başarılı') || // Turkish for successful
        messageContent.includes('başarıyla') || // Turkish for successfully
        messageContent.includes('eklendi')) { // Turkish for added
      response.success = true;
      response.message = 'Freebet assigned successfully';
      console.log('✅ Assignment successful');
      return true;
    }

    // Look for specific error indicators
    if (messageContent.includes('error') ||
        messageContent.includes('failed') ||
        messageContent.includes('invalid') ||
        messageContent.includes('not found') ||
        messageContent.includes('insufficient') ||
        messageContent.includes('duplicate') ||
        messageContent.includes('hata') || // Turkish for error
        messageContent.includes('bulunamadı') || // Turkish for not found
        messageContent.includes('yetersiz') || // Turkish for insufficient
        messageContent.includes('geçersiz')) { // Turkish for invalid
      response.success = false;
      response.message = 'Failed to assign freebet: ' + this.extractErrorMessage(messageContent);
      console.log('❌ Assignment failed:', response.message);
      return false;
    }

    // Check for empty or minimal content (might indicate success)
    if (messageContent.trim().length < 10) {
      response.success = true;
      response.message = 'Freebet assigned successfully';
      console.log('✅ Assignment assumed successful (minimal response)');
      return true;
    }

    // Default to success if no clear indicators
    response.success = true;
    response.message = 'Freebet assigned successfully';
    console.log('✅ Assignment assumed successful (no error indicators)');
    return true;
  }

  /**
   * Extract error message from HTML content
   */
  private extractErrorMessage(content: string): string {
    // Try to extract text from HTML
    const root = parse(content);
    const textContent = root.text || content;

    // Clean up the message
    return textContent.replace(/\s+/g, ' ').trim() || 'Unknown error';
  }

  /**
   * Validate freebet assignment parameters
   */
  static validateAssignmentParams(params: {
    customerId: string;
    freebetId: number;
    amount: string;
    numericAmount: number;
    note?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate customerId
    if (!params.customerId || params.customerId.trim() === '') {
      errors.push('Customer ID is required');
    }

    // Validate freebetId
    if (!params.freebetId || params.freebetId <= 0) {
      errors.push('Freebet ID must be a positive number');
    }

    // Validate amount format
    if (!params.amount || params.amount.trim() === '') {
      errors.push('Amount is required');
    } else {
      // Check if amount has currency (e.g., "250.00 TRY")
      const amountPattern = /^\d+(\.\d{2})?\s+[A-Z]{3}$/;
      if (!amountPattern.test(params.amount.trim())) {
        errors.push('Amount must be in format "250.00 TRY"');
      }
    }

    // Validate numeric amount
    if (!params.numericAmount || params.numericAmount <= 0) {
      errors.push('Numeric amount must be a positive number');
    }

    // Validate amount consistency
    if (params.amount && params.numericAmount) {
      const amountParts = params.amount.split(' ');
      if (amountParts.length > 0 && amountParts[0]) {
        const extractedAmount = parseFloat(amountParts[0]);
        if (!isNaN(extractedAmount) && Math.abs(extractedAmount - params.numericAmount) > 0.01) {
          errors.push('Amount and numeric amount must match');
        }
      }
    }

    // Validate note length (optional but if provided, should be reasonable)
    if (params.note && params.note.length > 500) {
      errors.push('Note must be less than 500 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
