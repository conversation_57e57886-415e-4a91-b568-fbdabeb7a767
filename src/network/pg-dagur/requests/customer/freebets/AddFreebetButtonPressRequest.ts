import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { findFreebetAssignmentFormElements } from '../../../utils/javax';

/**
 * Options for the add button press request
 * @interface AddFreebetButtonPressRequestOptions
 */
export interface AddFreebetButtonPressRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
}

/**
 * Response from the add freebet button press containing dynamic JSF element IDs
 * @interface AddFreebetButtonPressResponse
 */
export interface AddFreebetButtonPressResponse {
  /** Dynamic form element IDs extracted from the dialog */
  formElementIds: {
    /** Submit button ID for freebet assignment */
    submitButtonId: string;
    /** Freebet ID input field ID */
    freebetIdInputId: string;
    /** Cleanup button ID for dialog reset */
    cleanupButtonId: string;
    /** Cleanup input field ID */
    cleanupInputId: string;
  };
}

/**
 * Request to press the add freebet button (opens the assignment dialog)
 */
export class AddFreebetButtonPressRequest extends PGDagurRequest<AddFreebetButtonPressResponse, any> {
  constructor(private options: AddFreebetButtonPressRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for AddFreebetButtonPressRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    console.log('🔧 Building add freebet button press form body...');

    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'addFreebetDialog',
      'javax.faces.partial.execute': 'addFreebetDialog',
      'javax.faces.partial.render': 'addFreebetDialog',
      'addFreebetDialog': 'addFreebetDialog',
      'addFreebetDialog_contentLoad': 'true',
      'addFreebetDialogForm': 'addFreebetDialogForm',
    };

    console.log('📋 Complete AddFreebetButtonPressRequest form body:');
    console.log(JSON.stringify(body, null, 2));

    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<AddFreebetButtonPressResponse> {
    console.log('🔍 Processing add freebet button press response...');
    console.log('📄 Response length:', xml.length, 'characters');

    try {
      // Extract dynamic element IDs from the dialog HTML
      console.log('🔍 Extracting dynamic j_idt elements from dialog response...');
      const formElementIds = findFreebetAssignmentFormElements(xml);

      console.log('✅ Successfully extracted form element IDs:', JSON.stringify(formElementIds, null, 2));

      const response: AddFreebetButtonPressResponse = {
        formElementIds,
      };

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      console.error('❌ Error extracting form element IDs:', error);

      // Return fallback values based on the HTML you provided
      const fallbackFormElementIds = {
        submitButtonId: 'j_idt3472',
        freebetIdInputId: 'j_idt3467',
        cleanupButtonId: 'j_idt3472',
        cleanupInputId: 'j_idt3467',
      };

      console.log('⚠️ Using fallback form element IDs:', JSON.stringify(fallbackFormElementIds, null, 2));

      const response: AddFreebetButtonPressResponse = {
        formElementIds: fallbackFormElementIds,
      };

      return {
        success: true,
        data: response,
      };
    }
  }
}
