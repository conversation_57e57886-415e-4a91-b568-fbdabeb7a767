import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { AddFreebetButtonPressResponse } from './AddFreebetButtonPressRequest';

/**
 * Options for the dropdown select request
 * @interface FreebetDropdownSelectRequestOptions
 */
export interface FreebetDropdownSelectRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** The freebet ID to select in dropdown */
  freebetId: number;
  /** JSF element data from the add button press request */
  javax: AddFreebetButtonPressResponse;
}

/**
 * Request to select a freebet from the dropdown
 */
export class FreebetDropdownSelectRequest extends PGDagurRequest<any, any> {
  constructor(private options: FreebetDropdownSelectRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for FreebetDropdownSelectRequest');
    }

    if (!this.options.freebetId || this.options.freebetId <= 0) {
      throw new Error('freebetId is required and must be a positive number');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for FreebetDropdownSelectRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    console.log('🔧 Building freebet dropdown select form body...');
    console.log('🔧 Using freebet dropdown ID:', this.options.javax.formElementIds.freebetIdInputId);
    console.log('🔧 Using freebet ID:', this.options.freebetId);

    // The source should be the container div ID, not the input ID
    const sourceId = this.options.javax.formElementIds.freebetIdInputId;
    const inputId = `${sourceId}_input`;

    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': sourceId,
      'javax.faces.partial.execute': sourceId,
      'javax.faces.partial.render': 'addFreebetDialogPanel',
      'javax.faces.behavior.event': 'change',
      'javax.faces.partial.event': 'change',
      'addFreebetDialogForm': 'addFreebetDialogForm',
      [`${sourceId}_focus`]: '',
      [inputId]: this.options.freebetId.toString(),
      'txtCustomerFreebetAmount_input': '',
      'txtCustomerFreebetAmount_hinput': '',
      'txtCustomerFreebetNote': '',
    };

    console.log('📋 Complete FreebetDropdownSelectRequest form body:');
    console.log(JSON.stringify(body, null, 2));

    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<any> {
    console.log('🔍 Processing freebet dropdown select response...');
    console.log('📄 Response length:', xml.length, 'characters');

    // Print full response body for debugging
    console.log('📄 Full Response Body:', xml);

    // For dropdown select, we don't need to parse the response body
    // We assume success if we got a response
    return {
      success: true,
      data: {},
    };
  }
}
