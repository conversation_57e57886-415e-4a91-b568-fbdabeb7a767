import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { AddFreebetButtonPressResponse } from './AddFreebetButtonPressRequest';

/**
 * Options for the assign customer freebet cleanup request
 * @interface AssignCustomerFreebetCleanupRequestOptions
 */
export interface AssignCustomerFreebetCleanupRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** Optional note to preserve in the cleanup */
  note?: string;
  /** JSF element data from the add button press request */
  javax: AddFreebetButtonPressResponse;
}

/**
 * Response from the assign freebet cleanup request
 * @interface AssignCustomerFreebetCleanupResponse
 */
export interface AssignCustomerFreebetCleanupResponse {
  /** Whether the cleanup was successful */
  success: boolean;
  /** Success or error message */
  message: string;
}

/**
 * Request to cleanup/reset the freebet assignment dialog after successful assignment
 */
export class AssignCustomerFreebetCleanupRequest extends PGDagurRequest<AssignCustomerFreebetCleanupResponse, any> {
  constructor(private options: AssignCustomerFreebetCleanupRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for AssignCustomerFreebetCleanupRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for AssignCustomerFreebetCleanupRequest');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    console.log('🧹 Building freebet assignment cleanup form body...');
    console.log('🧹 Cleanup parameters:', {
      customerId: this.options.customerId,
      note: this.options.note
    });

    // The freebet dropdown container ID and input ID
    const freebetContainerId = this.options.javax.formElementIds.cleanupInputId;
    const freebetInputId = `${freebetContainerId}_input`;

    // This request clears the dialog form fields after successful assignment
    // Based on the form data pattern: cleanup button as source, @all execution, empty field values
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.formElementIds.cleanupButtonId,
      'javax.faces.partial.execute': '@all', // Execute all components
      [this.options.javax.formElementIds.cleanupButtonId]: this.options.javax.formElementIds.cleanupButtonId,
      'addFreebetDialogForm': 'addFreebetDialogForm',
      [`${freebetContainerId}_focus`]: '', // Empty focus field
      [freebetInputId]: '', // Empty freebet ID input
      'txtCustomerFreebetAmount_input': '', // Empty amount input
      'txtCustomerFreebetAmount_hinput': '', // Empty numeric amount
      'txtCustomerFreebetNote': this.options.note || '', // Preserve note if provided
    };

    console.log('📋 Complete AssignCustomerFreebetCleanupRequest form body:');
    console.log(JSON.stringify(body, null, 2));

    // Log the exact form data that will be sent
    console.log('📋 Cleanup form data as it will be sent (key=value format):');
    Object.entries(body).forEach(([key, value]) => {
      console.log(`${key}=${value}`);
    });

    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<AssignCustomerFreebetCleanupResponse> {
    console.log('🔍 Processing freebet assignment cleanup response...');
    console.log('📄 Response length:', xml.length, 'characters');
    console.log('📄 Response preview (first 500 chars):', xml.substring(0, 500));

    // For cleanup request, we don't need to parse the response body
    // The response only contains updated HTML which we don't need
    // We assume success if we got a response (Dagur would return an error page if it failed)

    const response: AssignCustomerFreebetCleanupResponse = {
      success: true,
      message: 'Freebet assignment cleanup completed successfully',
    };

    return {
      success: true,
      data: response,
    };
  }
}
