import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '.././CustomerDetailsPreloadRequest';

/**
 * Options for the customer freebets tab change request
 * @interface CustomerFreebetsTabChangeRequestOptions
 */
export interface CustomerFreebetsTabChangeRequestOptions {
  /** The unique identifier of the customer */
  customerId: string;
  /** JSF element data from the preload request */
  javax: CustomerDetailsPreloadRequestResponse;
}

/**
 * Individual freebet item data structure
 * @interface FreebetItem
 */
export interface FreebetItem {
  /** Unique identifier for the customer freebet */
  customerFreebetId: string;
  /** Associated sport bet ID (if any) */
  sportBetId: string | null;
  /** Associated transaction ID (if any) */
  transactionId: string | null;
  /** Customer ID who owns the freebet */
  customerId: string;
  /** Display name of the freebet */
  freebetName: string;
  /** Freebet amount with currency */
  amount: string;
  /** Date when the freebet was created */
  creationDate: string;
  /** User who created the freebet */
  createdBy: string;
  /** User who last updated the freebet */
  updatedBy: string;
  /** Date when the freebet was last updated */
  updateDate: string;
  /** Description or notes about the freebet */
  description: string;
}

/**
 * Response containing customer freebets data
 * @interface CustomerFreebetsResponse
 */
export interface CustomerFreebetsResponse {
  /** List of active freebets */
  activeFreebets: FreebetItem[];
  /** List of inactive freebets */
  inactiveFreebets: FreebetItem[];
  /** Total count of active freebets */
  totalActiveCount: number;
  /** Total count of inactive freebets */
  totalInactiveCount: number;
}

/**
 * Tab change request to get customer freebets data
 */
export class CustomerFreebetsTabChangeRequest extends PGDagurRequest<CustomerFreebetsResponse, any> {
  constructor(private options: CustomerFreebetsTabChangeRequestOptions) {
    super();

    // Validate required options
    if (!this.options.customerId || this.options.customerId.trim() === '') {
      throw new Error('customerId is required for CustomerFreebetsTabChangeRequest');
    }

    if (!this.options.javax) {
      throw new Error('javax data is required for CustomerFreebetsTabChangeRequest');
    }

    if (!this.options.javax.tabViewId) {
      throw new Error('tabViewId is required in javax data');
    }
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.tabViewId,
      'javax.faces.partial.execute': this.options.javax.tabViewId,
      'javax.faces.partial.render': this.options.javax.tabViewId,
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      [`${this.options.javax.tabViewId}_contentLoad`]: 'true',
      [`${this.options.javax.tabViewId}_newTab`]: `${this.options.javax.tabViewId}:customerFreebets`,
      [`${this.options.javax.tabViewId}_tabindex`]: '8',
      'form': 'form',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt627}_input`]: 'on',
      [`${this.options.javax.tabViewId}:${this.options.javax.formElementIds.j_idt629}:0:j_idt630_collapsed`]: 'false',
      [`${this.options.javax.tabViewId}:customerNotesDt_rppDD`]: '10',
      [`${this.options.javax.tabViewId}_activeIndex`]: '8',
    };

    console.log('📋 CustomerFreebetsTabChangeRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerFreebetsResponse> {
    try {
      // Initialize response structure
      const response: CustomerFreebetsResponse = {
        activeFreebets: [],
        inactiveFreebets: [],
        totalActiveCount: 0,
        totalInactiveCount: 0,
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the tab view data
        let tabViewUpdate = update;
        if (Array.isArray(update)) {
          tabViewUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('form:tabView') &&
              !u?.['@id']?.includes('javax.faces.ViewState'),
          );
        }

        if (!tabViewUpdate) {
          return {
            success: false,
            message: 'No tab view data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tabViewUpdate['#text'] || tabViewUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in tab view update',
          };
        }

        // Parse the HTML content
        console.log('🔍 Parsing HTML content for freebets data...');
        this.parseFreebetHTML(htmlContent, response);

        // Find ViewState update for next request
        const viewStateUpdate = Array.isArray(update)
          ? update.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'))
          : null;
        const viewState = viewStateUpdate?.['#text'] || viewStateUpdate;

        console.log('📊 Parsing complete - Response data:', JSON.stringify({
          totalActiveCount: response.totalActiveCount,
          totalInactiveCount: response.totalInactiveCount,
          hasViewState: !!viewState
        }, null, 2));

        return {
          success: true,
          data: response,
          viewState: viewState || undefined,
        };
      } else {
        // Handle direct HTML content (fallback)
        this.parseFreebetHTML(xml, response);

        return {
          success: true,
          data: response,
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Error parsing freebets data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Parse HTML table structure to extract freebet data
   */
  private parseFreebetHTML(html: string, response: CustomerFreebetsResponse): void {
    try {
      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Find active freebets table - try multiple possible selectors
      const activeFreebetsTable = root.querySelector('#activeFreebetsListDataTable table tbody') ||
                                  root.querySelector('#freeBetListTab table tbody') ||
                                  root.querySelector('div[id*="freeBetListTab"] table tbody') ||
                                  root.querySelector('table tbody');

      if (activeFreebetsTable) {
        const activeRows = activeFreebetsTable.querySelectorAll('tr[data-ri]');
        if (activeRows && activeRows.length > 0) {
          response.activeFreebets = this.parseFreebetRows(activeRows);
          response.totalActiveCount = response.activeFreebets.length;
          console.log(`Found ${response.totalActiveCount} active freebets`);
        } else {
          console.log('No active freebet rows found in table');
        }
      } else {
        console.log('Active freebets table not found in HTML');
      }

      // Find inactive freebets table (in the second tab panel) - try multiple possible selectors
      const inactiveFreebetsTable = root.querySelector('#inActiveFreeBetListTab table tbody') ||
                                    root.querySelector('div[id*="inActiveFreeBetListTab"] table tbody');

      if (inactiveFreebetsTable) {
        const inactiveRows = inactiveFreebetsTable.querySelectorAll('tr[data-ri]');
        if (inactiveRows && inactiveRows.length > 0) {
          response.inactiveFreebets = this.parseFreebetRows(inactiveRows);
          response.totalInactiveCount = response.inactiveFreebets.length;
          console.log(`Found ${response.totalInactiveCount} inactive freebets`);
        } else {
          console.log('No inactive freebet rows found in table');
        }
      } else {
        console.log('Inactive freebets table not found in HTML');
      }

      // If no freebets found at all, log a warning
      if (response.totalActiveCount === 0 && response.totalInactiveCount === 0) {
        console.warn('No freebets found in HTML content');
      }
    } catch (error) {
      console.error('Error parsing freebet HTML:', error);
      // Keep empty arrays as fallback
    }
  }

  /**
   * Parse individual table rows to extract freebet data
   */
  private parseFreebetRows(rows: any[]): FreebetItem[] {
    const freebets: FreebetItem[] = [];

    if (!rows || rows.length === 0) {
      console.log('No freebet rows to parse');
      return freebets;
    }

    console.log(`Parsing ${rows.length} freebet rows...`);

    for (let i = 0; i < rows.length; i++) {
      try {
        const row = rows[i];
        const cells = row.querySelectorAll('td');

        if (!cells || cells.length === 0) {
          console.warn(`Row ${i}: No cells found`);
          continue;
        }

        if (cells.length < 11) {
          console.warn(`Row ${i}: Expected 11 columns, found ${cells.length}`);
          // Try to parse with available columns, filling missing ones with empty strings
        }

        const freebet: FreebetItem = {
          customerFreebetId: this.getCellText(cells[0]),
          sportBetId: this.getCellText(cells[1]) || null,
          transactionId: this.getCellText(cells[2]) || null,
          customerId: this.getCellText(cells[3]),
          freebetName: this.getCellText(cells[4]),
          amount: this.getCellText(cells[5]),
          creationDate: this.getCellText(cells[6]),
          createdBy: this.getCellText(cells[7]),
          updatedBy: this.getCellText(cells[8]),
          updateDate: this.getCellText(cells[9]),
          description: this.getCellText(cells[10]),
        };

        // Validate that we have at least the essential fields
        if (!freebet.customerFreebetId || !freebet.customerId) {
          console.warn(`Row ${i}: Missing essential fields (customerFreebetId or customerId)`);
          continue;
        }

        freebets.push(freebet);
        console.log(`Row ${i}: Successfully parsed freebet ${freebet.customerFreebetId}`);
      } catch (error) {
        console.error(`Error parsing freebet row ${i}:`, error);
        // Skip this row and continue
      }
    }

    console.log(`Successfully parsed ${freebets.length} out of ${rows.length} freebet rows`);
    return freebets;
  }

  /**
   * Extract text content from table cell, handling empty cells and various edge cases
   */
  private getCellText(cell: any): string {
    if (!cell) return '';

    // Try multiple ways to extract text content
    let text = '';

    if (cell.text) {
      text = cell.text.trim();
    } else if (cell.innerText) {
      text = cell.innerText.trim();
    } else if (cell.textContent) {
      text = cell.textContent.trim();
    } else if (cell.innerHTML) {
      // Strip HTML tags and get text content
      text = cell.innerHTML.replace(/<[^>]*>/g, '').trim();
    }

    // Handle various empty representations
    if (text === '' || text === ' ' || text === '&nbsp;' || text === '-' || text === 'N/A') {
      return '';
    }

    // Decode HTML entities
    text = text.replace(/&amp;/g, '&')
               .replace(/&lt;/g, '<')
               .replace(/&gt;/g, '>')
               .replace(/&quot;/g, '"')
               .replace(/&#39;/g, "'")
               .replace(/&nbsp;/g, ' ');

    return text.trim();
  }
}
