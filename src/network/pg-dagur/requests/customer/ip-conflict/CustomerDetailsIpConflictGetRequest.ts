import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';
import { parse } from 'node-html-parser';

export type CustomerDetailsIpConflictGetRequestResponse = {
  conflicts: {
    customerId: number;
    customerCode: string;
    username: string;
    registerDate: Date;
    loginStatus: string;
    lastLoginDate: Date;
    lastLoginIp: string;
  }[];
};

export class CustomerDetailsIpConflictGetRequest extends PGDagurRequest<
  CustomerDetailsIpConflictGetRequestResponse,
  any
> {
  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'form:tabView:showLinkedCustomers',
      'javax.faces.partial.execute': 'form:tabView:linkedCustomersPanel',
      'javax.faces.partial.render': 'form:tabView:loginlogsDt form:tabView:outformDial',
      'javax.faces.behavior.event': 'click',
      'javax.faces.partial.event': 'click',
      form: 'form',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<CustomerDetailsIpConflictGetRequestResponse> {
    // Check for "No records found" message
    if (xml.includes('No records found')) {
      return {
        success: true,
        data: {
          conflicts: [],
        },
      };
    }

    try {
      const conflicts = this.parseConflictsTable(xml);
      return {
        success: true,
        data: {
          conflicts,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to parse IP conflicts: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private parseConflictsTable(xml: string): CustomerDetailsIpConflictGetRequestResponse['conflicts'] {
    const doc = parse(xml);
    const conflicts: CustomerDetailsIpConflictGetRequestResponse['conflicts'] = [];

    // Find the table body with data
    const tbody = doc.querySelector('#form\\:tabView\\:loginlogsDt_data');
    if (!tbody) {
      return conflicts;
    }

    // Find all data rows
    const rows = tbody.querySelectorAll('tr[data-ri]');

    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 7) {
        try {
          const conflict = this.parseConflictRow(cells);
          conflicts.push(conflict);
        } catch (error) {
          console.warn('Failed to parse conflict row:', error);
          // Continue parsing other rows even if one fails
        }
      }
    }

    return conflicts;
  }

  private parseConflictRow(cells: any[]): CustomerDetailsIpConflictGetRequestResponse['conflicts'][0] {
    // Helper function to parse date from format "11.06.2025 20:58:05" as UTC
    const parseDate = (dateStr: string): Date => {
      const match = dateStr.match(/(\d{2})\.(\d{2})\.(\d{4}) (\d{2}):(\d{2}):(\d{2})/);
      if (match) {
        const [, day, month, year, hour, minute, second] = match;
        if (!day || !month || !year || !hour || !minute || !second) return new Date();

        return new Date(
          Date.UTC(
            parseInt(year, 10),
            parseInt(month, 10) - 1,
            parseInt(day, 10),
            parseInt(hour, 10),
            parseInt(minute, 10),
            parseInt(second, 10),
          ),
        );
      }
      return new Date();
    };

    // Extract Customer ID from link href
    const customerIdLink = cells[0].querySelector('a');
    const customerIdStr = customerIdLink?.text?.trim() || '0';
    const customerId = parseInt(customerIdStr, 10) || 0;

    // Extract Customer Code from label
    const customerCode = cells[1].querySelector('label')?.text?.trim() || '';

    // Extract Username from label
    const username = cells[2].querySelector('label')?.text?.trim() || '';

    // Extract Register Date from label
    const registerDateStr = cells[3].querySelector('label')?.text?.trim() || '';
    const registerDate = parseDate(registerDateStr);

    // Extract Login Status from label
    const loginStatus = cells[4].querySelector('label')?.text?.trim() || '';

    // Extract Last Login Date from label
    const lastLoginDateStr = cells[5].querySelector('label')?.text?.trim() || '';
    const lastLoginDate = parseDate(lastLoginDateStr);

    // Extract Last Login IP from label
    const lastLoginIp = cells[6].querySelector('label')?.text?.trim() || '';

    return {
      customerId,
      customerCode,
      username,
      registerDate,
      loginStatus,
      lastLoginDate,
      lastLoginIp,
    };
  }
}
