import { HttpMethod, PGDagurRequest } from '../../Request';
import { CustomerDetailsPreloadRequestResponse } from '../CustomerDetailsPreloadRequest';

export interface CustomerDetailsIpConflictPreloadRequestOptions {
  javax: CustomerDetailsPreloadRequestResponse;
}

export class CustomerDetailsIpConflictPreloadRequest extends PGDagurRequest<null, any> {
  constructor(private options: CustomerDetailsIpConflictPreloadRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/customer-details.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': `form:tabView:${this.options.javax.formElementIds.ipConflictsModalWindowId}`,
      'javax.faces.partial.execute': `form:tabView:${this.options.javax.formElementIds.ipConflictsModalWindowId}`,
      'javax.faces.partial.render': `form:tabView:${this.options.javax.formElementIds.ipConflictsModalWindowId}`,
      [`form:tabView:${this.options.javax.formElementIds.ipConflictsModalWindowId}`]: `form:tabView:${this.options.javax.formElementIds.ipConflictsModalWindowId}`,
      [`form:tabView:${this.options.javax.formElementIds.ipConflictsModalWindowId}_contentLoad`]: 'true',
      form: 'form',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
