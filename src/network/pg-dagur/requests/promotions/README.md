# PG Dagur Promotions Requests

This folder contains request classes for PG Dagur promotions-related operations.

## Overview

All promotion-related pipelines start with the `PromotionsPreloadRequest` which loads the initial promotions page state from:
```
https://dagur.pgbo.io/restricted/promotions.xhtml
```

## Structure

### PromotionsPreloadRequest
- **Purpose**: Simple preload request to get initial page state and ViewState
- **URL**: `/restricted/promotions.xhtml`
- **Method**: GET
- **Parameters**: None
- **Returns**: ViewState and basic form element IDs for subsequent requests

## Future Pipelines

This folder will contain subfolders for specific promotion pipelines:

- `freebets/` - Freebet assignment and management requests
- `discounts/` - Promotion discount requests  
- `bonuses/` - Bonus promotion requests
- etc.

## Usage Example

```typescript
import { PromotionsPreloadRequest } from './PromotionsPreloadRequest';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';

// Step 1: Preload promotions page
const preloadRequest = new PromotionsPreloadRequest();
const response = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

if (response.success) {
  const viewState = response.viewState;
  const formElementIds = response.data.formElementIds;
  
  // Use viewState and formElementIds for subsequent requests
}
```

## Controller Integration

The promotions functionality is exposed through the `PromotionsController` with endpoints:

- `GET /api/pg-dagur/v1/internal/promotions/preload` - Test preload functionality
- `GET /api/pg-dagur/v1/promotions/preload` - Public preload endpoint

Future endpoints will be added as new promotion pipelines are implemented.
