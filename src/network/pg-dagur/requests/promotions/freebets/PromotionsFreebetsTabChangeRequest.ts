import { XMLParser } from 'fast-xml-parser';
import { parse } from 'node-html-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../../Request';

/**
 * Options for the promotions freebets tab change request
 * @interface PromotionsFreebetsTabChangeRequestOptions
 */
export interface PromotionsFreebetsTabChangeRequestOptions {
  /** ViewState from the promotions preload request */
  viewState: string;
}

/**
 * Interface for a single freebet item from the promotions table
 */
export interface PromotionsFreebet {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  isThirdParty: boolean;
  friendReferral: boolean;
  createAppUser: string;
  createDate: string;
  updateAppUser: string;
  updateDate: string;
  status: string;
}

/**
 * Response from the freebets tab change request
 * @interface PromotionsFreebetsTabChangeRequestResponse
 */
export interface PromotionsFreebetsTabChangeRequestResponse {
  /** Parsed freebets data */
  freebets: PromotionsFreebet[];
  /** Total number of freebets */
  total: number;
}

/**
 * Request to change to the freebets tab in the promotions page
 * This request navigates to the freebets section within the promotions interface
 */
export class PromotionsFreebetsTabChangeRequest extends PGDagurRequest<PromotionsFreebetsTabChangeRequestResponse> {
  constructor(private options: PromotionsFreebetsTabChangeRequestOptions) {
    super();

    // Validate required options
    if (!this.options.viewState || this.options.viewState.trim() === '') {
      throw new Error('viewState is required for PromotionsFreebetsTabChangeRequest');
    }
  }

  getPath(): string {
    return '/restricted/promotions.xhtml';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    // Hardcoded form data with dynamic ViewState
    const body = {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': 'tabView',
      'javax.faces.partial.execute': 'tabView',
      'javax.faces.partial.render': 'tabView breadCrumbElement',
      'javax.faces.behavior.event': 'tabChange',
      'javax.faces.partial.event': 'tabChange',
      'tabView_contentLoad': 'true',
      'tabView_newTab': 'tabView:freebets',
      'tabView_tabindex': '1',
      'sportBetDialogForm': 'sportBetDialogForm',
      'sportBetDialogForm:sportBetsDataTable_selection': '',
      'javax.faces.ViewState': this.options.viewState,
    };

    console.log('📋 PromotionsFreebetsTabChangeRequest body:', JSON.stringify(body, null, 2));
    return body;
  }

  getHeaders(baseUrl: string): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<PromotionsFreebetsTabChangeRequestResponse> {
    try {
      console.log('🔍 Processing promotions freebets tab change response...');
      console.log('📄 Response length:', xml.length, 'characters');

      // Check for authentication redirect
      if (xml.includes('redirect url="/login.xhtml"')) {
        console.error('❌ Authentication failed - redirected to login');
        return {
          success: false,
          message: 'Authentication failed - redirected to login page',
        };
      }

      // Initialize response structure
      const response: PromotionsFreebetsTabChangeRequestResponse = {
        freebets: [],
        total: 0,
      };

      // Check if this is a JSF partial response or direct HTML
      if (xml.includes('<partial-response')) {
        // Handle JSF partial response
        const parser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@',
          unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
          stopNodes: ['*.pre', '*.script'],
          processEntities: true,
          htmlEntities: true,
        });
        const obj = parser.parse(xml);

        // Navigate to the partial response content
        const partialResponse = obj?.['partial-response'];
        if (!partialResponse) {
          return {
            success: false,
            message: 'Missing partial-response in XML',
          };
        }

        const changes = partialResponse?.['changes'];
        if (!changes) {
          return {
            success: false,
            message: 'Missing changes in partial-response',
          };
        }

        const update = changes?.['update'];
        if (!update) {
          return {
            success: false,
            message: 'Missing update in changes',
          };
        }

        // Find the update containing the tab view data
        let tabViewUpdate = update;
        if (Array.isArray(update)) {
          tabViewUpdate = update.find(
            (u: any) =>
              u?.['@id']?.includes('tabView') &&
              !u?.['@id']?.includes('javax.faces.ViewState'),
          );
        }

        if (!tabViewUpdate) {
          return {
            success: false,
            message: 'No tab view data found in response',
          };
        }

        // Extract HTML content from the update
        const htmlContent = tabViewUpdate['#text'] || tabViewUpdate;
        if (typeof htmlContent !== 'string') {
          return {
            success: false,
            message: 'Invalid HTML content in tab view update',
          };
        }

        // Parse the HTML content and extract freebets data
        console.log('🔍 Parsing HTML content for freebets data...');
        this.parseFreebetsHTML(htmlContent, response);
      } else {
        // Handle direct HTML response
        console.log('🔍 Parsing direct HTML response for freebets data...');
        this.parseFreebetsHTML(xml, response);
      }

      console.log('✅ Promotions freebets tab change response processed successfully');
      console.log('📊 Found freebets:', response.total);

      return {
        success: true,
        data: response,
      };
    } catch (error: any) {
      console.error('❌ Error processing promotions freebets tab change response:', error.message);
      return {
        success: false,
        message: `Failed to process promotions freebets tab change response: ${error.message}`,
      };
    }
  }

  /**
   * Parse HTML content to extract freebets table data
   */
  private parseFreebetsHTML(html: string, response: PromotionsFreebetsTabChangeRequestResponse): void {
    try {
      console.log('🔍 Parsing promotions freebets table...');
      console.log('📄 HTML length:', html.length, 'characters');

      // Parse HTML using node-html-parser
      const root = parse(html);

      if (!root) {
        console.error('Failed to parse HTML content');
        return;
      }

      // Find the freebets table by its specific ID
      const tbody = root.querySelector('#tabView\\:freeBetDt_data') ||
                    root.querySelector('tbody[id*="freeBetDt_data"]') ||
                    root.querySelector('tbody.ui-datatable-data');

      if (!tbody) {
        console.warn('Freebets table tbody not found in HTML');
        return;
      }

      // Find all table rows with data-ri attribute (data rows)
      const rows = tbody.querySelectorAll('tr[data-ri]');

      if (!rows || rows.length === 0) {
        console.log('No freebet rows found in table');
        return;
      }

      console.log(`Found ${rows.length} freebet rows to parse`);

      // Parse each row
      const freebets: PromotionsFreebet[] = [];
      for (const row of rows) {
        try {
          const freebet = this.parseFreebetRow(row);
          if (freebet) {
            freebets.push(freebet);
          }
        } catch (error: any) {
          console.warn('Failed to parse freebet row:', error.message);
          // Continue parsing other rows even if one fails
        }
      }

      response.freebets = freebets;
      response.total = freebets.length;

      console.log(`✅ Successfully parsed ${freebets.length} freebets`);
    } catch (error: any) {
      console.error('❌ Error parsing freebets table:', error.message);
    }
  }

  /**
   * Parse a single freebet row from the table
   */
  private parseFreebetRow(row: any): PromotionsFreebet | null {
    const cells = row.querySelectorAll('td');

    if (cells.length < 11) {
      console.warn(`Row has ${cells.length} cells, expected at least 11`);
      return null;
    }

    try {
      // Helper function to get text content safely
      const getCellText = (index: number): string => {
        return cells[index]?.text?.trim() || '';
      };

      // Helper function to parse boolean values
      const parseBoolean = (value: string): boolean => {
        return value.toLowerCase() === 'true';
      };

      const freebet: PromotionsFreebet = {
        id: getCellText(0),                    // ID
        name: getCellText(1),                  // Name
        startDate: getCellText(2),             // Start Date
        endDate: getCellText(3),               // End Date
        isThirdParty: parseBoolean(getCellText(4)),  // Is Third Party
        friendReferral: parseBoolean(getCellText(5)), // Friend Referral
        createAppUser: getCellText(6),         // Create App User
        createDate: getCellText(7),            // Create Date
        updateAppUser: getCellText(8),         // Update App User
        updateDate: getCellText(9),            // Update Date
        status: getCellText(10),               // Status
      };

      return freebet;
    } catch (error: any) {
      console.warn('Error parsing freebet row cells:', error.message);
      return null;
    }
  }
}