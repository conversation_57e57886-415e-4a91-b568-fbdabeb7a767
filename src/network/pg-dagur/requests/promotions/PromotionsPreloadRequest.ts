import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';

/**
 * Response from the promotions preload request containing dynamic JSF element IDs
 * @interface PromotionsPreloadRequestResponse
 */
export interface PromotionsPreloadRequestResponse {
  /** ViewState for subsequent requests */
  viewState?: string;
  /** Any dynamic form element IDs that might be needed for future operations */
  formElementIds?: Record<string, string>;
}

/**
 * Preload request to get initial promotions page state and dynamic element IDs
 * This is a simple preload request that loads the promotions.xhtml page
 * without any query parameters or complex extraction logic
 */
export class PromotionsPreloadRequest extends PGDagurRequest<PromotionsPreloadRequestResponse> {
  constructor() {
    super();
  }

  getPath(): string {
    return '/restricted/promotions.xhtml';
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(baseUrl: string): Record<string, string> {
    return {};
  }

  override validateResponse(html: string): PGDagurRequestResponse<PromotionsPreloadRequestResponse> {
    try {
      console.log('🔍 Processing promotions preload response...');
      console.log('📄 Response length:', html.length, 'characters');

      // Check for authentication redirect
      if (html.includes('redirect url="/login.xhtml"')) {
        console.error('❌ Authentication failed - redirected to login');
        return {
          success: false,
          message: 'Authentication failed - redirected to login page',
        };
      }

      // Extract ViewState for future requests
      const viewStateMatch = html.match(/name="javax\.faces\.ViewState"[^>]*value="([^"]+)"/);
      const viewState = viewStateMatch ? viewStateMatch[1] : '';

      console.log('✅ Promotions preload response processed successfully');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');

      return {
        success: true,
        data: {
          viewState: viewState || undefined,
          formElementIds: {}, // Can be extended in the future as needed
        },
        viewState,
      };
    } catch (error: any) {
      console.error('❌ Error processing promotions preload response:', error.message);
      return {
        success: false,
        message: `Failed to process promotions preload response: ${error.message}`,
      };
    }
  }
}