import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';

export type AccountingLoginResponse = string;

export class AccountingLoginRequest extends PGDagurRequest<AccountingLoginResponse, any> {
  getPath(): string {
    return `/restricted/cust-money-dep-withdraw-new.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(obj: string): PGDagurRequestResponse<AccountingLoginResponse> {
    let startIndex = obj.indexOf("topic: 'pg:auth'");
    if (startIndex === -1) {
      return {
        success: false,
        message: 'Failed to find auth topic in response',
      };
    }

    startIndex = obj.indexOf("token: '", startIndex);
    if (startIndex === -1) {
      return {
        success: false,
        message: 'Failed to find token in response',
      };
    }

    const token = obj.substring(startIndex + 8, obj.indexOf("'", startIndex + 8));

    return {
      success: true,
      data: token,
    };
  }
}
