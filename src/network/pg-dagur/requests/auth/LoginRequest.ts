import { XMLParser } from 'fast-xml-parser';

import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';
import { findJavaxElementIdBefore, findJavaxViewState } from '../../utils/javax';
import { isAsyncFunction } from 'util/types';

export interface LoginRequestOptions {
  username: string;
  password: string;
}

export type LoginResponse = {
  buttonId: string;
};

export class LoginRequest extends PGDagurRequest<LoginResponse, any> {
  constructor(private options: LoginRequestOptions) {
    super();
  }

  getPath(): string {
    return `/login.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      form: 'form',
      username: this.options.username,
      password: this.options.password,
      loginButton: '',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<LoginResponse> {
    console.log(xml);
    const viewState = findJavaxViewState(xml);
    if (!viewState) {
      return {
        success: false,
        message: 'Failed to find view state',
      };
    }

    const buttonId = findJavaxElementIdBefore(xml, '', '<span class="ui-button-text ui-c">Send</span>');
    if (!buttonId) {
      return {
        success: false,
        message: 'Failed to find button id',
      };
    }

    return {
      success: true,
      data: {
        buttonId,
      },
      viewState,
    };
  }
}
