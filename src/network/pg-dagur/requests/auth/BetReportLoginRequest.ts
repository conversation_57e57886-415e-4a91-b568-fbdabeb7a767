import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';

export type BetReportLoginResponse = string;

export class BetReportLoginRequest extends PGDagurRequest<BetReportLoginResponse, any> {
  getPath(): string {
    return `/restricted/new-bo-daily-bet-report.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(obj: string): PGDagurRequestResponse<BetReportLoginResponse> {
    let startIndex = obj.indexOf("topic: 'pg:auth'");

    if (startIndex === -1) {
      return {
        success: false,
        message: 'Failed to find auth topic in response',
      };
    }

    startIndex = obj.indexOf("token: '", startIndex);

    if (startIndex === -1) {
      return {
        success: false,
        message: 'Failed to find token in response',
      };
    }

    const tokenStart = startIndex + 8;
    const tokenEnd = obj.indexOf("'", tokenStart);
    const token = obj.substring(tokenStart, tokenEnd);

    return {
      success: true,
      data: token,
    };
  }
}
