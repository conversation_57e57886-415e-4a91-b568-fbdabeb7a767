import { XMLParser } from 'fast-xml-parser';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';

export interface LoginWithOtpRequestOptions {
  otp: string;
  buttonId: string;
}

export type LoginWithOtpResponse = null;

export class LoginWithOtpRequest extends PGDagurRequest<LoginWithOtpResponse, any> {
  constructor(private options: LoginWithOtpRequestOptions) {
    super();
  }

  getPath(): string {
    return `/login.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.source': this.options.buttonId,
      'javax.faces.partial.execute': '@all',
      'javax.faces.partial.render': 'form',
      'javax.faces.partial.ajax': 'true',
      [this.options.buttonId]: this.options.buttonId,
      form: 'form',
      onetimePassword: this.options.otp,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<LoginWithOtpResponse> {
    const parser = new XMLParser({ ignoreAttributes: false, attributeNamePrefix: '@' });
    const obj = parser.parse(xml);

    const partialResponse = obj['partial-response'];
    if (!partialResponse) {
      return {
        success: false,
        message: 'Missing partial response tag',
      };
    }

    const redirect = partialResponse['redirect'];
    if (!redirect) {
      return {
        success: false,
        message: 'Missing redirect tag',
      };
    }

    const url = redirect['@url'];
    if (!url) {
      return {
        success: false,
        message: 'Missing redirect url',
      };
    }

    if (url !== '/restricted/dashboard.xhtml') {
      return {
        success: false,
        message: `Unexpected redirect url: ${url}`,
      };
    }

    return {
      success: true,
      data: null,
    };
  }
}
