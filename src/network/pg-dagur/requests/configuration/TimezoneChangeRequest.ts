import { findJavaxElementIdBefore, findJavaxViewState } from '../../utils/javax';
import { HttpMethod, PGDagurRequest, PGDagurRequestResponse } from '../Request';

export interface TimezoneChangePreloadRequestResponse {
  timezoneSelectInputId: string;
}

export class TimezoneChangePreloadRequest extends PGDagurRequest<TimezoneChangePreloadRequestResponse, any> {
  getPath(): string {
    return `/restricted/dashboard.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(xml: string): PGDagurRequestResponse<TimezoneChangePreloadRequestResponse> {
    const timezoneSelectInputId = findJavaxElementIdBefore(xml, '', '<option value="GMT-12">GMT-12</option>');
    if (!timezoneSelectInputId) {
      return {
        success: false,
        message: 'Failed to find timezone select input id',
      };
    }

    const viewState = findJavaxViewState(xml);
    if (!viewState) {
      return {
        success: false,
        message: 'Failed to find view state',
      };
    }

    return {
      success: true,
      data: {
        timezoneSelectInputId,
      },
      viewState,
    };
  }
}

export interface TimezoneChangeRequestOptions {
  javax: TimezoneChangePreloadRequestResponse;
  // GMT, GMT+3, GMT-3
  timezone: string;
  traderId?: number;
}

export class TimezoneChangeRequest extends PGDagurRequest<null, any> {
  constructor(private options: TimezoneChangeRequestOptions) {
    super();
  }

  getPath(): string {
    return `/restricted/dashboard.xhtml`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      'javax.faces.partial.ajax': 'true',
      'javax.faces.source': this.options.javax.timezoneSelectInputId,
      'javax.faces.partial.execute': this.options.javax.timezoneSelectInputId,
      'javax.faces.partial.render': '@all',
      'javax.faces.behavior.event': 'change',
      'javax.faces.partial.event': 'change',
      menuForm1: 'menuForm1',
      'trader-selection_input': this.options.traderId ?? 15, // MAKROBET
      [`${this.options.javax.timezoneSelectInputId}_input`]: 'GMT',
      boLanguage_input: 'en',
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
