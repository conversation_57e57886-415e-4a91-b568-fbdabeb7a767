export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export abstract class PGDagurRequest<ResponseModel = any, Body = any> {
  abstract getPath(): string;

  getPreloadRequest(): PGDagurRequest | null {
    return null;
  }

  abstract getMethod(): HttpMethod;

  abstract getBody(): Body;

  abstract getHeaders(baseUrl: string): Record<string, string>;

  validateResponse(xml: string): PGDagurRequestResponse<ResponseModel> {
    if (1 == 1) {
      return {
        data: xml as any,
        success: true,
      };
    } else {
      return {
        message: 'Something went wrong',
        success: false,
      };
    }
  }
}

export type PGDagurRequestSuccessResponse<T = any> = {
  data: T;
  viewState?: string;
  success: true;
};

export type PGDagurRequestErrorResponse = {
  success: false;
  viewState?: string;
  message: string;
};

export type PGDagurRequestResponse<T> = PGDagurRequestSuccessResponse<T> | PGDagurRequestErrorResponse;
