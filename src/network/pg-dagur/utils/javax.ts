import { XMLParser } from 'fast-xml-parser';

export const findJavaxElementId = (source: string, idPrefix: string, after: string = '', skip: number = 0) => {
  let startIndex = 0;
  if (after !== '') {
    startIndex = source.indexOf(after);
  }

  if (startIndex === -1) {
    return null;
  }

  do {
    startIndex = source.indexOf(`id="${idPrefix}j_idt`, startIndex + after.length);
    if (startIndex === -1) {
      return null;
    }

    const endIndex = source.indexOf('"', startIndex + `id="${idPrefix}j_idt`.length);
    if (endIndex === -1) {
      return null;
    }

    const text = source.substring(startIndex + `id="${idPrefix}`.length, endIndex);
    if (/j_idt\d+$/.test(text)) {
      if (skip === 0) {
        return text;
      }

      skip--;
    }

    startIndex += `id="${idPrefix}`.length;
  } while (startIndex < source.length - 1);
};

export const findJavaxElementIdBefore = (source: string, idPrefix: string, before: string, skip: number = 0) => {
  let startIndex = source.indexOf(before);
  if (startIndex === -1) {
    return null;
  }

  do {
    startIndex = source.lastIndexOf(`id="${idPrefix}j_idt`, startIndex);
    if (startIndex === -1) {
      return null;
    }

    const endIndex = source.indexOf('"', startIndex + `id="${idPrefix}j_idt`.length);
    if (endIndex === -1) {
      return null;
    }

    const text = source.substring(startIndex + `id="${idPrefix}`.length, endIndex);
    if (/j_idt\d+$/.test(text)) {
      if (skip === 0) {
        return text;
      }

      skip--;
    }

    startIndex--;
  } while (startIndex > 0);
};

export const findJavaxElementIdWithPrefix = (
  source: string,
  idPrefix: string,
  after: string = '',
  skip: number = 0,
) => {
  let startIndex = 0;
  if (after !== '') {
    startIndex = source.indexOf(after);
  }

  if (startIndex === -1) {
    return null;
  }

  do {
    startIndex = source.indexOf(`id="${idPrefix}j_idt`, startIndex + after.length);
    if (startIndex === -1) {
      return null;
    }

    const endIndex = source.indexOf('"', startIndex + `id="${idPrefix}j_idt`.length);
    if (endIndex === -1) {
      return null;
    }

    const text = source.substring(startIndex + `id="`.length, endIndex);
    if (/j_idt\d+$/.test(text)) {
      if (skip === 0) {
        return text;
      }

      skip--;
    }

    startIndex += `id="${idPrefix}`.length;
  } while (startIndex < source.length - 1);
};

export const findJavaxViewState = (source: string, after: string = 'name="javax.faces.ViewState"') => {
  if (source.includes('partial-response')) {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@',
      unpairedTags: ['hr', 'br', 'link', 'meta', 'img'],
      stopNodes: ['*.pre', '*.script'],
      processEntities: true,
      htmlEntities: true,
    });
    const obj = parser.parse(source);

    const partialResponse = obj?.['partial-response'];
    if (!partialResponse) {
      return null;
    }

    const changes = partialResponse?.['changes'];
    if (!changes) {
      return null;
    }

    const update = changes['update']?.find((u: any) => u?.['@id']?.includes('javax.faces.ViewState'));
    if (!update) {
      return null;
    }

    return update['#text'] || update;
  } else {
    let startIndex = 0;
    if (after !== '') {
      startIndex = source.indexOf(after);
    }

    if (startIndex === -1) {
      return null;
    }

    startIndex = source.indexOf('value="', startIndex + after.length);
    if (startIndex === -1) {
      return null;
    }

    const endIndex = source.indexOf('"', startIndex + 'value="'.length);
    if (endIndex === -1) {
      return null;
    }

    return source.substring(startIndex + 'value="'.length, endIndex);
  }
};

/**
 * Find the customer details tab view ID
 */
export const findCustomerDetailsTabViewId = (source: string): string | null => {
  // Look for the main tabView component in customer details page
  const tabViewMatch = source.match(/id="(form:tabView[^"]*?)"/);
  if (tabViewMatch) {
    return tabViewMatch[1];
  }

  // Fallback to default pattern
  return 'form:tabView';
};

/**
 * Find customer details form element IDs (j_idt627, j_idt629, etc.)
 */
export const findCustomerDetailsFormElements = (source: string): { j_idt627: string; j_idt629: string } | null => {
  // Find j_idt627 - typically a checkbox input
  const j_idt627 = findJavaxElementId(source, 'form:tabView:', 'input');

  // Find j_idt629 - typically a collapsible panel
  const j_idt629 = findJavaxElementId(source, 'form:tabView:', 'collapsed');

  if (j_idt627 && j_idt629) {
    return {
      j_idt627: j_idt627.replace('form:tabView:', ''),
      j_idt629: j_idt629.replace('form:tabView:', ''),
    };
  }

  // Return default values if not found
  return {
    j_idt627: 'j_idt627',
    j_idt629: 'j_idt629',
  };
};

/**
 * Find the freebets tab ID within the customer details page
 */
export const findFreebetsTabId = (source: string): string | null => {
  // Look for customerFreebets tab reference
  const freebetsTabMatch = source.match(/id="([^"]*customerFreebets[^"]*?)"/);
  if (freebetsTabMatch) {
    return freebetsTabMatch[1];
  }

  // Fallback to expected pattern
  return 'form:tabView:customerFreebets';
};

/**
 * Find the freebet assignment dialog form elements
 */
export const findFreebetAssignmentFormElements = (
  source: string,
): {
  submitButtonId: string;
  freebetIdInputId: string;
  cleanupButtonId: string;
  cleanupInputId: string;
} => {
  // Find submit button ID - look for button with "Add" text and specific PrimeFaces patterns
  let submitButtonId = 'j_idt3613'; // Default fallback based on the provided HTML

  // Pattern 1: Look for button with "Add" text in span
  const submitButtonMatch = source.match(
    /<button[^>]+id="(j_idt\d+)"[^>]*type="submit"[^>]*>[\s\S]*?<span[^>]*>Add<\/span>/i,
  );
  if (submitButtonMatch && submitButtonMatch[1]) {
    submitButtonId = submitButtonMatch[1];
    console.log('✅ Found submit button ID (Add text pattern):', submitButtonId);
  } else {
    // Pattern 2: Look for button with PrimeFaces onclick containing 'saveAssignedFreeBetBtn'
    const pfSubmitButtonMatch = source.match(
      /<button[^>]+id="(j_idt\d+)"[^>]*onclick="[^"]*PF\('saveAssignedFreeBetBtn'\)[^"]*"/,
    );
    if (pfSubmitButtonMatch && pfSubmitButtonMatch[1]) {
      submitButtonId = pfSubmitButtonMatch[1];
      console.log('✅ Found submit button ID (PrimeFaces pattern):', submitButtonId);
    } else {
      // Pattern 3: Look for button with PrimeFaces.ab containing addFreebetDialogForm
      const abSubmitButtonMatch = source.match(
        /<button[^>]+id="(j_idt\d+)"[^>]*onclick="[^"]*PrimeFaces\.ab\(\{[^}]*p:'addFreebetDialogForm'[^}]*\}/,
      );
      if (abSubmitButtonMatch && abSubmitButtonMatch[1]) {
        submitButtonId = abSubmitButtonMatch[1];
        console.log('✅ Found submit button ID (PrimeFaces.ab pattern):', submitButtonId);
      } else {
        console.warn('⚠️ Could not find submit button ID, using fallback:', submitButtonId);
      }
    }
  }

  // Find freebet dropdown select ID - look for the ui-selectonemenu div container
  let freebetIdInputId = 'j_idt3608'; // Default fallback based on the provided HTML

  // Pattern 1: Look for div with ui-selectonemenu class containing select with freebet options
  const freebetSelectMatch = source.match(
    /<div[^>]+id="(j_idt\d+)"[^>]+class="[^"]*ui-selectonemenu[^"]*"[^>]*>[\s\S]*?<select[^>]*>[\s\S]*?Freebet[\s\S]*?<\/select>/i,
  );
  if (freebetSelectMatch && freebetSelectMatch[1]) {
    freebetIdInputId = freebetSelectMatch[1];
    console.log('✅ Found freebet dropdown ID (ui-selectonemenu pattern):', freebetIdInputId);
  } else {
    // Pattern 2: Look for select input with freebet options and extract container ID
    const altFreebetSelectMatch = source.match(
      /<select[^>]+id="(j_idt\d+)_input"[^>]+name="[^"]*"[^>]*>[\s\S]*?Freebet[\s\S]*?<\/select>/i,
    );
    if (altFreebetSelectMatch && altFreebetSelectMatch[1]) {
      // Extract the container div ID by removing _input suffix
      freebetIdInputId = altFreebetSelectMatch[1];
      console.log('✅ Found freebet dropdown ID (select input pattern):', freebetIdInputId);
    } else {
      // Pattern 3: Look for any select with onchange containing addFreebetDialogPanel
      const onchangeSelectMatch = source.match(
        /<select[^>]+id="(j_idt\d+)_input"[^>]*onchange="[^"]*addFreebetDialogPanel[^"]*"/,
      );
      if (onchangeSelectMatch && onchangeSelectMatch[1]) {
        freebetIdInputId = onchangeSelectMatch[1];
        console.log('✅ Found freebet dropdown ID (onchange pattern):', freebetIdInputId);
      } else {
        console.warn('⚠️ Could not find freebet dropdown ID, using fallback:', freebetIdInputId);
      }
    }
  }

  // For cleanup, we use the same IDs as the assignment
  const cleanupButtonId = submitButtonId;
  const cleanupInputId = freebetIdInputId;

  console.log('🔍 Extracted freebet assignment form elements:', {
    submitButtonId,
    freebetIdInputId,
    cleanupButtonId,
    cleanupInputId,
  });

  return {
    submitButtonId,
    freebetIdInputId,
    cleanupButtonId,
    cleanupInputId,
  };
};
