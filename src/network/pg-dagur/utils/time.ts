const pad = (num: number) => String(num).padStart(2, '0');

export const formatDate = (date?: Date | null, format: 'date' | 'datetime' = 'datetime') => {
  if (!date) {
    return '';
  }

  const day = pad(date.getUTCDate());
  const month = pad(date.getUTCMonth() + 1); // Months are 0-based
  const year = date.getUTCFullYear();

  const hours = pad(date.getUTCHours());
  const minutes = pad(date.getUTCMinutes());
  const seconds = pad(date.getUTCSeconds());

  switch (format) {
    case 'date': {
      return `${day}.${month}.${year}`;
    }
    default: {
      return `${day}.${month}.${year} ${hours}:${minutes}:${seconds}`;
    }
  }
};
