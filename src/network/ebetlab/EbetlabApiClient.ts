import fetch from 'node-fetch';
import { TOTP } from 'totp-generator';

import { AuthData } from '@/types/auth';
import { calculateXFingerprint, cfInit, getAuthSignature, getSignedChallenge } from '@/utils/authUtils';
import { EbetlabRequest, EbetlabResponse } from './requests/Request';
import { cache } from '@/utils/cache';
import { LoginRequest } from './requests/auth/LoginRequest';

const USER_AGENT =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36';
const REFERER = 'https://studio.ebetlab.com/';
const ORIGIN = 'https://studio.ebetlab.com';
const BASE_URL = 'https://service.ebetlab.com/api';

export class EbetlabApiClient {
  private authData: AuthData | null = null;

  protected async initAuthData() {
    let authData = cache.get('ebetlab:auth_data') as AuthData;
    if (!authData) {
      // Step 1: Get cf_clearance
      console.log('📡 Getting cf_clearance...');
      const cf_clearance = await cfInit();
      console.log('✅ cf_clearance obtained');

      // Step 2: Calculate X-Fingerprint
      console.log('🔍 Calculating X-Fingerprint...');
      const x_fingerprint = calculateXFingerprint();
      console.log('✅ X-Fingerprint calculated');

      // Step 3: Get signed_challenge
      console.log('🔑 Getting signed_challenge...');
      const signed_challenge = await getSignedChallenge(cf_clearance, x_fingerprint);
      console.log('✅ signed_challenge obtained');

      authData = { cf_clearance, x_fingerprint, signed_challenge };
      cache.set('ebetlab:auth_data', authData);
    }

    return authData;
  }

  async makeRequest<ResponseModel extends any, Request extends EbetlabRequest<ResponseModel>>(
    request: Request,
    additionalHeaders: Record<string, any> = {},
    retries = 0,
  ): Promise<ReturnType<Request['validateResponse']>> {
    try {
      this.authData = await this.initAuthData();

      if (!this.authData) {
        throw new Error('Failed to initialize authentication data');
      }

      const url = `${BASE_URL}${request.getPath()}`;

      console.log('🚀 Making authenticated request...');
      console.log(`📍 URL: ${request.getMethod()} ${url}`);

      // Get auth signature for the request
      const authSignature = await getAuthSignature(
        this.authData.cf_clearance,
        this.authData.signed_challenge,
        this.authData.x_fingerprint,
      );

      const headers: Record<string, string> = {
        'X-Fingerprint': this.authData.x_fingerprint,
        'X-Auth-Signature': authSignature,
        'User-Agent': USER_AGENT,
        Referer: REFERER,
        Origin: ORIGIN,
        Cookie: `cf_clearance=${this.authData.cf_clearance}; signed_challenge=${this.authData.signed_challenge}`,
        ...request.getHeaders(),
        ...additionalHeaders,
      };

      if (['POST', 'PATCH', 'PUT'].includes(request.getMethod())) {
        headers['Content-Type'] = 'application/json';
      }

      // Log request details
      console.log('📤 Request Headers:', JSON.stringify(headers, null, 2));
      const body = request.getBody();
      if (body) {
        console.log('📤 Request Body:', JSON.stringify(body, null, 2));
      }

      const response = await fetch(url, {
        method: request.getMethod(),
        headers,
        body: body ? JSON.stringify(body) : undefined,
      });

      // Log response details
      console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
      console.log('📥 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

      if (!response.ok) {
        const errorText = await response.text();

        if (errorText.includes('signed') && retries < 3) {
          this.authData = null;
          cache.delete('ebetlab:auth_data');

          return this.makeRequest(request, additionalHeaders, retries + 1);
        }

        console.error('❌ Response Error Body:', errorText);
        throw new Error(`Request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = (await response.json()) as any;
      console.log('📥 Response Body:', JSON.stringify(data, null, 2).slice(0, 512));

      if ('success' in data && data.success !== true) {
        console.error('❌ API returned success: false');
        throw new Error(`Request failed: ${JSON.stringify(data)}`);
      }

      console.log('✅ Request completed successfully');
      return request.validateResponse(data) as ReturnType<Request['validateResponse']>;
    } catch (error) {
      if ((error as Error).message.includes('signed') && retries < 3) {
        this.authData = null;
        cache.delete('ebetlab:auth_data');

        return this.makeRequest(request, additionalHeaders, retries + 1);
      }

      console.error('❌ Authenticated request failed:', error);
      throw error;
    }
  }

  /**
   * Make an authenticated request to EbetLab API
   */
  async makeAuthenticatedRequest<ResponseModel extends any, Request extends EbetlabRequest<ResponseModel>>(
    request: Request,
    authorization: string,
    retries = 0,
  ): Promise<ReturnType<Request['validateResponse']>> {
    return this.makeRequest(
      request,
      {
        Authorization: authorization,
      },
      retries,
    );
  }

  async login(username: string, password: string, otp: string) {
    const request1 = new LoginRequest({
      username,
      password,
    });

    const response1 = await this.makeRequest(request1, {});
    if (!response1.success) {
      throw new Error(`Failed to login: ${response1.message}`);
    }

    if ('type' in response1.data) {
      const request2 = new LoginRequest({
        username,
        password,
        otp,
        secret: response1.data.secret,
      });

      // Required! (use secret not before X seconds)
      await new Promise((resolve) => setTimeout(resolve, 3 * 1000));

      const response2 = await this.makeRequest(request2, {});
      if (!response2.success) {
        throw new Error(`Failed to login: ${response2.message}`);
      }

      if ('type' in response2.data) {
        throw new Error(`Failed to login: asked for otp again`);
      }

      return response2.data;
    } else {
      return response1.data;
    }
  }
}

class AdminEbetlabApiClient {
  private client: EbetlabApiClient;
  private accessToken: string | null = null;
  private lastLoginTime: number = 0;
  private readonly LOGIN_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours in milliseconds

  constructor(client: EbetlabApiClient) {
    this.client = client;
  }

  /**
   * Generate TOTP from secret with 30-second interval
   */
  private generateTOTP(secret: string): string {
    try {
      console.log(`🔐 Generating TOTP from secret: ${secret.substring(0, 4)}...`);

      const otpCode = TOTP.generate(secret, {
        period: 30,
      }).otp;

      console.log(`🔐 OTP Generated: ${otpCode}`);
      return otpCode;
    } catch (error) {
      console.error(`❌ TOTP generation failed:`, error);
      throw error;
    }
  }

  /**
   * Check if login is needed (6 hours have passed or no token exists)
   */
  private needsLogin(): boolean {
    const now = Date.now();
    const timeSinceLastLogin = now - this.lastLoginTime;

    if (!this.accessToken) {
      console.log('🔐 No access token found, login required');
      return true;
    }

    if (timeSinceLastLogin >= this.LOGIN_INTERVAL) {
      console.log(`🔐 ${Math.floor(timeSinceLastLogin / (60 * 60 * 1000))} hours since last login, re-login required`);
      return true;
    }

    const hoursRemaining = Math.floor((this.LOGIN_INTERVAL - timeSinceLastLogin) / (60 * 60 * 1000));
    const minutesRemaining = Math.floor(((this.LOGIN_INTERVAL - timeSinceLastLogin) % (60 * 60 * 1000)) / (60 * 1000));
    console.log(`🔐 Token still valid for ${hoursRemaining}h ${minutesRemaining}m`);
    return false;
  }

  /**
   * Perform login with OTP secret and store token with timestamp
   */
  async loginWithOtpSecret(username: string, password: string, otpSecret: string) {
    console.log('🔐 Starting admin login process...');

    // Generate OTP from secret
    const otp = this.generateTOTP(otpSecret);
    console.log(`🔑 Using OTP: ${otp} for admin login`);

    const data = await this.client.login(username, password, otp);
    this.accessToken = data.token;
    this.lastLoginTime = Date.now();

    console.log('✅ Admin login successful, token stored');
    console.log(`🕐 Next login required at: ${new Date(this.lastLoginTime + this.LOGIN_INTERVAL).toISOString()}`);
  }

  /**
   * Ensure valid login before making requests
   */
  private async ensureValidLogin(): Promise<void> {
    if (this.needsLogin()) {
      const username = process.env['API_USERNAME'] || process.env['EBLB_USERNAME'] || '';
      const password = process.env['API_PASSWORD'] || process.env['EBLB_PASSWORD'] || '';
      const otpSecret = process.env['API_OTP_SECRET'] || process.env['EBLB_OTP_SECRET'] || '';
      console.log(username, password, otpSecret);
      if (!username || !password || !otpSecret) {
        throw new Error(
          'Missing admin credentials in environment variables (API_USERNAME, API_PASSWORD, API_OTP_SECRET)',
        );
      }

      await this.loginWithOtpSecret(username, password, otpSecret);
    }
  }

  /**
   * Make authenticated request with automatic login management
   */
  async makeAuthenticatedRequest<ResponseModel extends unknown, Request extends EbetlabRequest<ResponseModel>>(
    request: Request,
    authorization?: string,
    retries = 0,
  ): Promise<ReturnType<Request['validateResponse']>> {
    try {
      // Ensure we have a valid login before making the request
      await this.ensureValidLogin();

      if (!this.accessToken) {
        throw new Error('Failed to obtain access token');
      }

      // Make the request with the stored token
      return await this.client.makeAuthenticatedRequest(request, `Bearer ${this.accessToken}`, retries);
    } catch (error: any) {
      // Handle 401 errors by forcing re-login
      if (error.message.includes('401') && retries < 3) {
        console.log('🔄 401 error detected, forcing re-login...');
        this.accessToken = null;
        this.lastLoginTime = 0;

        return this.makeAuthenticatedRequest(request, authorization, retries + 1);
      }

      throw error;
    }
  }
}

export const ebetlabApiClient = new EbetlabApiClient();
export const adminEbetlabApiClient = new AdminEbetlabApiClient(ebetlabApiClient);
