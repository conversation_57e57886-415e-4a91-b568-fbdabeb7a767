import { Currency } from './currency';
import { Operator } from './operator';

export type Bonus = {
  id: number;
  operator_id: number;
  is_active: boolean;
  name: string;
  description: string;
  currency_id: number;
  model: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  phone_verification: null;
  email_verification: null;
  request_able: boolean;
  disable_withdraw: boolean;
  cancel_able: boolean;

  operator?: Operator;
  currency?: Currency;
};
