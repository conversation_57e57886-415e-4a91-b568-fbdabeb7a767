import { EbetlabRequest, HttpMethod } from './Request';

export interface AffiliateSetRequestOptions {
  id: number;
  code: string;
}

export type AffiliateSetResponse = {
  data: string;
  status: number;
  success: boolean;
};

export class AffiliateSetRequest extends EbetlabRequest<AffiliateSetResponse, any> {
  constructor(private options: AffiliateSetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/affiliates/set/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      code: this.options.code,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
