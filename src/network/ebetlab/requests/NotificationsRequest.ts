import { EbetlabRequest, HttpMethod } from './Request';

export interface NotificationsRequestOptions {
  page: number;
  limit: number;
  customer_id: string;
}

export interface NotificationData {
  currency: string;
  amount: string;
}

export interface Notification {
  id: number;
  customer_id: number;
  type: string;
  lang_key: string;
  timestamp: string;
  data: NotificationData;
}

export interface NotificationsResponseData {
  total: number;
  data: Notification[];
}

export type NotificationsResponse = {
  data: NotificationsResponseData;
  status: number;
  success: boolean;
};

export class NotificationsRequest extends EbetlabRequest<NotificationsResponse, any> {
  constructor(private options: NotificationsRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/notifications/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      limit: this.options.limit,
      page: this.options.page,
      customer_id: this.options.customer_id,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
