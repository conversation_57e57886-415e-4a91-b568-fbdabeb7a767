import { EbetlabRequest, HttpMethod } from '../Request';

export interface CssSettingsListRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: number | null;
  type?: string | null;
  status_id?: number | null;
  tx?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type CssSettingsListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class CssSettingsListRequest extends EbetlabRequest<CssSettingsListResponse, any> {
  constructor(private options: CssSettingsListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/css-settings/index2/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      username: this.options.username || null,
      currency: this.options.currency || null,
      operator_id: this.options.operator_id || null,
      type: this.options.type || null,
      status_id: this.options.status_id || null,
      tx: this.options.tx || null,
      from: this.options.from || null,
      to: this.options.to || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
