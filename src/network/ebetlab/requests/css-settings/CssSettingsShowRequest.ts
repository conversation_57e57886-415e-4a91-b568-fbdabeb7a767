import { EbetlabRequest, HttpMethod } from '../Request';

export interface CssSettingsShowRequestOptions {
  id: string;
}

export type CssSettingsShowResponse = {
  data: {
    id: number;
    name: string;
    theme: string;
    merchant_id: number;
    website_id: number;
    operator_id: number;
    path: string;
    last_update: number;
    is_active: boolean;
  };
  status: number;
  success: boolean;
};

export class CssSettingsShowRequest extends EbetlabRequest<CssSettingsShowResponse, any> {
  constructor(private options: CssSettingsShowRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/css-settings/show/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
