import { EbetlabRequest, HttpMethod } from '../Request';

export interface VerificationCodeListRequestOptions {
  page: number;
  limit: number;
  // Filter options based on the task specification
  username?: string | null;
  from?: number | null;
  to?: number | null;
  rt?: number;
}

export type VerificationCodeListResponse = {
  data: {
    total: number;
    data: Array<{
      id: number;
      code: string;
      customer_id: number;
      type: string;
      timestamp: string;
      used_at: string | null;
      customer: {
        id: number;
        username: string;
        email: string;
        rank: string;
        masked_username: string;
        last_action: number;
      };
    }>;
  };
  status: number;
  success: boolean;
};

export class VerificationCodeListRequest extends EbetlabRequest<VerificationCodeListResponse, any> {
  constructor(private options: VerificationCodeListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/verification-codes/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      username: this.options.username,
      from: this.options.from,
      to: this.options.to,
      page: this.options.page,
      limit: this.options.limit,
      rt: this.options.rt || Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
