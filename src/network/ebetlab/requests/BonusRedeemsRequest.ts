import { EbetlabRequest, HttpMethod } from './Request';

export interface BonusRedeemsRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  is_active?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  amount_min?: string | null;
  amount_max?: string | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  type?: string | null;
}

export type BonusRedeemsResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusRedeemsRequest extends EbetlabRequest<BonusRedeemsResponse, any> {
  constructor(private options: BonusRedeemsRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-redeems/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      username: this.options.username || null,
      currency: this.options.currency || null,
      operator_id: this.options.operator_id || null,
      is_active: this.options.is_active || null,
      usd_min: this.options.usd_min || null,
      usd_max: this.options.usd_max || null,
      amount_min: this.options.amount_min || null,
      amount_max: this.options.amount_max || null,
      from: this.options.from || null,
      to: this.options.to || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      customer_id: this.options.customer_id || null,
      type: this.options.type || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
