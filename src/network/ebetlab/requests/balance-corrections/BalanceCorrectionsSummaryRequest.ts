import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionsSummaryRequestOptions {
  username?: string | null;
}

export type BalanceCorrectionsSummaryResponse = {
  data: {
    deposit: {
      deposit: string;
      total: number;
    };
    withdraw: {
      withdraw: string;
      total: number;
    };
  };
  status: number;
  success: boolean;
};

export class BalanceCorrectionsSummaryRequest extends EbetlabRequest<BalanceCorrectionsSummaryResponse, any> {
  constructor(private options: BalanceCorrectionsSummaryRequestOptions) {
    super();
  }

  getPath(): string {
    return '/operator/balance-corrections/summary';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      username: this.options.username || null,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
