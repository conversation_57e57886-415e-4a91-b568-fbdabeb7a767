import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionsListRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  username?: string | null;
  model?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  way?: string | null;
  note?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BalanceCorrectionsListResponse = {
  data: {
    total: number;
    data: Array<{
      id: number;
      model: string;
      customer_id: number;
      operator_id: number | null;
      reason: string | null;
      is_active: boolean;
      currency: string;
      wallet_id: number;
      merchant_id: number;
      website_id: number;
      amount: string;
      usd_amount: string;
      timestamp: number;
      before_balance: string;
      way: string;
      after_balance: string;
      completed_at: number | null;
      required_wager_currency: string;
      note: string;
      wagered: string;
      wagered_currency: string;
      required_wager: string;
      completed: boolean;
      customer: {
        id: number;
        username: string;
        email: string;
        rank: string;
        masked_username: string;
        last_action: number;
      };
      operator: any | null;
    }>;
  };
  status: number;
  success: boolean;
};

export class BalanceCorrectionsListRequest extends EbetlabRequest<BalanceCorrectionsListResponse, any> {
  constructor(private options: BalanceCorrectionsListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-corrections/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      username: this.options.username || null,
      model: this.options.model || null,
      currency: this.options.currency || null,
      operator_id: this.options.operator_id || null,
      way: this.options.way || null,
      note: this.options.note || null,
      usd_min: this.options.usd_min || null,
      usd_max: this.options.usd_max || null,
      from: this.options.from || null,
      to: this.options.to || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
