import { EbetlabRequest, HttpMethod } from '../Request';

export interface DiscountsSummaryRequestOptions {
  username?: string | null;
  currency?: string | null;
  from?: number | null;
  to?: number | null;
  page?: number;
  limit?: number;
}

export type DiscountsSummaryResponse = {
  data: Array<{
    sum: string;
    count: number;
    code: string;
    depositAmount: string;
  }>;
  status: number;
  success: boolean;
};

export class DiscountsSummaryRequest extends EbetlabRequest<DiscountsSummaryResponse, any> {
  constructor(private options: DiscountsSummaryRequestOptions) {
    super();
  }

  getPath(): string {
    return '/operator/discounts/summary';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      username: this.options.username || null,
      currency: this.options.currency || null,
      from: this.options.from || null,
      to: this.options.to || null,
      page: this.options.page || 1,
      limit: this.options.limit || 20,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
