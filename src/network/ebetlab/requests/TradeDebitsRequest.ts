import { EbetlabRequest, HttpMethod } from './Request';

export interface TradeDebitsRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  game?: string | null;
  provider_id?: string | null;
  type?: string | null;
  status?: string | null;
  currency?: string | null;
  wallet_currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | null;
  to?: number | null;
  amount_min?: string | null;
  amount_max?: string | null;
  multiplier_min?: string | null;
  multiplier_max?: string | null;
  income_min?: string | null;
  income_max?: string | null;
  net_min?: string | null;
  net_max?: string | null;
  income_usd_min?: string | null;
  income_usd_max?: string | null;
  wallet_amount_min?: string | null;
  wallet_amount_max?: string | null;
  win_wallet_amount_min?: string | null;
  win_wallet_amount_max?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
}

export type TradeDebitsResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class TradeDebitsRequest extends EbetlabRequest<TradeDebitsResponse, any> {
  constructor(private options: TradeDebitsRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/trade-debits/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      game: this.options.game || null,
      provider_id: this.options.provider_id || null,
      type: this.options.type || null,
      status: this.options.status || null,
      currency: this.options.currency || null,
      wallet_currency: this.options.wallet_currency || null,
      usd_min: this.options.usd_min || null,
      usd_max: this.options.usd_max || null,
      from: this.options.from || null,
      to: this.options.to || null,
      amount_min: this.options.amount_min || null,
      amount_max: this.options.amount_max || null,
      multiplier_min: this.options.multiplier_min || null,
      multiplier_max: this.options.multiplier_max || null,
      income_min: this.options.income_min || null,
      income_max: this.options.income_max || null,
      net_min: this.options.net_min || null,
      net_max: this.options.net_max || null,
      income_usd_min: this.options.income_usd_min || null,
      income_usd_max: this.options.income_usd_max || null,
      wallet_amount_min: this.options.wallet_amount_min || null,
      wallet_amount_max: this.options.wallet_amount_max || null,
      win_wallet_amount_min: this.options.win_wallet_amount_min || null,
      win_wallet_amount_max: this.options.win_wallet_amount_max || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      customer_id: this.options.customer_id || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
