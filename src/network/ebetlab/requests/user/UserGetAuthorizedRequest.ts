import { EbetlabRequest, HttpMethod } from '../Request';

export interface UserGetAuthorizedRequestOptions {
  // No specific options needed for this request
}

export type UserGetAuthorizedResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class UserGetAuthorizedRequest extends EbetlabRequest<UserGetAuthorizedResponse, any> {
  constructor(private options: UserGetAuthorizedRequestOptions = {}) {
    super();
  }

  getPath(): string {
    return `/operator/user`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
