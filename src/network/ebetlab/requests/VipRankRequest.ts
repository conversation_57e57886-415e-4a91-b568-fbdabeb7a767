import { EbetlabRequest, HttpMethod } from './Request';

export interface VipRankRequestOptions {
  slug: string;
  add_gift: boolean;
  customer_id: string;
}

export type VipRankResponse = {
  data: boolean;
  status: number;
  success: boolean;
};

export class VipRankRequest extends EbetlabRequest<VipRankResponse, any> {
  constructor(private options: VipRankRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/vip-state/rank/${this.options.slug}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      slug: this.options.slug,
      add_gift: this.options.add_gift,
      customer_id: this.options.customer_id,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
