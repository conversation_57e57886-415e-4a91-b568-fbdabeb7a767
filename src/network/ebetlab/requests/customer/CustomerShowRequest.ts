import { EbetlabRequest, HttpMethod } from '../Request';

export interface CustomerShowRequestOptions {
  id: string;
}

export type CustomerShowResponse = {
  data: {
    username?: string;
    [key: string]: any;
  };
  status: number;
  success: boolean;
};

export class CustomerShowRequest extends EbetlabRequest<CustomerShowResponse, any> {
  constructor(private options: CustomerShowRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/customers/show/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
