import { EbetlabRequest, HttpMethod } from '../Request';

export interface WelcomeBonusGetRequestOptions {
  id: number;
}

export type WelcomeBonusGetResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class WelcomeBonusGetRequest extends EbetlabRequest<WelcomeBonusGetResponse, any> {
  constructor(private options: WelcomeBonusGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/welcome-bonuses/detail/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
