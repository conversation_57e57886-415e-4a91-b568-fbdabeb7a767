import { EbetlabRequest, HttpMethod } from '../Request';

export interface WelcomeBonusCreateRequestOptions {
  code: string;
  currency_code: string;
  usd_amount: string;
  from: number;
  to: number;
  required_wager: string;
  amount: string;
  required_wager_currency: string;
  total: string;
  ref_code: string;
}

export type WelcomeBonusCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class WelcomeBonusCreateRequest extends EbetlabRequest<WelcomeBonusCreateResponse, any> {
  constructor(private options: WelcomeBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/welcome-bonuses/store`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      code: this.options.code,
      currency_code: this.options.currency_code,
      usd_amount: this.options.usd_amount,
      from: this.options.from,
      to: this.options.to,
      required_wager: this.options.required_wager,
      amount: this.options.amount,
      required_wager_currency: this.options.required_wager_currency,
      total: this.options.total,
      ref_code: this.options.ref_code,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
