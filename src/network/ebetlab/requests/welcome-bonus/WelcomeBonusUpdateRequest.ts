import { EbetlabRequest, HttpMethod } from '../Request';

export interface WelcomeBonusUpdateRequestOptions {
  id: number;
  code: string;
  currency_code: string;
  currency: string;
  total: number;
  amount: string;
  usd_amount: string;
  ref_code: string;
  required_wager: string;
  required_wager_currency: string;
  from: number;
  to: number;
}

export type WelcomeBonusUpdateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class WelcomeBonusUpdateRequest extends EbetlabRequest<WelcomeBonusUpdateResponse, any> {
  constructor(private options: WelcomeBonusUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/welcome-bonuses/update/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      code: this.options.code,
      currency_code: this.options.currency_code,
      currency: this.options.currency,
      total: this.options.total,
      amount: this.options.amount,
      usd_amount: this.options.usd_amount,
      ref_code: this.options.ref_code,
      required_wager: this.options.required_wager,
      required_wager_currency: this.options.required_wager_currency,
      from: this.options.from,
      to: this.options.to,
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
