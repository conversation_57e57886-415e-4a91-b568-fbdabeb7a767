import { EbetlabRequest, HttpMethod } from '../Request';

export interface WelcomeBonusCancellationCreateRequestOptions {
  id: number;
}

export type WelcomeBonusCancellationCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class WelcomeBonusCancellationCreateRequest extends EbetlabRequest<WelcomeBonusCancellationCreateResponse, any> {
  constructor(private options: WelcomeBonusCancellationCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/welcome-bonuses/deactivate/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
