import { EbetlabRequest, HttpMethod } from '../Request';

export interface WelcomeBonusListRequestOptions {
  page: number;
  limit: number;
  // Add common filter options that might be used
  code?: string | null;
  currency_code?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type WelcomeBonusListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class WelcomeBonusListRequest extends EbetlabRequest<WelcomeBonusListResponse, any> {
  constructor(private options: WelcomeBonusListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/welcome-bonuses/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      code: this.options.code,
      currency_code: this.options.currency_code,
      from: this.options.from,
      to: this.options.to,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
