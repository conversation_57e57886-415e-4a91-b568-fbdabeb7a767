import { EbetlabRequest, HttpMethod } from '../Request';

export interface WelcomeBonusDeleteRequestOptions {
  id: number;
}

export type WelcomeBonusDeleteResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class WelcomeBonusDeleteRequest extends EbetlabRequest<WelcomeBonusDeleteResponse, any> {
  constructor(private options: WelcomeBonusDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/welcome-bonuses/delete/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
