import { EbetlabRequest, HttpMethod } from '../Request';

export interface ChatBlacklistListRequestOptions {
  page: number;
  limit: number;
  // Filter options based on the task specification
  id?: number | null;
  username?: string | null;
  name?: string | null;
  verification_level?: number | null;
  email?: string | null;
  ip?: string | null;
  surname?: string | null;
  status_id?: number | null;
  operator_id?: number | null;
  registration_country?: string | null;
  language?: string | null;
  rank?: string | null;
  register_start?: string | null;
  register_end?: string | null;
  first_deposit_start?: string | null;
  first_deposit_end?: string | null;
  last_deposit_start?: string | null;
  last_deposit_end?: string | null;
  last_login_start?: string | null;
  last_login_end?: string | null;
  total_reload_min?: number | null;
  total_reload_max?: number | null;
  total_rain_min?: number | null;
  total_rain_max?: number | null;
  total_deposit_greater?: number | null;
  total_deposit_lower?: number | null;
  total_withdraw_greater?: number | null;
  total_withdraw_lower?: number | null;
  total_bonus_drop_min?: number | null;
  total_bonus_drop_max?: number | null;
  total_turnover_greater?: number | null;
  total_turnover_lower?: number | null;
  net_percentage_min?: number | null;
  net_percentage_max?: number | null;
  rakebackMin?: number | null;
  rakebackMax?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  rt?: number;
}

export type ChatBlacklistListResponse = {
  data: {
    total: number;
    data: Array<{
      id: number;
      email: string;
      username: string;
      last_online_at: number;
      last_ip: string;
      registration_country: string;
      registration_ts: number;
      operator_id: number | null;
      status_id: number;
      is_public: boolean;
      lang: string;
      rank: string;
      rank_percentage: string;
      total_turnover: string;
      next_wager_limit: string;
      chat_muted_at: number | null;
      masked_username: string;
      last_action: number | null;
      profile: {
        id: number;
        name: string;
        surname: string;
        customer_id: number;
        verification_level: number;
      };
      last_deposit: {
        id: number;
        customer_id: number;
        timestamp: number;
        amount: string;
        currency: string;
        type: number;
        provider: string;
        payment_provider: string | null;
      } | null;
      first_deposit: {
        id: number;
        customer_id: number;
        timestamp: number;
        amount: string;
        currency: string;
        type: number;
        provider: string;
        payment_provider: string | null;
      } | null;
      summary: {
        id: number;
        customer_id: number;
        total_in_usd: string;
        total_out_usd: string;
        percentage: string;
        total_rakeback_usd: string;
        total_reload_usd: string;
        total_bonus_drop_usd: string;
        total_rain_usd: string;
      };
      status: {
        id: number;
        name: string;
      };
      operator: any | null;
      blacklists: any[];
    }>;
  };
  status: number;
  success: boolean;
};

export class ChatBlacklistListRequest extends EbetlabRequest<ChatBlacklistListResponse, any> {
  constructor(private options: ChatBlacklistListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/customers/chat/blacklist/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username,
      name: this.options.name,
      verification_level: this.options.verification_level,
      email: this.options.email,
      ip: this.options.ip,
      surname: this.options.surname,
      status_id: this.options.status_id,
      operator_id: this.options.operator_id,
      registration_country: this.options.registration_country,
      language: this.options.language,
      rank: this.options.rank,
      register_start: this.options.register_start,
      register_end: this.options.register_end,
      first_deposit_start: this.options.first_deposit_start,
      first_deposit_end: this.options.first_deposit_end,
      last_deposit_start: this.options.last_deposit_start,
      last_deposit_end: this.options.last_deposit_end,
      last_login_start: this.options.last_login_start,
      last_login_end: this.options.last_login_end,
      total_reload_min: this.options.total_reload_min,
      total_reload_max: this.options.total_reload_max,
      total_rain_min: this.options.total_rain_min,
      total_rain_max: this.options.total_rain_max,
      total_deposit_greater: this.options.total_deposit_greater,
      total_deposit_lower: this.options.total_deposit_lower,
      total_withdraw_greater: this.options.total_withdraw_greater,
      total_withdraw_lower: this.options.total_withdraw_lower,
      total_bonus_drop_min: this.options.total_bonus_drop_min,
      total_bonus_drop_max: this.options.total_bonus_drop_max,
      total_turnover_greater: this.options.total_turnover_greater,
      total_turnover_lower: this.options.total_turnover_lower,
      net_percentage_min: this.options.net_percentage_min,
      net_percentage_max: this.options.net_percentage_max,
      rakebackMin: this.options.rakebackMin,
      rakebackMax: this.options.rakebackMax,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
      rt: this.options.rt || Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
