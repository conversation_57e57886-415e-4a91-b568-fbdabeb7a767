import { EbetlabRequest, HttpMethod } from '../Request';

export interface SelfExclusionListRequestOptions {
  page: number;
  limit: number;
  // Filter options based on the task specification
  id?: number | null;
  username?: string | null;
  step?: number | null;
  is_active?: number | null;
  from?: string | null;
  to?: string | null;
  rt?: number;
}

export type SelfExclusionListResponse = {
  data: {
    total: number;
    data: Array<{
      id: number;
      customer_id: number;
      timestamp: number;
      excluded_till: number;
      step: number;
      is_active: number;
      customer: {
        id: number;
        username: string;
        masked_username: string;
        last_action: number;
      };
    }>;
  };
  status: number;
  success: boolean;
};

export class SelfExclusionListRequest extends EbetlabRequest<SelfExclusionListResponse, any> {
  constructor(private options: SelfExclusionListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/self-exclusions/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username,
      step: this.options.step,
      is_active: this.options.is_active,
      from: this.options.from,
      to: this.options.to,
      page: this.options.page,
      limit: this.options.limit,
      rt: this.options.rt || Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
