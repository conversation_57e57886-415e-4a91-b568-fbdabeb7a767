import { EbetlabRequest, HttpMethod } from './Request';

export interface TipsRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  sender?: string | null;
  taker?: string | null;
  currency?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
}

export type TipsResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class TipsRequest extends EbetlabRequest<TipsResponse, any> {
  constructor(private options: TipsRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/tips/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      sender: this.options.sender || null,
      taker: this.options.taker || null,
      currency: this.options.currency || null,
      usd_min: this.options.usd_min || null,
      usd_max: this.options.usd_max || null,
      from: this.options.from || null,
      to: this.options.to || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      customer_id: this.options.customer_id || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
