import { EbetlabRequest, HttpMethod } from './Request';

export interface SessionsRequestOptions {
  page: number;
  limit: number;
  from?: number | null;
  to?: number | null;
  customer_id?: string | null;
}

export type SessionsResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class SessionsRequest extends EbetlabRequest<SessionsResponse, any> {
  constructor(private options: SessionsRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/sessions/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      from: this.options.from || null,
      to: this.options.to || null,
      customer_id: this.options.customer_id || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
