import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropRedeemListRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  from?: string | null;
  to?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
}

export type BonusDropRedeemListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class BonusDropRedeemListRequest extends EbetlabRequest<BonusDropRedeemListResponse, any> {
  constructor(private options: BonusDropRedeemListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/redeems/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username,
      currency: this.options.currency,
      from: this.options.from,
      to: this.options.to,
      usd_min: this.options.usd_min,
      usd_max: this.options.usd_max,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
