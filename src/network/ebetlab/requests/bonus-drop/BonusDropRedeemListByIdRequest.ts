import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropRedeemListByIdRequestOptions {
  id: number;
  page: number;
  limit: number;
  // Add common filter options that might be used for redeems
  username?: string | null;
  currency?: string | null;
  status?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BonusDropRedeemListByIdResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class BonusDropRedeemListByIdRequest extends EbetlabRequest<BonusDropRedeemListByIdResponse, any> {
  constructor(private options: BonusDropRedeemListByIdRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/show/${this.options.id}/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username,
      currency: this.options.currency,
      status: this.options.status,
      from: this.options.from,
      to: this.options.to,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
