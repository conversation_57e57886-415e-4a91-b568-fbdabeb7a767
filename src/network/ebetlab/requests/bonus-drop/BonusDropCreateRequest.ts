import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropCreateRequestOptions {
  code: string;
  currency: string;
  amount: string;
  start: number;
  start_date?: string;
  count: string;
  end: number;
  required_wager: string;
  required_wager_currency: string;
  ref_code: string;
  reference_tag: string;
  rules: Array<{
    field: string;
    operator: string;
    value: string;
  }>;
}

export type BonusDropCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropCreateRequest extends EbetlabRequest<BonusDropCreateResponse, any> {
  constructor(private options: BonusDropCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/store`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      code: this.options.code,
      currency: this.options.currency,
      amount: this.options.amount,
      start: this.options.start,
      start_date: this.options.start_date || '',
      count: this.options.count,
      end: this.options.end,
      required_wager: this.options.required_wager,
      required_wager_currency: this.options.required_wager_currency,
      ref_code: this.options.ref_code,
      reference_tag: this.options.reference_tag,
      rules: this.options.rules,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
