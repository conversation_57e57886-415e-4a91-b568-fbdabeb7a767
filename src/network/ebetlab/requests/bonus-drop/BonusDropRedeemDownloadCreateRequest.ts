import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropRedeemDownloadCreateRequestOptions {
  id: number;
}

export type BonusDropRedeemDownloadCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropRedeemDownloadCreateRequest extends EbetlabRequest<BonusDropRedeemDownloadCreateResponse, any> {
  constructor(private options: BonusDropRedeemDownloadCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/download/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
