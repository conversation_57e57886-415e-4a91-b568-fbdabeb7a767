import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropDeleteRequestOptions {
  id: number;
}

export type BonusDropDeleteResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropDeleteRequest extends EbetlabRequest<BonusDropDeleteResponse, any> {
  constructor(private options: BonusDropDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/delete/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
