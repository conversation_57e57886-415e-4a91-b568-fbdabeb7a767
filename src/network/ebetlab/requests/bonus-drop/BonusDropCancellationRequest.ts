import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropCancellationRequestOptions {
  id: number;
}

export type BonusDropCancellationResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropCancellationRequest extends EbetlabRequest<BonusDropCancellationResponse, any> {
  constructor(private options: BonusDropCancellationRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/deactivate/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
