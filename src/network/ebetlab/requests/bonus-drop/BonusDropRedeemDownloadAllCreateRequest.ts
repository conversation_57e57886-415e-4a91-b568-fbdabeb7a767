import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropRedeemDownloadAllCreateRequestOptions {
  // Add common filter options that might be used for download
  username?: string | null;
  currency?: string | null;
  status?: string | null;
  bonus_id?: string | null;
  from?: string | null;
  to?: string | null;
}

export type BonusDropRedeemDownloadAllCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropRedeemDownloadAllCreateRequest extends EbetlabRequest<BonusDropRedeemDownloadAllCreateResponse, any> {
  constructor(private options: BonusDropRedeemDownloadAllCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/redeems/download`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      username: this.options.username,
      currency: this.options.currency,
      status: this.options.status,
      bonus_id: this.options.bonus_id,
      from: this.options.from,
      to: this.options.to,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
