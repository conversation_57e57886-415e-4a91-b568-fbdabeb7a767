import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropListRequestOptions {
  page: number;
  limit: number;
  // Add common filter options that might be used
  code?: string | null;
  currency?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BonusDropListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class BonusDropListRequest extends EbetlabRequest<BonusDropListResponse, any> {
  constructor(private options: BonusDropListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      code: this.options.code,
      currency: this.options.currency,
      from: this.options.from,
      to: this.options.to,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
