import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropUpdateRequestOptions {
  id: number;
  code: string;
  currency: string;
  amount: string;
  count: number;
  ref_code: string;
  reference_tag: string;
  required_wager: string;
  required_wager_currency: string;
  start: number;
  end: number;
  rules: Array<{
    field: string;
    value: string;
    operator: string;
  }>;
}

export type BonusDropUpdateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropUpdateRequest extends EbetlabRequest<BonusDropUpdateResponse, any> {
  constructor(private options: BonusDropUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/update/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      code: this.options.code,
      currency: this.options.currency,
      amount: this.options.amount,
      count: this.options.count,
      ref_code: this.options.ref_code,
      reference_tag: this.options.reference_tag,
      required_wager: this.options.required_wager,
      required_wager_currency: this.options.required_wager_currency,
      start: this.options.start,
      end: this.options.end,
      rules: this.options.rules,
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
