import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusDropGetRequestOptions {
  id: number;
}

export type BonusDropGetResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDropGetRequest extends EbetlabRequest<BonusDropGetResponse, any> {
  constructor(private options: BonusDropGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-drops/detail/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
