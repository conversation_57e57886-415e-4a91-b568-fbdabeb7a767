import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleListRequestOptions {
  page: number;
  limit: number;
  from?: string | null;
  to?: string | null;
}

export type AutoBonusRuleListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class AutoBonusRuleListRequest extends EbetlabRequest<AutoBonusRuleListResponse, any> {
  constructor(private options: AutoBonusRuleListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      from: this.options.from,
      to: this.options.to,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
