import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleCreateRequestOptions {
  bonus_code: string;
  rules: Array<{
    field: string;
    operator: string;
    value: string;
  }>;
  bonus_id: number;
  currency: string;
  reference_tag: string;
  total: string;
}

export type AutoBonusRuleCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class AutoBonusRuleCreateRequest extends EbetlabRequest<AutoBonusRuleCreateResponse, any> {
  constructor(private options: AutoBonusRuleCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules/store`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      bonus_code: this.options.bonus_code,
      rules: this.options.rules,
      bonus_id: this.options.bonus_id,
      currency: this.options.currency,
      reference_tag: this.options.reference_tag,
      total: this.options.total,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
