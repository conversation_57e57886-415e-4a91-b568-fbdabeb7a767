import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleClaimListRequestOptions {
  page: number;
  limit: number;
  bonus_code?: string | null;
  from?: string | null;
  to?: string | null;
}

export type AutoBonusRuleClaimListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class AutoBonusRuleClaimListRequest extends EbetlabRequest<AutoBonusRuleClaimListResponse, any> {
  constructor(private options: AutoBonusRuleClaimListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules-claims/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      bonus_code: this.options.bonus_code,
      from: this.options.from,
      to: this.options.to,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
