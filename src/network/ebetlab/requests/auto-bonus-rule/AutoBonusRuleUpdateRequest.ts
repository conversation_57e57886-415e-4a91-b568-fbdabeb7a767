import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleUpdateRequestOptions {
  id: number;
  bonus_code: string;
  rules: Array<{
    field: string;
    value: string;
    operator: string;
  }>;
  bonus_id: number;
  currency: string;
  reference_tag?: string;
  total: number;
}

export type AutoBonusRuleUpdateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class AutoBonusRuleUpdateRequest extends EbetlabRequest<AutoBonusRuleUpdateResponse, any> {
  constructor(private options: AutoBonusRuleUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules/update/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      bonus_code: this.options.bonus_code,
      rules: this.options.rules,
      bonus_id: this.options.bonus_id,
      currency: this.options.currency,
      reference_tag: this.options.reference_tag || '',
      total: this.options.total,
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
