import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleClaimListByIdRequestOptions {
  id: string;
  page: number;
  limit: number;
  username?: string | null;
  from?: string | null;
  to?: string | null;
}

export type AutoBonusRuleClaimListByIdResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class AutoBonusRuleClaimListByIdRequest extends EbetlabRequest<AutoBonusRuleClaimListByIdResponse, any> {
  constructor(private options: AutoBonusRuleClaimListByIdRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules-claims/${this.options.id}/claims/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      username: this.options.username,
      from: this.options.from,
      to: this.options.to,
      page: this.options.page,
      limit: this.options.limit,
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
