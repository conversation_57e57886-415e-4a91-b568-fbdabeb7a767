import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleGetRequestOptions {
  id: number;
}

export type AutoBonusRuleGetResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class AutoBonusRuleGetRequest extends EbetlabRequest<AutoBonusRuleGetResponse, any> {
  constructor(private options: AutoBonusRuleGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules/show/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
