import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleCancellationCreateRequestOptions {
  id: number;
}

export type AutoBonusRuleCancellationCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class AutoBonusRuleCancellationCreateRequest extends EbetlabRequest<AutoBonusRuleCancellationCreateResponse, any> {
  constructor(private options: AutoBonusRuleCancellationCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules/cancel/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
