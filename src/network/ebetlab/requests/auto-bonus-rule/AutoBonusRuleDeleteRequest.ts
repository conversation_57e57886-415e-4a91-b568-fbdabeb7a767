import { EbetlabRequest, HttpMethod } from '../Request';

export interface AutoBonusRuleDeleteRequestOptions {
  id: number;
}

export type AutoBonusRuleDeleteResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class AutoBonusRuleDeleteRequest extends EbetlabRequest<AutoBonusRuleDeleteResponse, any> {
  constructor(private options: AutoBonusRuleDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auto-bonus-rules/delete/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
