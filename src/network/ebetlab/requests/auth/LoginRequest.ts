import { EbetlabRequest, EbetlabResponse, HttpMethod } from '../Request';

export interface LoginRequestOptions {
  username: string;
  password: string;
  otp?: string;
  agentInfo?: AgentInfo;
  secret?: string;
}

export interface AgentInfo {
  browserName: string;
  isMobile: boolean;
  isTablet: boolean;
  isBrowser: boolean;
  browserVersion: string;
  osVersion: string;
  engineVersion: string;
  osName: string;
  engineName: string;
}

export type LoginResponse = {
  data:
    | {
        token: string;
        ws: string;
        user: {
          id: number;
        };
      }
    | { type: '2factor'; secret: string };
  status: number;
  success: boolean;
};

export class LoginRequest extends EbetlabRequest<LoginResponse['data'], any> {
  constructor(private options: LoginRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/auth/login`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    const defaultAgentInfo: AgentInfo = {
      browserName: 'Chrome',
      isMobile: false,
      isTablet: false,
      isBrowser: true,
      browserVersion: '136',
      osVersion: '10.15.7',
      engineVersion: '136.0.0.0',
      osName: 'Mac OS',
      engineName: 'Blink',
    };

    return {
      email: this.options.username,
      password: this.options.password,
      otp: this.options.otp || '',
      agent: this.options.agentInfo || defaultAgentInfo,
      sms: '',
      fingerprint: '',
      secret: this.options.secret || '',
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  override validateResponse(obj: any): EbetlabResponse<LoginResponse['data']> {
    if (obj.success) {
      return {
        data: obj.data,
        success: obj.success,
        status: obj.status,
      };
    } else {
      return {
        success: obj.success,
        status: obj.status,
        message: obj.message ?? 'Something went wrong',
      };
    }
  }
}
