import { EbetlabRequest, HttpMethod } from '../Request';

export interface FtwTransactionsRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  is_manuel?: boolean | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type FtwTransactionsResponse = {
  data: {
    total_count: number;
    total_sum: string;
    data: Array<{
      customer_id: number;
      username: string;
      registration_ts: number;
      currency: string;
      first_withdrawal_date: number;
      first_withdrawal_id: number;
      first_withdrawal_amount: string;
      first_withdrawal_currency: string;
      first_withdrawal_amount_usd: string;
      total_withdrawal_amount: string;
      total_usd_amount: string;
      phone_full: string | null;
    }>;
  };
  status: number;
  success: boolean;
};

export class FtwTransactionsRequest extends EbetlabRequest<FtwTransactionsResponse, any> {
  constructor(private options: FtwTransactionsRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/transactions/ftw/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      method: this.options.method || null,
      provider: this.options.provider || null,
      username: this.options.username || null,
      affiliator: this.options.affiliator || null,
      ref_code: this.options.ref_code || null,
      currency: this.options.currency || null,
      operator_id: this.options.operator_id || null,
      type: this.options.type || null,
      is_manuel: this.options.is_manuel || null,
      status_id: this.options.status_id || null,
      tx: this.options.tx || null,
      related: this.options.related || null,
      usd_min: this.options.usd_min || null,
      usd_max: this.options.usd_max || null,
      from: this.options.from || null,
      to: this.options.to || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
