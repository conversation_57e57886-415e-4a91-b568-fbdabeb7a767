import { EbetlabRequest, HttpMethod } from '../Request';

export interface TransactionsSummaryRequestOptions {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  is_manuel?: boolean | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  amount_min?: number | null;
  amount_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
}

export type TransactionsSummaryResponse = {
  data: {
    deposit: {
      deposit: string;
      total: number;
      provider: string;
      payment_provider: any;
    };
    withdraw: {
      withdraw: string;
      total: number;
      provider: string;
      payment_provider: any;
    };
    currency: string;
  };
  status: number;
  success: boolean;
};

export class TransactionsSummaryRequest extends EbetlabRequest<TransactionsSummaryResponse, any> {
  constructor(private options: TransactionsSummaryRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/transactions/summary`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || null,
      method: this.options.method || null,
      provider: this.options.provider || null,
      username: this.options.username || null,
      currency: this.options.currency || null,
      operator_id: this.options.operator_id || null,
      type: this.options.type || null,
      affiliator: this.options.affiliator || null,
      ref_code: this.options.ref_code || null,
      is_manuel: this.options.is_manuel || null,
      status_id: this.options.status_id || null,
      tx: this.options.tx || null,
      related: this.options.related || null,
      usd_min: this.options.usd_min || null,
      usd_max: this.options.usd_max || null,
      amount_min: this.options.amount_min || null,
      amount_max: this.options.amount_max || null,
      from: this.options.from || null,
      to: this.options.to || null,
      sortBy: this.options.sortBy || null,
      direction: this.options.direction || null,
      page: this.options.page || 1,
      limit: this.options.limit || 20,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
