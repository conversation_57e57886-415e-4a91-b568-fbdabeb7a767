import { EbetlabRequest, HttpMethod } from '../Request';

export interface CurrencyRateConversionListRequestOptions {
  currency: string;
}

export type CurrencyRateConversionListResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class CurrencyRateConversionListRequest extends EbetlabRequest<CurrencyRateConversionListResponse, any> {
  constructor(private options: CurrencyRateConversionListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/rates/conversion/${this.options.currency}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      currency: this.options.currency,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
