export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export abstract class EbetlabRequest<ResponseModel = any, Body = any> {
  abstract getPath(): string;

  abstract getMethod(): HttpMethod;

  abstract getBody(): Body;

  abstract getHeaders(): Record<string, string>;

  validateResponse(obj: any): EbetlabResponse<ResponseModel> {
    if (obj.success) {
      return {
        data: obj.data,
        success: obj.success,
        status: obj.status,
      };
    } else {
      return {
        success: obj.success,
        status: obj.status,
        message: obj.message ?? 'Something went wrong',
      };
    }
  }
}

export type EbetlabSuccessResponse<T = any> = {
  data: T;
  success: true;
  status: number;
};

export type EbetlabErrorResponse = {
  success: false;
  status: number;
  message: string;
};

export type EbetlabResponse<T> = EbetlabSuccessResponse<T> | EbetlabErrorResponse;
