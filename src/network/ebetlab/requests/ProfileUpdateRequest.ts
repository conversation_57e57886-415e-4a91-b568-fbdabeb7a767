import { EbetlabRequest, HttpMethod } from './Request';

export interface ProfileUpdateRequestOptions {
  id: number;
  username?: string;
  phone?: string;
  country_code?: string;
  email?: string;
  name?: string;
  surname?: string;
  occupation?: string;
  identity_no?: string;
  residential?: string;
  birthday?: string;
  city?: string;
  ghost_mode?: boolean;
  hide_statistics?: boolean;
  hide_race_statistics?: boolean;
  exclude_rain?: boolean;
  receive_marketing_mails?: boolean;
}

export type ProfileUpdateResponse = {
  data: string;
  status: number;
  success: boolean;
};

export class ProfileUpdateRequest extends EbetlabRequest<ProfileUpdateResponse, any> {
  constructor(private options: ProfileUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/profile/update/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username || '',
      phone: this.options.phone || '',
      country_code: this.options.country_code || '',
      email: this.options.email || '',
      name: this.options.name || '',
      surname: this.options.surname || '',
      occupation: this.options.occupation || '',
      identity_no: this.options.identity_no || '',
      residential: this.options.residential || '',
      birthday: this.options.birthday || '',
      city: this.options.city || '',
      ghost_mode: this.options.ghost_mode ?? false,
      hide_statistics: this.options.hide_statistics ?? false,
      hide_race_statistics: this.options.hide_race_statistics ?? false,
      exclude_rain: this.options.exclude_rain ?? false,
      receive_marketing_mails: this.options.receive_marketing_mails ?? false,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
