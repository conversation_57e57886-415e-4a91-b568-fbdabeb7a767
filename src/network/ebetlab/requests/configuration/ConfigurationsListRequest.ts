import { EbetlabRequest, HttpMethod } from '../Request';

export interface ConfigurationsListRequestOptions {
  // No specific options needed for this request
}

export type ConfigurationsListResponse = {
  data: {
    providers: {
      id: number;
      identifier: string;
      name: string;
      image: string;
      total: number;
    }[];
    currencies: string[];
    game_categories: { id: number; name: string }[];
  };
  status: number;
  success: boolean;
};

export type ConfigurationsDTO = ConfigurationsListResponse['data'];

export class ConfigurationsListRequest extends EbetlabRequest<ConfigurationsDTO, any> {
  constructor(private options: ConfigurationsListRequestOptions = {}) {
    super();
  }

  getPath(): string {
    return `/operator/configuration`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
