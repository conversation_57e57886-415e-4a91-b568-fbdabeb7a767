import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleDeleteRequestOptions {
  id: number;
}

export type BalanceCorrectionRuleDeleteResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleDeleteRequest extends EbetlabRequest<BalanceCorrectionRuleDeleteResponse, any> {
  constructor(private options: BalanceCorrectionRuleDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/delete/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
