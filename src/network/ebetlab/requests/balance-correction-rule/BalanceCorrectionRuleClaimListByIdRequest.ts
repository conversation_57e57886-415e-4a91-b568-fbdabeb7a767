import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleClaimListByIdRequestOptions {
  id: string;
  page: number;
  limit: number;
  name?: string | null;
  description?: string | null;
  operator_id?: string | null;
  currency?: string | null;
  is_active?: boolean | null;
  model?: string | null;
  to_start?: string | null;
  to_end?: string | null;
  from_start?: string | null;
  from_end?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BalanceCorrectionRuleClaimListByIdResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleClaimListByIdRequest extends EbetlabRequest<BalanceCorrectionRuleClaimListByIdResponse, any> {
  constructor(private options: BalanceCorrectionRuleClaimListByIdRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/list/${this.options.id}/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      name: this.options.name,
      description: this.options.description,
      operator_id: this.options.operator_id,
      currency: this.options.currency,
      is_active: this.options.is_active,
      model: this.options.model,
      to_start: this.options.to_start,
      to_end: this.options.to_end,
      from_start: this.options.from_start,
      from_end: this.options.from_end,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
