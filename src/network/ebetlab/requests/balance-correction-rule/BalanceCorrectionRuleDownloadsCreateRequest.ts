import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleDownloadsCreateRequestOptions {
  id: string;
}

export type BalanceCorrectionRuleDownloadsCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleDownloadsCreateRequest extends EbetlabRequest<BalanceCorrectionRuleDownloadsCreateResponse, any> {
  constructor(private options: BalanceCorrectionRuleDownloadsCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/download/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
