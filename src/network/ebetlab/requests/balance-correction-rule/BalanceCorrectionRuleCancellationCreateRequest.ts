import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleCancellationCreateRequestOptions {
  id: number;
}

export type BalanceCorrectionRuleCancellationCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleCancellationCreateRequest extends EbetlabRequest<BalanceCorrectionRuleCancellationCreateResponse, any> {
  constructor(private options: BalanceCorrectionRuleCancellationCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/deactivate/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
