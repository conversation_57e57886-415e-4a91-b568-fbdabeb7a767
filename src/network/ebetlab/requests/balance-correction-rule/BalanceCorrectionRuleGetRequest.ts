import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleGetRequestOptions {
  id: number;
}

export type BalanceCorrectionRuleGetResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleGetRequest extends EbetlabRequest<BalanceCorrectionRuleGetResponse, any> {
  constructor(private options: BalanceCorrectionRuleGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/show/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
