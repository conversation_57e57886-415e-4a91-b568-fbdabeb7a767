import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleListRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  way?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BalanceCorrectionRuleListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleListRequest extends EbetlabRequest<BalanceCorrectionRuleListResponse, any> {
  constructor(private options: BalanceCorrectionRuleListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username,
      currency: this.options.currency,
      operator_id: this.options.operator_id,
      way: this.options.way,
      usd_min: this.options.usd_min,
      usd_max: this.options.usd_max,
      from: this.options.from,
      to: this.options.to,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
