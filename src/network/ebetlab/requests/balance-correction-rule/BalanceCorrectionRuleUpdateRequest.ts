import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleUpdateRequestOptions {
  id: number;
  bonus_tag?: string | null;
  bonus_tag_time_range?: string | null;
  wager_percentage: string;
  bonus_wager_percentage?: string | null;
  reference_tag: string;
  max_usd_amount: string;
  amount_percentage: string;
  deposit_order?: string | null;
  is_auto: boolean;
  disable_withdraw_players: boolean;
  disable_discount: boolean;
  ref_code?: string | null;
  range: string;
  name: string;
  affiliator_id?: string | null;
  ref_id?: string | null;
  min_usd: string;
  max_usd: string;
  bonus_id?: number;
  redeem_code: string;
  from: number;
  to: number;
  currencies?: string[] | null;
  type: string;
}

export type BalanceCorrectionRuleUpdateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleUpdateRequest extends EbetlabRequest<BalanceCorrectionRuleUpdateResponse, any> {
  constructor(private options: BalanceCorrectionRuleUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/update/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      bonus_tag: this.options.bonus_tag,
      bonus_tag_time_range: this.options.bonus_tag_time_range,
      wager_percentage: this.options.wager_percentage,
      bonus_wager_percentage: this.options.bonus_wager_percentage,
      reference_tag: this.options.reference_tag,
      max_usd_amount: this.options.max_usd_amount,
      amount_percentage: this.options.amount_percentage,
      deposit_order: this.options.deposit_order,
      is_auto: this.options.is_auto,
      disable_withdraw_players: this.options.disable_withdraw_players,
      disable_discount: this.options.disable_discount,
      ref_code: this.options.ref_code,
      range: this.options.range,
      name: this.options.name,
      affiliator_id: this.options.affiliator_id,
      ref_id: this.options.ref_id,
      min_usd: this.options.min_usd,
      max_usd: this.options.max_usd,
      bonus_id: this.options.bonus_id,
      redeem_code: this.options.redeem_code,
      from: this.options.from,
      to: this.options.to,
      currencies: this.options.currencies,
      type: this.options.type,
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
