import { EbetlabRequest, HttpMethod } from '../Request';

export interface BalanceCorrectionRuleCreateRequestOptions {
  wager_percentage: string;
  bonus_wager_percentage: string;
  reference_tag: string;
  max_usd_amount: string;
  amount_percentage: string;
  deposit_order: string;
  is_auto: boolean;
  disable_discount: boolean;
  ref_code: string;
  range: string;
  name: string;
  affiliator_id?: string;
  ref_id?: string;
  min_usd?: string;
  max_usd?: string;
  from: number;
  bonus_id?: string;
  to: number;
  limits: Record<string, {
    min_usd: string;
    max_usd: string;
  }>;
  bonus_tag: string;
  bonus_tag_time_range: string;
  disable_withdraw_players: boolean;
  redeem_code: string;
  currencies: string[];
  type: string;
}

export type BalanceCorrectionRuleCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BalanceCorrectionRuleCreateRequest extends EbetlabRequest<BalanceCorrectionRuleCreateResponse, any> {
  constructor(private options: BalanceCorrectionRuleCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/balance-correction-rules/store`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      wager_percentage: this.options.wager_percentage,
      bonus_wager_percentage: this.options.bonus_wager_percentage,
      reference_tag: this.options.reference_tag,
      max_usd_amount: this.options.max_usd_amount,
      amount_percentage: this.options.amount_percentage,
      deposit_order: this.options.deposit_order,
      is_auto: this.options.is_auto,
      disable_discount: this.options.disable_discount,
      ref_code: this.options.ref_code,
      range: this.options.range,
      name: this.options.name,
      affiliator_id: this.options.affiliator_id || '',
      ref_id: this.options.ref_id || '',
      min_usd: this.options.min_usd || '',
      max_usd: this.options.max_usd || '',
      from: this.options.from,
      bonus_id: this.options.bonus_id || '',
      to: this.options.to,
      limits: this.options.limits,
      bonus_tag: this.options.bonus_tag,
      bonus_tag_time_range: this.options.bonus_tag_time_range,
      disable_withdraw_players: this.options.disable_withdraw_players,
      redeem_code: this.options.redeem_code,
      currencies: this.options.currencies,
      type: this.options.type,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
