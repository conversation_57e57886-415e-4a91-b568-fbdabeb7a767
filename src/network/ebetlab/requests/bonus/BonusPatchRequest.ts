import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusPatchRequestOptions {
  id: number;
  provider_id?: string;
  provider?: string;
  type: string;
  name: string;
  from: number;
  to: number;
  currency: string;
  product: string;
  maximum_payout?: string;
  maximum_payout_percentage?: string;
  expire_type: string;
  expire_unit: string;
  verified_players: boolean;
  request_able: boolean;
  cancel_able: boolean;
  disable_withdraw: boolean;
  note?: string;
  description?: string;
  multiplier?: string;
  minimum_bet?: string;
  maximum_bet?: string;
  minimum_amount?: string;
  maximum_amount?: string;
  bet_level?: number;
  max_win?: string;
  quantity?: number;
  allowed_min_usd?: string | null;
  allowed_max_usd?: string | null;
  wager_multiplier?: string | null;
  disable_bet_amount?: string | null;
  from_edit?: string;
  to_edit?: string;
  selected_games?: Array<{
    id: number;
    percentage: number;
  }>;
  games?: Array<{
    id: number;
    percentage: number;
  }>;
}

export type BonusPatchResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusPatchRequest extends EbetlabRequest<BonusPatchResponse, any> {
  constructor(private options: BonusPatchRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonuses/update/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      provider_id: this.options.provider_id || '',
      provider: this.options.provider || '',
      type: this.options.type,
      name: this.options.name,
      from: this.options.from,
      to: this.options.to,
      currency: this.options.currency,
      product: this.options.product,
      maximum_payout: this.options.maximum_payout || '',
      maximum_payout_percentage: this.options.maximum_payout_percentage || '',
      expire_type: this.options.expire_type,
      expire_unit: this.options.expire_unit,
      verified_players: this.options.verified_players,
      request_able: this.options.request_able,
      cancel_able: this.options.cancel_able,
      disable_withdraw: this.options.disable_withdraw,
      note: this.options.note || '',
      description: this.options.description || '',
      multiplier: this.options.multiplier || '',
      minimum_bet: this.options.minimum_bet || '',
      maximum_bet: this.options.maximum_bet || '',
      minimum_amount: this.options.minimum_amount || '',
      maximum_amount: this.options.maximum_amount || '',
      bet_level: this.options.bet_level || 0,
      max_win: this.options.max_win || '',
      quantity: this.options.quantity || 0,
      allowed_min_usd: this.options.allowed_min_usd,
      allowed_max_usd: this.options.allowed_max_usd,
      wager_multiplier: this.options.wager_multiplier,
      disable_bet_amount: this.options.disable_bet_amount,
      from_edit: this.options.from_edit || this.options.from.toString(),
      to_edit: this.options.to_edit || this.options.to.toString(),
      selected_games: this.options.selected_games || [],
      games: this.options.games || [],
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
