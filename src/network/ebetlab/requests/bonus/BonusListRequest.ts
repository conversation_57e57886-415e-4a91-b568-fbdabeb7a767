import { Bonus } from '@/network/ebetlab/dto/Bonus';
import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusListRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  name?: string | null;
  description?: string | null;
  operator_id?: number | null;
  currency?: string | null;
  is_active?: boolean | null;
  model?: string | null;
  to_start?: number | null;
  to_end?: number | null;
  from_start?: number | null;
  from_end?: number | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BonusListResponse = {
  total: number;
  data: Bonus[];
};

export class BonusListRequest extends EbetlabRequest<BonusListResponse, any> {
  constructor(private options: BonusListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonuses/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      name: this.options.name,
      description: this.options.description,
      operator_id: this.options.operator_id,
      currency: this.options.currency,
      is_active: this.options.is_active,
      model: this.options.model,
      to_start: this.options.to_start,
      to_end: this.options.to_end,
      from_start: this.options.from_start,
      from_end: this.options.from_end,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
