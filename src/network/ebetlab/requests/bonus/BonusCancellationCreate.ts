import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusCancellationCreateRequestOptions {
  id: number;
}

export type BonusCancellationCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusCancellationCreateRequest extends EbetlabRequest<BonusCancellationCreateResponse, any> {
  constructor(private options: BonusCancellationCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonuses/cancel/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
