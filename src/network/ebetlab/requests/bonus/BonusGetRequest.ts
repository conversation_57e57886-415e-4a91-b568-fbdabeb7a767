import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusGetRequestOptions {
  id: number;
}

export interface BonusGetResponseData {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  model: string;
  currency: string;
  currency_id: number;
  name: string;
  description: string;
  note: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  email_verification: null | any;
  phone_verification: null | any;
  request_able: boolean;
  typeable_id: number;
  typeable_type: string;
  is_active: boolean;
  disable_withdraw: boolean;
  cancel_able: boolean;
  expire_type: string;
  expire_unit: string;
  games: Array<{
    id: number;
    percentage: number;
  }>;
  block_balance: number;
  is_deleted: boolean;
  typeable: {
    id: number;
    quantity: number;
    bet_level: number;
    game_merchant_id: number;
    aggregator_id: number;
    game_merchant_name: string;
    max_win: string;
    wager_multiplier: null | number;
    disable_bet_amount: null | string;
    allowed_min_usd: null | string;
    allowed_max_usd: null | string;
    game_merchant: {
      id: number;
      name: string;
    };
  };
}

export type BonusGetResponse = {
  data: BonusGetResponseData;
  status: number;
  success: boolean;
};

export class BonusGetRequest extends EbetlabRequest<BonusGetResponse, any> {
  constructor(private options: BonusGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonuses/show/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
