import { Bonus } from '@/network/ebetlab/dto/Bonus';
import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusCreateRequestOptions {
  provider_id: number;
  provider: {
    label: string;
    value: number;
  };
  type: string;
  name: string;
  from: number;
  to: number;
  currency: string;
  product: string;
  maximum_payout?: string;
  maximum_payout_percentage?: string;
  expire_type: string;
  expire_unit: string;
  verified_players: boolean;
  request_able: boolean;
  cancel_able: boolean;
  disable_withdraw: boolean;
  note?: string;
  description?: string;
  multiplier?: string;
  minimum_bet?: string;
  maximum_bet?: string;
  minimum_amount?: string;
  maximum_amount?: string;
  bet_level?: string;
  max_win?: string;
  quantity?: string;
  allowed_min_usd?: string;
  allowed_max_usd?: string;
  disable_bet_amount?: string;
  wager_multiplier?: string;
  games?: Array<{
    id: number;
    percentage: number;
  }>;
}

export type BonusCreateResponse = {
  data: Bonus;
};

export class BonusCreateRequest extends EbetlabRequest<BonusCreateResponse, BonusCreateRequestOptions> {
  constructor(private options: BonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/operator/bonuses/store';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): BonusCreateRequestOptions {
    return {
      provider_id: this.options.provider_id,
      provider: this.options.provider,
      type: this.options.type,
      name: this.options.name,
      from: this.options.from,
      to: this.options.to,
      currency: this.options.currency,
      product: this.options.product,
      maximum_payout: this.options.maximum_payout || '',
      maximum_payout_percentage: this.options.maximum_payout_percentage || '',
      expire_type: this.options.expire_type,
      expire_unit: this.options.expire_unit,
      verified_players: this.options.verified_players,
      request_able: this.options.request_able,
      cancel_able: this.options.cancel_able,
      disable_withdraw: this.options.disable_withdraw,
      note: this.options.note || '',
      description: this.options.description || '',
      multiplier: this.options.multiplier || '',
      minimum_bet: this.options.minimum_bet || '',
      maximum_bet: this.options.maximum_bet || '',
      minimum_amount: this.options.minimum_amount || '',
      maximum_amount: this.options.maximum_amount || '',
      bet_level: this.options.bet_level || '',
      max_win: this.options.max_win || '',
      quantity: this.options.quantity || '',
      allowed_min_usd: this.options.allowed_min_usd || '',
      allowed_max_usd: this.options.allowed_max_usd || '',
      disable_bet_amount: this.options.disable_bet_amount || '',
      wager_multiplier: this.options.wager_multiplier || '',
      games: this.options.games || [],
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
