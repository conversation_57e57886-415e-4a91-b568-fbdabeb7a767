import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusDeleteRequestOptions {
  id: number;
}

export type BonusDeleteResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusDeleteRequest extends EbetlabRequest<BonusDeleteResponse, any> {
  constructor(private options: BonusDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonuses/delete/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
