import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface BonusFreespinRedeemsListRequestOptions {
  bonusId: number;
  page: number;
  limit: number;
  id?: string;
  name?: string | null;
  description?: string | null;
  operator_id?: string | null;
  currency?: string | null;
  is_active?: boolean | null;
  model?: string | null;
  to_start?: string | null;
  to_end?: string | null;
  from_start?: string | null;
  from_end?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export interface BonusFreespinClaim {
  id: number;
  customer_id: number;
  currency: string;
  bonus_id: number;
  income: string;
  usd_income: string;
  is_completed: boolean;
  is_expired: boolean;
  is_active: boolean;
  bonus_active_id: number;
  timestamp: number;
  customer: {
    id: number;
    username: string;
    masked_username: string;
    last_action: string | null;
  };
}

export interface BonusFreespinData {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  model: string;
  currency: string;
  currency_id: number;
  name: string;
  description: string;
  note: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  email_verification: null | any;
  phone_verification: null | any;
  request_able: boolean;
  typeable_id: number;
  typeable_type: string;
  is_active: boolean;
  disable_withdraw: boolean;
  cancel_able: boolean;
  expire_type: string;
  expire_unit: string;
  games: Array<{
    id: number;
    percentage: number;
  }>;
  block_balance: number;
  is_deleted: boolean;
  typeable: {
    id: number;
    quantity: number;
    bet_level: number;
    game_merchant_id: number;
    aggregator_id: number;
    game_merchant_name: string;
    max_win: string;
    wager_multiplier: null | number;
    disable_bet_amount: null | string;
    allowed_min_usd: null | string;
    allowed_max_usd: null | string;
  };
}

export type BonusFreespinRedeemsListResponse = {
  data: {
    bonus: BonusFreespinData;
    claims: {
      total: number;
      data: BonusFreespinClaim[];
    };
  };
  status: number;
  success: boolean;
};

export class BonusFreespinRedeemsListRequest extends EbetlabRequest<BonusFreespinRedeemsListResponse, any> {
  constructor(private options: BonusFreespinRedeemsListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/freespins/show/${this.options.bonusId}/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id || this.options.bonusId.toString(),
      name: this.options.name,
      description: this.options.description,
      operator_id: this.options.operator_id,
      currency: this.options.currency,
      is_active: this.options.is_active,
      model: this.options.model,
      to_start: this.options.to_start,
      to_end: this.options.to_end,
      from_start: this.options.from_start,
      from_end: this.options.from_end,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
