import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface GameListRequestOptions {
  name?: string | undefined;
  category?: string | undefined;
  active?: boolean | undefined;
  systemActive?: boolean | undefined;
  merchantId?: number | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export type GameDTO = {
  id: number;
  name: string;
  image: string;
};

export type GameListResponse = {
  data: {
    data: GameDTO[];
    total: number;
  };
};

export class GameListRequest extends EbetlabRequest<GameListResponse['data'], any> {
  constructor(private options: GameListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/games/configuration/index/${this.options.page ?? 1}/${this.options.limit ?? 20}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    let active = null;
    if (this.options.active === true) {
      active = 'active';
    } else if (this.options.active === false) {
      active = 'passive';
    }

    let systemActive = null;
    if (this.options.systemActive === true) {
      systemActive = 'active';
    } else if (this.options.systemActive === false) {
      systemActive = 'passive';
    }

    return {
      id: null,
      name: this.options.name ?? null,
      description: null,
      is_active: active,
      system_active: null,
      merchant_id: this.options.merchantId ?? null,
      category: this.options.category ?? null,
      sortBy: null,
      direction: null,
      page: this.options.page ?? 1,
      limit: this.options.limit ?? 20,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
