import { EbetlabRequest, HttpMethod } from '@/network/ebetlab/requests/Request';

export interface GameListByProviderRequestOptions {
  providers: number[];
  type: string;
}

export type GameListByProviderResponse = {
  data: any[];
};

export class GameListByProviderRequest extends EbetlabRequest<GameListByProviderResponse, any> {
  constructor(private options: GameListByProviderRequestOptions) {
    super();
  }

  getPath(): string {
    return '/operator/games/by-provider';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      providers: this.options.providers,
      type: this.options.type,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
