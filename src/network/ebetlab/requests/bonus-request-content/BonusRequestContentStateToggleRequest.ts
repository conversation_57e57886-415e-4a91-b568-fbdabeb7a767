import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusRequestContentStateToggleRequestOptions {
  id: number;
}

export type BonusRequestContentStateToggleResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusRequestContentStateToggleRequest extends EbetlabRequest<BonusRequestContentStateToggleResponse, any> {
  constructor(private options: BonusRequestContentStateToggleRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-request-content/activate/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
