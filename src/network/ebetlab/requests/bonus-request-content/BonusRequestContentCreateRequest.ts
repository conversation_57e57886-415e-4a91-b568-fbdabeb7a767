import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusRequestContentCreateRequestOptions {
  title: string;
  request_able: boolean;
  gmt: string;
  days: string;
  from: number;
  to: number;
  values: Array<{
    lang: string;
    value: {
      title: string;
      content: string;
    };
  }>;
  bonus_id: number;
  bonus_type: string;
}

export type BonusRequestContentCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusRequestContentCreateRequest extends EbetlabRequest<BonusRequestContentCreateResponse, any> {
  constructor(private options: BonusRequestContentCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-request-content/store`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      title: this.options.title,
      request_able: this.options.request_able,
      gmt: this.options.gmt,
      days: this.options.days,
      from: this.options.from,
      to: this.options.to,
      values: this.options.values,
      bonus_id: this.options.bonus_id,
      bonus_type: this.options.bonus_type,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
