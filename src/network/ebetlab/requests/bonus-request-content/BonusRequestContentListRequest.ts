import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusRequestContentListRequestOptions {
  page: number;
  limit: number;
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  status_id?: string | null;
  tx?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export type BonusRequestContentListResponse = {
  data: any[];
  status: number;
  success: boolean;
};

export class BonusRequestContentListRequest extends EbetlabRequest<BonusRequestContentListResponse, any> {
  constructor(private options: BonusRequestContentListRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-request-content/index/${this.options.page}/${this.options.limit}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
      username: this.options.username,
      currency: this.options.currency,
      operator_id: this.options.operator_id,
      type: this.options.type,
      status_id: this.options.status_id,
      tx: this.options.tx,
      from: this.options.from,
      to: this.options.to,
      sortBy: this.options.sortBy,
      direction: this.options.direction,
      page: this.options.page,
      limit: this.options.limit,
      rt: Math.floor(Date.now() / 1000),
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
