import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusRequestContentSortCreateRequestOptions {
  sorts: number[];
}

export type BonusRequestContentSortCreateResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusRequestContentSortCreateRequest extends EbetlabRequest<BonusRequestContentSortCreateResponse, any> {
  constructor(private options: BonusRequestContentSortCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-request-content/sort`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      sorts: this.options.sorts,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
