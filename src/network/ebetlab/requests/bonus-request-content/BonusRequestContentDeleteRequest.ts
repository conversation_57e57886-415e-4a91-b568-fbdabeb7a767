import { EbetlabRequest, HttpMethod } from '../Request';

export interface BonusRequestContentDeleteRequestOptions {
  id: number;
}

export type BonusRequestContentDeleteResponse = {
  data: any;
  status: number;
  success: boolean;
};

export class BonusRequestContentDeleteRequest extends EbetlabRequest<BonusRequestContentDeleteResponse, any> {
  constructor(private options: BonusRequestContentDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/operator/bonus-request-content/delete/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody(): any {
    return {
      id: this.options.id,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
