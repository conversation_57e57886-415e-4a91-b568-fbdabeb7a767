import fetch from 'node-fetch';
import { getPronetCredentials, buildPronetApiUrl, minifyJsonObject, generatePronetChecksum, PronetCredentials } from '../../utils/pronetAuth';

/**
 * HTTP Methods supported by the Pronet API Client
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * Health check result interface
 */
export interface HealthCheckResult {
  success: boolean;
  message: string;
  timestamp: string;
}

/**
 * Credentials info interface (without exposing sensitive data)
 */
export interface CredentialsInfo {
  username: string;
  host: string;
  hasApiKey: boolean;
}

/**
 * Pronet API Client
 * 
 * This client provides a high-level interface for making authenticated requests
 * to the Pronet API with automatic checksum generation and error handling.
 */
export class PronetApiClient {
  private credentials: PronetCredentials | null = null;

  /**
   * Initialize credentials (lazy loading)
   */
  private async initCredentials(): Promise<void> {
    if (!this.credentials) {
      this.credentials = getPronetCredentials();
      console.log('🔑 Pronet credentials initialized');
    }
  }

  /**
   * Make a request to the Pronet API
   *
   * @param endpoint - API endpoint (e.g., 'users/profile')
   * @param method - HTTP method
   * @param body - Request body object
   * @param additionalHeaders - Additional headers to include
   * @param isProxy - Whether this is a proxy request (affects URL construction)
   * @returns Promise with the API response
   */
  async makeRequest(
    endpoint: string,
    method: HttpMethod = 'POST',
    body?: any,
    additionalHeaders?: Record<string, string>,
    isProxy: boolean = false
  ): Promise<any> {
    try {
      await this.initCredentials();
      
      if (!this.credentials) {
        throw new Error('Failed to initialize Pronet credentials');
      }

      console.log('🚀 Making Pronet API request...');
      console.log(`📍 Endpoint: ${method} ${endpoint}`);

      // Build full URL
      const url = buildPronetApiUrl(endpoint, isProxy);

      // Prepare request body
      const requestBody = body || {};
      const jsonBody = method !== 'GET' ? minifyJsonObject(requestBody) : '';

      // Generate checksum (empty string for GET requests)
      const checksum = generatePronetChecksum(jsonBody, this.credentials.apiKey);

      // Prepare headers - start with additional headers, then add required ones
      const headers: Record<string, string> = {
        ...additionalHeaders,
        'checksum': checksum,
      };

      // Add Content-Type for requests with body
      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      console.log('📤 Additional headers received:', JSON.stringify(additionalHeaders || {}, null, 2));
      console.log('📤 Final request headers being sent:', JSON.stringify(headers, null, 2));
      
      if (method !== 'GET') {
        console.log('📤 Request Body:', jsonBody);
      }

      // Make the request
      const response = await fetch(url, {
        method,
        headers,
        body: method !== 'GET' ? jsonBody : undefined,
      });

      // Log response details
      console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
      console.log('📥 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Pronet API Error Response:', errorText);
        throw new Error(`Pronet API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // Parse response
      const responseData = await response.json();
      
      console.log('✅ Pronet API request successful');
      console.log('📥 Response Data:', JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (error) {
      console.error('❌ Pronet API request failed:', error);
      throw error;
    }
  }

  /**
   * GET request helper
   */
  async get(endpoint: string, additionalHeaders?: Record<string, string>, isProxy: boolean = false): Promise<any> {
    return this.makeRequest(endpoint, 'GET', undefined, additionalHeaders, isProxy);
  }

  /**
   * POST request helper
   */
  async post(endpoint: string, body?: any, additionalHeaders?: Record<string, string>, isProxy: boolean = false): Promise<any> {
    return this.makeRequest(endpoint, 'POST', body, additionalHeaders, isProxy);
  }

  /**
   * PUT request helper
   */
  async put(endpoint: string, body?: any, additionalHeaders?: Record<string, string>): Promise<any> {
    return this.makeRequest(endpoint, 'PUT', body, additionalHeaders);
  }

  /**
   * PATCH request helper
   */
  async patch(endpoint: string, body?: any, additionalHeaders?: Record<string, string>): Promise<any> {
    return this.makeRequest(endpoint, 'PATCH', body, additionalHeaders);
  }

  /**
   * DELETE request helper
   */
  async delete(endpoint: string, body?: any, additionalHeaders?: Record<string, string>): Promise<any> {
    return this.makeRequest(endpoint, 'DELETE', body, additionalHeaders);
  }

  /**
   * Health check method to test Pronet API connectivity
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      console.log('🏥 Performing Pronet API health check...');
      
      // Try to make a simple request (you may need to adjust this endpoint based on actual Pronet API)
      await this.get('health');
      
      return {
        success: true,
        message: 'Pronet API is accessible',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('❌ Pronet API health check failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get current credentials info (without exposing the API key)
   */
  getCredentialsInfo(): CredentialsInfo | null {
    if (!this.credentials) {
      return null;
    }

    return {
      username: this.credentials.username,
      host: this.credentials.host,
      hasApiKey: !!this.credentials.apiKey,
    };
  }
}

// Export singleton instance
export const pronetApiClient = new PronetApiClient();
