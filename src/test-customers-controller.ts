/**
 * Test script for the new Customers Controller
 * 
 * This script tests the new customers endpoints to ensure they work correctly.
 * Run this after starting the server to verify the implementation.
 */

import fetch from 'node-fetch';

const BASE_URL = process.env['BASE_URL'] || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/pronet/v1`;

async function testCustomersEndpoints() {
  console.log('🧪 Testing Customers Controller endpoints...\n');

  try {
    // Test 1: List customers with default pagination
    console.log('📋 Test 1: List customers (default pagination)');
    const listResponse = await fetch(`${API_BASE}/customers`);
    const listData = await listResponse.json();
    
    console.log(`Status: ${listResponse.status}`);
    console.log(`Response:`, JSON.stringify(listData, null, 2));
    
    if (listResponse.ok && listData.success) {
      console.log('✅ List customers test passed\n');
    } else {
      console.log('❌ List customers test failed\n');
    }

    // Test 2: List customers with custom pagination
    console.log('📋 Test 2: List customers (custom pagination)');
    const paginatedResponse = await fetch(`${API_BASE}/customers?page=1&limit=5&sortBy=username&sortOrder=ASC`);
    const paginatedData = await paginatedResponse.json();
    
    console.log(`Status: ${paginatedResponse.status}`);
    console.log(`Response:`, JSON.stringify(paginatedData, null, 2));
    
    if (paginatedResponse.ok && paginatedData.success) {
      console.log('✅ Paginated list customers test passed\n');
    } else {
      console.log('❌ Paginated list customers test failed\n');
    }

    // Test 3: Get customer by ID (if we have customers from the list)
    if (listData.success && listData.data?.customers?.length > 0) {
      const customerId = listData.data.customers[0].id;
      
      console.log(`🔍 Test 3: Get customer by ID (${customerId})`);
      const getResponse = await fetch(`${API_BASE}/customers/${customerId}`);
      const getData = await getResponse.json();
      
      console.log(`Status: ${getResponse.status}`);
      console.log(`Response:`, JSON.stringify(getData, null, 2));
      
      if (getResponse.ok && getData.success) {
        console.log('✅ Get customer by ID test passed\n');
      } else {
        console.log('❌ Get customer by ID test failed\n');
      }
    } else {
      console.log('⏭️  Skipping get customer by ID test (no customers available)\n');
    }

    // Test 4: Get non-existent customer
    console.log('🔍 Test 4: Get non-existent customer');
    const notFoundResponse = await fetch(`${API_BASE}/customers/99999`);
    const notFoundData = await notFoundResponse.json();
    
    console.log(`Status: ${notFoundResponse.status}`);
    console.log(`Response:`, JSON.stringify(notFoundData, null, 2));
    
    if (notFoundResponse.status === 404 && !notFoundData.success) {
      console.log('✅ Non-existent customer test passed\n');
    } else {
      console.log('❌ Non-existent customer test failed\n');
    }

    // Test 5: Invalid pagination parameters
    console.log('📋 Test 5: Invalid pagination parameters');
    const invalidResponse = await fetch(`${API_BASE}/customers?page=0&limit=200`);
    const invalidData = await invalidResponse.json();
    
    console.log(`Status: ${invalidResponse.status}`);
    console.log(`Response:`, JSON.stringify(invalidData, null, 2));
    
    if (invalidResponse.status === 400 && !invalidData.success) {
      console.log('✅ Invalid pagination test passed\n');
    } else {
      console.log('❌ Invalid pagination test failed\n');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  testCustomersEndpoints();
}

export { testCustomersEndpoints };
