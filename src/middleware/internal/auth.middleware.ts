import { Request, Response, NextFunction } from 'express';
import { InternalAuthService } from '@/services/internal/auth.service';
import { User } from '@/entities/internal/User';
import { UnauthorizedError } from '@/types/errors';

// Extend Request interface to include internal user
declare global {
  namespace Express {
    interface Request {
      internalUser?: User;
    }
  }
}

export class InternalAuthMiddleware {
  private authService: InternalAuthService;

  constructor() {
    this.authService = new InternalAuthService();
  }

  /**
   * Middleware to authenticate internal requests using JWT tokens
   *
   * This middleware:
   * 1. Extracts the Authorization header from the request
   * 2. Validates the JWT access token
   * 3. Attaches the authenticated user to the request object
   * 4. If authentication fails, returns 401 Unauthorized
   */
  authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      console.log('🔐 Internal authentication middleware triggered');

      // Extract authorization header
      const authorization = req.headers.authorization;
      if (!authorization) {
        console.log('❌ No authorization header provided');
        res.status(401).json({
          success: false,
          message: 'Authorization header is required',
        });
        return;
      }

      // Extract token from "Bearer <token>" format
      const token = this.extractTokenFromHeader(authorization);
      if (!token) {
        console.log('❌ Invalid authorization header format');
        res.status(401).json({
          success: false,
          message: 'Invalid authorization header format. Expected: Bearer <token>',
        });
        return;
      }

      console.log('📡 Verifying JWT access token...');

      // Verify access token and get user
      const user = await this.authService.verifyAccessToken(token);

      console.log('✅ Internal authentication successful');
      console.log('👤 Authenticated user:', user.email);

      // Attach user to request for use in controllers
      req.internalUser = user;

      // Continue to the next middleware/controller
      next();
    } catch (error) {
      console.error('❌ Internal authentication failed:', error);

      // Return 401 Unauthorized if authentication fails
      res.status(401).json({
        success: false,
        message: error instanceof UnauthorizedError ? error.message : 'Authentication failed',
      });
    }
  };

  /**
   * Optional middleware that doesn't fail if no token is provided
   * Useful for endpoints that can work with or without authentication
   */
  optionalAuthenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authorization = req.headers.authorization;

      if (!authorization) {
        // No token provided, continue without authentication
        next();
        return;
      }

      const token = this.extractTokenFromHeader(authorization);
      if (!token) {
        // Invalid format, continue without authentication
        next();
        return;
      }

      // Try to verify token
      const user = await this.authService.verifyAccessToken(token);
      req.internalUser = user;

      console.log('✅ Optional internal authentication successful for:', user.email);
      next();
    } catch (error) {
      // Authentication failed, but continue without user
      console.log('⚠️ Optional internal authentication failed, continuing without user');
      next();
    }
  };

  private extractTokenFromHeader(authorization: string): string | null {
    // Check if header starts with "Bearer "
    if (!authorization.startsWith('Bearer ')) {
      return null;
    }

    // Extract token part
    const token = authorization.substring(7); // Remove "Bearer " prefix

    if (!token || token.trim() === '') {
      return null;
    }

    return token.trim();
  }
}

// Create singleton instance
export const internalAuthMiddleware = new InternalAuthMiddleware();
