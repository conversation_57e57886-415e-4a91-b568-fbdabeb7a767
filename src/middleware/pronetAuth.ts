import { Request, Response, NextFunction } from 'express';
import fetch from 'node-fetch';
import { generatePronetChecksum, minifyJsonObject, buildPronetApiUrl, getPronetCredentials } from '@/utils/pronetAuth';

/**
 * Pronet Authentication Middleware
 * 
 * This middleware handles authentication for Pronet API endpoints by:
 * 1. Extracting the request body
 * 2. Generating the required checksum header
 * 3. Adding necessary headers for Pronet API communication
 * 4. Validating that Pronet configuration is available
 * 
 * Usage: Apply this middleware to any endpoint that needs to communicate with Pronet API
 */
export const pronetAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log('🔐 Pronet authentication middleware triggered');
    console.log('📍 Request path:', req.path);
    console.log('📋 Request method:', req.method);

    // Get Pronet credentials (this will throw if not configured)
    const credentials = getPronetCredentials();
    
    console.log('✅ Pronet credentials loaded');
    console.log('👤 Username:', credentials.username);
    console.log('🌐 Host:', credentials.host);

    // Get request body - it should already be parsed by express.json() middleware
    const requestBody = req.body || {};
    
    // Convert request body to minified JSON string
    const jsonBody = minifyJsonObject(requestBody);
    
    console.log('📝 Request body for checksum calculation:');
    console.log('📋 Body:', JSON.stringify(requestBody, null, 2));
    console.log('🔧 Minified body:', jsonBody);

    // Generate checksum
    const checksum = generatePronetChecksum(jsonBody, credentials.apiKey);
    
    console.log('🔐 Generated checksum:', checksum);

    // Attach Pronet-specific data to the request object for use in controllers
    req.pronetAuth = {
      credentials,
      checksum,
      jsonBody,
    };

    console.log('✅ Pronet authentication middleware completed successfully');
    
    // Continue to the next middleware/controller
    next();
  } catch (error) {
    console.error('❌ Pronet authentication middleware failed:', error);

    // Return 500 Internal Server Error if Pronet auth setup fails
    res.status(500).json({
      success: false,
      message: 'Pronet authentication configuration error',
      error: error instanceof Error ? error.message : 'Authentication setup failed',
    });
  }
};

/**
 * Pronet API Client Helper
 * 
 * This helper function makes authenticated requests to the Pronet API
 * using the authentication data prepared by the pronetAuth middleware.
 */
export const makePronetApiRequest = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST',
  body?: any,
  additionalHeaders?: Record<string, string>
): Promise<any> => {
  try {
    console.log('🚀 Making Pronet API request...');
    console.log(`📍 Endpoint: ${method} ${endpoint}`);

    // Get credentials
    const credentials = getPronetCredentials();
    
    // Build full URL
    const url = buildPronetApiUrl(endpoint);
    
    // Prepare request body
    const requestBody = body || {};
    const jsonBody = minifyJsonObject(requestBody);
    
    // Generate checksum
    const checksum = generatePronetChecksum(jsonBody, credentials.apiKey);
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'checksum': checksum,
      ...additionalHeaders,
    };

    console.log('📤 Request Headers:', JSON.stringify(headers, null, 2));
    console.log('📤 Request Body:', jsonBody);

    // Make the request
    const response = await fetch(url, {
      method,
      headers,
      body: method !== 'GET' ? jsonBody : undefined,
    });

    // Log response details
    console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
    console.log('📥 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Pronet API Error Response:', errorText);
      throw new Error(`Pronet API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // Parse response
    const responseData = await response.json();
    
    console.log('✅ Pronet API request successful');
    console.log('📥 Response Data:', JSON.stringify(responseData, null, 2));

    return responseData;
  } catch (error) {
    console.error('❌ Pronet API request failed:', error);
    throw error;
  }
};

// Extend Request interface to include pronetAuth data
declare global {
  namespace Express {
    interface Request {
      pronetAuth?: {
        credentials: {
          username: string;
          apiKey: string;
          host: string;
        };
        checksum: string;
        jsonBody: string;
      };
    }
  }
}
