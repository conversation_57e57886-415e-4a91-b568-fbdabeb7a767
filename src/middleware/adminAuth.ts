import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { UserGetAuthorizedRequest } from '@/network/ebetlab/requests/user/UserGetAuthorizedRequest';
import { Request, Response, NextFunction } from 'express';
import fetch from 'node-fetch';

/**
 * Middleware to authenticate admin requests by pinging our own proxy endpoint
 *
 * This middleware:
 * 1. Extracts the Authorization header from the request
 * 2. Makes a request to our own proxy endpoint (/api/v1/users/me) which handles EbetLab auth
 * 3. If successful, allows the request to proceed
 * 4. If failed, returns 401 Unauthorized
 *
 * Usage: Apply this middleware to any admin endpoint that requires authentication
 */
export const adminAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log('🔐 Admin authentication middleware triggered');

    // Extract authorization header
    const authorization = req.headers.authorization;
    if (!authorization) {
      console.log('❌ No authorization header provided');
      res.status(401).json({
        success: false,
        message: 'Authorization header is required for admin endpoints',
      });
      return;
    }

    console.log('📡 Verifying admin credentials via proxy endpoint /api/v1/users/me');

    // Get the base URL for our own server
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    const userRequest = new UserGetAuthorizedRequest();

    const result = await ebetlabApiClient.makeAuthenticatedRequest(userRequest, req.headers['authorization'] || '');
    if (!result.success) {
      console.log(`❌ Proxy authentication failed: ${result.status}`);
      res.status(401).json({
        success: false,
        message: 'Invalid or expired admin credentials',
      });
      return;
    }

    const userResponse = result.data;

    // If we get here, the request was successful
    console.log('✅ Admin authentication successful');
    console.log('👤 Authenticated user:', userResponse?.data?.email || 'Unknown');

    // Optionally attach user info to request for use in controllers
    req.adminUser = userResponse?.data;

    // Continue to the next middleware/controller
    next();
  } catch (error) {
    console.error('❌ Admin authentication failed:', error);

    // Return 401 Unauthorized if authentication fails
    res.status(401).json({
      success: false,
      message: 'Invalid or expired admin credentials',
      error: error instanceof Error ? error.message : 'Authentication failed',
    });
  }
};

// Extend Request interface to include adminUser
declare global {
  namespace Express {
    interface Request {
      adminUser?: any;
    }
  }
}
