import { Router } from 'express';
import { slackBotService } from '../services/slackBot.service';
import { asyncHandler } from '../utils/asyncHandler';

/**
 * Slack Bot Routes Middleware
 * 
 * This middleware automatically generates Express POST routes for all registered Slack bots.
 * It integrates with the bot registry to create endpoints that handle validation,
 * processing, and Slack message sending.
 */

/**
 * Create automatic routes for all registered Slack bots
 * 
 * This function generates Express POST routes for each registered bot,
 * connecting them to the bot processing pipeline.
 * 
 * @returns Express Router with all bot routes
 */
export function createSlackBotRoutes(): Router {
  const router = Router();
  
  console.log('🤖 Creating automatic Slack bot routes...');
  
  // Get all registered bots
  const registeredBots = slackBotService.getAllBots();
  
  if (registeredBots.length === 0) {
    console.log('⚠️ No Slack bots registered - no routes created');
    return router;
  }

  // Create a POST route for each registered bot
  registeredBots.forEach(bot => {
    console.log(`📍 Creating POST route: ${bot.route} -> ${bot.channel}`);
    
    // Create the route handler
    const routeHandler = asyncHandler(async (req, res) => {
      console.log(`🤖 Slack bot route triggered: ${bot.route}`);
      console.log(`📢 Target channel: ${bot.channel}`);
      console.log(`🆔 Bot ID: ${bot.id}`);
      
      // Process the bot request using the service
      await slackBotService.processBotRequest(bot.route, req, res);
    });

    // Register the POST route
    router.post(bot.route, routeHandler);
    
    console.log(`✅ Route registered: POST ${bot.route}`);
  });

  console.log(`🎉 Created ${registeredBots.length} Slack bot routes`);
  
  return router;
}

/**
 * Get route registration statistics
 * 
 * @returns Statistics about registered routes
 */
export function getRouteStats(): {
  totalRoutes: number;
  routes: Array<{
    route: string;
    channel: string;
    botId: string;
    registeredAt: Date;
  }>;
} {
  const registeredBots = slackBotService.getAllBots();
  
  return {
    totalRoutes: registeredBots.length,
    routes: registeredBots.map(bot => ({
      route: bot.route,
      channel: bot.channel,
      botId: bot.id,
      registeredAt: bot.registeredAt,
    })),
  };
}

/**
 * Middleware to log route registration info
 * 
 * This can be used to debug route registration issues
 */
export function logRouteRegistration(): void {
  const stats = getRouteStats();
  
  console.log('📊 Slack Bot Route Registration Summary:');
  console.log(`📈 Total routes: ${stats.totalRoutes}`);
  
  if (stats.totalRoutes > 0) {
    console.log('📋 Registered routes:');
    stats.routes.forEach((route, index) => {
      console.log(`  ${index + 1}. POST ${route.route}`);
      console.log(`     Channel: ${route.channel}`);
      console.log(`     Bot ID: ${route.botId}`);
      console.log(`     Registered: ${route.registeredAt.toISOString()}`);
    });
  } else {
    console.log('⚠️ No routes registered');
  }
}

/**
 * Validate that all registered bots have valid configurations
 * 
 * @returns Validation results
 */
export function validateBotConfigurations(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const registeredBots = slackBotService.getAllBots();
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if Slack client is configured
  if (!slackBotService.isConfigured()) {
    errors.push('Slack client is not properly configured (missing SLACK_BOT_TOKEN)');
  }

  // Validate each bot configuration
  registeredBots.forEach(bot => {
    // Check route format
    if (!bot.route.startsWith('/')) {
      errors.push(`Bot ${bot.id}: Route must start with '/' (got: ${bot.route})`);
    }

    // Check channel format
    if (!bot.channel || bot.channel.trim() === '') {
      errors.push(`Bot ${bot.id}: Channel cannot be empty`);
    }

    // Check if validator and handler are functions
    if (typeof bot.validator !== 'function') {
      errors.push(`Bot ${bot.id}: Validator must be a function`);
    }

    if (typeof bot.handler !== 'function') {
      errors.push(`Bot ${bot.id}: Handler must be a function`);
    }

    // Warnings for common issues
    if (bot.channel.startsWith('#')) {
      warnings.push(`Bot ${bot.id}: Channel name includes '#' - this may not be necessary`);
    }

    if (!bot.route.includes('/v1/')) {
      warnings.push(`Bot ${bot.id}: Route doesn't follow /v1/ pattern - consider consistency`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Express middleware to add bot route information to requests
 * 
 * This middleware adds information about which bot is handling the request
 */
export function addBotInfo() {
  return (req: any, res: any, next: any) => {
    // Add bot information to request object for debugging
    const bot = slackBotService.getBotByRoute(req.path);
    
    if (bot) {
      req.slackBot = {
        id: bot.id,
        channel: bot.channel,
        registeredAt: bot.registeredAt,
      };
    }

    next();
  };
}

/**
 * Health check endpoint for Slack bot system
 * 
 * @returns Health check information
 */
export function createHealthCheckRoute(): Router {
  const router = Router();

  router.get('/slack-bots/health', asyncHandler(async (req, res) => {
    const stats = slackBotService.getStats();
    const validation = validateBotConfigurations();
    const routeStats = getRouteStats();

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      slackBotSystem: {
        isConfigured: stats.isClientConfigured,
        totalBots: stats.totalBots,
        totalRoutes: routeStats.totalRoutes,
        registeredRoutes: stats.registeredRoutes,
        validation: {
          isValid: validation.isValid,
          errors: validation.errors,
          warnings: validation.warnings,
        },
      },
    });
  }));

  return router;
}
