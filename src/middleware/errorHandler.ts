import { Request, Response, NextFunction } from 'express';
import { ApiError } from '@/types/errors';

export const errorHandler = (error: Error | ApiError, req: Request, res: Response, _next: NextFunction): void => {
  // Log the error
  console.error('❌ Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
  });

  // Check if it's a custom API error
  if (error instanceof ApiError) {
    res.status(error.statusCode).json({
      success: false,
      error: error.message,
      ...(process.env['NODE_ENV'] === 'development' && { stack: error.stack }),
    });
    return;
  }

  // Handle specific error types
  if (error.name === 'ValidationError') {
    res.status(400).json({
      success: false,
      error: 'Validation Error',
      details: error.message,
    });
    return;
  }

  if (error.name === 'CastError') {
    res.status(400).json({
      success: false,
      error: 'Invalid ID format',
    });
    return;
  }

  // Default error response
  res.status(500).json({
    success: false,
    // error: process.env['NODE_ENV'] === 'production' ? 'Internal Server Error' : error.message,
    error: error.message,
    ...(process.env['NODE_ENV'] === 'development' && { stack: error.stack }),
  });
};
