/**
 * Test script for the Site Claimable Bonuses implementation
 * 
 * This script tests the new site claimable bonuses endpoints to ensure they work correctly.
 * Run this after starting the server and running the migration to verify the implementation.
 */

import fetch from 'node-fetch';

const BASE_URL = process.env['BASE_URL'] || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/pronet/v1`;

async function testSiteClaimableBonusesEndpoints() {
  console.log('🧪 Testing Site Claimable Bonuses endpoints...\n');

  try {
    // Test 1: List all site claimable bonus configurations
    console.log('📋 Test 1: List all site claimable bonus configurations');
    const listResponse = await fetch(`${API_BASE}/site-claimable-bonuses`);
    const listData = await listResponse.json();
    
    console.log(`Status: ${listResponse.status}`);
    console.log(`Response:`, JSON.stringify(listData, null, 2));
    
    if (listResponse.ok && listData.success) {
      console.log('✅ List configurations test passed\n');
    } else {
      console.log('❌ List configurations test failed\n');
    }

    // Test 2: Get claimable bonuses
    console.log('📋 Test 2: Get currently claimable bonuses');
    const claimableResponse = await fetch(`${API_BASE}/site-claimable-bonuses/claimable`);
    const claimableData = await claimableResponse.json();
    
    console.log(`Status: ${claimableResponse.status}`);
    console.log(`Response:`, JSON.stringify(claimableData, null, 2));
    
    if (claimableResponse.ok && claimableData.success) {
      console.log('✅ Get claimable bonuses test passed\n');
    } else {
      console.log('❌ Get claimable bonuses test failed\n');
    }

    // Test 3: Check if a specific bonus is claimable
    console.log('🔍 Test 3: Check if bonus ID 1 is claimable');
    const checkResponse = await fetch(`${API_BASE}/site-claimable-bonuses/check/1`);
    const checkData = await checkResponse.json();
    
    console.log(`Status: ${checkResponse.status}`);
    console.log(`Response:`, JSON.stringify(checkData, null, 2));
    
    if (checkResponse.ok && checkData.success) {
      console.log('✅ Check claimable test passed\n');
    } else {
      console.log('❌ Check claimable test failed\n');
    }

    // Test 4: Try to update a slot (this will likely fail without a valid bonus ID)
    console.log('🔄 Test 4: Try to update slot_1 configuration');
    const updateResponse = await fetch(`${API_BASE}/site-claimable-bonuses/slot_1`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bonusId: 1,
        isActive: true,
      }),
    });
    const updateData = await updateResponse.json();
    
    console.log(`Status: ${updateResponse.status}`);
    console.log(`Response:`, JSON.stringify(updateData, null, 2));
    
    if (updateResponse.ok && updateData.success) {
      console.log('✅ Update slot test passed\n');
    } else {
      console.log('❌ Update slot test failed (expected if no valid bonus exists)\n');
    }

    // Test 5: Try to toggle slot active status
    console.log('🔄 Test 5: Try to toggle slot_1 active status');
    const toggleResponse = await fetch(`${API_BASE}/site-claimable-bonuses/slot_1/toggle`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        isActive: false,
      }),
    });
    const toggleData = await toggleResponse.json();
    
    console.log(`Status: ${toggleResponse.status}`);
    console.log(`Response:`, JSON.stringify(toggleData, null, 2));
    
    if (toggleResponse.ok && toggleData.success) {
      console.log('✅ Toggle slot test passed\n');
    } else {
      console.log('❌ Toggle slot test failed\n');
    }

    // Test 6: Invalid bonus ID check
    console.log('🔍 Test 6: Check invalid bonus ID');
    const invalidCheckResponse = await fetch(`${API_BASE}/site-claimable-bonuses/check/invalid`);
    const invalidCheckData = await invalidCheckResponse.json();
    
    console.log(`Status: ${invalidCheckResponse.status}`);
    console.log(`Response:`, JSON.stringify(invalidCheckData, null, 2));
    
    if (invalidCheckResponse.status === 400 && !invalidCheckData.success) {
      console.log('✅ Invalid bonus ID test passed\n');
    } else {
      console.log('❌ Invalid bonus ID test failed\n');
    }

    // Test 7: Non-existent slot update
    console.log('🔄 Test 7: Try to update non-existent slot');
    const nonExistentResponse = await fetch(`${API_BASE}/site-claimable-bonuses/slot_999`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bonusId: 1,
        isActive: true,
      }),
    });
    const nonExistentData = await nonExistentResponse.json();
    
    console.log(`Status: ${nonExistentResponse.status}`);
    console.log(`Response:`, JSON.stringify(nonExistentData, null, 2));
    
    if (nonExistentResponse.status === 404 && !nonExistentData.success) {
      console.log('✅ Non-existent slot test passed\n');
    } else {
      console.log('❌ Non-existent slot test failed\n');
    }

    // Test 8: Create a new slot
    console.log('➕ Test 8: Create a new slot');
    const createResponse = await fetch(`${API_BASE}/site-claimable-bonuses`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        slotName: 'test_slot_new',
        bonusId: 1,
        isActive: true,
      }),
    });
    const createData = await createResponse.json();

    console.log(`Status: ${createResponse.status}`);
    console.log(`Response:`, JSON.stringify(createData, null, 2));

    if (createResponse.status === 201 && createData.success) {
      console.log('✅ Create slot test passed\n');
    } else {
      console.log('❌ Create slot test failed (expected if bonus ID 1 doesn\'t exist)\n');
    }

    // Test 9: Try to create a slot with duplicate name
    console.log('➕ Test 9: Try to create slot with duplicate name');
    const duplicateResponse = await fetch(`${API_BASE}/site-claimable-bonuses`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        slotName: 'slot_1', // This should already exist from migration
        bonusId: 1,
        isActive: true,
      }),
    });
    const duplicateData = await duplicateResponse.json();

    console.log(`Status: ${duplicateResponse.status}`);
    console.log(`Response:`, JSON.stringify(duplicateData, null, 2));

    if (duplicateResponse.status === 409 && !duplicateData.success) {
      console.log('✅ Duplicate slot name test passed\n');
    } else {
      console.log('❌ Duplicate slot name test failed\n');
    }

    // Test 10: Create slot with invalid data
    console.log('➕ Test 10: Create slot with invalid data');
    const invalidCreateResponse = await fetch(`${API_BASE}/site-claimable-bonuses`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        slotName: '', // Invalid empty name
        bonusId: 'invalid', // Invalid bonus ID
      }),
    });
    const invalidCreateData = await invalidCreateResponse.json();

    console.log(`Status: ${invalidCreateResponse.status}`);
    console.log(`Response:`, JSON.stringify(invalidCreateData, null, 2));

    if (invalidCreateResponse.status === 400 && !invalidCreateData.success) {
      console.log('✅ Invalid create data test passed\n');
    } else {
      console.log('❌ Invalid create data test failed\n');
    }

    // Test 11: Delete slot by name (if it was created in test 8)
    console.log('🗑️ Test 11: Delete slot by name');
    const deleteResponse = await fetch(`${API_BASE}/site-claimable-bonuses/test_slot_new`, {
      method: 'DELETE',
    });
    const deleteData = await deleteResponse.json();

    console.log(`Status: ${deleteResponse.status}`);
    console.log(`Response:`, JSON.stringify(deleteData, null, 2));

    if (deleteResponse.ok && deleteData.success) {
      console.log('✅ Delete slot by name test passed\n');
    } else {
      console.log('❌ Delete slot by name test failed (expected if slot wasn\'t created)\n');
    }

    // Test 12: Try to delete non-existent slot
    console.log('🗑️ Test 12: Try to delete non-existent slot');
    const deleteNonExistentResponse = await fetch(`${API_BASE}/site-claimable-bonuses/non_existent_slot`, {
      method: 'DELETE',
    });
    const deleteNonExistentData = await deleteNonExistentResponse.json();

    console.log(`Status: ${deleteNonExistentResponse.status}`);
    console.log(`Response:`, JSON.stringify(deleteNonExistentData, null, 2));

    if (deleteNonExistentResponse.status === 404 && !deleteNonExistentData.success) {
      console.log('✅ Delete non-existent slot test passed\n');
    } else {
      console.log('❌ Delete non-existent slot test failed\n');
    }

    // Test 13: Delete slot by ID
    console.log('🗑️ Test 13: Delete slot by ID (testing with ID 999)');
    const deleteByIdResponse = await fetch(`${API_BASE}/site-claimable-bonuses/id/999`, {
      method: 'DELETE',
    });
    const deleteByIdData = await deleteByIdResponse.json();

    console.log(`Status: ${deleteByIdResponse.status}`);
    console.log(`Response:`, JSON.stringify(deleteByIdData, null, 2));

    if (deleteByIdResponse.status === 404 && !deleteByIdData.success) {
      console.log('✅ Delete slot by ID test passed\n');
    } else {
      console.log('❌ Delete slot by ID test failed\n');
    }

    // Test 14: Delete with invalid ID
    console.log('🗑️ Test 14: Delete with invalid ID');
    const deleteInvalidIdResponse = await fetch(`${API_BASE}/site-claimable-bonuses/id/invalid`, {
      method: 'DELETE',
    });
    const deleteInvalidIdData = await deleteInvalidIdResponse.json();

    console.log(`Status: ${deleteInvalidIdResponse.status}`);
    console.log(`Response:`, JSON.stringify(deleteInvalidIdData, null, 2));

    if (deleteInvalidIdResponse.status === 400 && !deleteInvalidIdData.success) {
      console.log('✅ Delete with invalid ID test passed\n');
    } else {
      console.log('❌ Delete with invalid ID test failed\n');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  testSiteClaimableBonusesEndpoints();
}

export { testSiteClaimableBonusesEndpoints };
