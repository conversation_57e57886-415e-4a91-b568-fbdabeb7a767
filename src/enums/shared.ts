/**
 * Shared enums used across multiple entities to avoid duplication
 */

/**
 * Compare operators used in mission rules and objectives
 */
export enum CompareOperator {
  EQUAL = 'eq',
  NOT_EQUAL = 'ne',
  GREATER_THAN = 'gt',
  LESS_THAN = 'lt',
  GREATER_EQUAL = 'ge',
  LESS_EQUAL = 'le',
}

/**
 * Mission types for categorizing missions
 */
export enum MissionType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  CUSTOM = 'custom',
}

/**
 * Rule types for mission eligibility rules
 */
export enum RuleType {
  JOINED_AT = 'joinedAt',
  TOTAL_DEPOSIT = 'totalDeposit',
  TOTAL_WITHDRAW = 'totalWithdraw',
  NET_PROFIT = 'netProfit',
  VIP_RANK = 'vipRank',
  COUNTRY = 'country',
  PHONE_NUMBER = 'phoneNumber',
  KYC_LEVEL = 'kycLevel',
  REFERRER = 'referrer',
}

/**
 * VIP levels in hierarchical order (lowest to highest)
 */
export enum VipLevel {
  NO_VIP = 'no-vip',
  IRON = 'iron',
  COPPER = 'copper',
  BRONZE = 'bronze',
  BRASS = 'brass',
  NICKEL = 'nickel',
  STEEL = 'steel',
  COBALT = 'cobalt',
  TITANIUM = 'titanium',
  TUNGSTEN = 'tungsten',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum',
  PALLADIUM = 'palladium',
  RHODIUM = 'rhodium',
  OSMIUM = 'osmium',
  IRIDIUM = 'iridium',
  MITHRIL = 'mithril',
  ADAMANTITE = 'adamantite',
  ORICHALCUM = 'orichalcum',
  VIBRANIUM = 'vibranium',
  UNOBTANIUM = 'unobtanium',
  ETERNIUM = 'eternium',
}

/**
 * Objective types for mission objectives (matches mission types)
 */
export enum ObjectiveType {
  SLOT = 'slot',
  LIVE_CASINO = 'liveCasino',
  DEPOSIT = 'deposit',
  WITHDRAW = 'withdraw',
  TURNOVER = 'turnover',
}

export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  CHARGE = 'charge',
  REFUND = 'refund',
}

export enum TransactionCategory {
  MISSION_REWARD = 'mission_reward',
  MARKET_PURCHASE = 'market_purchase',
  ADMIN_ADJUSTMENT = 'admin_adjustment',
}

export enum MarketProductType {
  GENERAL = 'general',
  SLOTS = 'slots',
}

export enum MarketProductCategory {
  FREE_SPINS = 'free_spins',
  CASH = 'cash',
  RELOAD = 'reload',
  SCATTER = 'scatter',
}

export enum MarketProductRequestStatus {
  REJECTED = 'rejected',
  REFUNDED = 'refunded',
  PENDING = 'pending',
  COMPLETED = 'completed',
}
