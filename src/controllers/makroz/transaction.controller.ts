import { Request, Response } from 'express';
import { TransactionsService, AdminTransactionFilters } from '@/services/transactions.service';
import { TransactionType, TransactionCategory } from '@/enums/shared';
import { asyncHandler } from '@/utils/asyncHandler';
import { extractUserIdFromToken, validateJwtToken } from '@/utils/jwtUtils';

const transactionsService = new TransactionsService();

export const getTransactionStats = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching transaction statistics');
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  try {
    const stats = await transactionsService.getTransactionStats();

    res.status(200).json({
      success: true,
      message: 'Transaction statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching transaction statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getTransactions = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching transactions list with query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Parse and validate query parameters
  const filters: AdminTransactionFilters = {};
  let page = 1;
  let limit = 20;
  let sortBy = 'createdAt';
  let sortOrder: 'ASC' | 'DESC' = 'DESC';

  // Pagination
  if (req.query['page']) {
    const pageNum = parseInt(req.query['page'] as string);
    if (isNaN(pageNum) || pageNum < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page must be a positive integer',
      });
    }
    page = pageNum;
  }

  if (req.query['limit']) {
    const limitNum = parseInt(req.query['limit'] as string);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
      });
    }
    limit = limitNum;
  }

  // Sorting
  if (req.query['sortBy']) {
    const validSortFields = ['id', 'fromUserId', 'toUserId', 'type', 'category', 'amount', 'createdAt', 'updatedAt'];
    const sortByParam = req.query['sortBy'] as string;
    if (!validSortFields.includes(sortByParam)) {
      return res.status(400).json({
        success: false,
        message: `sortBy must be one of: ${validSortFields.join(', ')}`,
      });
    }
    sortBy = sortByParam;
  }

  if (req.query['sortOrder']) {
    const sortOrderParam = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC', 'asc', 'desc'].includes(sortOrderParam)) {
      return res.status(400).json({
        success: false,
        message: 'sortOrder must be either "ASC", "DESC", "asc", or "desc"',
      });
    }
    sortOrder = sortOrderParam.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filters
  if (req.query['id']) {
    const id = parseInt(req.query['id'] as string);
    if (isNaN(id) || id <= 0) {
      return res.status(400).json({
        success: false,
        message: 'ID must be a positive integer',
      });
    }
    filters.id = id;
  }

  if (req.query['fromUserId']) {
    const fromUserId = parseInt(req.query['fromUserId'] as string);
    if (isNaN(fromUserId) || fromUserId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'fromUserId must be a positive integer',
      });
    }
    filters.fromUserId = fromUserId;
  }

  if (req.query['toUserId']) {
    const toUserId = parseInt(req.query['toUserId'] as string);
    if (isNaN(toUserId) || toUserId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'toUserId must be a positive integer',
      });
    }
    filters.toUserId = toUserId;
  }

  if (req.query['type']) {
    const type = req.query['type'] as string;
    if (!Object.values(TransactionType).includes(type as TransactionType)) {
      return res.status(400).json({
        success: false,
        message: `type must be one of: ${Object.values(TransactionType).join(', ')}`,
      });
    }
    filters.type = type as TransactionType;
  }

  if (req.query['category']) {
    const category = req.query['category'] as string;
    if (!Object.values(TransactionCategory).includes(category as TransactionCategory)) {
      return res.status(400).json({
        success: false,
        message: `category must be one of: ${Object.values(TransactionCategory).join(', ')}`,
      });
    }
    filters.category = category as TransactionCategory;
  }

  if (req.query['minAmount']) {
    const minAmount = parseFloat(req.query['minAmount'] as string);
    if (isNaN(minAmount) || minAmount < 0) {
      return res.status(400).json({
        success: false,
        message: 'minAmount must be a non-negative number',
      });
    }
    filters.minAmount = minAmount;
  }

  if (req.query['maxAmount']) {
    const maxAmount = parseFloat(req.query['maxAmount'] as string);
    if (isNaN(maxAmount) || maxAmount < 0) {
      return res.status(400).json({
        success: false,
        message: 'maxAmount must be a non-negative number',
      });
    }
    filters.maxAmount = maxAmount;
  }

  if (req.query['search']) {
    filters.search = req.query['search'] as string;
  }

  if (req.query['createdAfter']) {
    const createdAfter = new Date(req.query['createdAfter'] as string);
    if (isNaN(createdAfter.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'createdAfter must be a valid date',
      });
    }
    filters.createdAfter = createdAfter;
  }

  if (req.query['createdBefore']) {
    const createdBefore = new Date(req.query['createdBefore'] as string);
    if (isNaN(createdBefore.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'createdBefore must be a valid date',
      });
    }
    filters.createdBefore = createdBefore;
  }

  try {
    const result = await transactionsService.listTransactions({
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    });

    res.status(200).json({
      success: true,
      message: 'Transactions retrieved successfully',
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transactions',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getUserTransactions = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching user transactions');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract external ID from JWT token
  const externalId = extractUserIdFromToken(authHeader);
  if (!externalId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log('Extracted external ID from token:', externalId);

  // Parse query parameters for pagination and filtering
  let page = 1;
  let limit = 20;
  let sortBy = 'createdAt';
  let sortOrder: 'ASC' | 'DESC' = 'DESC';

  // Pagination
  if (req.query['page']) {
    const pageNum = parseInt(req.query['page'] as string);
    if (isNaN(pageNum) || pageNum < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page must be a positive integer',
      });
    }
    page = pageNum;
  }

  if (req.query['limit']) {
    const limitNum = parseInt(req.query['limit'] as string);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
      });
    }
    limit = limitNum;
  }

  // Sorting
  if (req.query['sortBy']) {
    const validSortFields = ['id', 'type', 'category', 'amount', 'createdAt'];
    const sortByParam = req.query['sortBy'] as string;
    if (!validSortFields.includes(sortByParam)) {
      return res.status(400).json({
        success: false,
        message: `sortBy must be one of: ${validSortFields.join(', ')}`,
      });
    }
    sortBy = sortByParam;
  }

  if (req.query['sortOrder']) {
    const sortOrderParam = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC', 'asc', 'desc'].includes(sortOrderParam)) {
      return res.status(400).json({
        success: false,
        message: 'sortOrder must be either "ASC", "DESC", "asc", or "desc"',
      });
    }
    sortOrder = sortOrderParam.toUpperCase() as 'ASC' | 'DESC';
  }

  // Optional filters
  const filters: AdminTransactionFilters = {
    toUserId: externalId, // Only show transactions for this user
  };

  if (req.query['type']) {
    const type = req.query['type'] as string;
    if (!Object.values(TransactionType).includes(type as TransactionType)) {
      return res.status(400).json({
        success: false,
        message: `type must be one of: ${Object.values(TransactionType).join(', ')}`,
      });
    }
    filters.type = type as TransactionType;
  }

  if (req.query['category']) {
    const category = req.query['category'] as string;
    if (!Object.values(TransactionCategory).includes(category as TransactionCategory)) {
      return res.status(400).json({
        success: false,
        message: `category must be one of: ${Object.values(TransactionCategory).join(', ')}`,
      });
    }
    filters.category = category as TransactionCategory;
  }

  if (req.query['createdAfter']) {
    const createdAfter = new Date(req.query['createdAfter'] as string);
    if (isNaN(createdAfter.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'createdAfter must be a valid date',
      });
    }
    filters.createdAfter = createdAfter;
  }

  if (req.query['createdBefore']) {
    const createdBefore = new Date(req.query['createdBefore'] as string);
    if (isNaN(createdBefore.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'createdBefore must be a valid date',
      });
    }
    filters.createdBefore = createdBefore;
  }

  try {
    const result = await transactionsService.listTransactions({
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    });

    res.status(200).json({
      success: true,
      message: 'User transactions retrieved successfully',
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching user transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user transactions',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteTransaction = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting transaction ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid transaction ID is required',
    });
  }

  try {
    const deleted = await transactionsService.deleteTransaction(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Transaction deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete transaction',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
