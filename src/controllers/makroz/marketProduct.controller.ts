import { Request, Response } from 'express';
import { MarketProductsService } from '@/services/marketProducts.service';
import { MarketProductType, MarketProductCategory } from '@/enums/shared';
import { asyncHandler } from '@/utils/asyncHandler';
import { validateJwtToken, extractUserIdFromToken } from '@/utils/jwtUtils';

// Initialize service
const marketProductsService = new MarketProductsService();

/**
 * POST /makroz/admin/market-products
 * Create a new market product
 */
export const createMarketProduct = asyncHandler(async (req: Request, res: Response) => {
  console.log('🛍️ Creating market product:', JSON.stringify(req.body, null, 2));

  const {
    name,
    slug,
    name_i18n,
    description_i18n,
    type,
    category,
    availableAmount,
    isMultiPerBuyer,
    photoUrl,
    price,
    currencies,
    providers,
  } = req.body;

  // Validate required fields
  if (!name?.trim()) {
    return res.status(400).json({
      success: false,
      error: 'name is required',
    });
  }

  if (!type) {
    return res.status(400).json({
      success: false,
      error: 'type is required',
    });
  }

  if (!category) {
    return res.status(400).json({
      success: false,
      error: 'category is required',
    });
  }

  if (!photoUrl?.trim()) {
    return res.status(400).json({
      success: false,
      error: 'photoUrl is required',
    });
  }

  if (price === undefined || price === null || price <= 0) {
    return res.status(400).json({
      success: false,
      error: 'price must be a positive number',
    });
  }

  if (!currencies || !Array.isArray(currencies) || currencies.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'currencies is required and must be a non-empty array',
    });
  }

  // Validate providers field - must be an array or null
  if (providers !== null && providers !== undefined) {
    if (!Array.isArray(providers)) {
      return res.status(400).json({
        success: false,
        error: 'providers must be an array or null',
      });
    }
  }

  // Validate currencies format (array of strings)
  if (!currencies.every((currency: any) => typeof currency === 'string' && currency.trim().length > 0)) {
    return res.status(400).json({
      success: false,
      error: 'currencies must be an array of non-empty strings',
    });
  }

  // Validate enum values
  if (!Object.values(MarketProductType).includes(type)) {
    return res.status(400).json({
      success: false,
      error: `Invalid type. Must be one of: ${Object.values(MarketProductType).join(', ')}`,
    });
  }

  if (!Object.values(MarketProductCategory).includes(category)) {
    return res.status(400).json({
      success: false,
      error: `Invalid category. Must be one of: ${Object.values(MarketProductCategory).join(', ')}`,
    });
  }

  try {
    const product = await marketProductsService.createProduct({
      slug: slug?.trim(),
      name_i18n: name_i18n || {},
      description_i18n: description_i18n || {},
      type,
      category,
      availableAmount: availableAmount ?? null,
      isMultiPerBuyer: isMultiPerBuyer || false,
      photoUrl: photoUrl.trim(),
      price: price.toString(),
      currencies: currencies.map((c: string) => c.trim()),
      providers,
    });

    console.log('✅ Market product created successfully:', product.id);

    res.status(201).json({
      success: true,
      data: product,
    });
  } catch (error: any) {
    console.error('❌ Failed to create market product:', error);

    if (error.message.includes('already exists')) {
      return res.status(409).json({
        success: false,
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create market product',
    });
  }
});

/**
 * GET /makroz/admin/market-products
 * List market products with optional filters and pagination
 */
export const listMarketProducts = asyncHandler(async (req: Request, res: Response) => {
  console.log('📋 Listing market products with query:', JSON.stringify(req.query, null, 2));

  const { type, category, page = '1', limit = '20', minPrice, maxPrice, minAmount, maxAmount, search } = req.query;

  // Validate pagination parameters
  const pageNum = parseInt(page as string, 10);
  const limitNum = parseInt(limit as string, 10);

  if (isNaN(pageNum) || pageNum < 1) {
    return res.status(400).json({
      success: false,
      error: 'page must be a positive integer',
    });
  }

  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    return res.status(400).json({
      success: false,
      error: 'limit must be between 1 and 100',
    });
  }

  // Validate price filters
  let minPriceNum: number | undefined;
  let maxPriceNum: number | undefined;

  if (minPrice) {
    minPriceNum = parseFloat(minPrice as string);
    if (isNaN(minPriceNum) || minPriceNum < 0) {
      return res.status(400).json({
        success: false,
        error: 'minPrice must be a non-negative number',
      });
    }
  }

  if (maxPrice) {
    maxPriceNum = parseFloat(maxPrice as string);
    if (isNaN(maxPriceNum) || maxPriceNum < 0) {
      return res.status(400).json({
        success: false,
        error: 'maxPrice must be a non-negative number',
      });
    }
  }

  if (minPriceNum !== undefined && maxPriceNum !== undefined && minPriceNum > maxPriceNum) {
    return res.status(400).json({
      success: false,
      error: 'minPrice cannot be greater than maxPrice',
    });
  }

  // Validate amount filters
  let minAmountNum: number | undefined;
  let maxAmountNum: number | undefined;

  if (minAmount) {
    minAmountNum = parseInt(minAmount as string, 10);
    if (isNaN(minAmountNum) || minAmountNum < 0) {
      return res.status(400).json({
        success: false,
        error: 'minAmount must be a non-negative integer',
      });
    }
  }

  if (maxAmount) {
    maxAmountNum = parseInt(maxAmount as string, 10);
    if (isNaN(maxAmountNum) || maxAmountNum < 0) {
      return res.status(400).json({
        success: false,
        error: 'maxAmount must be a non-negative integer',
      });
    }
  }

  if (minAmountNum !== undefined && maxAmountNum !== undefined && minAmountNum > maxAmountNum) {
    return res.status(400).json({
      success: false,
      error: 'minAmount cannot be greater than maxAmount',
    });
  }

  // Validate enum values if provided
  if (type && !Object.values(MarketProductType).includes(type as MarketProductType)) {
    return res.status(400).json({
      success: false,
      error: `Invalid type. Must be one of: ${Object.values(MarketProductType).join(', ')}`,
    });
  }

  if (category && !Object.values(MarketProductCategory).includes(category as MarketProductCategory)) {
    return res.status(400).json({
      success: false,
      error: `Invalid category. Must be one of: ${Object.values(MarketProductCategory).join(', ')}`,
    });
  }

  try {
    const filters: any = {};
    if (type) filters.type = type as MarketProductType;
    if (category) filters.category = category as MarketProductCategory;
    if (minPriceNum !== undefined) filters.minPrice = minPriceNum;
    if (maxPriceNum !== undefined) filters.maxPrice = maxPriceNum;
    if (minAmountNum !== undefined) filters.minAmount = minAmountNum;
    if (maxAmountNum !== undefined) filters.maxAmount = maxAmountNum;
    if (search) filters.search = (search as string).trim();

    const result = await marketProductsService.listProducts({
      filters,
      page: pageNum,
      limit: limitNum,
    });

    console.log(`✅ Retrieved ${result.data.length} market products (${result.total} total)`);

    res.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error: any) {
    console.error('❌ Failed to list market products:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to list market products',
    });
  }
});

/**
 * GET /makroz/admin/market-products/:id
 * Get market product by ID
 */
export const getMarketProductById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  console.log(`🔍 Getting market product by ID: ${id}`);

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Product ID is required',
    });
  }

  const productId = parseInt(id, 10);
  if (isNaN(productId) || productId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid product ID',
    });
  }

  try {
    const product = await marketProductsService.getProductById(productId);

    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Market product not found',
      });
    }

    console.log('✅ Market product found:', product.id);

    res.json({
      success: true,
      data: product,
    });
  } catch (error: any) {
    console.error('❌ Failed to get market product:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to get market product',
    });
  }
});

/**
 * DELETE /makroz/admin/market-products/:id
 * Delete market product by ID
 */
export const deleteMarketProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  console.log(`🗑️ Deleting market product: ${id}`);

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Product ID is required',
    });
  }

  const productId = parseInt(id, 10);
  if (isNaN(productId) || productId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid product ID',
    });
  }

  try {
    await marketProductsService.deleteProduct(productId);

    console.log('✅ Market product deleted successfully:', productId);

    res.json({
      success: true,
      message: 'Market product deleted successfully',
    });
  } catch (error: any) {
    console.error('❌ Failed to delete market product:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Market product not found',
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete market product',
    });
  }
});

/**
 * PATCH /makroz/admin/market-products/:id
 * Update market product by ID
 */
export const updateMarketProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  console.log(`🔄 Updating market product ${id}:`, JSON.stringify(req.body, null, 2));

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Product ID is required',
    });
  }

  const productId = parseInt(id, 10);
  if (isNaN(productId) || productId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid product ID',
    });
  }

  const {
    name,
    slug,
    name_i18n,
    description_i18n,
    type,
    category,
    availableAmount,
    isMultiPerBuyer,
    photoUrl,
    price,
    currencies,
    providers,
  } = req.body;

  // Validate enum values if provided
  if (type && !Object.values(MarketProductType).includes(type)) {
    return res.status(400).json({
      success: false,
      error: `Invalid type. Must be one of: ${Object.values(MarketProductType).join(', ')}`,
    });
  }

  if (category && !Object.values(MarketProductCategory).includes(category)) {
    return res.status(400).json({
      success: false,
      error: `Invalid category. Must be one of: ${Object.values(MarketProductCategory).join(', ')}`,
    });
  }

  // Validate price if provided
  if (price !== undefined && (price === null || price <= 0)) {
    return res.status(400).json({
      success: false,
      error: 'price must be a positive number',
    });
  }

  // Validate currencies if provided
  if (currencies !== undefined) {
    if (!Array.isArray(currencies) || currencies.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'currencies must be a non-empty array',
      });
    }

    if (!currencies.every((currency: any) => typeof currency === 'string' && currency.trim().length > 0)) {
      return res.status(400).json({
        success: false,
        error: 'currencies must be an array of non-empty strings',
      });
    }
  }

  // Validate providers field - must be an array or null
  if (providers !== undefined) {
    if (providers !== null && !Array.isArray(providers)) {
      return res.status(400).json({
        success: false,
        error: 'providers must be an array or null',
      });
    }
  }

  try {
    // Build update options
    const updateOptions: any = {};

    if (name !== undefined) updateOptions.name = name;
    if (slug !== undefined) updateOptions.slug = slug;
    if (name_i18n !== undefined) updateOptions.name_i18n = name_i18n;
    if (description_i18n !== undefined) updateOptions.description_i18n = description_i18n;
    if (type !== undefined) updateOptions.type = type;
    if (category !== undefined) updateOptions.category = category;
    if (availableAmount !== undefined) updateOptions.availableAmount = availableAmount;
    if (isMultiPerBuyer !== undefined) updateOptions.isMultiPerBuyer = isMultiPerBuyer;
    if (photoUrl !== undefined) updateOptions.photoUrl = photoUrl;
    if (price !== undefined) updateOptions.price = price.toString();
    if (currencies !== undefined) updateOptions.currencies = currencies.map((c: string) => c.trim());
    if (providers !== undefined) updateOptions.providers = providers;

    const updatedProduct = await marketProductsService.updateProduct(productId, updateOptions);

    console.log('✅ Market product updated successfully:', productId);

    res.json({
      success: true,
      data: updatedProduct,
    });
  } catch (error: any) {
    console.error('❌ Failed to update market product:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Market product not found',
      });
    }

    if (error.message.includes('already exists')) {
      return res.status(409).json({
        success: false,
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to update market product',
    });
  }
});

/**
 * GET /makroz/public/market-products
 * List market products for public access with optional user-specific availability
 * Uses SQL-based availability logic for optimal performance
 */
export const listPublicMarketProducts = asyncHandler(async (req: Request, res: Response) => {
  console.log('📋 Listing public market products with query:', JSON.stringify(req.query, null, 2));

  // Extract optional JWT token from Authorization header
  const authHeader = req.headers.authorization;
  let userId: number | undefined;

  if (authHeader) {
    // Validate JWT token if provided
    const tokenValidation = validateJwtToken(authHeader);
    if (tokenValidation.valid) {
      userId = extractUserIdFromToken(authHeader) || undefined;
      console.log('🔐 Valid token provided, user ID:', userId);
    } else {
      console.log('⚠️ Invalid token provided, proceeding without user context');
    }
  }

  const { type, category, page = '1', limit = '20' } = req.query;

  // Validate pagination parameters
  const pageNum = parseInt(page as string, 10);
  const limitNum = parseInt(limit as string, 10);

  if (isNaN(pageNum) || pageNum < 1) {
    return res.status(400).json({
      success: false,
      error: 'page must be a positive integer',
    });
  }

  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    return res.status(400).json({
      success: false,
      error: 'limit must be between 1 and 100',
    });
  }

  // Validate type filter if provided
  if (type && !Object.values(MarketProductType).includes(type as MarketProductType)) {
    return res.status(400).json({
      success: false,
      error: `Invalid type. Must be one of: ${Object.values(MarketProductType).join(', ')}`,
    });
  }

  // Validate category filter if provided
  if (category && !Object.values(MarketProductCategory).includes(category as MarketProductCategory)) {
    return res.status(400).json({
      success: false,
      error: `Invalid category. Must be one of: ${Object.values(MarketProductCategory).join(', ')}`,
    });
  }

  try {
    const filters: any = {};
    if (type) filters.type = type as MarketProductType;
    if (category) filters.category = category as MarketProductCategory;

    const result = await marketProductsService.listPublicProducts({
      filters,
      page: pageNum,
      limit: limitNum,
      userId,
    });

    console.log(`✅ Retrieved ${result.data.length} public market products (${result.total} total)`);

    res.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error: any) {
    console.error('❌ Failed to list public market products:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to list market products',
    });
  }
});
