import { Request, Response } from 'express';
import { slackBotService } from '@/services/slackBot.service';
import { asyncHandler } from '@/utils/asyncHandler';
import { ApiResponse } from '@/types/api';

/**
 * Slack Bot Controller
 * 
 * Provides admin endpoints for managing and monitoring Slack bots
 */
export class SlackBotController {
  /**
   * GET /api/v1/makroz/admin/slack-bots - Get information about all registered Slack bots
   * 
   * Returns detailed information about all registered Slack bots including:
   * - Bot ID and registration timestamp
   * - Route and channel configuration
   * - Bot status and health information
   * - Registration statistics
   */
  static getAllBots = asyncHandler(async (req: Request, res: Response) => {
    console.log('📋 Admin request: Get all Slack bots');

    try {
      // Get all registered bots from the service
      const registeredBots = slackBotService.getAllBots();
      
      // Transform bot data for admin frontend
      const botsInfo = registeredBots.map(bot => ({
        id: bot.id,
        route: bot.route,
        channel: bot.channel,
        registeredAt: bot.registeredAt,
        status: 'active', // All registered bots are considered active
        description: getBotDescription(bot.route),
        type: getBotType(bot.route),
      }));

      // Get system statistics
      const stats = {
        totalBots: registeredBots.length,
        activeChannels: [...new Set(registeredBots.map(bot => bot.channel))].length,
        isSlackConfigured: slackBotService.isConfigured(),
        registrationTimeRange: registeredBots.length > 0 ? {
          earliest: Math.min(...registeredBots.map(bot => bot.registeredAt.getTime())),
          latest: Math.max(...registeredBots.map(bot => bot.registeredAt.getTime())),
        } : null,
      };

      const response: ApiResponse<{
        bots: typeof botsInfo;
        stats: typeof stats;
      }> = {
        success: true,
        message: `Retrieved ${botsInfo.length} registered Slack bots`,
        data: {
          bots: botsInfo,
          stats,
        },
      };

      console.log(`✅ Successfully retrieved ${botsInfo.length} Slack bots`);
      console.log(`📊 Stats: ${stats.totalBots} bots, ${stats.activeChannels} channels`);

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to retrieve Slack bots:', error);
      
      const response: ApiResponse<null> = {
        success: false,
        message: 'Failed to retrieve Slack bot information',
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: null,
      };

      res.status(500).json(response);
    }
  });

  /**
   * GET /api/v1/makroz/admin/slack-bots/:id - Get information about a specific Slack bot
   */
  static getBotById = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    console.log(`🔍 Admin request: Get Slack bot by ID: ${id}`);

    try {
      const bot = slackBotService.getBotById(id);
      
      if (!bot) {
        const response: ApiResponse<null> = {
          success: false,
          message: `Slack bot with ID '${id}' not found`,
          data: null,
        };
        
        return res.status(404).json(response);
      }

      const botInfo = {
        id: bot.id,
        route: bot.route,
        channel: bot.channel,
        registeredAt: bot.registeredAt,
        status: 'active',
        description: getBotDescription(bot.route),
        type: getBotType(bot.route),
      };

      const response: ApiResponse<typeof botInfo> = {
        success: true,
        message: `Retrieved Slack bot information for ID: ${id}`,
        data: botInfo,
      };

      console.log(`✅ Successfully retrieved bot: ${bot.route} -> ${bot.channel}`);
      res.status(200).json(response);
    } catch (error) {
      console.error(`❌ Failed to retrieve Slack bot ${id}:`, error);
      
      const response: ApiResponse<null> = {
        success: false,
        message: `Failed to retrieve Slack bot with ID: ${id}`,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: null,
      };

      res.status(500).json(response);
    }
  });
}

/**
 * Helper function to get bot description based on route
 */
function getBotDescription(route: string): string {
  const descriptions: Record<string, string> = {
    '/pronet/v1/call-requests': 'Handles call service demand requests and sends customer information to Slack',
    '/pronet/v1/bonus-requests': 'Handles bonus requests and sends simple notification to Slack',
    '/pronet/v1/market-requests': 'Handles market product requests and sends customer information with product details to Slack',
  };

  return descriptions[route] || 'Custom Slack bot endpoint';
}

/**
 * Helper function to get bot type based on route
 */
function getBotType(route: string): string {
  if (route.includes('/call-requests')) return 'call-service';
  if (route.includes('/bonus-requests')) return 'bonus';
  if (route.includes('/market-requests')) return 'market';
  
  return 'custom';
}
