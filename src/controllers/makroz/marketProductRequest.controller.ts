import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import { validateJwtToken, extractUserIdFromToken } from '@/utils/jwtUtils';
import { MarketProductRequestService } from '@/services/marketProductRequest.service';
import { MarketProductRequestStatus } from '@/enums/shared';

const marketProductRequestService = new MarketProductRequestService();

/**
 * GET /makroz/public/market-product-requests?userId=1
 * List market product requests for a specific user (with JWT token validation)
 */
export const listUserMarketProductRequests = asyncHandler(async (req: Request, res: Response) => {
  console.log('📋 Listing user market product requests');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from JWT token
  const tokenUserId = extractUserIdFromToken(authHeader);
  if (!tokenUserId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  // Get userId from query parameters
  const { userId } = req.query;
  const requestedUserId = userId ? parseInt(userId as string, 10) : null;

  // Validate that the token user ID matches the requested user ID
  if (requestedUserId && requestedUserId !== tokenUserId) {
    return res.status(403).json({
      success: false,
      message: 'Access denied: You can only view your own requests',
    });
  }

  // Use the token user ID for the request
  const userIdToQuery = requestedUserId || tokenUserId;

  try {
    // Extract pagination and filtering parameters
    const page = parseInt((req.query['page'] as string) || '1') || 1;
    const limit = parseInt((req.query['limit'] as string) || '20') || 20;
    const status = req.query['status'] as MarketProductRequestStatus;
    const statuses = req.query['statuses'] ? (req.query['statuses'] as string).split(',') as MarketProductRequestStatus[] : undefined;
    const currency = req.query['currency'] as string;
    const currencies = req.query['currencies'] ? (req.query['currencies'] as string).split(',') : undefined;
    const createdFrom = req.query['createdFrom'] ? new Date(req.query['createdFrom'] as string) : undefined;
    const createdTo = req.query['createdTo'] ? new Date(req.query['createdTo'] as string) : undefined;
    const productType = req.query['productType'] as string;
    const productCategory = req.query['productCategory'] as string;
    const hasRejectReason = req.query['hasRejectReason'] ? req.query['hasRejectReason'] === 'true' : undefined;
    const sortBy = (req.query['sortBy'] as string) || 'createdAt';
    const sortOrder = (req.query['sortOrder'] as 'ASC' | 'DESC') || 'DESC';

    // Build filters
    const filters: any = {
      userId: userIdToQuery,
    };

    if (status && Object.values(MarketProductRequestStatus).includes(status)) {
      filters.status = status;
    }

    if (statuses && statuses.length > 0) {
      const validStatuses = statuses.filter(s => Object.values(MarketProductRequestStatus).includes(s));
      if (validStatuses.length > 0) {
        filters.statuses = validStatuses;
      }
    }

    if (currency?.trim()) {
      filters.currency = currency.trim();
    }

    if (currencies && currencies.length > 0) {
      filters.currencies = currencies.map(c => c.trim()).filter(c => c.length > 0);
    }

    if (createdFrom && !isNaN(createdFrom.getTime())) {
      filters.createdFrom = createdFrom;
    }

    if (createdTo && !isNaN(createdTo.getTime())) {
      filters.createdTo = createdTo;
    }

    if (productType?.trim()) {
      filters.productType = productType.trim();
    }

    if (productCategory?.trim()) {
      filters.productCategory = productCategory.trim();
    }

    if (hasRejectReason !== undefined) {
      filters.hasRejectReason = hasRejectReason;
    }

    // Get requests
    const result = await marketProductRequestService.listRequests({
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    });

    console.log(`✅ Retrieved ${result.data.length} market product requests for user ${userIdToQuery}`);

    res.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error: any) {
    console.error('❌ Failed to list user market product requests:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to list market product requests',
    });
  }
});

/**
 * POST /makroz/public/market-product-requests
 * Create a new market product request (with JWT token validation)
 */
export const createMarketProductRequest = asyncHandler(async (req: Request, res: Response) => {
  console.log('🛒 Creating market product request:', JSON.stringify(req.body, null, 2));

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from JWT token
  const userId = extractUserIdFromToken(authHeader);
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  const { productId, currency, providers } = req.body;

  // Validate required fields
  if (!productId) {
    return res.status(400).json({
      success: false,
      error: 'productId is required',
    });
  }

  if (!currency?.trim()) {
    return res.status(400).json({
      success: false,
      error: 'currency is required',
    });
  }

  // Validate productId is a valid number
  const productIdNum = parseInt(productId, 10);
  if (isNaN(productIdNum) || productIdNum < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid product ID',
    });
  }

  // We'll validate providers in the service after fetching the product to check its type

  try {
    const request = await marketProductRequestService.createRequest({
      userId,
      productId: productIdNum,
      currency: currency.trim(),
      providers,
    });

    console.log('✅ Market product request created successfully:', request.id);

    res.status(201).json({
      success: true,
      data: request,
    });
  } catch (error: any) {
    console.error('❌ Failed to create market product request:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: error.message,
      });
    }

    if (error.message.includes('Insufficient balance')) {
      return res.status(400).json({
        success: false,
        error: error.message,
      });
    }

    if (
      error.message.includes('not available') ||
      error.message.includes('out of stock') ||
      error.message.includes('already purchased') ||
      error.message.includes('pending request')
    ) {
      return res.status(400).json({
        success: false,
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create market product request',
    });
  }
});

/**
 * GET /makroz/admin/market-product-requests
 * List all market product requests (admin only)
 */
export const listAllMarketProductRequests = asyncHandler(async (req: Request, res: Response) => {
  console.log('📋 Listing all market product requests (admin)');

  try {
    // Extract pagination and filtering parameters
    const page = parseInt((req.query['page'] as string) || '1') || 1;
    const limit = parseInt((req.query['limit'] as string) || '20') || 20;
    const status = req.query['status'] as MarketProductRequestStatus;
    const statuses = req.query['statuses'] ? (req.query['statuses'] as string).split(',') as MarketProductRequestStatus[] : undefined;
    const userId = req.query['userId'] ? parseInt(req.query['userId'] as string, 10) : undefined;
    const productId = req.query['productId'] ? parseInt(req.query['productId'] as string, 10) : undefined;
    const currency = req.query['currency'] as string;
    const currencies = req.query['currencies'] ? (req.query['currencies'] as string).split(',') : undefined;
    const createdFrom = req.query['createdFrom'] ? new Date(req.query['createdFrom'] as string) : undefined;
    const createdTo = req.query['createdTo'] ? new Date(req.query['createdTo'] as string) : undefined;
    const updatedFrom = req.query['updatedFrom'] ? new Date(req.query['updatedFrom'] as string) : undefined;
    const updatedTo = req.query['updatedTo'] ? new Date(req.query['updatedTo'] as string) : undefined;
    const search = req.query['search'] as string;
    const productType = req.query['productType'] as string;
    const productCategory = req.query['productCategory'] as string;
    const minPrice = req.query['minPrice'] ? parseFloat(req.query['minPrice'] as string) : undefined;
    const maxPrice = req.query['maxPrice'] ? parseFloat(req.query['maxPrice'] as string) : undefined;
    const hasRejectReason = req.query['hasRejectReason'] ? req.query['hasRejectReason'] === 'true' : undefined;
    const sortBy = (req.query['sortBy'] as string) || 'createdAt';
    const sortOrder = (req.query['sortOrder'] as 'ASC' | 'DESC') || 'DESC';

    // Build filters
    const filters: any = {};

    if (status && Object.values(MarketProductRequestStatus).includes(status)) {
      filters.status = status;
    }

    if (statuses && statuses.length > 0) {
      const validStatuses = statuses.filter(s => Object.values(MarketProductRequestStatus).includes(s));
      if (validStatuses.length > 0) {
        filters.statuses = validStatuses;
      }
    }

    if (userId && !isNaN(userId)) {
      filters.userId = userId;
    }

    if (productId && !isNaN(productId)) {
      filters.productId = productId;
    }

    if (currency?.trim()) {
      filters.currency = currency.trim();
    }

    if (currencies && currencies.length > 0) {
      filters.currencies = currencies.map(c => c.trim()).filter(c => c.length > 0);
    }

    if (createdFrom && !isNaN(createdFrom.getTime())) {
      filters.createdFrom = createdFrom;
    }

    if (createdTo && !isNaN(createdTo.getTime())) {
      filters.createdTo = createdTo;
    }

    if (updatedFrom && !isNaN(updatedFrom.getTime())) {
      filters.updatedFrom = updatedFrom;
    }

    if (updatedTo && !isNaN(updatedTo.getTime())) {
      filters.updatedTo = updatedTo;
    }

    if (search?.trim()) {
      filters.search = search.trim();
    }

    if (productType?.trim()) {
      filters.productType = productType.trim();
    }

    if (productCategory?.trim()) {
      filters.productCategory = productCategory.trim();
    }

    if (minPrice !== undefined && !isNaN(minPrice)) {
      filters.minPrice = minPrice;
    }

    if (maxPrice !== undefined && !isNaN(maxPrice)) {
      filters.maxPrice = maxPrice;
    }

    if (hasRejectReason !== undefined) {
      filters.hasRejectReason = hasRejectReason;
    }

    // Get requests
    const result = await marketProductRequestService.listRequests({
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    });

    console.log(`✅ Retrieved ${result.data.length} market product requests (admin)`);

    res.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error: any) {
    console.error('❌ Failed to list market product requests (admin):', error);

    res.status(500).json({
      success: false,
      error: 'Failed to list market product requests',
    });
  }
});

/**
 * POST /makroz/admin/market-product-requests/:id/rejections
 * Reject a market product request (admin only)
 */
export const rejectMarketProductRequest = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  console.log(`❌ Rejecting market product request ${id}:`, JSON.stringify(req.body, null, 2));

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Request ID is required',
    });
  }

  const requestId = parseInt(id, 10);
  if (isNaN(requestId) || requestId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid request ID',
    });
  }

  const { reason } = req.body;

  // Validate required fields
  if (!reason?.trim()) {
    return res.status(400).json({
      success: false,
      error: 'reason is required',
    });
  }

  try {
    const request = await marketProductRequestService.rejectRequest(requestId, {
      reason: reason.trim(),
    });

    console.log('✅ Market product request rejected successfully:', requestId);

    res.json({
      success: true,
      data: request,
    });
  } catch (error: any) {
    console.error('❌ Failed to reject market product request:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Market product request not found',
      });
    }

    if (error.message.includes('Cannot reject')) {
      return res.status(400).json({
        success: false,
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to reject market product request',
    });
  }
});

/**
 * POST /makroz/admin/market-product-requests/:id/completions
 * Complete a market product request (admin only)
 */
export const completeMarketProductRequest = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  console.log(`✅ Completing market product request ${id}`);

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Request ID is required',
    });
  }

  const requestId = parseInt(id, 10);
  if (isNaN(requestId) || requestId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid request ID',
    });
  }

  try {
    const request = await marketProductRequestService.completeRequest(requestId);

    console.log('✅ Market product request completed successfully:', requestId);

    res.json({
      success: true,
      data: request,
    });
  } catch (error: any) {
    console.error('❌ Failed to complete market product request:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Market product request not found',
      });
    }

    if (error.message.includes('Cannot complete')) {
      return res.status(400).json({
        success: false,
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to complete market product request',
    });
  }
});

/**
 * POST /makroz/admin/market-product-requests/:id/refunds
 * Refund a market product request (admin only)
 */
export const refundMarketProductRequest = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  console.log(`💰 Refunding market product request ${id}:`, JSON.stringify(req.body, null, 2));

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Request ID is required',
    });
  }

  const requestId = parseInt(id, 10);
  if (isNaN(requestId) || requestId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid request ID',
    });
  }

  const { reason } = req.body;

  // Validate required fields
  if (!reason?.trim()) {
    return res.status(400).json({
      success: false,
      error: 'reason is required',
    });
  }

  try {
    const request = await marketProductRequestService.refundRequest(requestId, {
      reason: reason.trim(),
    });

    console.log('✅ Market product request refunded successfully:', requestId);

    res.json({
      success: true,
      data: request,
    });
  } catch (error: any) {
    console.error('❌ Failed to refund market product request:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Market product request not found',
      });
    }

    if (error.message.includes('Cannot refund')) {
      return res.status(400).json({
        success: false,
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to refund market product request',
    });
  }
});

/**
 * DELETE /makroz/admin/market-product-requests/:id
 * Delete a market product request (admin only)
 */
export const deleteMarketProductRequest = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  console.log(`🗑️ Deleting market product request ${id}`);

  // Validate ID parameter
  if (!id) {
    return res.status(400).json({
      success: false,
      error: 'Request ID is required',
    });
  }

  const requestId = parseInt(id, 10);
  if (isNaN(requestId) || requestId < 1) {
    return res.status(400).json({
      success: false,
      error: 'Invalid request ID',
    });
  }

  try {
    const success = await marketProductRequestService.deleteRequest(requestId);

    if (success) {
      console.log('✅ Market product request deleted successfully:', requestId);

      res.json({
        success: true,
        message: 'Market product request deleted successfully',
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Market product request not found',
      });
    }
  } catch (error: any) {
    console.error('❌ Failed to delete market product request:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Market product request not found',
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete market product request',
    });
  }
});
