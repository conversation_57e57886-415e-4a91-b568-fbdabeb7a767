import { Request, Response, NextFunction } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { BonusRuleCheckerService } from '@/services/pronet/bonusRuleChecker/bonusRuleChecker.service';
import { ValidationError } from '@/types/errors';
import { ApiResponse } from '@/types/api';

export class DebugController {
  /**
   * POST /debug/test-bonus-rule-checker - Test bonus rule checker
   *
   * This endpoint simulates the real call of the Bonus Rule Checker
   * for debugging and testing purposes.
   *
   * Request body:
   * {
   *   "customerId": number,
   *   "bonusId": number
   * }
   */
  static testBonusRuleChecker = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🧪 Bonus rule checker test requested');
      console.log('📤 Request body:', JSON.stringify(req.body, null, 2));

      // Extract and validate request parameters
      const { customerId, bonusId } = req.body;

      if (!customerId || !bonusId) {
        throw new ValidationError('Both customerId and bonusId are required');
      }

      if (typeof customerId !== 'number' || typeof bonusId !== 'number') {
        throw new ValidationError('customerId and bonusId must be numbers');
      }

      // Create bonus rule checker service instance
      const bonusRuleChecker = new BonusRuleCheckerService();

      console.log(`🔍 Checking bonus eligibility for customer ${customerId}, bonus ${bonusId}`);

      // Call the bonus rule checker
      const result = await bonusRuleChecker.checkBonusEligibility(customerId, bonusId);

      console.log('📥 Bonus rule checker result:', JSON.stringify(result, null, 2));

      // Return the result in API response format
      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus rule checker test completed',
        data: {
          customerId,
          bonusId,
          result,
          supportedBonusTypes: bonusRuleChecker.getSupportedBonusTypes(),
          testedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Bonus rule checker test failed:', error);

      // Return error in a format that's useful for debugging
      res.status(500).json({
        success: false,
        message: 'Bonus rule checker test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        ...(process.env['NODE_ENV'] === 'development' && {
          stack: error instanceof Error ? error.stack : undefined,
        }),
        timestamp: new Date().toISOString(),
      });
    }
  });

  /**
   * POST /debug/* - Wildcard proxy endpoint for EbetLab API exploration
   *
   * This endpoint acts as a proxy for any EbetLab API endpoint by:
   * 1. Extracting the real API path by removing the /debug prefix
   * 2. Making an authenticated request to the actual EbetLab API
   * 3. Returning the raw response without additional wrapping
   *
   * Example:
   * POST /debug/api/operator/customers/index/1/20
   * -> Proxies to: https://service.ebetlab.com/api/operator/customers/index/1/20
   */
  static proxyEbetLabRequest = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Extract authorization header
      const authorization = req.headers.authorization;
      if (!authorization) {
        return res.status(401).json({
          success: false,
          message: 'Authorization header is required',
        });
      }

      // Extract the real API path from the original URL
      // Since we're using middleware, we extract the path by removing /debug prefix
      const originalUrl = req.originalUrl; // e.g., "/debug/api/operator/customers/index/1/20"
      const realApiPath = originalUrl.replace(/^\/debug/, ''); // e.g., "/api/operator/customers/index/1/20"

      if (!realApiPath || realApiPath === '/' || realApiPath === '') {
        return res.status(400).json({
          success: false,
          message: 'Invalid debug path. Expected format: /debug/api/...',
        });
      }

      // Log the debug request for monitoring
      console.log('🐛 Debug proxy request:');
      console.log(`📍 Original URL: ${originalUrl}`);
      console.log(`📍 Real API path: ${realApiPath}`);
      console.log('📤 Request body:', JSON.stringify(req.body, null, 2));

      // Create EbetLab service instance
      const response = await ebetlabApiClient.makeAuthenticatedRequest(
        {
          getBody: () => req.body,
          validateResponse: (obj) => obj,
          getPath: () => realApiPath.replace('/api', ''),
          getMethod: () => 'POST',
          getHeaders: () => ({}),
        },
        authorization,
      );

      // Log the response for debugging
      console.log('📥 EbetLab response received');
      console.log('📥 Response data:', JSON.stringify(response, null, 2));

      // Return the raw response without additional wrapping
      // This is important for frontend debug section to display raw EbetLab responses
      res.json(response);
    } catch (error) {
      console.error('❌ Debug proxy request failed:', error);

      // Return error in a format that's useful for debugging
      res.status(500).json({
        success: false,
        message: 'Debug proxy request failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        ...(process.env['NODE_ENV'] === 'development' && {
          stack: error instanceof Error ? error.stack : undefined,
        }),
      });
    }
  });
}
