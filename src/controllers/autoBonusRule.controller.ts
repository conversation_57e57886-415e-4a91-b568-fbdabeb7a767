import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { AutoBonusRuleListRequest } from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleListRequest';
import { AutoBonusRuleGetRequest } from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleGetRequest';
import {
  AutoBonusRuleCreateRequest,
  AutoBonusRuleCreateRequestOptions,
} from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleCreateRequest';
import {
  AutoBonusRuleUpdateRequest,
  AutoBonusRuleUpdateRequestOptions,
} from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleUpdateRequest';
import { AutoBonusRuleCancellationCreateRequest } from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleCancellationCreateRequest';
import { AutoBonusRuleDeleteRequest } from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleDeleteRequest';
import { AutoBonusRuleClaimListByIdRequest } from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleClaimListByIdRequest';
import { AutoBonusRuleClaimListRequest } from '@/network/ebetlab/requests/auto-bonus-rule/AutoBonusRuleClaimListRequest';

export class AutoBonusRuleController {
  /**
   * GET /auto-bonus-rules - Get auto bonus rules from EbetLab
   */
  static listAutoBonusRules = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, from, to } = req.query;

    try {
      console.log(`🤖 Fetching auto bonus rules - page: ${page}, limit: ${limit}, from: ${from}, to: ${to}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Use EbetLab API client singleton and make request
      const autoBonusRuleRequest = new AutoBonusRuleListRequest({
        page: pageNum,
        limit: limitNum,
        from: from ? (from as string) : null,
        to: to ? (to as string) : null,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        autoBonusRuleRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch auto bonus rules: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Auto bonus rules retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch auto bonus rules:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch auto bonus rules: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /auto-bonus-rules - Create a new auto bonus rule
   */
  static createAutoBonusRule = asyncHandler(
    async (req: Request<any, any, AutoBonusRuleCreateRequestOptions>, res: Response) => {
      try {
        console.log('🤖 Creating auto bonus rule');
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate required fields
        const requiredFields: (keyof AutoBonusRuleCreateRequestOptions)[] = [
          'bonus_code',
          'rules',
          'bonus_id',
          'currency',
          'reference_tag',
          'total',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate rules array
        if (!Array.isArray(req.body.rules) || req.body.rules.length === 0) {
          throw new ValidationError('rules must be a non-empty array');
        }

        // Validate each rule in the array
        req.body.rules.forEach((rule, index) => {
          if (!rule.field || !rule.operator || !rule.value) {
            throw new ValidationError(`Rule at index ${index} must have field, operator, and value`);
          }
        });

        // Use EbetLab API client singleton and make request
        const createRequest = new AutoBonusRuleCreateRequest(req.body);

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          createRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to create auto bonus rule: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Auto bonus rule created successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(201).json(response);
      } catch (error) {
        console.error('❌ Failed to create auto bonus rule:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to create auto bonus rule: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * GET /auto-bonus-rules/:id - Get a specific auto bonus rule by ID from EbetLab
   */
  static getAutoBonusRuleById = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🤖 Fetching auto bonus rule by ID - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const ruleId = parseInt(id);
      if (isNaN(ruleId) || ruleId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const autoBonusRuleRequest = new AutoBonusRuleGetRequest({
        id: ruleId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        autoBonusRuleRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch auto bonus rule: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Auto bonus rule retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch auto bonus rule by ID:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch auto bonus rule: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * PATCH /auto-bonus-rules/:id - Update a specific auto bonus rule by ID
   */
  static updateAutoBonusRule = asyncHandler(
    async (req: Request<{ id: string }, any, AutoBonusRuleUpdateRequestOptions>, res: Response) => {
      // Extract ID from route parameters
      const { id } = req.params;

      try {
        console.log(`🤖 Updating auto bonus rule - id: ${id}`);
        console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate ID parameter
        if (!id) {
          throw new ValidationError('id parameter is required');
        }

        const ruleId = parseInt(id);
        if (isNaN(ruleId) || ruleId < 1) {
          throw new ValidationError('id must be a positive integer');
        }

        // Validate required fields
        const requiredFields: (keyof AutoBonusRuleUpdateRequestOptions)[] = [
          'bonus_code',
          'rules',
          'bonus_id',
          'currency',
          'total',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate rules array
        if (!Array.isArray(req.body.rules)) {
          throw new ValidationError('rules must be an array');
        }

        // Use EbetLab API client singleton and make request
        const updateRequest = new AutoBonusRuleUpdateRequest({
          ...req.body,
          id: ruleId,
        });

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          updateRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to update auto bonus rule: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Auto bonus rule updated successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);
      } catch (error) {
        console.error('❌ Failed to update auto bonus rule:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to update auto bonus rule: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * POST /auto-bonus-rules/:id/cancellations - Cancel a specific auto bonus rule by ID
   */
  static cancelAutoBonusRule = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🚫 Cancelling auto bonus rule - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const ruleId = parseInt(id);
      if (isNaN(ruleId) || ruleId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const cancellationRequest = new AutoBonusRuleCancellationCreateRequest({
        id: ruleId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        cancellationRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to cancel auto bonus rule: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Auto bonus rule cancelled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to cancel auto bonus rule:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to cancel auto bonus rule: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * DELETE /auto-bonus-rules/:id - Delete a specific auto bonus rule by ID
   */
  static deleteAutoBonusRule = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🗑️ Deleting auto bonus rule - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const ruleId = parseInt(id);
      if (isNaN(ruleId) || ruleId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const deleteRequest = new AutoBonusRuleDeleteRequest({
        id: ruleId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(deleteRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to delete auto bonus rule: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Auto bonus rule deleted successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete auto bonus rule:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete auto bonus rule: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /auto-bonus-rules/:id/claims - Get claims for a specific auto bonus rule
   */
  static getAutoBonusRuleClaims = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters and query parameters
    const { id } = req.params;
    const { page = 1, limit = 20, username, from, to } = req.query;

    try {
      console.log(`🤖 Fetching auto bonus rule claims - id: ${id}, page: ${page}, limit: ${limit}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Use EbetLab API client singleton and make request
      const claimRequest = new AutoBonusRuleClaimListByIdRequest({
        id: id as string,
        page: pageNum,
        limit: limitNum,
        username: username ? (username as string) : null,
        from: from ? (from as string) : null,
        to: to ? (to as string) : null,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(claimRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch auto bonus rule claims: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Auto bonus rule claims retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch auto bonus rule claims:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch auto bonus rule claims: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /auto-bonus-rules/claims - Get all auto bonus rule claims from EbetLab
   */
  static listAutoBonusRuleClaims = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, bonus_code, from, to } = req.query;

    try {
      console.log(`🤖 Fetching auto bonus rule claims - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Use EbetLab API client singleton and make request
      const claimListRequest = new AutoBonusRuleClaimListRequest({
        page: pageNum,
        limit: limitNum,
        bonus_code: bonus_code ? (bonus_code as string) : null,
        from: from ? (from as string) : null,
        to: to ? (to as string) : null,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        claimListRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch auto bonus rule claims: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Auto bonus rule claims retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch auto bonus rule claims:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch auto bonus rule claims: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
