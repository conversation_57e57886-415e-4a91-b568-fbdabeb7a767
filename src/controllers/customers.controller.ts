import { Request, Response } from 'express';
import {
  AffiliateSetRequest,
  AffiliateSetResponse,
  ApiResponse,
  BonusRedeemsRequest,
  BonusRedeemsResponse,
  CasinoDebitsRequest,
  CasinoDebitsResponse,
  CommitsRequest,
  CommitsResponse,
  CustomerBigWinLoseDebitsRequest,
  CustomerBigWinLoseDebitsResponse,
  CustomerDetail,
  CustomerGameWinLoseDebitsRequest,
  CustomerGameWinLoseDebitsResponse,
  CustomerInfoRequest,
  CustomerInfoResponse,
  CustomerShowRequest,
  CustomerSummaryRangeRequest,
  CustomerSummaryRangeResponse,
  CustomersRequest,
  CustomersResponse,
  DiscountsRequest,
  DiscountsResponse,
  FiatTransactionsRequest,
  FiatTransactionsResponse,
  FinancialLimitsRequest,
  FinancialLimitsResponse,
  FinancialLimitsUpdateRequest,
  FtdTransactionsRequest,
  FtdTransactionsResponse,
  FtwTransactionsRequest,
  FtwTransactionsResponse,
  GeneralLimitsApplyRequest,
  GeneralLimitsApplyResponse,
  GeneralLimitsRequest,
  GeneralLimitsResponse,
  NotificationsRequest,
  NotificationsResponse,
  PasswordUpdateRequest,
  PasswordUpdateResponse,
  PlayerActionsRequest,
  PlayerActionsResponse,
  ProfileUpdateRequest,
  ProfileUpdateResponse,
  RakebackAvailablesRequest,
  RakebackAvailablesResponse,
  RakebackUsagesRequest,
  RakebackUsagesResponse,
  SessionsRequest,
  SessionsResponse,
  SportsbookDebitsRequest,
  SportsbookDebitsResponse,
  SportsbookPlayersRequest,
  SportsbookPlayersResponse,
  TipsRequest,
  TipsResponse,
  TradeDebitsRequest,
  TradeDebitsResponse,
  TransactionsRequest,
  TransactionsResponse,
  TransactionsSummaryRequest,
  TransactionsSummaryResponse,
  VaultsRequest,
  VaultsResponse,
  VipRankRequest,
  VipRankResponse,
  VipStateChangesRequest,
  VipStateChangesResponse,
  WalletsRequest,
  WalletsResponse,
} from '@/types/api';
import { ValidationError } from '@/types/errors';
import { createEbetLabService } from '@/utils/ebetlabService';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { VipRankRequest as VipRankRequestClass } from '@/network/ebetlab/requests/VipRankRequest';
import { ProfileUpdateRequest as ProfileUpdateRequestClass } from '@/network/ebetlab/requests/ProfileUpdateRequest';
import { NotificationsRequest as NotificationsRequestClass } from '@/network/ebetlab/requests/NotificationsRequest';
import { AffiliateSetRequest as AffiliateSetRequestClass } from '@/network/ebetlab/requests/AffiliateSetRequest';

export class CustomersController {
  /**
   * POST /operator/customers/index/{page}/{limit} - Get customers from EbetLab
   */
  static getCustomers = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: CustomersRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`👥 Fetching customers - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab customers endpoint
      const result = await ebetLabService.getCustomers(page, limit, filters);

      const response: ApiResponse<CustomersResponse> = {
        success: true,
        message: 'Customers retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch customers:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch customers: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customers/show/{id} - Get individual customer details from EbetLab
   */
  static getCustomerById = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { id }: CustomerShowRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id !== customerId) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    try {
      console.log(`👤 Fetching customer details - ID: ${customerId}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab customer detail endpoint
      const result = await ebetLabService.getCustomerById(customerId);

      const response: ApiResponse<CustomerDetail> = {
        success: true,
        message: 'Customer details retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch customer details:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch customer details: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customers/summary-range/{id} - Get customer summary range (daily/weekly/monthly/yearly deposits and withdrawals)
   */
  static getCustomerSummaryRange = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { id }: CustomerSummaryRangeRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id !== customerId) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    try {
      console.log(`📊 Fetching customer summary range - Customer ID: ${customerId}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab customer summary range endpoint
      const result = await ebetLabService.getCustomerSummaryRange(customerId);

      const response: ApiResponse<CustomerSummaryRangeResponse> = {
        success: true,
        message: 'Customer summary range retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch customer summary range:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch customer summary range: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customers/info/{id} - Get customer info (last deposit, withdraw, discount, corrections, bonus)
   */
  static getCustomerInfo = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { customer_id, id }: CustomerInfoRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body IDs match path ID (if provided)
    if (customer_id && customer_id !== customerId) {
      throw new ValidationError('customer_id in request body must match URL path parameter');
    }

    if (id && id !== customerId) {
      throw new ValidationError('id in request body must match URL path parameter');
    }

    try {
      console.log(`ℹ️ Fetching customer info - Customer ID: ${customerId}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab customer info endpoint
      const result = await ebetLabService.getCustomerInfo(customerId);

      const response: ApiResponse<CustomerInfoResponse> = {
        success: true,
        message: 'Customer info retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch customer info:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch customer info: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/general-limits/show/{id} - Get general limits for a customer
   */
  static getGeneralLimits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { id }: GeneralLimitsRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id !== customerId) {
      throw new ValidationError('id in request body must match URL path parameter');
    }

    try {
      console.log(`🚫 Fetching general limits - Customer ID: ${customerId}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab general limits endpoint
      const result = await ebetLabService.getGeneralLimits(customerId);

      const response: ApiResponse<GeneralLimitsResponse> = {
        success: true,
        message: 'General limits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch general limits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch general limits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/general-limits/apply/{id} - Apply/update general limits for a customer
   */
  static applyGeneralLimits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const limits: GeneralLimitsApplyRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    try {
      console.log(`🔧 Applying general limits - Customer ID: ${customerId}`);
      console.log('🔧 Limits:', JSON.stringify(limits, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab apply general limits endpoint
      const result = await ebetLabService.applyGeneralLimits(customerId, limits);

      const response: ApiResponse<GeneralLimitsApplyResponse> = {
        success: true,
        message: 'General limits applied successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to apply general limits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to apply general limits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customer-limits/show/{id} - Get financial limits for a customer
   */
  static getFinancialLimits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const requestBody: FinancialLimitsRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body ID matches path ID (if provided)
    if (requestBody.id && requestBody.id.toString() !== customerId) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    try {
      console.log(`💰 Fetching financial limits - Customer ID: ${customerId}`);
      console.log('💰 Request body:', JSON.stringify(requestBody, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab financial limits endpoint, proxying the request body
      const result = await ebetLabService.getFinancialLimits(customerId, requestBody);

      const response: ApiResponse<FinancialLimitsResponse> = {
        success: true,
        message: 'Financial limits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch financial limits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch financial limits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customer-limits/update/{id} - Update financial limits for a customer
   */
  static updateFinancialLimits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const limits: FinancialLimitsUpdateRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body ID matches path ID (if provided)
    if (limits.id && limits.id !== customerId) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    // Validate required fields
    if (!limits.merchant_id || !limits.website_id || !limits.customer_id) {
      throw new ValidationError('merchant_id, website_id, and customer_id are required in request body');
    }

    // Validate that customer_id matches path parameter
    if (limits.customer_id.toString() !== customerId) {
      throw new ValidationError('customer_id in request body must match URL path parameter');
    }

    try {
      console.log(`💰 Updating financial limits - Customer ID: ${customerId}`);
      console.log('💰 Limits:', JSON.stringify(limits, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab update financial limits endpoint
      const result = await ebetLabService.updateFinancialLimits(customerId, limits);

      const response: ApiResponse<FinancialLimitsResponse> = {
        success: true,
        message: 'Financial limits updated successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to update financial limits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to update financial limits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customers/password/{id} - Update customer password
   */
  static updateCustomerPassword = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { password, id }: PasswordUpdateRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate password
    if (!password || typeof password !== 'string' || password.trim().length === 0) {
      throw new ValidationError('Password is required and must be a non-empty string');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id.toString() !== customerId) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    try {
      console.log(`🔐 Updating customer password - Customer ID: ${customerId}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab update password endpoint
      const result = await ebetLabService.updateCustomerPassword(customerId, password);

      const response: ApiResponse<PasswordUpdateResponse> = {
        success: true,
        message: 'Customer password updated successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to update customer password:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to update customer password: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/transactions/index/{page}/{limit} - Get transactions from EbetLab
   */
  static getTransactions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: TransactionsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💳 Fetching transactions - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab transactions endpoint
      const result = await ebetLabService.getTransactions(page, limit, filters);

      const response: ApiResponse<TransactionsResponse> = {
        success: true,
        message: 'Transactions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch transactions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch transactions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/transactions/ftd/{page}/{limit} - Get FTD (First Time Deposit) transactions from EbetLab
   */
  static getFtdTransactions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: FtdTransactionsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💰 Fetching FTD transactions - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab FTD transactions endpoint
      const result = await ebetLabService.getFtdTransactions(page, limit, filters);

      const response: ApiResponse<FtdTransactionsResponse> = {
        success: true,
        message: 'FTD transactions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch FTD transactions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch FTD transactions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/transactions/ftw/{page}/{limit} - Get FTW (First Time Withdrawal) transactions from EbetLab
   */
  static getFtwTransactions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: FtwTransactionsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💸 Fetching FTW transactions - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab FTW transactions endpoint
      const result = await ebetLabService.getFtwTransactions(page, limit, filters);

      const response: ApiResponse<FtwTransactionsResponse> = {
        success: true,
        message: 'FTW transactions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch FTW transactions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch FTW transactions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/transactions/fiats/{page}/{limit} - Get fiat transactions (waiting deposits) from EbetLab
   */
  static getFiatTransactions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: FiatTransactionsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💰 Fetching fiat transactions (waiting deposits) - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab fiat transactions endpoint
      const result = await ebetLabService.getFiatTransactions(page, limit, filters);

      const response: ApiResponse<FiatTransactionsResponse> = {
        success: true,
        message: 'Fiat transactions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch fiat transactions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch fiat transactions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/transactions/summary - Get transactions summary from EbetLab
   */
  static getTransactionsSummary = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body as filters
    const filters: TransactionsSummaryRequest = req.body;

    try {
      console.log('📊 Fetching transactions summary');
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab transactions summary endpoint
      const result = await ebetLabService.getTransactionsSummary(filters);

      const response: ApiResponse<TransactionsSummaryResponse> = {
        success: true,
        message: 'Transactions summary retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch transactions summary:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch transactions summary: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/debits/index/{page}/{limit} - Get casino debits from EbetLab
   */
  static getCasinoDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: CasinoDebitsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🎰 Fetching casino debits - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab casino debits endpoint
      const result = await ebetLabService.getCasinoDebits(page, limit, filters);

      const response: ApiResponse<CasinoDebitsResponse> = {
        success: true,
        message: 'Casino debits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch casino debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch casino debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/players/sportsbook/{page}/{limit} - Get sportsbook players from EbetLab
   */
  static getSportsbookPlayers = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body
    const { customer_id }: SportsbookPlayersRequest = req.body;

    // Validate required fields
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🏈 Fetching sportsbook players - page: ${page}, limit: ${limit}, customer_id: ${customer_id}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab sportsbook players endpoint
      const result = await ebetLabService.getSportsbookPlayers(page, limit, customer_id);

      const response: ApiResponse<SportsbookPlayersResponse> = {
        success: true,
        message: 'Sportsbook players retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch sportsbook players:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch sportsbook players: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/sportsbook-debits/index/{page}/{limit} - Get sportsbook debits from EbetLab
   */
  static getSportsbookDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: SportsbookDebitsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🏈 Fetching sportsbook debits - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab sportsbook debits endpoint
      const result = await ebetLabService.getSportsbookDebits(page, limit, filters);

      const response: ApiResponse<SportsbookDebitsResponse> = {
        success: true,
        message: 'Sportsbook debits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch sportsbook debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch sportsbook debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/trade-debits/index/{page}/{limit} - Get trade debits from EbetLab
   */
  static getTradeDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: TradeDebitsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`📈 Fetching trade debits - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab trade debits endpoint
      const result = await ebetLabService.getTradeDebits(page, limit, filters);

      const response: ApiResponse<TradeDebitsResponse> = {
        success: true,
        message: 'Trade debits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch trade debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch trade debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/sessions/index/{page}/{limit} - Get sessions from EbetLab
   */
  static getSessions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '100') || 100;

    // Extract request body as filters
    const filters: SessionsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🔗 Fetching sessions - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab sessions endpoint
      const result = await ebetLabService.getSessions(page, limit, filters);

      const response: ApiResponse<SessionsResponse> = {
        success: true,
        message: 'Sessions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch sessions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch sessions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/bonus-redeems/index/{page}/{limit} - Get bonus redeems from EbetLab
   */
  static getBonusRedeems = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '50') || 50;

    // Extract request body as filters
    const filters: BonusRedeemsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🎁 Fetching bonus redeems - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab bonus redeems endpoint
      const result = await ebetLabService.getBonusRedeems(page, limit, filters);

      const response: ApiResponse<BonusRedeemsResponse> = {
        success: true,
        message: 'Bonus redeems retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus redeems:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus redeems: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/discounts/summary - Get discounts summary from EbetLab
   */
  static getDiscountsSummary = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body as filters
    const filters: DiscountsRequest = req.body;

    try {
      console.log('💰 Fetching discounts summary');
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab discounts summary endpoint
      const result = await ebetLabService.getDiscountsSummary(filters);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Discounts summary retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch discounts summary:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch discounts summary: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/discounts/index/{page}/{limit} - Get discounts from EbetLab
   */
  static getDiscounts = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: DiscountsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💰 Fetching discounts - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab discounts endpoint
      const result = await ebetLabService.getDiscounts(page, limit, filters);

      const response: ApiResponse<DiscountsResponse> = {
        success: true,
        message: 'Discounts retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch discounts:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch discounts: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/notifications/index/{page}/{limit} - Get notifications from EbetLab
   */
  static getNotifications = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body
    const { customer_id }: NotificationsRequest = req.body;

    // Validate required fields
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🔔 Fetching notifications - page: ${page}, limit: ${limit}, customer_id: ${customer_id}`);

      // Use EbetLab API client singleton and make request
      const notificationsRequest = new NotificationsRequestClass({
        page,
        limit,
        customer_id,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        notificationsRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch notifications: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<NotificationsResponse> = {
        success: true,
        message: 'Notifications retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch notifications:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch notifications: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customers/dashboard/debits/big-win-lose - Get customer-specific big win/lose debits from EbetLab
   */
  static getCustomerBigWinLoseDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body parameters
    const { customer_id, ...additionalParams }: CustomerBigWinLoseDebitsRequest = req.body;

    // Validate required fields
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    try {
      console.log(`💰 Fetching customer big win/lose debits - customer_id: ${customer_id}`);
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Prepare parameters including customer_id and any additional filters
      const params: Record<string, any> = {
        customer_id,
        ...additionalParams,
      };

      // Make authenticated request to EbetLab customer big win/lose debits endpoint
      const result = await ebetLabService.getCustomerBigWinLoseDebits(params);

      const response: ApiResponse<CustomerBigWinLoseDebitsResponse> = {
        success: true,
        message: 'Customer big win/lose debits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch customer big win/lose debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch customer big win/lose debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/customers/dashboard/debits/game-win-lose - Get customer-specific game win/lose debits from EbetLab
   */
  static getCustomerGameWinLoseDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body parameters
    const { customer_id, ...additionalParams }: CustomerGameWinLoseDebitsRequest = req.body;

    // Validate required fields
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    try {
      console.log(`🎮 Fetching customer game win/lose debits - customer_id: ${customer_id}`);
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Prepare parameters including customer_id and any additional filters
      const params: Record<string, any> = {
        customer_id,
        ...additionalParams,
      };

      // Make authenticated request to EbetLab customer game win/lose debits endpoint
      const result = await ebetLabService.getCustomerGameWinLoseDebits(params);

      const response: ApiResponse<CustomerGameWinLoseDebitsResponse> = {
        success: true,
        message: 'Customer game win/lose debits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch customer game win/lose debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch customer game win/lose debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/wallets/index/{page}/{limit} - Get wallets from EbetLab
   */
  static getWallets = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body
    const { customer_id }: WalletsRequest = req.body;

    // Validate required fields
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💰 Fetching wallets - page: ${page}, limit: ${limit}, customer_id: ${customer_id}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab wallets endpoint
      const result = await ebetLabService.getWallets(page, limit, customer_id);

      const response: ApiResponse<WalletsResponse> = {
        success: true,
        message: 'Wallets retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch wallets:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch wallets: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/vaults/index/{page}/{limit} - Get vaults from EbetLab
   */
  static getVaults = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: VaultsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🏦 Fetching vaults - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab vaults endpoint
      const result = await ebetLabService.getVaults(page, limit, filters);

      const response: ApiResponse<VaultsResponse> = {
        success: true,
        message: 'Vaults retrieved successfully',
        data: result,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch vaults:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch vaults: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/vip-state-changes/index/{page}/{limit} - Get VIP state changes from EbetLab
   */
  static getVipStateChanges = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body
    const { customer_id }: VipStateChangesRequest = req.body;

    // Validate required fields
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`👑 Fetching VIP state changes - page: ${page}, limit: ${limit}, customer_id: ${customer_id}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab VIP state changes endpoint
      const result = await ebetLabService.getVipStateChanges(page, limit, customer_id);

      const response: ApiResponse<VipStateChangesResponse> = {
        success: true,
        message: 'VIP state changes retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch VIP state changes:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch VIP state changes: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/player-actions/index/{page}/{limit} - Get player actions from EbetLab
   */
  static getPlayerActions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: PlayerActionsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`📋 Fetching player actions - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab player actions endpoint
      const result = await ebetLabService.getPlayerActions(page, limit, filters);

      const response: ApiResponse<PlayerActionsResponse> = {
        success: true,
        message: 'Player actions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch player actions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch player actions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/commits/index/{page}/{limit} - Get commits from EbetLab
   */
  static getCommits = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: CommitsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`📝 Fetching commits - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab commits endpoint
      const result = await ebetLabService.getCommits(page, limit, filters);

      const response: ApiResponse<CommitsResponse> = {
        success: true,
        message: 'Commits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch commits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch commits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/vip-state/rank/{slug} - Set VIP rank for a customer
   */
  static setVipRank = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const slug = req.params['slug'];

    // Extract request body
    const { slug: bodySlug, add_gift, customer_id }: VipRankRequest = req.body;

    // Validate slug parameter
    if (!slug) {
      throw new ValidationError('VIP rank slug is required in URL path');
    }

    // Validate required fields in request body
    if (!customer_id) {
      throw new ValidationError('customer_id is required in request body');
    }

    if (add_gift === undefined || add_gift === null) {
      throw new ValidationError('add_gift is required in request body');
    }

    // Validate that body slug matches path slug (if provided)
    if (bodySlug && bodySlug !== slug) {
      throw new ValidationError('slug in request body must match URL path parameter');
    }

    try {
      console.log(`👑 Setting VIP rank - slug: ${slug}, customer_id: ${customer_id}, add_gift: ${add_gift}`);
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Use EbetLab API client singleton and make request
      const vipRankRequest = new VipRankRequestClass({
        slug,
        add_gift,
        customer_id,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        vipRankRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to set VIP rank: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<VipRankResponse> = {
        success: true,
        message: 'VIP rank set successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to set VIP rank:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to set VIP rank: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/profile/update/{id} - Update customer profile information
   */
  static updateProfile = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const profileData: ProfileUpdateRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Convert customer ID to integer for validation
    const customerIdInt = parseInt(customerId);
    if (isNaN(customerIdInt) || customerIdInt < 1) {
      throw new ValidationError('Customer ID must be a positive integer');
    }

    // Validate that body ID matches path ID (if provided)
    if (profileData.id && profileData.id !== customerIdInt) {
      throw new ValidationError('id in request body must match URL path parameter');
    }

    // Validate required ID field
    if (!profileData.id) {
      throw new ValidationError('id is required in request body');
    }

    try {
      console.log(`👤 Updating customer profile - Customer ID: ${customerId}`);
      console.log('📋 Profile data:', JSON.stringify(profileData, null, 2));

      // Use EbetLab API client singleton and make request
      const profileUpdateRequest = new ProfileUpdateRequestClass({
        id: customerIdInt,
        username: profileData.username,
        phone: profileData.phone,
        country_code: profileData.country_code,
        email: profileData.email,
        name: profileData.name,
        surname: profileData.surname,
        occupation: profileData.occupation,
        identity_no: profileData.identity_no,
        residential: profileData.residential,
        birthday: profileData.birthday,
        city: profileData.city,
        ghost_mode: profileData.ghost_mode,
        hide_statistics: profileData.hide_statistics,
        hide_race_statistics: profileData.hide_race_statistics,
        exclude_rain: profileData.exclude_rain,
        receive_marketing_mails: profileData.receive_marketing_mails,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        profileUpdateRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to update profile: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<ProfileUpdateResponse> = {
        success: true,
        message: 'Profile updated successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to update profile:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to update profile: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/rakeback-usages/index/{page}/{limit} - Get rakeback usages from EbetLab
   */
  static getRakebackUsages = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: RakebackUsagesRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💰 Fetching rakeback usages - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab rakeback usages endpoint
      const result = await ebetLabService.getRakebackUsages(page, limit, filters);

      const response: ApiResponse<RakebackUsagesResponse> = {
        success: true,
        message: 'Rakeback usages retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch rakeback usages:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch rakeback usages: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/rakeback-availables/index/{page}/{limit} - Get rakeback availables from EbetLab
   */
  static getRakebackAvailables = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: RakebackAvailablesRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💎 Fetching rakeback availables - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab rakeback availables endpoint
      const result = await ebetLabService.getRakebackAvailables(page, limit, filters);

      const response: ApiResponse<RakebackAvailablesResponse> = {
        success: true,
        message: 'Rakeback availables retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch rakeback availables:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch rakeback availables: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/tips/index/{page}/{limit} - Get tips from EbetLab
   */
  static getTips = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters: TipsRequest = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💡 Fetching tips - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab tips endpoint
      const result = await ebetLabService.getTips(page, limit, filters);

      const response: ApiResponse<TipsResponse> = {
        success: true,
        message: 'Tips retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch tips:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch tips: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/affiliates/set/{id} - Set affiliate/referral code for a customer
   */
  static setAffiliateCode = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { id, code }: AffiliateSetRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Convert customer ID to number
    const customerIdInt = parseInt(customerId);
    if (isNaN(customerIdInt)) {
      throw new ValidationError('Customer ID must be a valid number');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id !== customerIdInt) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    // Validate code
    if (!code || typeof code !== 'string' || code.trim().length === 0) {
      throw new ValidationError('Affiliate code is required and must be a non-empty string');
    }

    try {
      console.log(`🔗 Setting affiliate code - Customer ID: ${customerId}, Code: ${code}`);
      console.log('📤 Request body:', JSON.stringify(req.body, null, 2));

      // Use EbetLab API client singleton and make request
      const affiliateSetRequest = new AffiliateSetRequestClass({
        id: customerIdInt,
        code: code.trim(),
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        affiliateSetRequest,
        req.headers['authorization'] || '',
      );

      const response: ApiResponse<AffiliateSetResponse> = {
        success: true,
        message: 'Affiliate code set successfully',
        ...(result.success && { data: result.data as AffiliateSetResponse }),
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to set affiliate code:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch tips: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/affiliates/set/{id} - Set affiliate/referral code for a customer
   */
  static setAffiliateCode = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { id, code }: AffiliateSetRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Convert customer ID to number
    const customerIdInt = parseInt(customerId);
    if (isNaN(customerIdInt)) {
      throw new ValidationError('Customer ID must be a valid number');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id !== customerIdInt) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    // Validate code
    if (!code || typeof code !== 'string' || code.trim().length === 0) {
      throw new ValidationError('Affiliate code is required and must be a non-empty string');
    }

    try {
      console.log(`🔗 Setting affiliate code - Customer ID: ${customerId}, Code: ${code}`);
      console.log('📤 Request body:', JSON.stringify(req.body, null, 2));

      // Use EbetLab API client singleton and make request
      const affiliateSetRequest = new AffiliateSetRequestClass({
        id: customerIdInt,
        code: code.trim(),
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        affiliateSetRequest,
        req.headers['authorization'] || '',
      );

      const response: ApiResponse<AffiliateSetResponse> = {
        success: true,
        message: 'Affiliate code set successfully',
        ...(result.success && { data: result.data as AffiliateSetResponse }),
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to set affiliate code:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') ||
            error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to set affiliate code: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
