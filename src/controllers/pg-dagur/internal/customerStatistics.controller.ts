import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { ApiResponse } from '@/types/api';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { CustomerDetailsPreloadRequest } from '@/network/pg-dagur/requests/customer/CustomerDetailsPreloadRequest';
import {
  CustomerDetailsStatisticsTabChangeRequest,
  CustomerDetailsStatisticsLifetimeTurnoverRequest,
  CustomerDetailsStatisticsWeeklyStatisticsRequest,
  CustomerDetailsStatisticsDailyStatisticsRequest,
  CustomerDetailsStatisticsMonthlyStatisticsRequest,
  CustomerStatisticsResponse,
  CustomerLifetimeTurnoverStatisticsResponse,
  CustomerWeeklyStatisticsResponse,
  CustomerDailyStatisticsResponse,
  CustomerMonthlyStatisticsResponse
} from '@/network/pg-dagur/requests/customer/statistics';

/**
 * Combined response interface for customer statistics including all statistics categories
 */
interface CombinedCustomerStatisticsResponse {
  statistics: {
    gameStatistics: CustomerStatisticsResponse['statistics']['gameStatistics'];
    depositWithdrawStatistics: CustomerStatisticsResponse['statistics']['depositWithdrawStatistics'];
    qrReferenceStatistics: CustomerStatisticsResponse['statistics']['qrReferenceStatistics'];
    profitStatistics: CustomerStatisticsResponse['statistics']['profitStatistics'];
    casinoStatistics: CustomerStatisticsResponse['statistics']['casinoStatistics'];
    manualDepositWithdrawStatistics: CustomerStatisticsResponse['statistics']['manualDepositWithdrawStatistics'];
    weeklyStatistics?: CustomerWeeklyStatisticsResponse['statistics']['weeklyStatistics'];
    dailyStatistics?: CustomerDailyStatisticsResponse['statistics']['dailyStatistics'];
    monthlyStatistics?: CustomerMonthlyStatisticsResponse['statistics']['monthlyStatistics'];
  };
  customerId: string;
}

export class CustomerStatisticsController {
  /**
   * GET /api/pg-dagur/v1/internal/customers/:customerId/statistics - Get customer statistics from PG Dagur
   */
  static getCustomerStatistics = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Customer statistics request initiated');
    console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
    console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

    // Extract customerId from URL parameters
    const { customerId } = req.params;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    let viewState = '';
    let javax: any = null;

    // Step 1: Preload request to get ViewState and dynamic element IDs
    {
      console.log('📥 Making preload request to get ViewState and element IDs...');
      const preloadRequest = new CustomerDetailsPreloadRequest({ customerId });
      const response = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!response.success) {
        console.error('❌ Preload request failed:', response.message);
        throw new ValidationError(`Failed to preload customer statistics: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      javax = response.data;

      console.log('✅ Preload request successful');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');
      console.log('🏷️ Dynamic element IDs:', JSON.stringify(javax, null, 2));
    }

    let gameStatistics: CustomerStatisticsResponse['statistics']['gameStatistics'] = {};
    let depositWithdrawStatistics: CustomerStatisticsResponse['statistics']['depositWithdrawStatistics'] = {};
    let qrReferenceStatistics: CustomerStatisticsResponse['statistics']['qrReferenceStatistics'] = {};
    let profitStatistics: CustomerStatisticsResponse['statistics']['profitStatistics'] = {};
    let casinoStatistics: CustomerStatisticsResponse['statistics']['casinoStatistics'] = {};
    let manualDepositWithdrawStatistics: CustomerStatisticsResponse['statistics']['manualDepositWithdrawStatistics'] = {};
    let weeklyStatistics: CustomerWeeklyStatisticsResponse['statistics']['weeklyStatistics'] = {};
    let dailyStatistics: CustomerDailyStatisticsResponse['statistics']['dailyStatistics'] = {};
    let monthlyStatistics: CustomerMonthlyStatisticsResponse['statistics']['monthlyStatistics'] = {};

    // Step 2: Tab change request to get basic statistics data
    {
      console.log('📊 Making tab change request to get basic statistics data...');
      const tabChangeRequest = new CustomerDetailsStatisticsTabChangeRequest({
        customerId,
        javax,
        viewState,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(tabChangeRequest);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Tab change request failed:', result.message);
        throw new ValidationError(`Failed to fetch customer statistics: ${result.message || 'Unknown error'}`);
      }

      console.log('✅ Tab change request successful');
      console.log('📈 Basic statistics data extracted:', JSON.stringify(result.data.statistics, null, 2));

      // Store all statistics from tab change request, update viewState for next request
      gameStatistics = result.data.statistics.gameStatistics;
      depositWithdrawStatistics = result.data.statistics.depositWithdrawStatistics;
      qrReferenceStatistics = result.data.statistics.qrReferenceStatistics;
      profitStatistics = result.data.statistics.profitStatistics;
      casinoStatistics = result.data.statistics.casinoStatistics;
      manualDepositWithdrawStatistics = result.data.statistics.manualDepositWithdrawStatistics;
      viewState = result.viewState || viewState;
    }

    // Step 3: Lifetime turnover request to get additional game statistics
    {
      console.log('📊 Making lifetime turnover request to get additional game statistics...');
      const lifetimeTurnoverRequest = new CustomerDetailsStatisticsLifetimeTurnoverRequest({
        customerId,
        javax,
        viewState,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(lifetimeTurnoverRequest);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Lifetime turnover request failed:', result.message);
        console.log('⚠️ Continuing without lifetime turnover data');
        // Don't throw error, just continue without lifetime turnover data
      } else {
        console.log('✅ Lifetime turnover request successful');
        console.log('📈 Lifetime turnover data extracted:', JSON.stringify(result.data.statistics, null, 2));

        // Merge lifetime turnover statistics into existing game statistics
        gameStatistics = {
          ...gameStatistics,
          ...result.data.statistics.gameStatistics,
        };

        // Update viewState for next request
        viewState = result.viewState || viewState;
      }
    }

    // Step 4: Weekly statistics request to get weekly data
    {
      console.log('📊 Making weekly statistics request to get weekly data...');
      const weeklyStatsRequest = new CustomerDetailsStatisticsWeeklyStatisticsRequest({
        customerId,
        javax,
        viewState,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(weeklyStatsRequest);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Weekly statistics request failed:', result.message);
        console.log('⚠️ Continuing without weekly statistics data');
        // Don't throw error, just continue without weekly statistics
      } else {
        console.log('✅ Weekly statistics request successful');
        console.log('📈 Weekly statistics data extracted:', JSON.stringify(result.data.statistics, null, 2));
        weeklyStatistics = result.data.statistics.weeklyStatistics;
        // Update viewState for next request
        viewState = result.viewState || viewState;
      }
    }

    // Step 5: Daily statistics request to get daily data
    {
      console.log('📊 Making daily statistics request to get daily data...');
      const dailyStatsRequest = new CustomerDetailsStatisticsDailyStatisticsRequest({
        customerId,
        javax,
        viewState,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(dailyStatsRequest);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Daily statistics request failed:', result.message);
        console.log('⚠️ Continuing without daily statistics data');
        // Don't throw error, just continue without daily statistics
      } else {
        console.log('✅ Daily statistics request successful');
        console.log('📈 Daily statistics data extracted:', JSON.stringify(result.data.statistics, null, 2));
        dailyStatistics = result.data.statistics.dailyStatistics;
        // Update viewState for next request
        viewState = result.viewState || viewState;
      }
    }

    // Step 6: Monthly statistics request to get monthly data
    {
      console.log('📊 Making monthly statistics request to get monthly data...');
      const monthlyStatsRequest = new CustomerDetailsStatisticsMonthlyStatisticsRequest({
        customerId,
        javax,
        viewState,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(monthlyStatsRequest);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Monthly statistics request failed:', result.message);
        console.log('⚠️ Continuing without monthly statistics data');
        // Don't throw error, just continue without monthly statistics
      } else {
        console.log('✅ Monthly statistics request successful');
        console.log('📈 Monthly statistics data extracted:', JSON.stringify(result.data.statistics, null, 2));
        monthlyStatistics = result.data.statistics.monthlyStatistics;
      }
    }

    // Combine all statistics data
    const combinedStatistics: CombinedCustomerStatisticsResponse = {
      statistics: {
        gameStatistics,
        depositWithdrawStatistics,
        qrReferenceStatistics,
        profitStatistics,
        casinoStatistics,
        manualDepositWithdrawStatistics,
        weeklyStatistics,
        dailyStatistics,
        monthlyStatistics,
      },
      customerId,
    };

    // Handle edge case where no statistics are found
    const hasGameStats = gameStatistics && Object.keys(gameStatistics).length > 0;
    const hasDepositWithdrawStats = depositWithdrawStatistics && Object.keys(depositWithdrawStatistics).length > 0;
    const hasQrReferenceStats = qrReferenceStatistics && Object.keys(qrReferenceStatistics).length > 0;
    const hasProfitStats = profitStatistics && Object.keys(profitStatistics).length > 0;
    const hasCasinoStats = casinoStatistics && Object.keys(casinoStatistics).length > 0;
    const hasManualDepositWithdrawStats = manualDepositWithdrawStatistics && Object.keys(manualDepositWithdrawStatistics).length > 0;
    const hasWeeklyStats = weeklyStatistics && Object.keys(weeklyStatistics).length > 0;
    const hasDailyStats = dailyStatistics && Object.keys(dailyStatistics).length > 0;
    const hasMonthlyStats = monthlyStatistics && Object.keys(monthlyStatistics).length > 0;

    const hasAnyStats = hasGameStats || hasDepositWithdrawStats || hasQrReferenceStats || hasProfitStats ||
                       hasCasinoStats || hasManualDepositWithdrawStats || hasWeeklyStats || hasDailyStats || hasMonthlyStats;

    if (!hasAnyStats) {
      console.log('ℹ️ No statistics found for customer:', customerId);
    }

    const response: ApiResponse<CombinedCustomerStatisticsResponse> = {
      success: true,
      data: combinedStatistics,
      message: !hasAnyStats
        ? 'No statistics found for this customer'
        : 'Customer statistics retrieved successfully',
    };

    res.status(200).json(response);
  });
}
