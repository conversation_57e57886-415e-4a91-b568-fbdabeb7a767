import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { CustomerDetailsDiscountTabChangeRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountTabChangeRequest';
import { CustomerDetailsPreloadRequest } from '@/network/pg-dagur/requests/customer/CustomerDetailsPreloadRequest';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { Request, Response } from 'express';
import { CustomerDetailsDiscountEnterCreateModeRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountEnterCreateModeRequest';
import { CustomerDetailsDiscountSelectRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountSelectRequest';
import { CustomerDetailsDiscountCreateRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountCreateRequest';
import { CustomerDetailsDiscountDialogGetRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountDialogGetRequest';

export class CustomerDiscountsController {
  static assignCustomerDiscount = asyncHandler(async (req: Request, res: Response) => {
    // Extract customerId from URL parameters
    const { customerId } = req.params;
    const { type, reasonId, amount, note, currencyId } = req.body;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    const preloadRequest = new CustomerDetailsPreloadRequest({ customerId });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new ValidationError(
        `Failed to preload customer discounts: ${preloadRequestResult.message || 'Unknown error'}`,
      );
    }

    const tabChangeRequest = new CustomerDetailsDiscountTabChangeRequest();
    const tabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      tabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!tabChangeRequestResult.success) {
      throw new ValidationError(
        `Failed to change tab to customer discounts: ${tabChangeRequestResult.message || 'Unknown error'}`,
      );
    }

    const enterCreateModeRequest = new CustomerDetailsDiscountEnterCreateModeRequest();
    const enterCreateModeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      enterCreateModeRequest,
      preloadRequestResult.viewState,
    );

    if (!enterCreateModeRequestResult.success) {
      throw new ValidationError(
        `Failed to enter create mode for customer discounts: ${
          enterCreateModeRequestResult.message || 'Unknown error'
        }`,
      );
    }

    const dialogGetRequest = new CustomerDetailsDiscountDialogGetRequest();
    const dialogGetRequestResult = await pgDagurAdminHttpClient.makeRequest(
      dialogGetRequest,
      preloadRequestResult.viewState,
    );

    if (!dialogGetRequestResult.success) {
      throw new ValidationError(
        `Failed to get customer discount dialog: ${dialogGetRequestResult.message || 'Unknown error'}`,
      );
    }

    const selectRequest = new CustomerDetailsDiscountSelectRequest({
      discountType: 'dt',
    });
    const selectRequestResult = await pgDagurAdminHttpClient.makeRequest(selectRequest, preloadRequestResult.viewState);

    if (!selectRequestResult.success) {
      throw new ValidationError(
        `Failed to select discount type for customer discounts: ${selectRequestResult.message || 'Unknown error'}`,
      );
    }

    const createRequest = new CustomerDetailsDiscountCreateRequest({
      javax: {
        ...dialogGetRequestResult.data,
        ...selectRequestResult.data,
      },

      discountType: type,
      customerId: Number(customerId),
      reasonId: reasonId,
      amount: amount,
      note: note,
    });
    const createRequestResult = await pgDagurAdminHttpClient.makeRequest(createRequest, preloadRequestResult.viewState);

    if (!createRequestResult.success) {
      throw new ValidationError(
        `Failed to create customer discount: ${createRequestResult.message || 'Unknown error'}`,
      );
    }

    const response: ApiResponse<any> = {
      success: true,
      data: null,
    };

    res.status(200).json(response);
  });

  static listDiscountTypes = asyncHandler(async (req: Request, res: Response) => {
    const response: ApiResponse<any> = {
      success: true,
      data: [
        {
          id: 'dt',
          name: 'Discount Transaction',
        },
        {
          id: 'rt',
          name: 'Rakeback Transaction',
        },
        {
          id: 'cd',
          name: 'Casino Discount',
        },
        {
          id: 'nd',
          name: 'Netent Discount',
        },
        {
          id: 'bd',
          name: 'Bet Discount',
        },
        {
          id: 'vd',
          name: 'VIP Discount',
        },
        {
          id: 'sd',
          name: 'Special Discount',
        },
        {
          id: 'ba',
          name: 'Bonus Adjustment',
        },
        {
          id: 'ac',
          name: 'Atm Cut',
        },
        {
          id: 'tb',
          name: 'Trial Bonus',
        },
        {
          id: 'nb',
          name: 'Non Turnover Bonus',
        },
        {
          id: 'wb',
          name: 'Welcome Bonus',
        },
        {
          id: 'dd',
          name: 'Deposit Discount',
        },
      ],
    };

    res.status(200).json(response);
  });

  static listReasons = asyncHandler(async (req: Request, res: Response) => {
    const { customerId } = req.params;

    const preloadRequest = new CustomerDetailsPreloadRequest({ customerId: customerId as string });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new ValidationError(
        `Failed to preload customer discounts: ${preloadRequestResult.message || 'Unknown error'}`,
      );
    }

    const tabChangeRequest = new CustomerDetailsDiscountTabChangeRequest();
    const tabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      tabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!tabChangeRequestResult.success) {
      throw new ValidationError(
        `Failed to change tab to customer discounts: ${tabChangeRequestResult.message || 'Unknown error'}`,
      );
    }

    const enterCreateModeRequest = new CustomerDetailsDiscountEnterCreateModeRequest();
    const enterCreateModeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      enterCreateModeRequest,
      preloadRequestResult.viewState,
    );

    if (!enterCreateModeRequestResult.success) {
      throw new ValidationError(
        `Failed to enter create mode for customer discounts: ${
          enterCreateModeRequestResult.message || 'Unknown error'
        }`,
      );
    }

    const dialogGetRequest = new CustomerDetailsDiscountDialogGetRequest();
    const dialogGetRequestResult = await pgDagurAdminHttpClient.makeRequest(
      dialogGetRequest,
      preloadRequestResult.viewState,
    );

    if (!dialogGetRequestResult.success) {
      throw new ValidationError(
        `Failed to get customer discount dialog: ${dialogGetRequestResult.message || 'Unknown error'}`,
      );
    }

    const response: ApiResponse<any> = {
      success: true,
      data: dialogGetRequestResult.data.reasons,
    };

    res.status(200).json(response);
  });
}
