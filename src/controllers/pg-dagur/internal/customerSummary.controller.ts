import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { ApiResponse } from '@/types/api';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { CustomerDetailsPreloadRequest } from '@/network/pg-dagur/requests/customer/CustomerDetailsPreloadRequest';
import {
  CustomerDetailsSummaryTabLoadRequest,
  CustomerSummaryResponse,
} from '@/network/pg-dagur/requests/customer/summary';

/**
 * Combined response interface for customer summary including customer, customer detail, balance data, calculated data and customer ID
 */
interface CombinedCustomerSummaryResponse {
  customer: CustomerSummaryResponse['customer'];
  customerDetail: CustomerSummaryResponse['customerDetail'];
  balances: CustomerSummaryResponse['balances'];
  calculatedData: CustomerSummaryResponse['calculatedData'];
  customerId: string;
}

export class CustomerSummaryController {
  /**
   * GET /api/pg-dagur/v1/internal/customers/:customerId/summary - Get customer summary balance data from PG Dagur
   */
  static getCustomerSummary = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Customer summary request initiated');
    console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
    console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

    // Extract customerId from URL parameters
    const { customerId } = req.params;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    let viewState = '';
    let javax: any = null;

    // Step 1: Preload request to get ViewState and dynamic element IDs
    {
      console.log('📥 Making preload request to get ViewState and element IDs...');
      const preloadRequest = new CustomerDetailsPreloadRequest({ customerId });
      const response = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!response.success) {
        console.error('❌ Preload request failed:', response.message);
        throw new ValidationError(`Failed to preload customer summary: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      javax = response.data;

      console.log('✅ Preload request successful');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');
      console.log('🏷️ Dynamic element IDs:', JSON.stringify(javax, null, 2));
    }

    let customer: CustomerSummaryResponse['customer'] = {};
    let customerDetail: CustomerSummaryResponse['customerDetail'] = {};
    let balances: CustomerSummaryResponse['balances'] = {};
    let calculatedData: CustomerSummaryResponse['calculatedData'] = {
      sportsFullBalance: 0,
      sportsBonusBalance: 0,
    };

    // Step 2: Summary tab load request to get customer, customer detail and balance data
    {
      console.log('📊 Making summary tab load request to get customer, customer detail and balance data...');
      const summaryTabLoadRequest = new CustomerDetailsSummaryTabLoadRequest({
        customerId,
        javax,
        viewState,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(summaryTabLoadRequest);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Summary tab load request failed:', result.message);
        throw new ValidationError(`Failed to fetch customer summary: ${result.message || 'Unknown error'}`);
      }

      console.log('✅ Summary tab load request successful');
      console.log('👤 Customer data extracted:', JSON.stringify(result.data.customer, null, 2));
      console.log('📋 Customer detail data extracted:', JSON.stringify(result.data.customerDetail, null, 2));
      console.log('💰 Balance data extracted:', JSON.stringify(result.data.balances, null, 2));
      console.log('🧮 Calculated data extracted:', JSON.stringify(result.data.calculatedData, null, 2));

      // Store data from summary tab load request
      customer = result.data.customer;
      customerDetail = result.data.customerDetail;
      balances = result.data.balances;
      calculatedData = result.data.calculatedData;
    }

    // Combine all data into final response
    const combinedSummary: CombinedCustomerSummaryResponse = {
      customer,
      customerDetail,
      balances,
      calculatedData,
      customerId,
    };

    console.log('📊 Final combined summary data:', JSON.stringify(combinedSummary, null, 2));

    // Check if we have any data
    const hasCustomerData = customer && Object.keys(customer).length > 0;
    const hasCustomerDetailData = customerDetail && Object.keys(customerDetail).length > 0;
    const hasBalanceData = balances && Object.keys(balances).length > 0;
    const hasAnyCustomerData = Object.values(customer).some(value => value !== null && value !== undefined);
    const hasAnyCustomerDetailData = Object.values(customerDetail).some(value => value !== null && value !== undefined);
    const hasAnyBalanceData = Object.values(balances).some(value => value !== null && value !== undefined);
    const hasAnyData = hasAnyCustomerData || hasAnyCustomerDetailData || hasAnyBalanceData;

    if (!hasAnyData) {
      console.log('ℹ️ No customer, customer detail or balance data found for customer:', customerId);
    }

    const response: ApiResponse<CombinedCustomerSummaryResponse> = {
      success: true,
      data: combinedSummary,
      message: !hasAnyData
        ? 'No customer, customer detail or balance data found for this customer'
        : 'Customer summary retrieved successfully',
    };

    res.status(200).json(response);
  });
}
