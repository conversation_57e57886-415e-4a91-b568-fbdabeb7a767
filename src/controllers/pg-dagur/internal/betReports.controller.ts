import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHandler } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { pgDagurApiClient } from '@/network/pg-dagur/PGDagurApiClient';

export class BetReportsController {
  /**
   * GET /api/pg-dagur/v1/internal/bet-reports/daily - Get daily bet reports from PG Dagur
   */
  static getDailyBetReport = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { startDate, endDate, page, limit } = req.query;

    // Validate required fields
    if (!startDate) {
      throw new ValidationError('startDate is required in query parameters');
    }
    if (!endDate) {
      throw new ValidationError('endDate is required in query parameters');
    }
    console.log('📊 Daily bet report request initiated');
    console.log('📋 Query parameters:', { startDate, endDate, page, limit });

    const requestOptions = {
      startDate: new Date(startDate as string),
      endDate: new Date(endDate as string),
      page: page ? Number(page) : 1,
      limit: limit ? Number(limit) : 100,
    };

    // Make authenticated request to PG Dagur bet reports endpoint
    const result = await pgDagurApiClient.betReports.getDailyBetReport(requestOptions);

    console.log('✅ Daily bet report retrieved successfully');
    console.log('📊 Report summary:', {
      totalReports: result.reports.total,
      pageSize: result.reports.pageSize,
      currentPage: result.reports.offset,
      itemsReturned: result.reports.items.length,
    });

    const response: ApiResponse<any> = {
      success: true,
      data: result,
      message: 'Daily bet report retrieved successfully',
    };

    res.status(200).json(response);
  });
}
