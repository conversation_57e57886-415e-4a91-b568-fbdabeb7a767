import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHandler } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { BetReportLoginRequest } from '@/network/pg-dagur/requests/auth/BetReportLoginRequest';
import fetch from 'node-fetch';

export class ReportsController {
  /**
   * POST /api/pg-dagur/v1/internal/reports/daily-bet-report - Get daily bet report from external API
   *
   * This endpoint acts as a proxy to the external daily bet report API using bearer token authentication.
   * It first obtains a bearer token from <PERSON><PERSON>r, then uses it to make the request to the external API.
   */
  static getDailyBetReport = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📊 Daily bet report request initiated');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Validate that we have a request body
      if (!req.body || Object.keys(req.body).length === 0) {
        throw new ValidationError('Request body is required');
      }

      // Ensure we have Dagur authentication
      await pgDagurAdminHttpClient.loginWithOtpSecret(
        process.env['PG_DAGUR_USERNAME'] || '',
        process.env['PG_DAGUR_PASSWORD'] || '',
        process.env['PG_DAGUR_OTP_SECRET'] || '',
      );

      console.log('✅ Dagur authentication successful');

      // Step 1: Get bearer token using BetReportLoginRequest
      console.log('🔑 Obtaining bearer token for daily bet report...');
      const tokenResponse = await pgDagurAdminHttpClient.makeRequest(new BetReportLoginRequest());

      if (!tokenResponse.success) {
        throw new Error(`Failed to obtain bearer token: ${tokenResponse.message}`);
      }

      const bearerToken = tokenResponse.data;
      console.log('✅ Bearer token obtained successfully');
      console.log('🔑 Token preview:', bearerToken.substring(0, 50) + '...');

      // Step 2: Use bearer token to make request to external API
      const externalApiUrl = 'https://jord.pronetgaming.eu/api/backoffice/report/dailyBetReport/byDay';
      console.log(`🎯 Making request to external API: ${externalApiUrl}`);

      // Prepare headers with bearer token authentication
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${bearerToken}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      };

      console.log('📤 Request headers prepared with bearer token');

      // Make the request to the external API
      const response = await fetch(externalApiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(req.body),
      });

      console.log(`📥 External API response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`External API request failed with status: ${response.status} ${response.statusText}`);
      }

      // Parse the response
      const responseData = await response.json();

      console.log('📥 External API response received');
      console.log('📊 Response data preview:', JSON.stringify(responseData, null, 2).substring(0, 500) + '...');

      // Return the response in the standard API format
      const apiResponse: ApiResponse<any> = {
        success: true,
        message: 'Daily bet report retrieved successfully',
        data: responseData,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(apiResponse);

    } catch (error) {
      console.error('❌ Daily bet report request failed:', error);

      // Return error response
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve daily bet report',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        ...(process.env['NODE_ENV'] === 'development' && {
          stack: error instanceof Error ? error.stack : undefined,
        }),
      });
    }
  });
}
