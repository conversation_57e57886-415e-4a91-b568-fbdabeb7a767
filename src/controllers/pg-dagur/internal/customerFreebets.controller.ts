import { ApiResponse } from '@/types/api';
import { Request, Response } from 'express';
import { CustomerDetailsPreloadRequest } from '@/network/pg-dagur/requests/customer/CustomerDetailsPreloadRequest';
import { CustomerFreebetsTabChangeRequest } from '@/network/pg-dagur/requests/customer/freebets/CustomerFreebetsTabChangeRequest';
import { AddFreebetButtonPressRequest } from '@/network/pg-dagur/requests/customer/freebets/AddFreebetButtonPressRequest';
import { FreebetDropdownSelectRequest } from '@/network/pg-dagur/requests/customer/freebets/FreebetDropdownSelectRequest';
import { AssignCustomerFreebetRequest } from '@/network/pg-dagur/requests/customer/freebets/AssignCustomerFreebetRequest';
import { AssignCustomerFreebetCleanupRequest } from '@/network/pg-dagur/requests/customer/freebets/AssignCustomerFreebetCleanupRequest';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { asyncHandler } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';

export class CustomerFreebetsController {
  /**
   * GET /api/pg-dagur/v1/internal/customers/:customerId/freebets - Get customer freebets from PG Dagur
   */
  static getCustomerFreebets = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Customer freebets request initiated');
    console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
    console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

    // Extract customerId from URL parameters
    const { customerId } = req.params;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    console.log(`🔍 Processing freebets request for customer: ${customerId}`);

    let viewState = '';
    let javax: any = null;

    // Step 1: Preload request to get ViewState and dynamic element IDs
    {
      console.log('📥 Making preload request to get ViewState and element IDs...');
      const preloadRequest = new CustomerDetailsPreloadRequest({ customerId });
      const response = await pgDagurAdminHttpClient.makeRequest(preloadRequest);
      
      if (!response.success) {
        console.error('❌ Preload request failed:', response.message);
        throw new ValidationError(`Failed to preload customer freebets: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      javax = response.data;
      
      console.log('✅ Preload request successful');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');
      console.log('🏷️ Dynamic element IDs:', JSON.stringify(javax, null, 2));
    }

    // Step 2: Tab change request to get freebets data
    {
      console.log('📊 Making tab change request to get freebets data...');
      const tabChangeRequest = new CustomerFreebetsTabChangeRequest({
        customerId,
        javax,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(tabChangeRequest, viewState);

      // Check if the request was successful
      if (!result.success) {
        console.error('❌ Tab change request failed:', result.message);
        throw new ValidationError(`Failed to fetch customer freebets: ${result.message || 'Unknown error'}`);
      }

      console.log('✅ Tab change request successful');
      console.log('📈 Active freebets found:', result.data.totalActiveCount);
      console.log('📉 Inactive freebets found:', result.data.totalInactiveCount);

      // Handle edge case where no freebets are found
      if (result.data.totalActiveCount === 0 && result.data.totalInactiveCount === 0) {
        console.log('ℹ️ No freebets found for customer:', customerId);
      }

      const response: ApiResponse<any> = {
        success: true,
        data: {
          ...result.data,
          customerId, // Include customerId in response for reference
        },
        message: result.data.totalActiveCount === 0 && result.data.totalInactiveCount === 0
          ? 'No freebets found for this customer'
          : 'Customer freebets retrieved successfully',
      };

      res.status(200).json(response);
    }
  });

  /**
   * POST /api/pg-dagur/v1/internal/customers/:customerId/freebets - Assign a freebet to a customer
   */
  static assignCustomerFreebet = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Customer freebet assignment request initiated');
    console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

    // Extract customerId from URL parameters
    const { customerId } = req.params;

    // Extract assignment parameters from request body
    const { freebetId, amount, numericAmount, note } = req.body;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    // Validate assignment parameters using the validation helper
    const validation = AssignCustomerFreebetRequest.validateAssignmentParams({
      customerId,
      freebetId,
      amount,
      numericAmount,
      note,
    });

    if (!validation.isValid) {
      throw new ValidationError(`Invalid assignment parameters: ${validation.errors.join(', ')}`);
    }

    console.log(`🔍 Processing freebet assignment for customer: ${customerId}, freebet: ${freebetId}`);

    let viewState = '';
    let javax: any = null;

    // Step 1: Get customer details to establish session and get ViewState + form element IDs
    let initialJavax: any = null;
    {
      console.log('📥 Making initial request to get ViewState and form element IDs...');
      const initialRequest = new CustomerDetailsPreloadRequest({ customerId });
      const response = await pgDagurAdminHttpClient.makeRequest(initialRequest);

      if (!response.success) {
        console.error('❌ Initial request failed:', response.message);
        throw new ValidationError(`Failed to initialize freebet assignment: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      initialJavax = response.data; // Contains tabViewId and formElementIds for tab change

      console.log('✅ Initial request successful');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');
      console.log('🏷️ Initial form element IDs:', JSON.stringify(initialJavax, null, 2));
    }

    // Step 2: Retrieve request to get freebets data (required for proper viewState progression)
    {
      console.log('📊 Making retrieve request to get freebets data (required for viewState progression)...');
      const tabChangeRequest = new CustomerFreebetsTabChangeRequest({
        customerId,
        javax: initialJavax, // Use the initial javax data for tab change
      });

      const retrieveResult = await pgDagurAdminHttpClient.makeRequest(tabChangeRequest, viewState);

      // Check if the request was successful
      if (!retrieveResult.success) {
        console.error('❌ Retrieve request failed:', retrieveResult.message);
        throw new ValidationError(`Failed to retrieve customer freebets for assignment: ${retrieveResult.message || 'Unknown error'}`);
      }

      console.log('✅ Retrieve request successful');
      console.log('📈 Active freebets found:', retrieveResult.data.totalActiveCount);
      console.log('📉 Inactive freebets found:', retrieveResult.data.totalInactiveCount);

      // Update viewState from the retrieve response if available
      if (retrieveResult.viewState) {
        viewState = retrieveResult.viewState;
        console.log('🔄 Updated ViewState from retrieve response');
      }
    }

    // Step 3: Add Button Press request to open the assignment dialog
    {
      console.log('🔘 Making add freebet button press request...');
      const addButtonRequest = new AddFreebetButtonPressRequest({
        customerId,
      });

      const addButtonResult = await pgDagurAdminHttpClient.makeRequest(addButtonRequest, viewState);

      // Check if the request was successful
      if (!addButtonResult.success) {
        console.error('❌ Add button press request failed:', addButtonResult.message);
        throw new ValidationError(`Failed to press add freebet button: ${addButtonResult.message || 'Unknown error'}`);
      }

      console.log('✅ Add button press request successful');

      // Extract dynamic element IDs from the dialog response
      javax = {
        formElementIds: addButtonResult.data.formElementIds,
      };

      console.log('🏷️ Dynamic element IDs extracted from dialog:', JSON.stringify(javax, null, 2));

      // Update viewState from the add button response if available
      if (addButtonResult.viewState) {
        viewState = addButtonResult.viewState;
        console.log('🔄 Updated ViewState from add button response');
      }
    }

    // Step 4: Dropdown Select request to select the freebet
    {
      console.log('📋 Making freebet dropdown select request...');
      console.log('🎯 Selecting freebet ID:', freebetId);

      const dropdownSelectRequest = new FreebetDropdownSelectRequest({
        customerId,
        freebetId,
        javax,
      });

      const dropdownResult = await pgDagurAdminHttpClient.makeRequest(dropdownSelectRequest, viewState);

      // Check if the request was successful
      if (!dropdownResult.success) {
        console.error('❌ Dropdown select request failed:', dropdownResult.message);
        throw new ValidationError(`Failed to select freebet from dropdown: ${dropdownResult.message || 'Unknown error'}`);
      }

      console.log('✅ Dropdown select request successful');

      // Update viewState from the dropdown response if available
      if (dropdownResult.viewState) {
        viewState = dropdownResult.viewState;
        console.log('🔄 Updated ViewState from dropdown response');
      }
    }

    // Step 5: Assignment request to assign the freebet
    {
      console.log('📊 Making freebet assignment request...');
      console.log('🎯 About to POST to: https://dagur.pgbo.io/restricted/customer-details.xhtml');
      console.log('🔑 Using ViewState length:', viewState.length);
      console.log('🏷️ Using dynamic element IDs:', JSON.stringify(javax, null, 2));

      try {
        const assignmentRequest = new AssignCustomerFreebetRequest({
          customerId,
          freebetId,
          amount,
          numericAmount,
          note,
          javax,
        });

        console.log('🚀 Executing POST request to Dagur now...');
        const result = await pgDagurAdminHttpClient.makeRequest(assignmentRequest, viewState);
        console.log('📥 POST request to Dagur completed');

        // Check if the request was successful
        if (!result.success) {
          console.error('❌ Assignment request failed:', result.message);

          // Handle specific error cases
          if (result.message.includes('timeout') || result.message.includes('network')) {
            throw new ValidationError('Network timeout or connection error. Please try again.');
          } else if (result.message.includes('authentication') || result.message.includes('session')) {
            throw new ValidationError('Authentication failed. Please check credentials.');
          } else {
            throw new ValidationError(`Failed to assign freebet: ${result.message || 'Unknown error'}`);
          }
        }

        console.log('✅ Assignment request completed successfully');
        console.log('📈 Assignment result:', JSON.stringify(result.data, null, 2));

        // Update viewState from the assignment response if available
        if (result.viewState) {
          viewState = result.viewState;
          console.log('🔄 Updated ViewState from assignment response');
        }
      } catch (error) {
        console.error('❌ Unexpected error during freebet assignment:', error);
        throw error;
      }
    }

    // Step 6: Cleanup request to reset the dialog form
    {
      console.log('🧹 Making freebet assignment cleanup request...');
      console.log('🎯 About to POST cleanup to: https://dagur.pgbo.io/restricted/customer-details.xhtml');
      console.log('🔑 Using ViewState length:', viewState.length);

      try {
        const cleanupRequest = new AssignCustomerFreebetCleanupRequest({
          customerId,
          note,
          javax,
        });

        console.log('🚀 Executing cleanup POST request to Dagur now...');
        const cleanupResult = await pgDagurAdminHttpClient.makeRequest(cleanupRequest, viewState);
        console.log('📥 Cleanup POST request to Dagur completed');

        // Check if the cleanup request was successful
        if (!cleanupResult.success) {
          console.error('❌ Cleanup request failed:', cleanupResult.message);
          // Don't throw error for cleanup failure, just log it
          console.log('⚠️ Continuing despite cleanup failure - assignment was successful');
        } else {
          console.log('✅ Cleanup request completed successfully');
          console.log('📈 Cleanup result:', JSON.stringify(cleanupResult.data, null, 2));
        }
      } catch (error) {
        console.error('❌ Unexpected error during cleanup:', error);
        // Don't throw error for cleanup failure, just log it
        console.log('⚠️ Continuing despite cleanup error - assignment was successful');
      }
    }

    // Since we don't parse the response body, we assume success if we got here
    // Dagur would return an error page or redirect if the assignment failed
    const response: ApiResponse<any> = {
      success: true,
      data: { customerId, freebetId, amount, numericAmount, note },
      message: 'Freebet assignment completed successfully',
    };

    console.log('📤 Sending success response to client');
    res.status(200).json(response);
  });
}
