import { ApiResponse } from '@/types/api';
import { Request, Response } from 'express';
import {
  GetTotalBalancesPreloadRequest,
  GetTotalBalancesRequest,
} from '@/network/pg-dagur/requests/customer/GetTotalBalancesRequest';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { asyncHandler } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import {
  CustomerActivityReportListPreloadRequest,
  CustomerActivityReportListRequest,
} from '@/network/pg-dagur/requests/customer/CustomerActivityReportListRequest';
import {
  CustomerReportListWithCursorRequest,
  CustomerReportListPreloadRequest,
  CustomerReportListPreloadRequestResponse,
  CustomerReportListRequest,
} from '@/network/pg-dagur/requests/customer/CustomerReportListRequest';
import { CustomerDetailsPreloadRequest } from '@/network/pg-dagur/requests/customer/CustomerDetailsPreloadRequest';
import { CustomerDetailsBetsSearchRequest } from '@/network/pg-dagur/requests/customer/bets/CustomerDetailsBetsSearchRequest';
import { CustomerDetailsBetsTabChangeRequest } from '@/network/pg-dagur/requests/customer/bets/CustomerDetailsBetsTabChangeRequest';
import { CustomerDetailsBetsFilterRequest } from '@/network/pg-dagur/requests/customer/bets/CustomerDetailsBetsFilterRequest';
import { CustomerDetailsDetailsTabChangeRequest } from '@/network/pg-dagur/requests/customer/details/CustomerDetailsDetailsTabChangeRequest';
import { CustomerDetailsIpConflictPreloadRequest } from '@/network/pg-dagur/requests/customer/ip-conflict/CustomerDetailsIpConflictPreloadRequest';
import { CustomerDetailsIpConflictGetRequest } from '@/network/pg-dagur/requests/customer/ip-conflict/CustomerDetailsIpConflictGetRequest';

export class CustomersController {
  /**
   * POST /operator/customers/total-balances - Get total balances from PG Dagur
   */
  static getTotalBalances = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body
    const { endDate } = req.query;

    // Validate required fields
    if (!endDate) {
      throw new ValidationError('endDate is required in request body');
    }

    let viewState = '';
    let testPlayerInputId = '';
    {
      const response = await pgDagurAdminHttpClient.makeRequest(new GetTotalBalancesPreloadRequest());
      if (!response.success) {
        throw new ValidationError(`Failed to preload total balances: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      testPlayerInputId = response.data.testPlayerInputId;
    }

    const request = new GetTotalBalancesRequest({
      endDate: new Date(endDate as string),
      testPlayerInputId: testPlayerInputId,
    });
    const preloadRequest = request.getPreloadRequest();

    if (preloadRequest) {
      const result = await pgDagurAdminHttpClient.makeRequest(preloadRequest);
      viewState = result.viewState || '';
    }

    // Make authenticated request to PG Dagur total balances endpoint
    const result = await pgDagurAdminHttpClient.makeRequest(request, viewState);

    // Check if the request was successful
    if (!result.success) {
      throw new ValidationError(`Failed to fetch total balances: ${result.message || 'Unknown error'}`);
    }

    const response: ApiResponse<any> = {
      success: true,
      data: result.data,
    };

    res.status(200).json(response);
  });

  static listActivityReports = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body
    const { startDate, endDate } = req.query;

    // Validate required fields
    if (!startDate) {
      throw new ValidationError('startDate is required in request body');
    }
    if (!endDate) {
      throw new ValidationError('endDate is required in request body');
    }

    let viewState = '';
    let currencyInputId = '';
    {
      const preloadRequest = new CustomerActivityReportListPreloadRequest();
      const result = await pgDagurAdminHttpClient.makeRequest(preloadRequest);
      if (!result.success) {
        throw new ValidationError(`Failed to preload customer activity report: ${result.message || 'Unknown error'}`);
      }

      viewState = result.viewState || '';
      currencyInputId = result.data.currencyInputId;
    }

    const request = new CustomerActivityReportListRequest({
      currencyInputId: currencyInputId,
      startDate: new Date(startDate as string),
      endDate: new Date(endDate as string),
    });

    // Make authenticated request to PG Dagur total balances endpoint
    const result = await pgDagurAdminHttpClient.makeRequest(request, viewState);

    // Check if the request was successful
    if (!result.success) {
      throw new ValidationError(`Failed to fetch total balances: ${result.message || 'Unknown error'}`);
    }

    const response: ApiResponse<any> = {
      success: true,
      data: result.data,
    };

    res.status(200).json(response);
  });

  static listCustomerReports = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body
    const {
      firstDepositStartDate,
      firstDepositEndDate,
      lastDepositStartDate,
      lastDepositEndDate,
      lastLoginStartDate,
      lastLoginEndDate,
      registrationStartDate,
      registrationEndDate,
      page,
      limit,
      cursor,
    } = req.query;

    let viewState = '';
    let javax: CustomerReportListPreloadRequestResponse | null = null;
    {
      const preloadRequest = new CustomerReportListPreloadRequest();
      const result = await pgDagurAdminHttpClient.makeRequest(preloadRequest);
      if (!result.success) {
        throw new ValidationError(`Failed to preload customer report: ${result.message || 'Unknown error'}`);
      }

      viewState = result.viewState || '';
      javax = result.data;
    }

    if (cursor) {
      const cursorRequest = new CustomerReportListWithCursorRequest({
        javax,
        firstDepositStartDate: firstDepositStartDate ? new Date(firstDepositStartDate as string) : undefined,
        firstDepositEndDate: firstDepositEndDate ? new Date(firstDepositEndDate as string) : undefined,
        lastDepositStartDate: lastDepositStartDate ? new Date(lastDepositStartDate as string) : undefined,
        lastDepositEndDate: lastDepositEndDate ? new Date(lastDepositEndDate as string) : undefined,
        lastLoginStartDate: lastLoginStartDate ? new Date(lastLoginStartDate as string) : undefined,
        lastLoginEndDate: lastLoginEndDate ? new Date(lastLoginEndDate as string) : undefined,
        registrationStartDate: registrationStartDate ? new Date(registrationStartDate as string) : undefined,
        registrationEndDate: registrationEndDate ? new Date(registrationEndDate as string) : undefined,
        cursor: cursor as string,
        page: page ? Number(page) : 1,
        limit: limit ? Number(limit) : 100,
      });

      const result = await pgDagurAdminHttpClient.makeRequest(cursorRequest, cursor as string);
      if (!result.success) {
        throw new ValidationError(`Failed to fetch customer report: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        data: result.data,
      };

      res.status(200).json(response);
    } else {
      const request = new CustomerReportListRequest({
        javax,
        firstDepositStartDate: firstDepositStartDate ? new Date(firstDepositStartDate as string) : undefined,
        firstDepositEndDate: firstDepositEndDate ? new Date(firstDepositEndDate as string) : undefined,
        lastDepositStartDate: lastDepositStartDate ? new Date(lastDepositStartDate as string) : undefined,
        lastDepositEndDate: lastDepositEndDate ? new Date(lastDepositEndDate as string) : undefined,
        lastLoginStartDate: lastLoginStartDate ? new Date(lastLoginStartDate as string) : undefined,
        lastLoginEndDate: lastLoginEndDate ? new Date(lastLoginEndDate as string) : undefined,
        registrationStartDate: registrationStartDate ? new Date(registrationStartDate as string) : undefined,
        registrationEndDate: registrationEndDate ? new Date(registrationEndDate as string) : undefined,
        cursor: viewState,
        page: page ? Number(page) : 1,
        limit: limit ? Number(limit) : 100,
      });

      // Make authenticated request to PG Dagur total balances endpoint
      const result = await pgDagurAdminHttpClient.makeRequest(request, viewState);

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch customer report: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        data: result.data,
      };

      res.status(200).json(response);
    }
  });

  static listCustomerBets = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body
    const { customerId } = req.params;
    const { startDate, endDate, status, page, limit } = req.query;

    // Validate required fields
    if (!startDate) {
      throw new ValidationError('startDate is required in request body');
    }
    if (!endDate) {
      throw new ValidationError('endDate is required in request body');
    }

    const preloadRequest = new CustomerDetailsPreloadRequest({
      customerId: customerId as string,
    });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);
    if (!preloadRequestResult.success) {
      throw new ValidationError(`Failed to preload customer bets: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    const customerDetailsTabChangeRequest = new CustomerDetailsBetsTabChangeRequest();
    const customerDetailsTabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      customerDetailsTabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!customerDetailsTabChangeRequestResult.success) {
      throw new ValidationError(
        `Failed to change tab to customer bets: ${customerDetailsTabChangeRequestResult.message || 'Unknown error'}`,
      );
    }

    const searchRequest = new CustomerDetailsBetsSearchRequest({
      javax: {
        ...customerDetailsTabChangeRequestResult.data,
      },
      startDate: new Date(startDate as string),
      endDate: new Date(endDate as string),
    });
    // Make authenticated request to PG Dagur total balances endpoint
    const searchRequestResult = await pgDagurAdminHttpClient.makeRequest(searchRequest, preloadRequestResult.viewState);
    // Check if the request was successful
    if (!searchRequestResult.success) {
      throw new ValidationError(`Failed to fetch customer bets: ${searchRequestResult.message || 'Unknown error'}`);
    }

    if (status) {
      const filterRequest = new CustomerDetailsBetsFilterRequest({
        javax: {
          ...preloadRequestResult.data.formElementIds,
          ...customerDetailsTabChangeRequestResult.data,
          ...searchRequestResult.data,
        },
        customerId: Number(customerId as string),
        status: status as string,
        startDate: new Date(startDate as string),
        endDate: new Date(endDate as string),
      });
      // Make authenticated request to PG Dagur total balances endpoint
      const filterRequestResult = await pgDagurAdminHttpClient.makeRequest(
        filterRequest,
        preloadRequestResult.viewState,
      );
      // Check if the request was successful
      if (!filterRequestResult.success) {
        throw new ValidationError(`Failed to fetch customer bets: ${filterRequestResult.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        data: filterRequestResult.data,
      };

      res.status(200).json(response);
    } else {
      const response: ApiResponse<any> = {
        success: true,
        data: searchRequestResult.data,
      };

      res.status(200).json(response);
    }
  });

  static getCustomerDetails = asyncHandler(async (req: Request, res: Response) => {
    // Extract customerId from URL parameters
    const { customerId } = req.params;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    const preloadRequest = new CustomerDetailsPreloadRequest({ customerId });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new ValidationError(
        `Failed to preload customer details: ${preloadRequestResult.message || 'Unknown error'}`,
      );
    }

    const tabChangeRequest = new CustomerDetailsDetailsTabChangeRequest();
    const tabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      tabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!tabChangeRequestResult.success) {
      throw new ValidationError(
        `Failed to change tab to customer details: ${tabChangeRequestResult.message || 'Unknown error'}`,
      );
    }

    const response: ApiResponse<any> = {
      success: true,
      data: tabChangeRequestResult.data,
    };

    res.status(200).json(response);
  });

  static getCustomerIpConflicts = asyncHandler(async (req: Request, res: Response) => {
    // Extract customerId from URL parameters
    const { customerId } = req.params;

    // Validate required fields
    if (!customerId || customerId.trim() === '') {
      throw new ValidationError('customerId is required as URL parameter');
    }

    const preloadRequest = new CustomerDetailsPreloadRequest({ customerId });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new ValidationError(
        `Failed to preload customer details: ${preloadRequestResult.message || 'Unknown error'}`,
      );
    }

    const formPreloadRequest = new CustomerDetailsIpConflictPreloadRequest({
      javax: preloadRequestResult.data,
    });
    const formPreloadRequestResult = await pgDagurAdminHttpClient.makeRequest(
      formPreloadRequest,
      preloadRequestResult.viewState,
    );

    if (!formPreloadRequestResult.success) {
      throw new ValidationError(
        `Failed to preload customer IP conflicts form: ${formPreloadRequestResult.message || 'Unknown error'}`,
      );
    }

    const getRequest = new CustomerDetailsIpConflictGetRequest();
    const getRequestResult = await pgDagurAdminHttpClient.makeRequest(getRequest, preloadRequestResult.viewState);

    if (!getRequestResult.success) {
      throw new ValidationError(`Failed to get customer IP conflicts: ${getRequestResult.message || 'Unknown error'}`);
    }

    const response: ApiResponse<any> = {
      success: true,
      data: getRequestResult.data,
    };

    res.status(200).json(response);
  });
}
