import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { ApiResponse } from '@/types/api';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { PromotionsPreloadRequest } from '@/network/pg-dagur/requests/promotions/PromotionsPreloadRequest';
import { PromotionsFreebetsTabChangeRequest } from '@/network/pg-dagur/requests/promotions/freebets/PromotionsFreebetsTabChangeRequest';


/**
 * Controller for PG Dagur promotions-related operations
 * This controller handles various promotion pipelines that start with PromotionsPreloadRequest
 */
export class PromotionsController {
  /**
   * GET /api/pg-dagur/v1/internal/promotions/preload - Preload promotions page
   * This endpoint can be used to test the promotions preload functionality
   * and will serve as a foundation for future promotion-related pipelines
   */
  static preloadPromotions = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Promotions preload request initiated');

    let viewState = '';
    let promotionsData: any = null;

    // Step 1: Preload request to get ViewState and any dynamic element IDs
    {
      console.log('📥 Making preload request to promotions page...');
      const preloadRequest = new PromotionsPreloadRequest();
      const response = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!response.success) {
        console.error('❌ Preload request failed:', response.message);
        throw new ValidationError(`Failed to preload promotions: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      promotionsData = response.data;

      console.log('✅ Preload request successful');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');
      console.log('🏷️ Promotions data:', JSON.stringify(promotionsData, null, 2));
    }

    const apiResponse: ApiResponse<any> = {
      success: true,
      message: 'Promotions preload completed successfully',
      data: {
        viewState,
        promotionsData,
      },
      timestamp: new Date().toISOString(),
    };

    console.log('🎉 Promotions preload request completed successfully');
    res.status(200).json(apiResponse);
  });

  /**
   * POST /api/pg-dagur/v1/internal/promotions/freebets/tab-change - Change to freebets tab
   * This endpoint tests the freebets tab change functionality
   * Requires viewState in the request body
   */
  static changeToFreebetsTab = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Promotions freebets tab change request initiated');
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

    const { viewState } = req.body;

    // Validate required fields
    if (!viewState || viewState.trim() === '') {
      throw new ValidationError('viewState is required in request body');
    }

    let tabChangeResponse: any = null;

    // Step 1: Change to freebets tab
    {
      console.log('📥 Making freebets tab change request...');
      const tabChangeRequest = new PromotionsFreebetsTabChangeRequest({ viewState });
      const response = await pgDagurAdminHttpClient.makeRequest(tabChangeRequest);

      if (!response.success) {
        console.error('❌ Tab change request failed:', response.message);
        throw new ValidationError(`Failed to change to freebets tab: ${response.message || 'Unknown error'}`);
      }

      tabChangeResponse = response.data;

      console.log('✅ Tab change request successful');
      console.log('📄 Response HTML length:', tabChangeResponse.html?.length || 0, 'characters');
    }

    const apiResponse: ApiResponse<any> = {
      success: true,
      message: 'Freebets tab change completed successfully',
      data: {
        html: tabChangeResponse.html,
        htmlLength: tabChangeResponse.html?.length || 0,
      },
      timestamp: new Date().toISOString(),
    };

    console.log('🎉 Promotions freebets tab change request completed successfully');
    res.status(200).json(apiResponse);
  });

  /**
   * GET /api/pg-dagur/v1/internal/promotions/freebets - Get promotions freebets list
   * This endpoint combines preload, tab change, and table parsing to return the freebets list
   */
  static getPromotionsFreebets = asyncHandler(async (req: Request, res: Response) => {
    console.log('🎯 Promotions freebets list request initiated');

    let viewState = '';
    let freebetsData: any = null;

    // Step 1: Preload request to get ViewState
    {
      console.log('📥 Step 1: Making preload request to get ViewState...');
      const preloadRequest = new PromotionsPreloadRequest();
      const response = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

      if (!response.success) {
        console.error('❌ Preload request failed:', response.message);
        throw new ValidationError(`Failed to preload promotions: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      console.log('✅ Preload request successful');
      console.log('🔑 ViewState extracted:', viewState ? 'Yes' : 'No');
    }

    // Step 2: Change to freebets tab and parse the table
    {
      console.log('📥 Step 2: Making freebets tab change request and parsing table...');
      const tabChangeRequest = new PromotionsFreebetsTabChangeRequest({ viewState });
      const response = await pgDagurAdminHttpClient.makeRequest(tabChangeRequest);

      if (!response.success) {
        console.error('❌ Tab change request failed:', response.message);
        throw new ValidationError(`Failed to change to freebets tab: ${response.message || 'Unknown error'}`);
      }

      freebetsData = response.data;
      console.log('✅ Tab change and parsing completed');
      console.log('📊 Found freebets:', freebetsData.total);
    }

    const apiResponse: ApiResponse<any> = {
      success: true,
      message: 'Promotions freebets retrieved successfully',
      data: freebetsData,
      timestamp: new Date().toISOString(),
    };

    console.log('🎉 Promotions freebets request completed successfully');
    res.status(200).json(apiResponse);
  });

  // TODO: Future promotion-related endpoints will be added here
  // Examples:
  // - assignFreebet
  // - createPromotion
  // - updatePromotion
  // etc.
}
