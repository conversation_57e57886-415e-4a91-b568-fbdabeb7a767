import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { pgDagurApiClient } from '@/network/pg-dagur/PGDagurApiClient';

export class AccountingController {
  /**
   * POST /operator/accounting/transactions - List transactions from PG Dagur
   */
  static listTransactions = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body
    const { startDate, endDate, status, masterType, username, page, limit, loadSubtotals } = req.query;

    // Validate required fields
    if (!startDate) {
      throw new ValidationError('startDate is required in request body');
    }
    if (!endDate) {
      throw new ValidationError('endDate is required in request body');
    }
    if (!status) {
      throw new ValidationError('status is required in request body');
    }

    // Make authenticated request to PG Dagur transactions endpoint
    const result = await pgDagurApiClient.accounting.listTransactions({
      startDate: new Date(startDate as string),
      endDate: new Date(endDate as string),
      status: typeof status === 'string' ? [status] : (status as string[]),
      masterType: masterType as string,
      username: username as string,
      page: page ? Number(page) : 1,
      limit: limit ? Number(limit) : 10,
      loadSubtotals: loadSubtotals === 'true',
    });

    const response: ApiResponse<any> = {
      success: true,
      data: result,
    };

    res.status(200).json(response);
  });
}
