import { Request, Response } from 'express';
import { FinalMissionClaimService, CreateFinalMissionClaimDto, FinalMissionClaimQueryParams } from '@/services/finalMissionClaim.service';
import { ExtendedUserService } from '@/services/extendedUser.service';
import { MissionService } from '@/services/mission.service';
import { MissionParticipationService } from '@/services/missionParticipation.service';
import { MissionType, TransactionCategory } from '@/enums/shared';
import { asyncHandler } from '@/utils/asyncHandler';
import { extractUserIdFromToken, validateJwtToken } from '@/utils/jwtUtils';
import { isWithinCurrentDay, isWithinCurrentWeek, isWithinCurrentMonth } from '@/utils/periodicValidator';
import { AppDataSource } from '@/database/connection';

const finalMissionClaimService = new FinalMissionClaimService();
const extendedUserService = new ExtendedUserService();
const missionService = new MissionService();
const missionParticipationService = new MissionParticipationService();

/**
 * Public endpoint to claim final mission reward
 * POST /api/v1/makroz/final-mission-claims
 */
export const claimFinalMissionReward = asyncHandler(async (req: Request, res: Response) => {
  console.log('🎯 Claiming final mission reward');
  console.log('Request body:', req.body);

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from token
  const externalId = extractUserIdFromToken(authHeader);
  if (!externalId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  // Validate request body
  const { claimType } = req.body;

  if (!claimType) {
    return res.status(400).json({
      success: false,
      message: 'Claim type is required',
    });
  }

  // Validate claim type
  const validClaimTypes = [MissionType.DAILY, MissionType.WEEKLY, MissionType.MONTHLY];
  if (!validClaimTypes.includes(claimType)) {
    return res.status(400).json({
      success: false,
      message: `Claim type must be one of: ${validClaimTypes.join(', ')}`,
    });
  }

  try {
    // Find or create extended user
    let extendedUser = await extendedUserService.findExtendedUserByExternalId(externalId);
    if (!extendedUser) {
      extendedUser = await extendedUserService.createExtendedUser({
        externalId,
        points: 0,
      });
    }

    console.log(`👤 Processing claim for user ${externalId} (${claimType} missions)`);

    // Get current timestamp for mission filtering
    const currentTimestamp = Math.floor(Date.now() / 1000);

    // Find all active missions of the specified type
    const activeMissions = await missionService.findActiveMissions(currentTimestamp);
    const activeMissionsOfType = activeMissions.filter(mission => mission.missionType === claimType);

    console.log(`📋 Found ${activeMissionsOfType.length} active ${claimType} missions`);

    if (activeMissionsOfType.length === 0) {
      return res.status(400).json({
        success: false,
        message: `No active ${claimType} missions found`,
      });
    }

    // Check if user has completed ALL active missions of this type
    const userParticipations = await missionParticipationService.findParticipationsByUserId(externalId);
    const completedMissionsOfType = userParticipations.filter(participation => 
      participation.isCompleted && 
      activeMissionsOfType.some(mission => mission.id === participation.missionId)
    );

    console.log(`✅ User has completed ${completedMissionsOfType.length} out of ${activeMissionsOfType.length} ${claimType} missions`);

    // Check if user has completed all missions of this type
    if (completedMissionsOfType.length < activeMissionsOfType.length) {
      const pendingCount = activeMissionsOfType.length - completedMissionsOfType.length;
      return res.status(400).json({
        success: false,
        message: `You still have ${pendingCount} pending ${claimType} mission(s) to complete before claiming the final reward`,
        data: {
          completedMissions: completedMissionsOfType.length,
          totalMissions: activeMissionsOfType.length,
          pendingMissions: pendingCount,
        },
      });
    }

    // Check if user has already claimed this type of reward in the current period
    const latestClaim = await finalMissionClaimService.findLatestClaimByUserAndType(externalId, claimType);
    
    if (latestClaim) {
      console.log(`🔍 Found latest ${claimType} claim from: ${latestClaim.createdAt}`);
      
      let canClaimAgain = false;
      let periodName = '';

      // Use Periodic Validator to check if the latest claim was in the current period
      switch (claimType) {
        case MissionType.DAILY:
          canClaimAgain = !isWithinCurrentDay(latestClaim.createdAt);
          periodName = 'day';
          break;
        case MissionType.WEEKLY:
          canClaimAgain = !isWithinCurrentWeek(latestClaim.createdAt);
          periodName = 'week';
          break;
        case MissionType.MONTHLY:
          canClaimAgain = !isWithinCurrentMonth(latestClaim.createdAt);
          periodName = 'month';
          break;
      }

      if (!canClaimAgain) {
        return res.status(400).json({
          success: false,
          message: `You have already claimed the ${claimType} final mission reward this ${periodName}`,
          data: {
            lastClaimDate: latestClaim.createdAt,
            lastReward: latestClaim.grantedReward,
          },
        });
      }
    }

    // Calculate reward based on mission type and number of completed missions
    let rewardMultiplier = 0;
    switch (claimType) {
      case MissionType.DAILY:
        rewardMultiplier = 10;
        break;
      case MissionType.WEEKLY:
        rewardMultiplier = 30;
        break;
      case MissionType.MONTHLY:
        rewardMultiplier = 100;
        break;
    }

    const grantedReward = rewardMultiplier * completedMissionsOfType.length;

    console.log(`💰 Calculating reward: ${rewardMultiplier} * ${completedMissionsOfType.length} = ${grantedReward} points`);

    // Use transaction to ensure atomicity
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create final mission claim record
      const finalMissionClaim = await finalMissionClaimService.createFinalMissionClaim({
        userId: externalId,
        claimType,
        grantedReward,
      });

      // Award final mission reward points with proper transaction logging
      await extendedUserService.depositPoints(
        extendedUser.externalId,
        grantedReward,
        TransactionCategory.MISSION_REWARD,
        {
          claimType,
          completedMissions: completedMissionsOfType.length,
          rewardMultiplier,
          finalMissionClaimId: finalMissionClaim.id,
        },
        queryRunner.manager,
      );

      // Get updated user for response
      const updatedUser = await extendedUserService.findExtendedUserById(extendedUser.id);

      await queryRunner.commitTransaction();

      console.log(`🎉 Final mission claim successful! User ${externalId} earned ${grantedReward} points`);

      return res.status(201).json({
        success: true,
        message: `${claimType.charAt(0).toUpperCase() + claimType.slice(1)} final mission reward claimed successfully`,
        data: {
          claimId: finalMissionClaim.id,
          claimType,
          completedMissions: completedMissionsOfType.length,
          pointsAwarded: grantedReward,
          newPointsTotal: updatedUser?.points || (extendedUser.points + grantedReward),
          claimDate: finalMissionClaim.createdAt,
        },
      });

    } catch (transactionError) {
      await queryRunner.rollbackTransaction();
      console.error('❌ Transaction failed:', transactionError);
      throw transactionError;
    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error('❌ Error claiming final mission reward:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while claiming final mission reward',
    });
  }
});

// Admin CRUD operations for final mission claims

export const createFinalMissionClaim = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { userId, claimType, grantedReward }: CreateFinalMissionClaimDto = req.body;

  // Validation
  if (!userId || typeof userId !== 'number') {
    return res.status(400).json({
      success: false,
      message: 'User ID is required and must be a number',
    });
  }

  const validClaimTypes = [MissionType.DAILY, MissionType.WEEKLY, MissionType.MONTHLY];
  if (!validClaimTypes.includes(claimType)) {
    return res.status(400).json({
      success: false,
      message: `Claim type must be one of: ${validClaimTypes.join(', ')}`,
    });
  }

  if (!grantedReward || typeof grantedReward !== 'number' || grantedReward <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Granted reward is required and must be a positive number',
    });
  }

  try {
    // Verify that the user exists
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    const finalMissionClaim = await finalMissionClaimService.createFinalMissionClaim({
      userId,
      claimType,
      grantedReward,
    });

    return res.status(201).json({
      success: true,
      message: 'Final mission claim created successfully',
      data: finalMissionClaim,
    });
  } catch (error) {
    console.error('Error creating final mission claim:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export const getFinalMissionClaims = asyncHandler(async (req: Request, res: Response) => {
  console.log('Query parameters:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Parse query parameters
  const queryParams: FinalMissionClaimQueryParams = {};

  // Pagination & Sorting
  if (req.query['page']) {
    const page = parseInt(req.query['page'] as string);
    if (isNaN(page) || page < 1) {
      return res.status(400).json({
        success: false,
        message: 'page must be a positive integer',
      });
    }
    queryParams.page = page;
  }

  if (req.query['limit']) {
    const limit = parseInt(req.query['limit'] as string);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        message: 'limit must be between 1 and 100',
      });
    }
    queryParams.limit = limit;
  }

  if (req.query['sortBy']) {
    const sortBy = req.query['sortBy'] as string;
    const validSortFields = ['id', 'userId', 'claimType', 'grantedReward', 'createdAt', 'updatedAt'];
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({
        success: false,
        message: `sortBy must be one of: ${validSortFields.join(', ')}`,
      });
    }
    queryParams.sortBy = sortBy as any;
  }

  if (req.query['sortOrder']) {
    const sortOrder = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC'].includes(sortOrder.toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'sortOrder must be ASC or DESC',
      });
    }
    queryParams.sortOrder = sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filters
  if (req.query['userId']) {
    const userId = parseInt(req.query['userId'] as string);
    if (isNaN(userId)) {
      return res.status(400).json({
        success: false,
        message: 'userId must be a number',
      });
    }
    queryParams.userId = userId;
  }

  if (req.query['claimType']) {
    const claimType = req.query['claimType'] as string;
    const validClaimTypes = [MissionType.DAILY, MissionType.WEEKLY, MissionType.MONTHLY];
    if (!validClaimTypes.includes(claimType as any)) {
      return res.status(400).json({
        success: false,
        message: `claimType must be one of: ${validClaimTypes.join(', ')}`,
      });
    }
    queryParams.claimType = claimType as any;
  }

  if (req.query['minReward']) {
    const minReward = parseInt(req.query['minReward'] as string);
    if (isNaN(minReward)) {
      return res.status(400).json({
        success: false,
        message: 'minReward must be a number',
      });
    }
    queryParams.minReward = minReward;
  }

  if (req.query['maxReward']) {
    const maxReward = parseInt(req.query['maxReward'] as string);
    if (isNaN(maxReward)) {
      return res.status(400).json({
        success: false,
        message: 'maxReward must be a number',
      });
    }
    queryParams.maxReward = maxReward;
  }

  // Date filters
  if (req.query['createdAtFrom']) {
    const createdAtFrom = new Date(req.query['createdAtFrom'] as string);
    if (isNaN(createdAtFrom.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'createdAtFrom must be a valid date',
      });
    }
    queryParams.createdAtFrom = createdAtFrom;
  }

  if (req.query['createdAtTo']) {
    const createdAtTo = new Date(req.query['createdAtTo'] as string);
    if (isNaN(createdAtTo.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'createdAtTo must be a valid date',
      });
    }
    queryParams.createdAtTo = createdAtTo;
  }

  try {
    const result = await finalMissionClaimService.findFinalMissionClaimsWithQuery(queryParams);

    return res.status(200).json({
      success: true,
      message: 'Final mission claims retrieved successfully',
      data: result.finalMissionClaims,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1,
      },
    });
  } catch (error) {
    console.error('Error retrieving final mission claims:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export const getFinalMissionClaimById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] as string);

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: 'Valid final mission claim ID is required',
    });
  }

  try {
    const finalMissionClaim = await finalMissionClaimService.findFinalMissionClaimById(id);

    if (!finalMissionClaim) {
      return res.status(404).json({
        success: false,
        message: 'Final mission claim not found',
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Final mission claim retrieved successfully',
      data: finalMissionClaim,
    });
  } catch (error) {
    console.error('Error retrieving final mission claim:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export const updateFinalMissionClaim = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] as string);

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: 'Valid final mission claim ID is required',
    });
  }

  // Check if final mission claim exists
  const existingFinalMissionClaim = await finalMissionClaimService.findFinalMissionClaimById(id);
  if (!existingFinalMissionClaim) {
    return res.status(404).json({
      success: false,
      message: 'Final mission claim not found',
    });
  }

  const { userId, claimType, grantedReward } = req.body;

  // Validate only provided fields (partial update)
  const updateData: Partial<CreateFinalMissionClaimDto> = {};

  if (userId !== undefined) {
    if (typeof userId !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'User ID must be a number',
      });
    }

    // Verify that the user exists
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    updateData.userId = userId;
  }

  if (claimType !== undefined) {
    const validClaimTypes = [MissionType.DAILY, MissionType.WEEKLY, MissionType.MONTHLY];
    if (!validClaimTypes.includes(claimType)) {
      return res.status(400).json({
        success: false,
        message: `Claim type must be one of: ${validClaimTypes.join(', ')}`,
      });
    }
    updateData.claimType = claimType;
  }

  if (grantedReward !== undefined) {
    if (typeof grantedReward !== 'number' || grantedReward <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Granted reward must be a positive number',
      });
    }
    updateData.grantedReward = grantedReward;
  }

  try {
    const updatedFinalMissionClaim = await finalMissionClaimService.updateFinalMissionClaim(id, updateData);

    return res.status(200).json({
      success: true,
      message: 'Final mission claim updated successfully',
      data: updatedFinalMissionClaim,
    });
  } catch (error) {
    console.error('Error updating final mission claim:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export const deleteFinalMissionClaim = asyncHandler(async (req: Request, res: Response) => {
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] as string);

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: 'Valid final mission claim ID is required',
    });
  }

  try {
    // Check if final mission claim exists
    const existingFinalMissionClaim = await finalMissionClaimService.findFinalMissionClaimById(id);
    if (!existingFinalMissionClaim) {
      return res.status(404).json({
        success: false,
        message: 'Final mission claim not found',
      });
    }

    const deleted = await finalMissionClaimService.deleteFinalMissionClaim(id);

    if (!deleted) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete final mission claim',
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Final mission claim deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting final mission claim:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export const getFinalMissionClaimStatistics = asyncHandler(async (req: Request, res: Response) => {
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  try {
    const statistics = await finalMissionClaimService.getClaimStatistics();

    return res.status(200).json({
      success: true,
      message: 'Final mission claim statistics retrieved successfully',
      data: statistics,
    });
  } catch (error) {
    console.error('Error retrieving final mission claim statistics:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

/**
 * Public endpoint to get user's latest final mission claims
 * GET /api/v1/makroz/final-mission-claims/me
 */
export const getUserLatestFinalMissionClaims = asyncHandler(async (req: Request, res: Response) => {
  console.log('🎯 Getting user latest final mission claims');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from token
  const externalId = extractUserIdFromToken(authHeader);
  if (!externalId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log('Extracted external ID from token:', externalId);

  try {
    // Verify that the user exists
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(externalId);
    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    // Get latest claims for each type (daily, weekly, monthly)
    const [dailyClaim, weeklyClaim, monthlyClaim] = await Promise.all([
      finalMissionClaimService.findLatestClaimByUserAndType(externalId, MissionType.DAILY),
      finalMissionClaimService.findLatestClaimByUserAndType(externalId, MissionType.WEEKLY),
      finalMissionClaimService.findLatestClaimByUserAndType(externalId, MissionType.MONTHLY),
    ]);

    console.log('Latest claims found:', {
      daily: dailyClaim ? `ID: ${dailyClaim.id}, Reward: ${dailyClaim.grantedReward}, Date: ${dailyClaim.createdAt}` : 'None',
      weekly: weeklyClaim ? `ID: ${weeklyClaim.id}, Reward: ${weeklyClaim.grantedReward}, Date: ${weeklyClaim.createdAt}` : 'None',
      monthly: monthlyClaim ? `ID: ${monthlyClaim.id}, Reward: ${monthlyClaim.grantedReward}, Date: ${monthlyClaim.createdAt}` : 'None',
    });

    return res.status(200).json({
      success: true,
      message: 'Latest final mission claims retrieved successfully',
      data: {
        daily: dailyClaim || null,
        weekly: weeklyClaim || null,
        monthly: monthlyClaim || null,
      },
    });
  } catch (error) {
    console.error('Error getting user latest final mission claims:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});
