import { Request, Response } from 'express';
import { MissionObjectiveService, CreateMissionObjectiveDto, MissionObjectiveQueryParams } from '@/services/missionObjective.service';
import { MissionService } from '@/services/mission.service';
import { asyncHandler } from '@/utils/asyncHandler';
import { ObjectiveType, CompareOperator } from '@/enums/shared';

const missionObjectiveService = new MissionObjectiveService();
const missionService = new MissionService();

export const createMissionObjective = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { 
    missionId, 
    objectiveType, 
    subtype, 
    operator, 
    targetValue, 
    description, 
    timeframeStart, 
    timeframeEnd, 
    metadata 
  }: CreateMissionObjectiveDto = req.body;

  // Validation
  if (!missionId || typeof missionId !== 'number' || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Mission ID is required and must be a positive number',
    });
  }

  if (!Object.values(ObjectiveType).includes(objectiveType)) {
    return res.status(400).json({
      success: false,
      message: `Objective type must be one of: ${Object.values(ObjectiveType).join(', ')}`,
    });
  }

  if (!Object.values(CompareOperator).includes(operator)) {
    return res.status(400).json({
      success: false,
      message: `Operator must be one of: ${Object.values(CompareOperator).join(', ')}`,
    });
  }

  if (!targetValue || typeof targetValue !== 'string' || targetValue.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Target value is required and must be a non-empty string',
    });
  }

  // Validate timeframe if provided
  if (timeframeStart !== null && timeframeStart !== undefined) {
    if (typeof timeframeStart !== 'number' || timeframeStart < 0) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe start must be a non-negative number',
      });
    }
  }

  if (timeframeEnd !== null && timeframeEnd !== undefined) {
    if (typeof timeframeEnd !== 'number' || timeframeEnd < 0) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe end must be a non-negative number',
      });
    }
  }

  if (timeframeStart && timeframeEnd && timeframeEnd <= timeframeStart) {
    return res.status(400).json({
      success: false,
      message: 'Timeframe end must be after timeframe start',
    });
  }

  try {
    // Validate foreign key existence - Check if mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(400).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    const missionObjective = await missionObjectiveService.createMissionObjective({
      missionId,
      objectiveType,
      subtype: subtype || null,
      operator,
      targetValue: targetValue.trim(),
      description: description?.trim() || null,
      timeframeStart: timeframeStart || null,
      timeframeEnd: timeframeEnd || null,
      metadata: metadata || null,
    });

    res.status(201).json({
      success: true,
      message: 'Mission objective created successfully',
      data: missionObjective,
    });
  } catch (error) {
    console.error('Error creating mission objective:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mission objective',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionObjectives = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching mission objectives list with query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Parse and validate query parameters
  const queryParams: MissionObjectiveQueryParams = {};

  // Pagination
  if (req.query['page']) {
    const page = parseInt(req.query['page'] as string);
    if (isNaN(page) || page < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page must be a positive integer',
      });
    }
    queryParams.page = page;
  }

  if (req.query['limit']) {
    const limit = parseInt(req.query['limit'] as string);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
      });
    }
    queryParams.limit = limit;
  }

  // Sorting
  if (req.query['sortBy']) {
    const sortBy = req.query['sortBy'] as string;
    const validSortFields = ['id', 'missionId', 'objectiveType', 'operator', 'targetValue', 'createdAt', 'updatedAt'];
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({
        success: false,
        message: `Sort field must be one of: ${validSortFields.join(', ')}`,
      });
    }
    queryParams.sortBy = sortBy as any;
  }

  if (req.query['sortOrder']) {
    const sortOrder = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC'].includes(sortOrder.toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'Sort order must be ASC or DESC',
      });
    }
    queryParams.sortOrder = sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filters
  if (req.query['missionId']) {
    const missionId = parseInt(req.query['missionId'] as string);
    if (isNaN(missionId) || missionId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Mission ID must be a positive integer',
      });
    }
    queryParams.missionId = missionId;
  }

  if (req.query['objectiveType']) {
    const objectiveType = req.query['objectiveType'] as string;
    if (!Object.values(ObjectiveType).includes(objectiveType as ObjectiveType)) {
      return res.status(400).json({
        success: false,
        message: `Objective type must be one of: ${Object.values(ObjectiveType).join(', ')}`,
      });
    }
    queryParams.objectiveType = objectiveType as ObjectiveType;
  }

  if (req.query['operator']) {
    const operator = req.query['operator'] as string;
    if (!Object.values(CompareOperator).includes(operator as CompareOperator)) {
      return res.status(400).json({
        success: false,
        message: `Operator must be one of: ${Object.values(CompareOperator).join(', ')}`,
      });
    }
    queryParams.operator = operator as CompareOperator;
  }

  // String filters
  if (req.query['subtype']) {
    queryParams.subtype = req.query['subtype'] as string;
  }

  if (req.query['targetValue']) {
    queryParams.targetValue = req.query['targetValue'] as string;
  }

  if (req.query['description']) {
    queryParams.description = req.query['description'] as string;
  }

  if (req.query['search']) {
    queryParams.search = req.query['search'] as string;
  }

  // Timeframe filters
  if (req.query['timeframeStartFrom']) {
    const timeframeStartFrom = parseInt(req.query['timeframeStartFrom'] as string);
    if (isNaN(timeframeStartFrom) || timeframeStartFrom < 0) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe start from must be a non-negative integer',
      });
    }
    queryParams.timeframeStartFrom = timeframeStartFrom;
  }

  if (req.query['timeframeStartTo']) {
    const timeframeStartTo = parseInt(req.query['timeframeStartTo'] as string);
    if (isNaN(timeframeStartTo) || timeframeStartTo < 0) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe start to must be a non-negative integer',
      });
    }
    queryParams.timeframeStartTo = timeframeStartTo;
  }

  if (req.query['timeframeEndFrom']) {
    const timeframeEndFrom = parseInt(req.query['timeframeEndFrom'] as string);
    if (isNaN(timeframeEndFrom) || timeframeEndFrom < 0) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe end from must be a non-negative integer',
      });
    }
    queryParams.timeframeEndFrom = timeframeEndFrom;
  }

  if (req.query['timeframeEndTo']) {
    const timeframeEndTo = parseInt(req.query['timeframeEndTo'] as string);
    if (isNaN(timeframeEndTo) || timeframeEndTo < 0) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe end to must be a non-negative integer',
      });
    }
    queryParams.timeframeEndTo = timeframeEndTo;
  }

  try {
    const result = await missionObjectiveService.findMissionObjectivesWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Mission objectives retrieved successfully',
      data: result.missionObjectives,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching mission objectives:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch mission objectives',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionObjectiveById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching mission objective by ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective ID is required',
    });
  }

  try {
    const missionObjective = await missionObjectiveService.findMissionObjectiveById(id);

    if (!missionObjective) {
      return res.status(404).json({
        success: false,
        message: 'Mission objective not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission objective retrieved successfully',
      data: missionObjective,
    });
  } catch (error) {
    console.error('Error fetching mission objective:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch mission objective',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateMissionObjective = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating mission objective ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective ID is required',
    });
  }

  // Check if mission objective exists
  const existingMissionObjective = await missionObjectiveService.findMissionObjectiveById(id);
  if (!existingMissionObjective) {
    return res.status(404).json({
      success: false,
      message: 'Mission objective not found',
    });
  }

  const {
    missionId,
    objectiveType,
    subtype,
    operator,
    targetValue,
    description,
    timeframeStart,
    timeframeEnd,
    metadata
  } = req.body;

  // Validate provided fields
  const updateData: Partial<CreateMissionObjectiveDto> = {};

  if (missionId !== undefined) {
    if (typeof missionId !== 'number' || missionId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Mission ID must be a positive number',
      });
    }
    updateData.missionId = missionId;
  }

  if (objectiveType !== undefined) {
    if (!Object.values(ObjectiveType).includes(objectiveType)) {
      return res.status(400).json({
        success: false,
        message: `Objective type must be one of: ${Object.values(ObjectiveType).join(', ')}`,
      });
    }
    updateData.objectiveType = objectiveType;
  }

  if (operator !== undefined) {
    if (!Object.values(CompareOperator).includes(operator)) {
      return res.status(400).json({
        success: false,
        message: `Operator must be one of: ${Object.values(CompareOperator).join(', ')}`,
      });
    }
    updateData.operator = operator;
  }

  if (targetValue !== undefined) {
    if (typeof targetValue !== 'string' || targetValue.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Target value must be a non-empty string',
      });
    }
    updateData.targetValue = targetValue.trim();
  }

  if (subtype !== undefined) {
    updateData.subtype = subtype || null;
  }

  if (description !== undefined) {
    updateData.description = description?.trim() || null;
  }

  if (timeframeStart !== undefined) {
    if (timeframeStart !== null && (typeof timeframeStart !== 'number' || timeframeStart < 0)) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe start must be null or a non-negative number',
      });
    }
    updateData.timeframeStart = timeframeStart;
  }

  if (timeframeEnd !== undefined) {
    if (timeframeEnd !== null && (typeof timeframeEnd !== 'number' || timeframeEnd < 0)) {
      return res.status(400).json({
        success: false,
        message: 'Timeframe end must be null or a non-negative number',
      });
    }
    updateData.timeframeEnd = timeframeEnd;
  }

  if (metadata !== undefined) {
    updateData.metadata = metadata;
  }

  // Validate timeframe relationship if both are being updated
  const finalTimeframeStart = updateData.timeframeStart !== undefined ? updateData.timeframeStart : existingMissionObjective.timeframeStart;
  const finalTimeframeEnd = updateData.timeframeEnd !== undefined ? updateData.timeframeEnd : existingMissionObjective.timeframeEnd;

  if (finalTimeframeStart && finalTimeframeEnd && finalTimeframeEnd <= finalTimeframeStart) {
    return res.status(400).json({
      success: false,
      message: 'Timeframe end must be after timeframe start',
    });
  }

  try {
    const updatedMissionObjective = await missionObjectiveService.updateMissionObjective(id, updateData);

    res.status(200).json({
      success: true,
      message: 'Mission objective updated successfully',
      data: updatedMissionObjective,
    });
  } catch (error) {
    console.error('Error updating mission objective:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mission objective',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteMissionObjective = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting mission objective ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective ID is required',
    });
  }

  // Check if mission objective exists
  const existingMissionObjective = await missionObjectiveService.findMissionObjectiveById(id);
  if (!existingMissionObjective) {
    return res.status(404).json({
      success: false,
      message: 'Mission objective not found',
    });
  }

  try {
    const deleted = await missionObjectiveService.deleteMissionObjective(id);

    if (!deleted) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete mission objective',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission objective deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting mission objective:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mission objective',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get all objectives for a specific mission
export const getMissionObjectivesByMissionId = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting mission objectives for mission ID:', req.params['missionId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    // Validate that mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(404).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Get all objectives for this mission
    const objectives = await missionObjectiveService.findMissionObjectivesByMissionId(missionId);

    res.status(200).json({
      success: true,
      message: 'Mission objectives retrieved successfully',
      data: {
        missionId,
        mission: existingMission,
        objectives,
        totalObjectives: objectives.length,
      },
    });
  } catch (error) {
    console.error('Error retrieving mission objectives:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission objectives',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
