import { Request, Response } from 'express';
import { ExtendedUserService, CreateExtendedUserDto, ExtendedUserQueryParams, BulkUpdateExtendedUserDto } from '@/services/extendedUser.service';
import { MissionParticipationService } from '@/services/missionParticipation.service';
import { MissionService } from '@/services/mission.service';
import { MissionType } from '@/enums/shared';
import { asyncHandler } from '@/utils/asyncHandler';
import { extractUserIdFromToken, validateJwtToken } from '@/utils/jwtUtils';
import { adminEbetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { CustomerShowRequest } from '@/network/ebetlab/requests/customer/CustomerShowRequest';

const extendedUserService = new ExtendedUserService();
const missionParticipationService = new MissionParticipationService();
const missionService = new MissionService();

// Helper function to fetch customer data using the dedicated admin client
const fetchCustomerData = async (customerId: number): Promise<string> => {
  try {
    console.log(`� Fetching username from EbetLab for customer ID: ${customerId}`);

    const customerRequest = new CustomerShowRequest({
      id: customerId.toString(),
    });

    const result = await adminEbetlabApiClient.makeAuthenticatedRequest(customerRequest);

    if (!result.success || !result.data) {
      throw new Error('Unable to fetch customer data for username sync');
    }

    const customerData = result.data;
    if (customerData?.username) {
      const username = customerData.username;
      console.log(`✅ Fetched username from EbetLab: ${username}`);
      return username;
    } else {
      console.warn('⚠️ Username not found in customer data');
      return '';
    }
  } catch (error) {
    console.warn('⚠️ Error fetching username from EbetLab:', error);
    return '';
  }
};

export const createExtendedUser = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { externalId, points, externalUsername }: CreateExtendedUserDto = req.body;

  // Validation
  if (!externalId || typeof externalId !== 'number' || externalId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'External ID is required and must be a positive number',
    });
  }

  if (points !== undefined && (typeof points !== 'number' || points < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Points must be a non-negative number',
    });
  }

  if (externalUsername !== undefined && (typeof externalUsername !== 'string' || externalUsername.length > 32)) {
    return res.status(400).json({
      success: false,
      message: 'External username must be a string with maximum 32 characters',
    });
  }

  try {
    // Always fetch username from EbetLab API to ensure it's up to date
    let finalUsername = externalUsername || '';

    // Fetch username from EbetLab using the dedicated admin client
    const fetchedUsername = await fetchCustomerData(externalId);
    if (fetchedUsername) {
      finalUsername = fetchedUsername;
    } else if (!externalUsername) {
      // If we couldn't fetch from EbetLab and no username was provided, use empty string
      finalUsername = '';
    }

    // Check if externalId already exists
    const existingUser = await extendedUserService.findExtendedUserByExternalId(externalId);
    if (existingUser) {
      // Update existing user's username to keep it in sync with EbetLab
      console.log(`👤 Extended user already exists, updating username from "${existingUser.externalUsername}" to "${finalUsername}"`);

      const updatedUser = await extendedUserService.updateExtendedUser(existingUser.id, {
        externalUsername: finalUsername,
        // Also update points if provided
        ...(points !== undefined && { points }),
      });

      return res.status(200).json({
        success: true,
        message: 'Extended user already exists, username updated to sync with EbetLab',
        data: updatedUser,
      });
    }

    // Create new extended user
    const extendedUser = await extendedUserService.createExtendedUser({
      externalId,
      points: points || 0,
      externalUsername: finalUsername,
    });

    res.status(201).json({
      success: true,
      message: 'Extended user created successfully',
      data: extendedUser,
    });
  } catch (error) {
    console.error('Error creating extended user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getExtendedUsers = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching extended users list with query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Parse and validate query parameters
  const queryParams: ExtendedUserQueryParams = {};

  // Pagination
  if (req.query['page']) {
    const page = parseInt(req.query['page'] as string);
    if (isNaN(page) || page < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page must be a positive integer',
      });
    }
    queryParams.page = page;
  }

  if (req.query['limit']) {
    const limit = parseInt(req.query['limit'] as string);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
      });
    }
    queryParams.limit = limit;
  }

  // Sorting
  if (req.query['sortBy']) {
    const validSortFields = ['id', 'externalId', 'points', 'externalUsername', 'createdAt', 'updatedAt'];
    const sortBy = req.query['sortBy'] as string;
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({
        success: false,
        message: `sortBy must be one of: ${validSortFields.join(', ')}`,
      });
    }
    queryParams.sortBy = sortBy as any;
  }

  if (req.query['sortOrder']) {
    const sortOrder = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC', 'asc', 'desc'].includes(sortOrder)) {
      return res.status(400).json({
        success: false,
        message: 'sortOrder must be either "ASC", "DESC", "asc", or "desc"',
      });
    }
    queryParams.sortOrder = sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filters
  if (req.query['externalId']) {
    const externalId = parseInt(req.query['externalId'] as string);
    if (isNaN(externalId) || externalId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'External ID must be a positive integer',
      });
    }
    queryParams.externalId = externalId;
  }

  if (req.query['points']) {
    const points = parseInt(req.query['points'] as string);
    if (isNaN(points) || points < 0) {
      return res.status(400).json({
        success: false,
        message: 'Points must be a non-negative integer',
      });
    }
    queryParams.points = points;
  }

  if (req.query['minPoints']) {
    const minPoints = parseInt(req.query['minPoints'] as string);
    if (isNaN(minPoints) || minPoints < 0) {
      return res.status(400).json({
        success: false,
        message: 'minPoints must be a non-negative integer',
      });
    }
    queryParams.minPoints = minPoints;
  }

  if (req.query['maxPoints']) {
    const maxPoints = parseInt(req.query['maxPoints'] as string);
    if (isNaN(maxPoints) || maxPoints < 0) {
      return res.status(400).json({
        success: false,
        message: 'maxPoints must be a non-negative integer',
      });
    }
    queryParams.maxPoints = maxPoints;
  }

  if (req.query['externalUsername']) {
    queryParams.externalUsername = req.query['externalUsername'] as string;
  }

  if (req.query['search']) {
    queryParams.search = req.query['search'] as string;
  }

  try {
    const result = await extendedUserService.findExtendedUsersWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Extended users retrieved successfully',
      data: result.extendedUsers,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching extended users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch extended users',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getExtendedUserById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching extended user by ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid extended user ID is required',
    });
  }

  try {
    const extendedUser = await extendedUserService.findExtendedUserById(id);

    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Extended user retrieved successfully',
      data: extendedUser,
    });
  } catch (error) {
    console.error('Error fetching extended user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateExtendedUser = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating extended user ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid extended user ID is required',
    });
  }

  // Check if extended user exists
  const existingExtendedUser = await extendedUserService.findExtendedUserById(id);
  if (!existingExtendedUser) {
    return res.status(404).json({
      success: false,
      message: 'Extended user not found',
    });
  }

  const { externalId, points, externalUsername } = req.body;

  // Validate only provided fields (partial update)
  const updateData: Partial<CreateExtendedUserDto> = {};

  if (externalId !== undefined) {
    if (typeof externalId !== 'number' || externalId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'External ID must be a positive number',
      });
    }

    // Check if new externalId already exists (but not for current user)
    const existingUserWithExternalId = await extendedUserService.findExtendedUserByExternalId(externalId);
    if (existingUserWithExternalId && existingUserWithExternalId.id !== id) {
      return res.status(409).json({
        success: false,
        message: 'Extended user with this external ID already exists',
      });
    }

    updateData.externalId = externalId;
  }

  if (points !== undefined) {
    if (typeof points !== 'number' || points < 0) {
      return res.status(400).json({
        success: false,
        message: 'Points must be a non-negative number',
      });
    }
    updateData.points = points;
  }

  if (externalUsername !== undefined) {
    if (typeof externalUsername !== 'string' || externalUsername.length > 32) {
      return res.status(400).json({
        success: false,
        message: 'External username must be a string with maximum 32 characters',
      });
    }
    updateData.externalUsername = externalUsername;
  }

  // Always fetch and update username from EbetLab to keep it in sync
  const targetExternalId = externalId || existingExtendedUser.externalId;

  // Fetch username from EbetLab using the dedicated admin client
  const fetchedUsername = await fetchCustomerData(targetExternalId);
  if (fetchedUsername) {
    console.log(`✅ Fetched username from EbetLab: ${fetchedUsername}`);

    // Always update username to keep it in sync, unless explicitly provided
    if (externalUsername === undefined) {
      updateData.externalUsername = fetchedUsername;
      console.log(`📝 Auto-updating username to: ${fetchedUsername}`);
    }
  } else {
    console.warn('⚠️ Username not found in customer data');
  }

  try {
    // If points are being updated, we need to handle it with proper transaction logging
    if (updateData.points !== undefined) {
      const currentPoints = existingExtendedUser.points;
      const newPoints = updateData.points;
      const pointsDifference = newPoints - currentPoints;

      // Update the user with transaction logging for point changes
      const updatedExtendedUser = await extendedUserService.updateExtendedUserWithTransaction(
        id,
        updateData,
        pointsDifference,
        {
          adminUserId: req.adminUser?.id || null,
          adminEmail: req.adminUser?.email || 'Unknown',
          reason: 'Admin point adjustment',
          previousPoints: currentPoints,
          newPoints: newPoints,
        }
      );

      if (!updatedExtendedUser) {
        return res.status(404).json({
          success: false,
          message: 'Extended user not found after update',
        });
      }

      res.status(200).json({
        success: true,
        message: 'Extended user updated successfully',
        data: updatedExtendedUser,
      });
    } else {
      // No points change, use regular update
      const updatedExtendedUser = await extendedUserService.updateExtendedUser(id, updateData);

      if (!updatedExtendedUser) {
        return res.status(404).json({
          success: false,
          message: 'Extended user not found after update',
        });
      }

      res.status(200).json({
        success: true,
        message: 'Extended user updated successfully',
        data: updatedExtendedUser,
      });
    }
  } catch (error) {
    console.error('Error updating extended user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteExtendedUser = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting extended user ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid extended user ID is required',
    });
  }

  try {
    // Check if extended user exists before deletion
    const existingExtendedUser = await extendedUserService.findExtendedUserById(id);
    if (!existingExtendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    const deleted = await extendedUserService.deleteExtendedUser(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Extended user deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting extended user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const bulkUpdateExtendedUsers = asyncHandler(async (req: Request, res: Response) => {
  console.log('Bulk updating extended users');
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { updates }: { updates: BulkUpdateExtendedUserDto[] } = req.body;

  // Validation
  if (!updates || !Array.isArray(updates)) {
    return res.status(400).json({
      success: false,
      message: 'Updates array is required',
    });
  }

  if (updates.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Updates array cannot be empty',
    });
  }

  if (updates.length > 100) {
    return res.status(400).json({
      success: false,
      message: 'Cannot update more than 100 users at once',
    });
  }

  // Validate each update object
  for (let i = 0; i < updates.length; i++) {
    const update = updates[i];

    if (!update.id || typeof update.id !== 'number' || update.id <= 0) {
      return res.status(400).json({
        success: false,
        message: `Invalid ID at index ${i}: ID must be a positive number`,
      });
    }

    if (update.externalId !== undefined && (typeof update.externalId !== 'number' || update.externalId <= 0)) {
      return res.status(400).json({
        success: false,
        message: `Invalid externalId at index ${i}: External ID must be a positive number`,
      });
    }

    if (update.points !== undefined && (typeof update.points !== 'number' || update.points < 0)) {
      return res.status(400).json({
        success: false,
        message: `Invalid points at index ${i}: Points must be a non-negative number`,
      });
    }

    // Ensure at least one field is being updated
    if (update.externalId === undefined && update.points === undefined) {
      return res.status(400).json({
        success: false,
        message: `No fields to update at index ${i}: At least one of externalId or points must be provided`,
      });
    }
  }

  try {
    const result = await extendedUserService.bulkUpdateExtendedUsers(updates);

    // Determine response status based on results
    let statusCode = 200;
    if (result.summary.failureCount > 0 && result.summary.successCount === 0) {
      statusCode = 400; // All failed
    } else if (result.summary.failureCount > 0) {
      statusCode = 207; // Partial success (Multi-Status)
    }

    res.status(statusCode).json({
      success: result.summary.successCount > 0,
      message: `Bulk update completed: ${result.summary.successCount} successful, ${result.summary.failureCount} failed`,
      data: {
        updated: result.updated,
        failed: result.failed,
        summary: result.summary,
      },
    });
  } catch (error) {
    console.error('Error in bulk update:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk update',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint for creating extended user by JWT token
export const createExtendedUserByToken = asyncHandler(async (req: Request, res: Response) => {
  console.log('Creating extended user by token');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract external ID from JWT token
  const externalId = extractUserIdFromToken(authHeader);
  if (!externalId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log('Extracted external ID from token:', externalId);

  try {
    // Fetch user information from EbetLab to get username using admin credentials
    const username = await fetchCustomerData(externalId);

    // Check if extended user already exists
    const existingUser = await extendedUserService.findExtendedUserByExternalId(externalId);

    if (existingUser) {
      console.log('Extended user already exists, updating username...');

      // Always update username to ensure it's in sync with EbetLab
      if (username) {
        console.log(`Updating username from "${existingUser.externalUsername}" to "${username}"`);
        const updatedUser = await extendedUserService.updateExtendedUser(existingUser.id, {
          externalUsername: username,
        });

        return res.status(200).json({
          success: true,
          message: 'Extended user exists, username updated',
          data: updatedUser,
        });
      } else {
        // If we couldn't fetch username from EbetLab, return existing user as-is
        console.log('No username fetched from EbetLab, returning existing user');
        return res.status(200).json({
          success: true,
          message: 'Extended user already exists',
          data: existingUser,
        });
      }
    }

    // Create new extended user with 0 points and username
    const extendedUser = await extendedUserService.createExtendedUser({
      externalId,
      points: 0,
      externalUsername: username,
    });

    console.log('Created new extended user:', extendedUser);

    res.status(201).json({
      success: true,
      message: 'Extended user created successfully',
      data: extendedUser,
    });
  } catch (error) {
    console.error('Error creating extended user by token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint for getting extended user by JWT token
export const getExtendedUserByToken = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting extended user by token');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract external ID from JWT token
  const externalId = extractUserIdFromToken(authHeader);
  if (!externalId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log('Extracted external ID from token:', externalId);

  try {
    // Find extended user by external ID
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(externalId);

    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    console.log('Found extended user:', extendedUser);

    res.status(200).json({
      success: true,
      message: 'Extended user retrieved successfully',
      data: extendedUser,
    });
  } catch (error) {
    console.error('Error getting extended user by token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint for getting extended user by external ID
export const getExtendedUserByExternalId = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting extended user by external ID');

  const externalId = parseInt(req.params['externalId'] || '0');

  if (!externalId || isNaN(externalId) || externalId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid external ID is required',
    });
  }

  console.log('Looking for external ID:', externalId);

  try {
    // Find extended user by external ID
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(externalId);

    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    console.log('Found extended user:', extendedUser);

    res.status(200).json({
      success: true,
      message: 'Extended user retrieved successfully',
      data: extendedUser,
    });
  } catch (error) {
    console.error('Error getting extended user by external ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve extended user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint for getting user mission statistics by JWT token
export const getUserMissionStats = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting user mission statistics');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from token
  const userId = extractUserIdFromToken(authHeader);
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log('Extracted user ID from token:', userId);

  try {
    // Get current timestamp for active mission filtering
    const currentTimestamp = Math.floor(Date.now() / 1000);

    // Get extended user to retrieve balance (points)
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(userId);
    const userBalance = extendedUser ? extendedUser.points : 0;

    // Get all user's mission participations
    const userParticipations = await missionParticipationService.findParticipationsByUserId(userId);
    const totalParticipations = userParticipations.length;
    const completedParticipations = userParticipations.filter(p => p.isCompleted).length;

    // Get all active missions
    const activeMissions = await missionService.findActiveMissions(currentTimestamp);

    // Group active missions by type
    const missionsByType = {
      [MissionType.DAILY]: activeMissions.filter(m => m.missionType === MissionType.DAILY),
      [MissionType.WEEKLY]: activeMissions.filter(m => m.missionType === MissionType.WEEKLY),
      [MissionType.MONTHLY]: activeMissions.filter(m => m.missionType === MissionType.MONTHLY),
      [MissionType.CUSTOM]: activeMissions.filter(m => m.missionType === MissionType.CUSTOM),
    };

    // Calculate eligible missions per type (missions user hasn't participated in yet)
    const participatedMissionIds = new Set(userParticipations.map(p => p.missionId));

    const eligibleMissionsByType = {
      [MissionType.DAILY]: missionsByType[MissionType.DAILY].filter(m => !participatedMissionIds.has(m.id)),
      [MissionType.WEEKLY]: missionsByType[MissionType.WEEKLY].filter(m => !participatedMissionIds.has(m.id)),
      [MissionType.MONTHLY]: missionsByType[MissionType.MONTHLY].filter(m => !participatedMissionIds.has(m.id)),
      [MissionType.CUSTOM]: missionsByType[MissionType.CUSTOM].filter(m => !participatedMissionIds.has(m.id)),
    };

    // Calculate completed eligible missions per type (missions user has participated in and completed)
    const completedEligibleMissionsByType = {
      [MissionType.DAILY]: userParticipations.filter(p =>
        p.isCompleted &&
        missionsByType[MissionType.DAILY].some(m => m.id === p.missionId)
      ),
      [MissionType.WEEKLY]: userParticipations.filter(p =>
        p.isCompleted &&
        missionsByType[MissionType.WEEKLY].some(m => m.id === p.missionId)
      ),
      [MissionType.MONTHLY]: userParticipations.filter(p =>
        p.isCompleted &&
        missionsByType[MissionType.MONTHLY].some(m => m.id === p.missionId)
      ),
      [MissionType.CUSTOM]: userParticipations.filter(p =>
        p.isCompleted &&
        missionsByType[MissionType.CUSTOM].some(m => m.id === p.missionId)
      ),
    };

    const stats = {
      balance: userBalance,
      totalParticipations,
      completedParticipations,
      eligibleMissions: {
        [MissionType.DAILY]: eligibleMissionsByType[MissionType.DAILY].length,
        [MissionType.WEEKLY]: eligibleMissionsByType[MissionType.WEEKLY].length,
        [MissionType.MONTHLY]: eligibleMissionsByType[MissionType.MONTHLY].length,
        [MissionType.CUSTOM]: eligibleMissionsByType[MissionType.CUSTOM].length,
      },
      completedEligibleMissions: {
        [MissionType.DAILY]: completedEligibleMissionsByType[MissionType.DAILY].length,
        [MissionType.WEEKLY]: completedEligibleMissionsByType[MissionType.WEEKLY].length,
        [MissionType.MONTHLY]: completedEligibleMissionsByType[MissionType.MONTHLY].length,
        [MissionType.CUSTOM]: completedEligibleMissionsByType[MissionType.CUSTOM].length,
      },
    };

    console.log('User mission statistics:', stats);

    res.status(200).json({
      success: true,
      message: 'User mission statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    console.error('Error getting user mission statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user mission statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getExtendedUserPointsStats = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching extended user points statistics');
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  try {
    const stats = await extendedUserService.getPointsStats();

    res.status(200).json({
      success: true,
      message: 'Extended user points statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching extended user points statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch extended user points statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
