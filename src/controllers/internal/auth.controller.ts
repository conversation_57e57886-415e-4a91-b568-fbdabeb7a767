import { Request, Response } from 'express';
import { InternalAuthService, SignInDto, ChangePasswordDto } from '@/services/internal/auth.service';
import { asyncHandler, validateRequiredFields, createResponse } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { ApiResponse } from '@/types/api';

export interface SignInRequest {
  email: string;
  password: string;
  otp: string;
}

export interface SignInResponse {
  user: {
    id: number;
    email: string;
    createdAt: Date;
    updatedAt: Date;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export class InternalAuthController {
  private authService: InternalAuthService;

  constructor() {
    this.authService = new InternalAuthService();
  }

  /**
   * POST /internal/auth/signin
   * Sign in with email, password, and OTP
   */
  signIn = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔐 Internal sign in attempt');

    const { email, password, otp }: SignInRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['email', 'password', 'otp']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError('Invalid email format');
    }

    // Validate OTP format (should be 6 digits)
    if (!/^\d{6}$/.test(otp)) {
      throw new ValidationError('OTP must be a 6-digit number');
    }

    try {
      // Attempt sign in
      const { user, tokens } = await this.authService.signIn({ email, password, otp });

      console.log('✅ Internal sign in successful for:', user.email);

      // Prepare response (exclude sensitive data)
      const response: ApiResponse<SignInResponse> = createResponse(
        true,
        'Sign in successful',
        {
          user: {
            id: user.id,
            email: user.email,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          tokens,
        }
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Internal sign in failed:', error);
      throw error;
    }
  });

  /**
   * POST /internal/auth/change-password
   * Change password for authenticated user
   */
  changePassword = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔐 Internal password change attempt');

    // Check if user is authenticated
    if (!req.internalUser) {
      throw new ValidationError('User not authenticated');
    }

    const { currentPassword, newPassword }: ChangePasswordRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['currentPassword', 'newPassword']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate that new password is different from current
    if (currentPassword === newPassword) {
      throw new ValidationError('New password must be different from current password');
    }

    try {
      // Change password
      await this.authService.changePassword(req.internalUser.id, { currentPassword, newPassword });

      console.log('✅ Internal password change successful for:', req.internalUser.email);

      const response: ApiResponse<{}> = createResponse(
        true,
        'Password changed successfully',
        {}
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Internal password change failed:', error);
      throw error;
    }
  });

  /**
   * POST /internal/auth/refresh
   * Refresh access token using refresh token
   */
  refreshToken = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔄 Internal token refresh attempt');

    const { refreshToken }: RefreshTokenRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['refreshToken']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    try {
      // Refresh tokens
      const tokens = await this.authService.refreshToken(refreshToken);

      console.log('✅ Internal token refresh successful');

      const response: ApiResponse<RefreshTokenResponse> = createResponse(
        true,
        'Token refreshed successfully',
        { tokens }
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Internal token refresh failed:', error);
      throw error;
    }
  });

  /**
   * GET /internal/auth/me
   * Get current authenticated user info
   */
  getCurrentUser = asyncHandler(async (req: Request, res: Response) => {
    console.log('👤 Get current internal user');

    // Check if user is authenticated
    if (!req.internalUser) {
      throw new ValidationError('User not authenticated');
    }

    const response: ApiResponse<{
      id: number;
      email: string;
      createdAt: Date;
      updatedAt: Date;
    }> = createResponse(
      true,
      'User retrieved successfully',
      {
        id: req.internalUser.id,
        email: req.internalUser.email,
        createdAt: req.internalUser.createdAt,
        updatedAt: req.internalUser.updatedAt,
      }
    );

    res.status(200).json(response);
  });
}

// Create singleton instance
export const internalAuthController = new InternalAuthController();
