import { Request, Response } from 'express';
import { InternalUserCredentialsService, CreateUserCredentialsDto } from '@/services/internal/userCredentials.service';
import { asyncHand<PERSON>, validateRequiredFields, createResponse } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { ApiResponse } from '@/types/api';

export interface CreateCredentialsRequest {
  panelName: string;
  login: string;
  password: string;
  otpSecret?: string;
}

export interface CredentialsResponse {
  userId: number;
  panelName: string;
  login: string;
  password: string;
  otpSecret?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GetCredentialsResponse {
  credentials: CredentialsResponse[];
}

export class InternalUserCredentialsController {
  private credentialsService: InternalUserCredentialsService;

  constructor() {
    this.credentialsService = new InternalUserCredentialsService();
  }

  /**
   * GET /internal/users/credentials
   * Retrieve all credential records for the authenticated user
   */
  getCredentials = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔐 Get user credentials attempt');

    // Check if user is authenticated
    if (!req.internalUser) {
      throw new ValidationError('User not authenticated');
    }

    try {
      // Get all credentials for the authenticated user
      const credentials = await this.credentialsService.findCredentialsByUserId(req.internalUser.id);

      console.log(`✅ Retrieved ${credentials.length} credential records for user:`, req.internalUser.email);

      // Prepare response
      const credentialsResponse: CredentialsResponse[] = credentials.map(cred => ({
        userId: cred.userId,
        panelName: cred.panelName,
        login: cred.login,
        password: cred.password,
        otpSecret: cred.otpSecret,
        createdAt: cred.createdAt,
        updatedAt: cred.updatedAt,
      }));

      const response: ApiResponse<GetCredentialsResponse> = createResponse(
        true,
        'Credentials retrieved successfully',
        { credentials: credentialsResponse }
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Get credentials failed:', error);
      throw error;
    }
  });

  /**
   * POST /internal/users/credentials
   * Create or update credentials for a specific panel for the authenticated user
   */
  createOrUpdateCredentials = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔐 Create/update user credentials attempt');

    // Check if user is authenticated
    if (!req.internalUser) {
      throw new ValidationError('User not authenticated');
    }

    const { panelName, login, password, otpSecret }: CreateCredentialsRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['panelName', 'login', 'password']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate panel name format (alphanumeric, hyphens, underscores only)
    if (!/^[a-zA-Z0-9_-]+$/.test(panelName)) {
      throw new ValidationError('Panel name can only contain letters, numbers, hyphens, and underscores');
    }

    // Validate panel name length
    if (panelName.length > 50) {
      throw new ValidationError('Panel name cannot exceed 50 characters');
    }

    // Validate login length
    if (login.length > 255) {
      throw new ValidationError('Login cannot exceed 255 characters');
    }

    // Validate password length
    if (password.length > 255) {
      throw new ValidationError('Password cannot exceed 255 characters');
    }

    // Validate OTP secret if provided
    if (otpSecret && otpSecret.length > 255) {
      throw new ValidationError('OTP secret cannot exceed 255 characters');
    }

    try {
      // Create or update credentials
      const credentials = await this.credentialsService.createOrUpdateCredentials(
        req.internalUser.id,
        { panelName, login, password, otpSecret }
      );

      console.log(`✅ Credentials created/updated for panel "${panelName}" for user:`, req.internalUser.email);

      // Prepare response
      const credentialsResponse: CredentialsResponse = {
        userId: credentials.userId,
        panelName: credentials.panelName,
        login: credentials.login,
        password: credentials.password,
        otpSecret: credentials.otpSecret,
        createdAt: credentials.createdAt,
        updatedAt: credentials.updatedAt,
      };

      const response: ApiResponse<CredentialsResponse> = createResponse(
        true,
        'Credentials saved successfully',
        credentialsResponse
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Create/update credentials failed:', error);
      throw error;
    }
  });
}

// Create singleton instance
export const internalUserCredentialsController = new InternalUserCredentialsController();
