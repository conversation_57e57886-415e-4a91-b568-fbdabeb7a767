import { Request, Response } from 'express';
import { asyncHand<PERSON>, validateRequired<PERSON><PERSON>s, createResponse } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { pgDagurHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { TOTP } from 'totp-generator';

export interface PGDagurLoginRequest {
  username: string;
  password: string;
  otpSecret: string;
}

export interface PGDagurLoginResponse {
  session: any; // The response from PGDagurHttpClient.loginWithOtp()
}

class PGDagurPanelController {
  /**
   * Generate TOTP from secret with 30-second interval
   */
  private generateTOTP(secret: string): string {
    try {
      console.log(`🔐 Generating PG Dagur TOTP from secret: ${secret.substring(0, 4)}...`);

      const otpCode = TOTP.generate(secret, {
        period: 30,
      }).otp;

      console.log(`🔐 PG Dagur OTP Generated: ${otpCode}`);
      return otpCode;
    } catch (error) {
      console.error(`❌ PG Dagur TOTP generation failed:`, error);
      throw error;
    }
  }

  /**
   * POST /internal/panels/pgdagur
   * Login to PG Dagur using provided credentials
   */
  login = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔐 PG Dagur panel login attempt');

    const { username, password, otpSecret }: PGDagurLoginRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['username', 'password', 'otpSecret']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate username format (basic validation)
    if (typeof username !== 'string' || username.trim().length === 0) {
      throw new ValidationError('Username must be a non-empty string');
    }

    // Validate password format (basic validation)
    if (typeof password !== 'string' || password.trim().length === 0) {
      throw new ValidationError('Password must be a non-empty string');
    }

    // Validate otpSecret format (should be a non-empty string)
    if (typeof otpSecret !== 'string' || otpSecret.trim().length === 0) {
      throw new ValidationError('OTP secret must be a non-empty string');
    }

    try {
      console.log('🚀 Attempting PG Dagur login for username:', username);

      // Generate OTP from secret
      const otp = this.generateTOTP(otpSecret);
      console.log(`🔑 Using OTP: ${otp} for PG Dagur login`);

      // Call the PGDagurHttpClient loginWithOtp method
      const session = await pgDagurHttpClient.loginWithOtp(username, password, otp);

      console.log('✅ PG Dagur login successful for username:', username);

      // Prepare response
      const response = createResponse(
        true,
        'PG Dagur login successful',
        {
          session,
        }
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ PG Dagur login failed:', error);

      if (error instanceof Error) {
        // Check if it's a TOTP generation error
        if (error.message.includes('TOTP generation failed')) {
          throw new ValidationError(`TOTP generation failed: ${error.message}`);
        }

        // Check if it's an authentication error from PGDagurHttpClient
        if (
          error.message.includes('Failed to login:') ||
          error.message.includes('Failed to prelogin:') ||
          error.message.includes('Failed to find view state') ||
          error.message.includes('Unauthorized') ||
          error.message.includes('JSESSIONID cookie not found') ||
          error.message.includes('__nxquid cookie not found')
        ) {
          throw new ValidationError(`PG Dagur authentication failed: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}

export const pgDagurPanelController = new PGDagurPanelController();
