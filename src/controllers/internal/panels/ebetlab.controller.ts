import { Request, Response } from 'express';
import { async<PERSON>and<PERSON>, validateRequired<PERSON><PERSON>s, createResponse } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { TOTP } from 'totp-generator';

export interface EbetlabLoginRequest {
  username: string;
  password: string;
  otpSecret: string;
}

export interface EbetlabLoginResponse {
  loginData: any; // The response from EbetlabApiClient.login()
}

class EbetlabPanelController {
  /**
   * Generate TOTP from secret with 30-second interval
   */
  private generateTOTP(secret: string): string {
    try {
      console.log(`🔐 Generating Ebetlab TOTP from secret: ${secret.substring(0, 4)}...`);

      const otpCode = TOTP.generate(secret, {
        period: 30,
      }).otp;

      console.log(`🔐 Ebetlab OTP Generated: ${otpCode}`);
      return otpCode;
    } catch (error) {
      console.error(`❌ Ebetlab TOTP generation failed:`, error);
      throw error;
    }
  }

  /**
   * POST /internal/panels/ebetlab
   * Login to Ebetlab using provided credentials
   */
  login = asyncHandler(async (req: Request, res: Response) => {
    console.log('🔐 Ebetlab panel login attempt');

    const { username, password, otpSecret }: EbetlabLoginRequest = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(req.body, ['username', 'password', 'otpSecret']);
    if (missingFields.length > 0) {
      throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate username format (basic validation)
    if (typeof username !== 'string' || username.trim().length === 0) {
      throw new ValidationError('Username must be a non-empty string');
    }

    // Validate password format (basic validation)
    if (typeof password !== 'string' || password.trim().length === 0) {
      throw new ValidationError('Password must be a non-empty string');
    }

    // Validate otpSecret format (should be a non-empty string)
    if (typeof otpSecret !== 'string' || otpSecret.trim().length === 0) {
      throw new ValidationError('OTP secret must be a non-empty string');
    }

    try {
      console.log('🚀 Attempting Ebetlab login for username:', username);

      // Generate OTP from secret
      const otp = this.generateTOTP(otpSecret);
      console.log(`🔑 Using OTP: ${otp} for Ebetlab login`);

      // Call the EbetlabApiClient login method
      const loginData = await ebetlabApiClient.login(username, password, otp);

      console.log('✅ Ebetlab login successful for username:', username);

      // Prepare response
      const response = createResponse(
        true,
        'Ebetlab login successful',
        {
          loginData,
        }
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Ebetlab login failed:', error);

      if (error instanceof Error) {
        // Check if it's a TOTP generation error
        if (error.message.includes('TOTP generation failed')) {
          throw new ValidationError(`TOTP generation failed: ${error.message}`);
        }

        // Check if it's an authentication error from EbetlabApiClient
        if (
          error.message.includes('Failed to login:') ||
          error.message.includes('Failed to init cf:') ||
          error.message.includes('Failed to get signed cookie:') ||
          error.message.includes('asked for otp again')
        ) {
          throw new ValidationError(`Ebetlab authentication failed: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}

export const ebetlabPanelController = new EbetlabPanelController();
