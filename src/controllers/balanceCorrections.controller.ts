import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { BalanceCorrectionsSummaryRequest, BalanceCorrectionsSummaryRequestOptions } from '@/network/ebetlab/requests/balance-corrections/BalanceCorrectionsSummaryRequest';
import { BalanceCorrectionsListRequest, BalanceCorrectionsListRequestOptions } from '@/network/ebetlab/requests/balance-corrections/BalanceCorrectionsListRequest';

export class BalanceCorrectionsController {
  /**
   * POST /operator/balance-corrections/summary - Get balance corrections summary from EbetLab
   */
  static getBalanceCorrectionsSummary = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('💰 Fetching balance corrections summary');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Extract parameters from request body
      const { username } = req.body;

      // Create request options
      const requestOptions: BalanceCorrectionsSummaryRequestOptions = {
        username: username || null,
      };

      // Create and execute the request
      const request = new BalanceCorrectionsSummaryRequest(requestOptions);
      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        request,
        req.headers['authorization'] || '',
      );

      const response: ApiResponse<typeof result.data> = {
        success: true,
        message: 'Balance corrections summary retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Error fetching balance corrections summary:', error);
      throw error;
    }
  });

  /**
   * POST /operator/balance-corrections/index/:page/:limit - Get balance corrections list from EbetLab
   */
  static getBalanceCorrections = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Extract path parameters
      const { page, limit } = req.params;
      const pageNum = parseInt(page) || 1;
      const limitNum = parseInt(limit) || 20;

      console.log(`💰 Fetching balance corrections - page: ${pageNum}, limit: ${limitNum}`);
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Validate page and limit parameters
      if (pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }
      if (limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be between 1 and 100');
      }

      // Extract filter parameters from request body
      const {
        id,
        username,
        model,
        currency,
        operator_id,
        way,
        note,
        usd_min,
        usd_max,
        from,
        to,
        sortBy,
        direction,
      } = req.body;

      // Create request options
      const requestOptions: BalanceCorrectionsListRequestOptions = {
        page: pageNum,
        limit: limitNum,
        id: id || null,
        username: username || null,
        model: model || null,
        currency: currency || null,
        operator_id: operator_id || null,
        way: way || null,
        note: note || null,
        usd_min: usd_min || null,
        usd_max: usd_max || null,
        from: from || null,
        to: to || null,
        sortBy: sortBy || null,
        direction: direction || null,
      };

      // Create and execute the request
      const request = new BalanceCorrectionsListRequest(requestOptions);
      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        request,
        req.headers['authorization'] || '',
      );

      const response: ApiResponse<typeof result.data> = {
        success: true,
        message: 'Balance corrections retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Error fetching balance corrections:', error);
      throw error;
    }
  });
}
