import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { UserGetAuthorizedRequest } from '@/network/ebetlab/requests/user/UserGetAuthorizedRequest';

export class UserController {
  /**
   * GET /user/authorized - Get authorized user information from EbetLab
   */
  static getAuthorizedUser = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('👤 Fetching authorized user information');

      // Use EbetLab API client singleton and make request
      const userRequest = new UserGetAuthorizedRequest();

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        userRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch authorized user: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Authorized user retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch authorized user:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch authorized user: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
