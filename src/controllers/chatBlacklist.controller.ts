import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { ChatBlacklistListRequest, ChatBlacklistListRequestOptions } from '@/network/ebetlab/requests/chat-blacklist/ChatBlacklistListRequest';

export class ChatBlacklistController {
  /**
   * POST /operator/customers/chat/blacklist/:page/:limit - Get chat blacklisted customers from EbetLab
   */
  static getChatBlacklistedCustomers = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`💬🚫 Fetching chat blacklisted customers - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Prepare request options with all possible filter parameters
      const requestOptions: ChatBlacklistListRequestOptions = {
        page,
        limit,
        id: filters.id || null,
        username: filters.username || null,
        name: filters.name || null,
        verification_level: filters.verification_level || null,
        email: filters.email || null,
        ip: filters.ip || null,
        surname: filters.surname || null,
        status_id: filters.status_id || null,
        operator_id: filters.operator_id || null,
        registration_country: filters.registration_country || null,
        language: filters.language || null,
        rank: filters.rank || null,
        register_start: filters.register_start || null,
        register_end: filters.register_end || null,
        first_deposit_start: filters.first_deposit_start || null,
        first_deposit_end: filters.first_deposit_end || null,
        last_deposit_start: filters.last_deposit_start || null,
        last_deposit_end: filters.last_deposit_end || null,
        last_login_start: filters.last_login_start || null,
        last_login_end: filters.last_login_end || null,
        total_reload_min: filters.total_reload_min || null,
        total_reload_max: filters.total_reload_max || null,
        total_rain_min: filters.total_rain_min || null,
        total_rain_max: filters.total_rain_max || null,
        total_deposit_greater: filters.total_deposit_greater || null,
        total_deposit_lower: filters.total_deposit_lower || null,
        total_withdraw_greater: filters.total_withdraw_greater || null,
        total_withdraw_lower: filters.total_withdraw_lower || null,
        total_bonus_drop_min: filters.total_bonus_drop_min || null,
        total_bonus_drop_max: filters.total_bonus_drop_max || null,
        total_turnover_greater: filters.total_turnover_greater || null,
        total_turnover_lower: filters.total_turnover_lower || null,
        net_percentage_min: filters.net_percentage_min || null,
        net_percentage_max: filters.net_percentage_max || null,
        rakebackMin: filters.rakebackMin || null,
        rakebackMax: filters.rakebackMax || null,
        sortBy: filters.sortBy || null,
        direction: filters.direction || null,
        rt: filters.rt || Math.floor(Date.now() / 1000),
      };

      // Use EbetLab API client singleton and make request
      const chatBlacklistRequest = new ChatBlacklistListRequest(requestOptions);

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        chatBlacklistRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch chat blacklisted customers: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Chat blacklisted customers retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to fetch chat blacklisted customers:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') ||
            error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch chat blacklisted customers: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
