import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { ValidationError } from '@/types/errors';
import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { BetReportLoginRequest } from '@/network/pg-dagur/requests/auth/BetReportLoginRequest';
import { betReportRecordService, BetReportRecordService } from '@/services/betReportRecord.service';
import fetch from 'node-fetch';

export class BetReportRecordController {
  /**
   * POST /api/v1/makroz/admin/bet-report-records/invoke - Invoke bet record creation
   * 
   * This endpoint:
   * 1. Calls the /api/pg-dagur/v1/internal/reports/daily-bet-report endpoint
   * 2. Extracts the totals data from the response
   * 3. Creates a new BetReportRecord in the database
   * 4. Returns the created record
   */
  static invokeBetRecord = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎯 Invoke bet record request initiated');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Create date range for today (GMT+3) - work in UTC and convert properly
      const nowUTC = new Date(); // This is always UTC when created from system time

      // Calculate GMT+3 time properly
      const gmtPlus3Offset = 3 * 60 * 60 * 1000; // 3 hours in milliseconds
      const nowGMTPlus3 = new Date(nowUTC.getTime() + gmtPlus3Offset);

      // For database record: Start date at 00:00 GMT+3, End date at current moment GMT+3
      // But store as UTC in database (subtract the offset back)
      const dbStartDateGMT3 = new Date(nowGMTPlus3);
      dbStartDateGMT3.setUTCHours(0, 0, 0, 0); // Set to 00:00 in the GMT+3 context
      const dbStartDate = new Date(dbStartDateGMT3.getTime() - gmtPlus3Offset); // Convert back to UTC for storage

      const dbEndDateGMT3 = new Date(nowGMTPlus3);
      const dbEndDate = new Date(dbEndDateGMT3.getTime() - gmtPlus3Offset); // Convert back to UTC for storage

      // For API request: Current date in YYYY-MM-DD format (GMT+3 date)
      const currentDateString = nowGMTPlus3.toISOString().split('T')[0];

      console.log('📅 Date range calculated:', {
        dbStartDate: dbStartDate.toISOString(),
        dbEndDate: dbEndDate.toISOString(),
        dbStartDateGMT3Display: new Date(dbStartDate.getTime() + gmtPlus3Offset).toISOString(),
        dbEndDateGMT3Display: new Date(dbEndDate.getTime() + gmtPlus3Offset).toISOString(),
        apiDateString: currentDateString,
        timezone: 'GMT+3 (stored as UTC, displayed as GMT+3)'
      });

      // Prepare request body for the daily bet report API with the specified format
      const requestBody = {
        timeZoneOffset: "GMT+3",
        newFetch: true,
        betBuilder: "a",
        traderIdsList: null,
        bySettlementDate: true,
        channel: "a",
        columns: "a",
        earlyPayout: "a",
        currency: 1,
        device: "a",
        endDate: currentDateString,
        startDate: currentDateString,
        testPlayer: "a",
        custom: "a",
        transactionCurrencyIds: [],
        isWithoutCurrency: false,
        pageSize: 10,
        offset: 1,
        sortBy: "playedDate",
        sortAsc: true,
        ...req.body // Allow additional parameters to be passed through
      };

      console.log('📤 Calling daily bet report API with body:', JSON.stringify(requestBody, null, 2));

      // Step 1: Ensure we have Dagur authentication
      await pgDagurAdminHttpClient.loginWithOtpSecret(
        process.env['PG_DAGUR_USERNAME'] || '',
        process.env['PG_DAGUR_PASSWORD'] || '',
        process.env['PG_DAGUR_OTP_SECRET'] || '',
      );

      console.log('✅ Dagur authentication successful');

      // Step 2: Get bearer token using BetReportLoginRequest
      console.log('🔑 Obtaining bearer token for daily bet report...');
      const tokenResponse = await pgDagurAdminHttpClient.makeRequest(new BetReportLoginRequest());

      if (!tokenResponse.success) {
        throw new Error(`Failed to obtain bearer token: ${tokenResponse.message}`);
      }

      const bearerToken = tokenResponse.data;
      console.log('✅ Bearer token obtained successfully');

      // Step 3: Use bearer token to make request to external API
      const externalApiUrl = 'https://jord.pronetgaming.eu/api/backoffice/report/dailyBetReport/byDay';
      console.log(`🎯 Making request to external API: ${externalApiUrl}`);

      // Prepare headers with bearer token authentication
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${bearerToken}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      };

      // Make the request to the external API
      const response = await fetch(externalApiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      console.log(`📥 External API response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`External API request failed with status: ${response.status} ${response.statusText}`);
      }

      // Parse the response
      const responseData = await response.json();

      console.log('📥 External API response received');
      console.log('📊 Response data preview:', JSON.stringify(responseData, null, 2).substring(0, 500) + '...');

      // Step 4: Extract totals data from the response
      // Check if totals is directly in responseData or nested under data
      let totals;
      if (responseData.totals) {
        // Direct format: { totals: {...} }
        totals = responseData.totals;
      } else if (responseData.data && responseData.data.totals) {
        // Nested format: { data: { totals: {...} } }
        totals = responseData.data.totals;
      } else {
        console.log('❌ Response structure:', JSON.stringify(responseData, null, 2));
        throw new Error('Invalid response format: missing totals data');
      }
      console.log('📊 Totals data extracted:', JSON.stringify(totals, null, 2));

      // Step 5: Create the main cumulative report (up to hour report for current day)
      console.log('💾 Creating main bet report record in database...');

      const mainCreateDto = BetReportRecordService.mapApiResponseToDto(totals, dbStartDate, dbEndDate, false);
      const mainRecord = await betReportRecordService.createBetReportRecord(mainCreateDto);

      console.log('✅ Main bet report record created successfully');
      console.log('📝 Main record ID:', mainRecord.id);

      // Step 6: Check for latest NON-HOURLY report within the current day to create difference report
      console.log('🔍 Looking for latest non-hourly report within current day for difference calculation...');

      const dayStart = new Date(dbStartDate);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(dbStartDate);
      dayEnd.setHours(23, 59, 59, 999);

      // Get only non-hourly reports for today (is_hourly_report = false)
      const cumulativeReportsResult = await betReportRecordService.findAllBetReportRecords({
        startDateFrom: dayStart,
        startDateTo: dayEnd,
        isHourlyReport: false, // Only get cumulative reports
        sortBy: 'createdAt',
        sortOrder: 'DESC',
        limit: 10, // Get enough records to find the previous one
      });

      const cumulativeReports = cumulativeReportsResult.items;
      console.log(`📊 Found ${cumulativeReports.length} cumulative (non-hourly) reports for today`);

      let differenceRecord = null;

      // If we have more than 1 cumulative report for today, create a difference report
      if (cumulativeReports.length > 1) {
        console.log('📊 Found previous cumulative report, calculating difference...');

        const currentReport = cumulativeReports[0]; // The one we just created (most recent cumulative)
        const previousReport = cumulativeReports[1]; // The previous cumulative report

        console.log('📈 Current report totals:', JSON.stringify(totals, null, 2));
        console.log('📉 Previous report data for comparison:', {
          id: previousReport.id,
          createdAt: previousReport.createdAt,
          totalCoupons: previousReport.totalCoupons,
          totalPlayAmount: previousReport.totalPlayAmount,
        });

        // Calculate differences
        const differenceTotals = {
          totalCoupons: totals.totalCoupons - previousReport.totalCoupons,
          totalPlayAmount: totals.totalPlayAmount - previousReport.totalPlayAmount,
          totalOpenCoupons: totals.totalOpenCoupons - previousReport.totalOpenCoupons,
          totalOpenReturn: totals.totalOpenReturn - previousReport.totalOpenReturn,
          totalOpenAmount: totals.totalOpenAmount - previousReport.totalOpenAmount,
          totalWinCoupons: totals.totalWinCoupons - previousReport.totalWinCoupons,
          totalWinAmount: totals.totalWinAmount - previousReport.totalWinAmount,
          totalWinReturn: totals.totalWinReturn - previousReport.totalWinReturn,
          totalLoseCoupons: totals.totalLoseCoupons - previousReport.totalLoseCoupons,
          totalLoseAmount: totals.totalLoseAmount - previousReport.totalLoseAmount,
          totalVoidCoupons: totals.totalVoidCoupons - previousReport.totalVoidCoupons,
          totalVoidAmount: totals.totalVoidAmount - previousReport.totalVoidAmount,
          totalVoidReturn: totals.totalVoidReturn - previousReport.totalVoidReturn,
          totalPartCashoutAmount: totals.totalPartCashoutAmount - previousReport.totalPartCashoutAmount,
          totalPartCashoutCount: totals.totalPartCashoutCount - previousReport.totalPartCashoutCount,
          totalCompCashoutAmount: totals.totalCompCashoutAmount - previousReport.totalCompCashoutAmount,
          totalCompCashoutCount: totals.totalCompCashoutCount - previousReport.totalCompCashoutCount,
          totRealBalPlayAmount: totals.totRealBalPlayAmount - previousReport.totRealBalPlayAmount,
          totBonBalPlayAmount: totals.totBonBalPlayAmount - previousReport.totBonBalPlayAmount,
          totFreeBalPlayAmount: totals.totFreeBalPlayAmount - previousReport.totFreeBalPlayAmount,
          totRealBalWinReturn: totals.totRealBalWinReturn - previousReport.totRealBalWinReturn,
          totBonBalWinReturn: totals.totBonBalWinReturn - previousReport.totBonBalWinReturn,
          totRealBalVoidReturn: totals.totRealBalVoidReturn - previousReport.totRealBalVoidReturn,
          totBonBalVoidReturn: totals.totBonBalVoidReturn - previousReport.totBonBalVoidReturn,
          ngr: totals.ngr - previousReport.ngr,
          ggr: totals.ggr - previousReport.ggr,
          transactionCurrency: totals.transactionCurrency,
        };

        console.log('🔢 Calculated differences:', JSON.stringify(differenceTotals, null, 2));

        // For hourly report: startDate = endDate of previous report, endDate = current time
        // Both are already in UTC from database, so use them directly
        const hourlyStartDate = new Date(previousReport.endDate); // Start from when the previous report ended (UTC)
        const hourlyEndDate = new Date(dbEndDate); // End at current time (UTC)

        console.log('⏰ Hourly report timeframe:', {
          startDate: hourlyStartDate.toISOString(),
          endDate: hourlyEndDate.toISOString(),
          startDateGMT3Display: new Date(hourlyStartDate.getTime() + gmtPlus3Offset).toISOString(),
          endDateGMT3Display: new Date(hourlyEndDate.getTime() + gmtPlus3Offset).toISOString(),
          previousReportEndDate: previousReport.endDate.toISOString(),
          timeframeDuration: `${Math.round((hourlyEndDate.getTime() - hourlyStartDate.getTime()) / (1000 * 60))} minutes`
        });

        // Create difference report with isHourlyReport = true and correct timeframe
        const differenceCreateDto = BetReportRecordService.mapApiResponseToDto(
          differenceTotals,
          hourlyStartDate,
          hourlyEndDate,
          true // isHourlyReport = true
        );

        differenceRecord = await betReportRecordService.createBetReportRecord(differenceCreateDto);

        console.log('✅ Difference report created successfully');
        console.log('📝 Difference record ID:', differenceRecord.id);
      } else {
        console.log('ℹ️ This is the first report of the day, no difference report created');
      }

      // Step 7: Return success response
      const apiResponse: ApiResponse<any> = {
        success: true,
        message: differenceRecord
          ? 'Bet report records invoked and created successfully (main + difference)'
          : 'Bet report record invoked and created successfully (main only - first of day)',
        data: {
          mainRecord: mainRecord,
          differenceRecord: differenceRecord,
          originalApiResponse: responseData,
          dateRange: {
            startDate: dbStartDate.toISOString(),
            endDate: dbEndDate.toISOString(),
            timezone: 'GMT+3'
          },
          summary: {
            recordsCreated: differenceRecord ? 2 : 1,
            isFirstReportOfDay: !differenceRecord,
            mainRecordId: mainRecord.id,
            differenceRecordId: differenceRecord?.id || null,
          }
        },
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(apiResponse);

    } catch (error) {
      console.error('❌ Invoke bet record request failed:', error);

      // Return error response
      res.status(500).json({
        success: false,
        message: 'Failed to invoke bet record',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        ...(process.env['NODE_ENV'] === 'development' && {
          stack: error instanceof Error ? error.stack : undefined,
        }),
      });
    }
  });

  /**
   * GET /api/v1/makroz/admin/bet-report-records - Get all bet report records
   */
  static getBetReportRecords = asyncHandler(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      createdAtFrom,
      createdAtTo,
    } = req.query;

    const queryParams = {
      page: Number(page),
      limit: Number(limit),
      sortBy: sortBy as any,
      sortOrder: sortOrder as any,
      startDateFrom: startDateFrom ? new Date(startDateFrom as string) : undefined,
      startDateTo: startDateTo ? new Date(startDateTo as string) : undefined,
      endDateFrom: endDateFrom ? new Date(endDateFrom as string) : undefined,
      endDateTo: endDateTo ? new Date(endDateTo as string) : undefined,
      createdAtFrom: createdAtFrom ? new Date(createdAtFrom as string) : undefined,
      createdAtTo: createdAtTo ? new Date(createdAtTo as string) : undefined,
    };

    const result = await betReportRecordService.findAllBetReportRecords(queryParams);

    const apiResponse: ApiResponse<typeof result> = {
      success: true,
      message: 'Bet report records retrieved successfully',
      data: result,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(apiResponse);
  });

  /**
   * GET /api/v1/makroz/admin/bet-report-records/:id - Get bet report record by ID
   */
  static getBetReportRecordById = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
      throw new ValidationError('Valid bet report record ID is required');
    }

    const record = await betReportRecordService.findBetReportRecordById(Number(id));

    if (!record) {
      return res.status(404).json({
        success: false,
        message: 'Bet report record not found',
        timestamp: new Date().toISOString(),
      });
    }

    const apiResponse: ApiResponse<typeof record> = {
      success: true,
      message: 'Bet report record retrieved successfully',
      data: record,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(apiResponse);
  });

  /**
   * DELETE /api/v1/makroz/admin/bet-report-records/:id - Delete bet report record
   */
  static deleteBetReportRecord = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
      throw new ValidationError('Valid bet report record ID is required');
    }

    const deleted = await betReportRecordService.deleteBetReportRecord(Number(id));

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Bet report record not found',
        timestamp: new Date().toISOString(),
      });
    }

    const apiResponse: ApiResponse<null> = {
      success: true,
      message: 'Bet report record deleted successfully',
      data: null,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(apiResponse);
  });
}
