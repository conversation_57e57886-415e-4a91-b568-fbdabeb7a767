import { Request, Response } from 'express';
import { ApiResponse, WidgetStatsRequest, WidgetStatsResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { createEbetLabService } from '@/utils/ebetlabService';
import { asyncHandler } from '@/utils/asyncHandler';

export class DashboardController {
  /**
   * GET /operator/dashboard/transactions/summary - Get transactions summary from EbetLab
   */
  static getTransactionsSummary = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { range, timezone } = req.query;

    try {
      console.log(`📊 Fetching transactions summary - range: ${range}, timezone: ${timezone}`);
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab transactions summary endpoint
      const result = await ebetLabService.getTransactionsSummary(range as string, timezone as string);

      const response: ApiResponse<any> = {
        success: true,
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch transactions summary:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch transactions summary: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /operator/dashboard/debits/big-win-lose - Get big win/lose debits from EbetLab
   */
  static getBigWinLoseDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters to pass as additional params
    const { ...queryParams } = req.query;

    try {
      console.log('💰 Fetching big win/lose debits...');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Convert query parameters to string format for the service
      const additionalParams: Record<string, string> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          additionalParams[key] = value as string;
        }
      });

      // Make authenticated request to EbetLab big win/lose debits endpoint
      const result = await ebetLabService.getBigWinLoseDebits(additionalParams);

      const response: ApiResponse<any> = {
        success: true,
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch big win/lose debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch big win/lose debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /operator/dashboard/debits/game-win-lose - Get game win/lose debits from EbetLab
   */
  static getGameWinLoseDebits = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters to pass as additional params
    const { ...queryParams } = req.query;

    try {
      console.log('🎮 Fetching game win/lose debits...');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Convert query parameters to string format for the service
      const additionalParams: Record<string, string> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          additionalParams[key] = value as string;
        }
      });

      // Make authenticated request to EbetLab game win/lose debits endpoint
      const result = await ebetLabService.getGameWinLoseDebits(additionalParams);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Game win/lose debits retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch game win/lose debits:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch game win/lose debits: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /operator/dashboard/widgets/stats - Get dashboard widget stats from EbetLab
   */
  static getWidgetStats = asyncHandler(async (req: Request, res: Response) => {
    // Extract request body parameters
    const { from, to }: WidgetStatsRequest = req.body;

    // Log request body
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

    // Validate required fields
    if (from === undefined || to === undefined) {
      throw new ValidationError('Missing required fields: from and to are required');
    }

    // Validate that from and to are numbers
    if (typeof from !== 'number' || typeof to !== 'number') {
      throw new ValidationError('Invalid field types: from and to must be numbers (Unix timestamps)');
    }

    try {
      console.log(`📈 Fetching dashboard widget stats - from: ${from}, to: ${to}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab widget stats endpoint
      const result = await ebetLabService.getWidgetStats(from, to);

      const response: ApiResponse<WidgetStatsResponse> = {
        success: true,
        message: 'Dashboard widget stats retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch dashboard widget stats:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch dashboard widget stats: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
