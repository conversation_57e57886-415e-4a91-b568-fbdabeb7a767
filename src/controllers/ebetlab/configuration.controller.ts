import { Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/utils/asyncHandler';
import { adminEbetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { ConfigurationsListRequest } from '@/network/ebetlab/requests/configuration/ConfigurationsListRequest';

/**
 * GET /ebetlab/public/providers
 * Get providers list from EbetLab configuration
 */
export const getProviders = asyncHandler(async (req: Request, res: Response) => {
  console.log('🎮 Fetching providers from EbetLab configuration');

  try {
    // Create configuration request
    const configRequest = new ConfigurationsListRequest();

    // Make request using admin client
    const result = await adminEbetlabApiClient.makeAuthenticatedRequest(configRequest);

    // Check if the request was successful
    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to fetch configuration data',
      });
    }

    // Extract providers from the configuration data
    const providers = result.data.providers || [];

    console.log(`✅ Retrieved ${providers.length} providers`);

    res.json({
      success: true,
      data: providers,
    });
  } catch (error: any) {
    console.error('❌ Failed to fetch providers:', error);

    if (error.message.includes('401')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication failed',
      });
    }

    if (error.message.includes('Missing admin credentials')) {
      return res.status(500).json({
        success: false,
        error: 'Server configuration error',
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch providers',
    });
  }
});

/**
 * GET /ebetlab/public/currencies
 * Get currencies list from EbetLab configuration
 */
export const getCurrencies = asyncHandler(async (req: Request, res: Response) => {
  console.log('💰 Fetching currencies from EbetLab configuration');

  try {
    // Create configuration request
    const configRequest = new ConfigurationsListRequest();

    // Make request using admin client
    const result = await adminEbetlabApiClient.makeAuthenticatedRequest(configRequest);

    // Check if the request was successful
    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to fetch configuration data',
      });
    }

    // Extract currencies from the configuration data
    const currencies = result.data.currencies || [];

    console.log(`✅ Retrieved ${currencies.length} currencies`);

    res.json({
      success: true,
      data: currencies,
    });
  } catch (error: any) {
    console.error('❌ Failed to fetch currencies:', error);

    if (error.message.includes('401')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication failed',
      });
    }

    if (error.message.includes('Missing admin credentials')) {
      return res.status(500).json({
        success: false,
        error: 'Server configuration error',
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch currencies',
    });
  }
});
