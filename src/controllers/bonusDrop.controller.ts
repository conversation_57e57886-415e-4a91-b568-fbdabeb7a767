import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { BonusDropListRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropListRequest';
import { BonusDropGetRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropGetRequest';
import { BonusDropRedeemListByIdRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropRedeemListByIdRequest';
import { BonusDropRedeemListRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropRedeemListRequest';
import { BonusDropRedeemDownloadAllCreateRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropRedeemDownloadAllCreateRequest';
import { BonusDropCancellationRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropCancellationRequest';
import { BonusDropDeleteRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropDeleteRequest';
import { BonusDropRedeemDownloadCreateRequest } from '@/network/ebetlab/requests/bonus-drop/BonusDropRedeemDownloadCreateRequest';
import {
  BonusDropCreateRequest,
  BonusDropCreateRequestOptions,
} from '@/network/ebetlab/requests/bonus-drop/BonusDropCreateRequest';
import {
  BonusDropUpdateRequest,
  BonusDropUpdateRequestOptions,
} from '@/network/ebetlab/requests/bonus-drop/BonusDropUpdateRequest';

export class BonusDropController {
  /**
   * GET /bonus-drops - Get bonus drops from EbetLab
   */
  static listBonusDrops = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎁 Fetching bonus drops - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const bonusDropRequest = new BonusDropListRequest({
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        bonusDropRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonus drops: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus drops retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus drops:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus drops: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /bonus-drops/:id - Get a specific bonus drop by ID
   */
  static getBonusDropById = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🎁 Fetching bonus drop by ID - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const dropId = parseInt(id);
      if (isNaN(dropId) || dropId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const bonusDropRequest = new BonusDropGetRequest({
        id: dropId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        bonusDropRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonus drop: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus drop retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus drop by ID:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus drop: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /bonus-drops/:id/redeems - Get redeems for a specific bonus drop
   */
  static getBonusDropRedeems = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters and query parameters
    const { id } = req.params;
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎁 Fetching bonus drop redeems - id: ${id}, page: ${page}, limit: ${limit}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const dropId = parseInt(id);
      if (isNaN(dropId) || dropId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const redeemRequest = new BonusDropRedeemListByIdRequest({
        id: dropId,
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(redeemRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonus drop redeems: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus drop redeems retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus drop redeems:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus drop redeems: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /bonus-drops/redeems - Get all bonus drop redeems
   */
  static listBonusDropRedeems = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎁 Fetching all bonus drop redeems - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const redeemRequest = new BonusDropRedeemListRequest({
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(redeemRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonus drop redeems: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus drop redeems retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus drop redeems:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus drop redeems: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonus-drops/redeems/downloads - Create download for all bonus drop redeems
   */
  static createBonusDropRedeemDownloadAll = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📥 Creating download for all bonus drop redeems');
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(req.query).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const downloadRequest = new BonusDropRedeemDownloadAllCreateRequest(filters);

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        downloadRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to create download: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Download created successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to create download:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create download: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonus-drops/:id/cancellations - Cancel a specific bonus drop
   */
  static cancelBonusDrop = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🚫 Cancelling bonus drop - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const dropId = parseInt(id);
      if (isNaN(dropId) || dropId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const cancellationRequest = new BonusDropCancellationRequest({
        id: dropId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        cancellationRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to cancel bonus drop: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus drop cancelled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to cancel bonus drop:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to cancel bonus drop: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * DELETE /bonus-drops/:id - Delete a specific bonus drop
   */
  static deleteBonusDrop = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🗑️ Deleting bonus drop - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const dropId = parseInt(id);
      if (isNaN(dropId) || dropId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const deleteRequest = new BonusDropDeleteRequest({
        id: dropId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(deleteRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to delete bonus drop: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus drop deleted successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete bonus drop:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete bonus drop: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonus-drops/:id/downloads - Create download for specific bonus drop redeems
   */
  static createBonusDropRedeemDownload = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`📥 Creating download for bonus drop redeems - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const dropId = parseInt(id);
      if (isNaN(dropId) || dropId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const downloadRequest = new BonusDropRedeemDownloadCreateRequest({
        id: dropId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        downloadRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to create download: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Download created successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to create download:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create download: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonus-drops - Create a new bonus drop
   */
  static createBonusDrop = asyncHandler(
    async (req: Request<any, any, BonusDropCreateRequestOptions>, res: Response) => {
      try {
        console.log('🎁 Creating bonus drop');
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate required fields
        const requiredFields: (keyof BonusDropCreateRequestOptions)[] = [
          'code',
          'currency',
          'amount',
          'start',
          'count',
          'end',
          'required_wager',
          'required_wager_currency',
          'ref_code',
          'reference_tag',
          'rules',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate rules array
        if (!Array.isArray(req.body.rules) || req.body.rules.length === 0) {
          throw new ValidationError('rules must be a non-empty array');
        }

        // Use EbetLab API client singleton and make request
        const createRequest = new BonusDropCreateRequest(req.body);

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          createRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to create bonus drop: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Bonus drop created successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(201).json(response);
      } catch (error) {
        console.error('❌ Failed to create bonus drop:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to create bonus drop: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * PATCH /bonus-drops/:id - Update a specific bonus drop
   */
  static updateBonusDrop = asyncHandler(
    async (req: Request<{ id: string }, any, BonusDropUpdateRequestOptions>, res: Response) => {
      // Extract ID from route parameters
      const { id } = req.params;

      try {
        console.log(`🎁 Updating bonus drop - id: ${id}`);
        console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate ID parameter
        if (!id) {
          throw new ValidationError('id parameter is required');
        }

        const dropId = parseInt(id);
        if (isNaN(dropId) || dropId < 1) {
          throw new ValidationError('id must be a positive integer');
        }

        // Validate required fields
        const requiredFields: (keyof BonusDropUpdateRequestOptions)[] = [
          'code',
          'currency',
          'amount',
          'count',
          'ref_code',
          'reference_tag',
          'required_wager',
          'required_wager_currency',
          'start',
          'end',
          'rules',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate rules array
        if (!Array.isArray(req.body.rules) || req.body.rules.length === 0) {
          throw new ValidationError('rules must be a non-empty array');
        }

        // Use EbetLab API client singleton and make request
        const updateRequest = new BonusDropUpdateRequest({
          ...req.body,
          id: dropId,
        });

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          updateRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to update bonus drop: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Bonus drop updated successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);
      } catch (error) {
        console.error('❌ Failed to update bonus drop:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to update bonus drop: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );
}
