import { Request, Response } from 'express';
import { MissionObjectiveAssignmentService, CreateMissionObjectiveAssignmentDto, MissionObjectiveAssignmentQueryParams } from '@/services/missionObjectiveAssignment.service';
import { ExtendedUserService } from '@/services/extendedUser.service';
import { MissionObjectiveService } from '@/services/missionObjective.service';
import { MissionService } from '@/services/mission.service';
import { asyncHandler } from '@/utils/asyncHandler';

const missionObjectiveAssignmentService = new MissionObjectiveAssignmentService();
const extendedUserService = new ExtendedUserService();
const missionObjectiveService = new MissionObjectiveService();
const missionService = new MissionService();

export const createMissionObjectiveAssignment = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { userId, missionObjectiveId, progress, lastCheckedRecordTimestamp, startDate, endDate }: CreateMissionObjectiveAssignmentDto = req.body;

  // Validation
  if (!userId || typeof userId !== 'number' || userId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'User ID is required and must be a positive number',
    });
  }

  if (!missionObjectiveId || typeof missionObjectiveId !== 'number' || missionObjectiveId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Mission Objective ID is required and must be a positive number',
    });
  }

  if (progress !== undefined && (typeof progress !== 'number' || progress < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Progress must be a non-negative number',
    });
  }

  if (lastCheckedRecordTimestamp !== undefined && lastCheckedRecordTimestamp !== null && 
      (typeof lastCheckedRecordTimestamp !== 'number' || lastCheckedRecordTimestamp < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Last checked record timestamp must be a non-negative number',
    });
  }

  try {
    // Validate foreign key existence - Check if user exists
    const existingUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this ID does not exist',
      });
    }

    // Validate foreign key existence - Check if mission objective exists
    const existingMissionObjective = await missionObjectiveService.findMissionObjectiveById(missionObjectiveId);
    if (!existingMissionObjective) {
      return res.status(400).json({
        success: false,
        message: 'Mission objective with this ID does not exist',
      });
    }

    // Check if assignment already exists
    const existingAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentByUserAndObjective(userId, missionObjectiveId);
    if (existingAssignment) {
      return res.status(409).json({
        success: false,
        message: 'Mission objective assignment already exists for this user and objective',
      });
    }

    const missionObjectiveAssignment = await missionObjectiveAssignmentService.createMissionObjectiveAssignment({
      userId,
      missionObjectiveId,
      progress: progress || 0,
      lastCheckedRecordTimestamp: lastCheckedRecordTimestamp || null,
      startDate: startDate || null,
      endDate: endDate || null,
    });

    res.status(201).json({
      success: true,
      message: 'Mission objective assignment created successfully',
      data: missionObjectiveAssignment,
    });
  } catch (error) {
    console.error('Error creating mission objective assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mission objective assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionObjectiveAssignments = asyncHandler(async (req: Request, res: Response) => {
  console.log('Query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const queryParams: MissionObjectiveAssignmentQueryParams = {
    page: req.query['page'] ? parseInt(req.query['page'] as string) : undefined,
    limit: req.query['limit'] ? parseInt(req.query['limit'] as string) : undefined,
    sortBy: req.query['sortBy'] as any,
    sortOrder: req.query['sortOrder'] as 'ASC' | 'DESC',
    userId: req.query['userId'] ? parseInt(req.query['userId'] as string) : undefined,
    missionObjectiveId: req.query['missionObjectiveId'] ? parseInt(req.query['missionObjectiveId'] as string) : undefined,
    progressMin: req.query['progressMin'] ? parseInt(req.query['progressMin'] as string) : undefined,
    progressMax: req.query['progressMax'] ? parseInt(req.query['progressMax'] as string) : undefined,
    createdAtFrom: req.query['createdAtFrom'] ? parseInt(req.query['createdAtFrom'] as string) : undefined,
    createdAtTo: req.query['createdAtTo'] ? parseInt(req.query['createdAtTo'] as string) : undefined,
    updatedAtFrom: req.query['updatedAtFrom'] ? parseInt(req.query['updatedAtFrom'] as string) : undefined,
    updatedAtTo: req.query['updatedAtTo'] ? parseInt(req.query['updatedAtTo'] as string) : undefined,
    lastCheckedFrom: req.query['lastCheckedFrom'] ? parseInt(req.query['lastCheckedFrom'] as string) : undefined,
    lastCheckedTo: req.query['lastCheckedTo'] ? parseInt(req.query['lastCheckedTo'] as string) : undefined,
    startDateFrom: req.query['startDateFrom'] ? parseInt(req.query['startDateFrom'] as string) : undefined,
    startDateTo: req.query['startDateTo'] ? parseInt(req.query['startDateTo'] as string) : undefined,
    endDateFrom: req.query['endDateFrom'] ? parseInt(req.query['endDateFrom'] as string) : undefined,
    endDateTo: req.query['endDateTo'] ? parseInt(req.query['endDateTo'] as string) : undefined,
    search: req.query['search'] as string,
  };

  try {
    const result = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentsWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Mission objective assignments retrieved successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error retrieving mission objective assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission objective assignments',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionObjectiveAssignmentById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Mission objective assignment ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective assignment ID is required',
    });
  }

  try {
    const missionObjectiveAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentById(id);

    if (!missionObjectiveAssignment) {
      return res.status(404).json({
        success: false,
        message: 'Mission objective assignment not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission objective assignment retrieved successfully',
      data: missionObjectiveAssignment,
    });
  } catch (error) {
    console.error('Error retrieving mission objective assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission objective assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateMissionObjectiveAssignment = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating mission objective assignment ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective assignment ID is required',
    });
  }

  // Check if mission objective assignment exists
  const existingMissionObjectiveAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentById(id);
  if (!existingMissionObjectiveAssignment) {
    return res.status(404).json({
      success: false,
      message: 'Mission objective assignment not found',
    });
  }

  const { userId, missionObjectiveId, progress, lastCheckedRecordTimestamp, startDate, endDate } = req.body;

  // Validation
  if (userId !== undefined && (typeof userId !== 'number' || userId <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'User ID must be a positive number',
    });
  }

  if (missionObjectiveId !== undefined && (typeof missionObjectiveId !== 'number' || missionObjectiveId <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'Mission Objective ID must be a positive number',
    });
  }

  if (progress !== undefined && (typeof progress !== 'number' || progress < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Progress must be a non-negative number',
    });
  }

  if (lastCheckedRecordTimestamp !== undefined && lastCheckedRecordTimestamp !== null && 
      (typeof lastCheckedRecordTimestamp !== 'number' || lastCheckedRecordTimestamp < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Last checked record timestamp must be a non-negative number',
    });
  }

  // Validate foreign key existence if userId is being updated
  if (userId !== undefined && userId !== existingMissionObjectiveAssignment.userId) {
    const existingUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this ID does not exist',
      });
    }
  }

  // Validate foreign key existence if missionObjectiveId is being updated
  if (missionObjectiveId !== undefined && missionObjectiveId !== existingMissionObjectiveAssignment.missionObjectiveId) {
    const existingMissionObjective = await missionObjectiveService.findMissionObjectiveById(missionObjectiveId);
    if (!existingMissionObjective) {
      return res.status(400).json({
        success: false,
        message: 'Mission objective with this ID does not exist',
      });
    }
  }

  // Check for unique constraint if userId or missionObjectiveId is being updated
  if ((userId !== undefined && userId !== existingMissionObjectiveAssignment.userId) || 
      (missionObjectiveId !== undefined && missionObjectiveId !== existingMissionObjectiveAssignment.missionObjectiveId)) {
    const checkUserId = userId !== undefined ? userId : existingMissionObjectiveAssignment.userId;
    const checkMissionObjectiveId = missionObjectiveId !== undefined ? missionObjectiveId : existingMissionObjectiveAssignment.missionObjectiveId;
    
    const existingAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentByUserAndObjective(checkUserId, checkMissionObjectiveId);
    if (existingAssignment && existingAssignment.id !== id) {
      return res.status(409).json({
        success: false,
        message: 'Mission objective assignment already exists for this user and objective combination',
      });
    }
  }

  try {
    const updateData: Partial<CreateMissionObjectiveAssignmentDto> = {};
    if (userId !== undefined) updateData.userId = userId;
    if (missionObjectiveId !== undefined) updateData.missionObjectiveId = missionObjectiveId;
    if (progress !== undefined) updateData.progress = progress;
    if (lastCheckedRecordTimestamp !== undefined) updateData.lastCheckedRecordTimestamp = lastCheckedRecordTimestamp;
    if (startDate !== undefined) updateData.startDate = startDate;
    if (endDate !== undefined) updateData.endDate = endDate;

    const updatedMissionObjectiveAssignment = await missionObjectiveAssignmentService.updateMissionObjectiveAssignment(id, updateData);

    res.status(200).json({
      success: true,
      message: 'Mission objective assignment updated successfully',
      data: updatedMissionObjectiveAssignment,
    });
  } catch (error) {
    console.error('Error updating mission objective assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mission objective assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteMissionObjectiveAssignment = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting mission objective assignment ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective assignment ID is required',
    });
  }

  try {
    // Check if mission objective assignment exists before deletion
    const existingMissionObjectiveAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentById(id);
    if (!existingMissionObjectiveAssignment) {
      return res.status(404).json({
        success: false,
        message: 'Mission objective assignment not found',
      });
    }

    const deleted = await missionObjectiveAssignmentService.deleteMissionObjectiveAssignment(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Mission objective assignment not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission objective assignment deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting mission objective assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mission objective assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Assign an objective to a user (PUT endpoint)
export const assignObjectiveToUser = asyncHandler(async (req: Request, res: Response) => {
  console.log('Assigning objective to user for mission objective ID:', req.params['missionObjectiveId']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionObjectiveId = parseInt(req.params['missionObjectiveId'] || '0');
  const { userId, progress, lastCheckedRecordTimestamp, startDate, endDate } = req.body;

  if (!missionObjectiveId || isNaN(missionObjectiveId) || missionObjectiveId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective ID is required',
    });
  }

  if (!userId || typeof userId !== 'number' || userId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'User ID is required and must be a positive number',
    });
  }

  try {
    // Validate foreign key existence - Check if user exists
    const existingUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this ID does not exist',
      });
    }

    // Validate foreign key existence - Check if mission objective exists
    const existingMissionObjective = await missionObjectiveService.findMissionObjectiveById(missionObjectiveId);
    if (!existingMissionObjective) {
      return res.status(400).json({
        success: false,
        message: 'Mission objective with this ID does not exist',
      });
    }

    // Check if assignment already exists
    const existingAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentByUserAndObjective(userId, missionObjectiveId);
    if (existingAssignment) {
      return res.status(409).json({
        success: false,
        message: 'Mission objective assignment already exists for this user and objective',
      });
    }

    // Create the assignment
    const missionObjectiveAssignment = await missionObjectiveAssignmentService.createMissionObjectiveAssignment({
      userId,
      missionObjectiveId,
      progress: progress || 0,
      lastCheckedRecordTimestamp: lastCheckedRecordTimestamp || null,
      startDate: startDate || null,
      endDate: endDate || null,
    });

    res.status(201).json({
      success: true,
      message: 'Objective assigned to user successfully',
      data: missionObjectiveAssignment,
    });
  } catch (error) {
    console.error('Error assigning objective to user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign objective to user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get all assignments for a specific mission objective
export const getAssignmentsForObjective = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting assignments for mission objective ID:', req.params['missionObjectiveId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionObjectiveId = parseInt(req.params['missionObjectiveId'] || '0');

  if (!missionObjectiveId || isNaN(missionObjectiveId) || missionObjectiveId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission objective ID is required',
    });
  }

  try {
    // Validate that mission objective exists
    const existingMissionObjective = await missionObjectiveService.findMissionObjectiveById(missionObjectiveId);
    if (!existingMissionObjective) {
      return res.status(404).json({
        success: false,
        message: 'Mission objective with this ID does not exist',
      });
    }

    // Get all assignments for this mission objective
    const assignments = await missionObjectiveAssignmentService.findAssignmentsByMissionObjectiveId(missionObjectiveId);

    res.status(200).json({
      success: true,
      message: 'Mission objective assignments retrieved successfully',
      data: {
        missionObjectiveId,
        missionObjective: existingMissionObjective,
        assignments,
        totalAssignments: assignments.length,
      },
    });
  } catch (error) {
    console.error('Error retrieving mission objective assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission objective assignments',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get all assignments for a specific mission (all objectives)
export const getAssignmentsForMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting assignments for mission ID:', req.params['missionId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    // Validate that mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(404).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Get all assignments for this mission
    const assignments = await missionObjectiveAssignmentService.findAssignmentsByMissionId(missionId);

    res.status(200).json({
      success: true,
      message: 'Mission objective assignments retrieved successfully',
      data: {
        missionId,
        mission: existingMission,
        assignments,
        totalAssignments: assignments.length,
      },
    });
  } catch (error) {
    console.error('Error retrieving mission assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission assignments',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get progress statistics for a specific mission
export const getMissionProgressStats = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting progress stats for mission ID:', req.params['missionId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    // Validate that mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(404).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Get progress statistics
    const stats = await missionObjectiveAssignmentService.getProgressStatsByMissionId(missionId);

    res.status(200).json({
      success: true,
      message: 'Mission progress statistics retrieved successfully',
      data: {
        missionId,
        mission: existingMission,
        statistics: stats,
      },
    });
  } catch (error) {
    console.error('Error retrieving mission progress statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission progress statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
