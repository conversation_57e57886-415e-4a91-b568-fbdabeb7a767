import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { BonusRequestContentListRequest } from '@/network/ebetlab/requests/bonus-request-content/BonusRequestContentListRequest';
import {
  BonusRequestContentCreateRequest,
  BonusRequestContentCreateRequestOptions,
} from '@/network/ebetlab/requests/bonus-request-content/BonusRequestContentCreateRequest';
import { BonusRequestContentSortCreateRequest } from '@/network/ebetlab/requests/bonus-request-content/BonusRequestContentSortCreateRequest';
import { BonusRequestContentStateToggleRequest } from '@/network/ebetlab/requests/bonus-request-content/BonusRequestContentStateToggleRequest';
import { BonusRequestContentDeleteRequest } from '@/network/ebetlab/requests/bonus-request-content/BonusRequestContentDeleteRequest';

export class BonusRequestContentController {
  /**
   * GET /bonus-request-contents - Get bonus request contents from EbetLab
   */
  static listBonusRequestContents = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`📝 Fetching bonus request contents - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const contentRequest = new BonusRequestContentListRequest({
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        contentRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonus request contents: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus request contents retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus request contents:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus request contents: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonus-request-contents - Create a new bonus request content
   */
  static createBonusRequestContent = asyncHandler(
    async (req: Request<any, any, BonusRequestContentCreateRequestOptions>, res: Response) => {
      try {
        console.log('📝 Creating bonus request content');
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate required fields
        const requiredFields: (keyof BonusRequestContentCreateRequestOptions)[] = [
          'title',
          'request_able',
          'gmt',
          'days',
          'from',
          'to',
          'values',
          'bonus_id',
          'bonus_type',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate values array
        if (!Array.isArray(req.body.values) || req.body.values.length === 0) {
          throw new ValidationError('values must be a non-empty array');
        }

        // Use EbetLab API client singleton and make request
        const createRequest = new BonusRequestContentCreateRequest(req.body);

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          createRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to create bonus request content: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Bonus request content created successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(201).json(response);
      } catch (error) {
        console.error('❌ Failed to create bonus request content:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to create bonus request content: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * POST /bonus-request-contents/sorts - Create sort order for bonus request contents
   */
  static createBonusRequestContentSort = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📝 Creating bonus request content sort');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Validate required fields
      if (!req.body.sorts || !Array.isArray(req.body.sorts)) {
        throw new ValidationError('sorts must be an array');
      }

      if (req.body.sorts.length === 0) {
        throw new ValidationError('sorts array cannot be empty');
      }

      // Validate that all sorts are numbers
      const invalidSorts = req.body.sorts.filter((sort: any) => typeof sort !== 'number' || sort < 1);
      if (invalidSorts.length > 0) {
        throw new ValidationError('All sorts must be positive integers');
      }

      // Use EbetLab API client singleton and make request
      const sortRequest = new BonusRequestContentSortCreateRequest({
        sorts: req.body.sorts,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        sortRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to create bonus request content sort: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus request content sort created successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to create bonus request content sort:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create bonus request content sort: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonus-request-contents/:id/states - Toggle state of a specific bonus request content
   */
  static toggleBonusRequestContentState = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`📝 Toggling bonus request content state - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const contentId = parseInt(id);
      if (isNaN(contentId) || contentId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const toggleRequest = new BonusRequestContentStateToggleRequest({
        id: contentId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        toggleRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to toggle bonus request content state: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus request content state toggled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to toggle bonus request content state:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to toggle bonus request content state: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * DELETE /bonus-request-contents/:id - Delete a specific bonus request content
   */
  static deleteBonusRequestContent = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🗑️ Deleting bonus request content - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const contentId = parseInt(id);
      if (isNaN(contentId) || contentId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const deleteRequest = new BonusRequestContentDeleteRequest({
        id: contentId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        deleteRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to delete bonus request content: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus request content deleted successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete bonus request content:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete bonus request content: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
