import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { CustomerGetRequest } from '@/network/pg-web/requests/customer/CustomerGetRequest';
import { pgWebHttpClient } from '@/network/pg-web/PGWebHttpClient';
import { ApiResponse } from '@/types/api';

export class CustomerController {
  /**
   * GET /api/pg-web/v1/internal/customers - Get customer from PG Web
   */
  static getCustomer = asyncHandler(async (req: Request, res: Response) => {
    const { referer, authorization } = req.headers;

    if (!referer || !authorization) {
      throw new Error('Referer and Authorization headers are required');
    }

    const request = new CustomerGetRequest();
    const result = await pgWebHttpClient.makeAuthorizedRequest(request, referer, authorization);

    if (!result.success) {
      throw new Error(result.error);
    }

    const response: ApiResponse<any> = {
      success: true,
      message: 'Customer retrieved successfully',
      data: result.data,
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  });
}
