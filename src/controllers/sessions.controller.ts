import { Request, Response } from 'express';
import { ApiResponse, SessionCheckRequest, SessionCheckResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { createEbetLabService } from '@/utils/ebetlabService';
import { asyncHandler } from '@/utils/asyncHandler';

export class SessionsController {
  /**
   * POST /operator/sessions/check/{id} - Check sessions for conflicting users with same IP
   */
  static checkSessions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameter
    const customerId = req.params['id'];

    // Extract request body
    const { id }: SessionCheckRequest = req.body;

    // Validate customer ID
    if (!customerId) {
      throw new ValidationError('Customer ID is required in URL path');
    }

    // Validate that body ID matches path ID (if provided)
    if (id && id !== customerId) {
      throw new ValidationError('Customer ID in request body must match URL path parameter');
    }

    try {
      console.log(`🔍 Checking sessions for conflicting users - Customer ID: ${customerId}`);

      // Create EbetLab service instance
      const ebetLabService = await createEbetLabService(req.headers['authorization'] || '');

      // Make authenticated request to EbetLab session check endpoint
      const result = await ebetLabService.checkSessions(customerId);

      const response: ApiResponse<SessionCheckResponse> = {
        success: true,
        message: 'Session conflicts retrieved successfully',
        data: result,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to check sessions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') ||
            error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to check sessions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
