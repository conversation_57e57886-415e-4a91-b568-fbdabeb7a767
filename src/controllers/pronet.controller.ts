import { Request, Response, NextFunction } from 'express';
import { async<PERSON>and<PERSON> } from '@/utils/asyncHandler';
import { pronetApiClient } from '@/network/pronet/pronetApiClient';
import { validatePronetSystem, generateSamplePronetRequest } from '@/utils/pronetValidator';
import fetch from 'node-fetch';
import { buildPronetApiUrl, getPronetCredentials } from '@/utils/pronetAuth';
import { BonusesAndFreeBetsResponse, CallRequest } from '@/types/pronet';

/**
 * Helper function to extract and filter headers for forwarding to Pronet API
 */
const extractHeadersForForwarding = (req: Request): Record<string, string> => {
  const headersToForward: Record<string, string> = {};
  const excludedHeaders = new Set([
    'host',
    'content-length',
    'content-encoding',
    'transfer-encoding',
    'connection',
    'upgrade',
    'proxy-authenticate',
    'proxy-authorization',
    'te',
    'trailer',
    'checksum', // We'll generate our own checksum or exclude it
  ]);

  console.log('🔍 Processing incoming headers for forwarding...');

  // Forward all headers except the excluded ones
  Object.entries(req.headers).forEach(([key, value]) => {
    const lowerKey = key.toLowerCase();
    if (!excludedHeaders.has(lowerKey) && value) {
      const headerValue = Array.isArray(value) ? value.join(', ') : value;
      headersToForward[key] = headerValue;
      console.log(`✅ Forwarding header: ${key} = ${headerValue}`);
    } else {
      console.log(`❌ Excluding header: ${key} (${excludedHeaders.has(lowerKey) ? 'excluded' : 'empty value'})`);
    }
  });

  return headersToForward;
};

/**
 * Pronet Controller
 *
 * This controller handles all Pronet API proxy endpoints.
 * All endpoints are prefixed with /pronet and proxy requests to the actual Pronet API.
 */
export class PronetController {
  /**
   * POST /pronet/* - Wildcard proxy endpoint for Pronet API
   *
   * This endpoint acts as a proxy for any Pronet API endpoint by:
   * 1. Extracting the real API path by removing the /pronet prefix
   * 2. Making an authenticated request to the actual Pronet API with proper checksum
   * 3. Returning the raw response without additional wrapping
   *
   * Example:
   * POST /pronet/users/profile
   * -> Proxies to: [PRONET_HOST]/api/pronet/v1/users/profile
   */
  static proxyPronetRequest = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    try {
      console.log('🔄 Pronet proxy request initiated');
      console.log(`📍 Original path: ${req.path}`);
      console.log(`📋 Method: ${req.method}`);
      console.log(' Request body:', JSON.stringify(req.body, null, 2));

      // Extract the actual API endpoint by removing the /pronet prefix
      // req.path will be something like "/users/profile" after the /pronet prefix is removed by routing
      const apiEndpoint = req.path.startsWith('/') ? req.path.slice(1) : req.path;

      console.log(`🎯 Target Pronet endpoint: ${apiEndpoint}`);

      // Extract headers for forwarding using helper function
      const headersToForward = extractHeadersForForwarding(req);

      console.log('📤 Headers being forwarded to PronetApiClient:', JSON.stringify(headersToForward, null, 2));
      console.log('📊 Total headers being forwarded:', Object.keys(headersToForward).length);

      // Make the request to Pronet API using the client
      // Use isProxy=true to strip /api/pronet/v1/ prefix from the URL
      const response = await pronetApiClient.makeRequest(
        apiEndpoint,
        req.method as any,
        req.body,
        headersToForward,
        true, // isProxy flag - transforms {{HOST}}/api/pronet/v1/smth to {{HOST}}/smth
      );

      // Log the response for debugging
      console.log('📥 Pronet response received');
      console.log('📥 Response data:', JSON.stringify(response, null, 2));

      // Return the raw response without additional wrapping
      // This is important for frontend to receive raw Pronet responses
      res.json(response);
    } catch (error) {
      console.error('❌ Pronet proxy request failed:', error);

      // Return error in a format that's useful for debugging
      res.status(500).json({
        success: false,
        message: 'Pronet proxy request failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        ...(process.env['NODE_ENV'] === 'development' && {
          stack: error instanceof Error ? error.stack : undefined,
        }),
      });
    }
  });

  /**
   * GET /pronet/health - Health check endpoint for Pronet API
   *
   * This endpoint checks if the Pronet API is accessible and properly configured.
   */
  static healthCheck = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🏥 Pronet health check requested');

      const healthResult = await pronetApiClient.healthCheck();

      const statusCode = healthResult.success ? 200 : 503;

      res.status(statusCode).json({
        success: healthResult.success,
        message: healthResult.message,
        timestamp: healthResult.timestamp,
        service: 'Pronet API',
        credentials: pronetApiClient.getCredentialsInfo(),
      });
    } catch (error) {
      console.error('❌ Pronet health check failed:', error);

      res.status(503).json({
        success: false,
        message: 'Pronet health check failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        service: 'Pronet API',
      });
    }
  });

  /**
   * GET /pronet/config - Get Pronet configuration info (without sensitive data)
   *
   * This endpoint returns information about the current Pronet configuration
   * without exposing sensitive credentials.
   */
  static getConfig = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('⚙️ Pronet configuration info requested');

      const credentialsInfo = pronetApiClient.getCredentialsInfo();

      if (!credentialsInfo) {
        return res.status(500).json({
          success: false,
          message: 'Pronet credentials not initialized',
          timestamp: new Date().toISOString(),
        });
      }

      res.json({
        success: true,
        message: 'Pronet configuration retrieved',
        data: {
          host: credentialsInfo.host,
          username: credentialsInfo.username,
          hasApiKey: credentialsInfo.hasApiKey,
          baseUrl: `${credentialsInfo.host}/api/pronet/v1`,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('❌ Failed to get Pronet configuration:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to get Pronet configuration',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  /**
   * GET /pronet/test - Test Pronet authentication system
   *
   * This endpoint validates the Pronet authentication system and generates
   * sample requests for testing purposes.
   */
  static testSystem = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🧪 Pronet system test requested');

      // Validate the entire system
      const systemValidation = validatePronetSystem();

      // Generate a sample request for testing
      const sampleRequest = generateSamplePronetRequest('test/endpoint', {
        message: 'Hello Pronet',
        timestamp: new Date().toISOString(),
      });

      res.json({
        success: systemValidation.success,
        message: 'Pronet system test completed',
        data: {
          systemValidation,
          sampleRequest,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('❌ Pronet system test failed:', error);

      res.status(500).json({
        success: false,
        message: 'Pronet system test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  /**
   * GET /pronet/external-api/getBonusesAndFreeBets - Get bonuses and free bets from Pronet API
   *
   * This endpoint fetches bonuses and free bets from the Pronet API.
   * Note: This endpoint does NOT require checksum validation as specified.
   */
  static getBonusesAndFreeBets = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎁 Fetching bonuses and free bets from Pronet API');
      console.log('📥 Incoming request headers:', JSON.stringify(req.headers, null, 2));

      // Get Pronet credentials
      const credentials = getPronetCredentials();

      // Build the URL for the external API endpoint
      // Use isProxy=true to strip /api/pronet/v1/ prefix: {{HOST}}/api/pronet/v1/smth becomes {{HOST}}/smth
      const url = buildPronetApiUrl('external-api/getBonusesAndFreeBets', true);

      console.log(`🌐 Making GET request to: ${url}`);

      // Extract headers for forwarding using helper function
      const headersToForward = extractHeadersForForwarding(req);

      // Add Content-Type if not already present
      if (!headersToForward['content-type'] && !headersToForward['Content-Type']) {
        headersToForward['Content-Type'] = 'application/json';
      }

      console.log('📤 Final headers for getBonusesAndFreeBets:', JSON.stringify(headersToForward, null, 2));
      console.log('📊 Total headers being sent:', Object.keys(headersToForward).length);

      // Make GET request with forwarded headers (no checksum required for this endpoint)
      const response = await fetch(url, {
        method: 'GET',
        headers: headersToForward,
      });

      console.log('🚀 Request sent to Pronet API with headers:', Object.keys(headersToForward).join(', '));

      // Log response details
      console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
      console.log('📥 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Pronet API Error Response:', errorText);

        return res.status(response.status).json({
          success: false,
          message: 'Failed to fetch bonuses and free bets from Pronet API',
          error: `${response.status} ${response.statusText} - ${errorText}`,
          timestamp: new Date().toISOString(),
        });
      }

      // Parse response as JSON
      const bonusesData: BonusesAndFreeBetsResponse = await response.json();

      console.log('✅ Successfully fetched bonuses and free bets');
      console.log(`📊 Retrieved ${bonusesData.length} bonuses/free bets`);
      console.log('📥 Response Data:', JSON.stringify(bonusesData, null, 2));

      // Return the data directly as an array (matching the expected response format)
      res.json(bonusesData);
    } catch (error) {
      console.error('❌ Failed to fetch bonuses and free bets:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch bonuses and free bets',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        ...(process.env['NODE_ENV'] === 'development' && {
          stack: error instanceof Error ? error.stack : undefined,
        }),
      });
    }
  });

  /**
   * POST /pronet/v1/call-requests - Forward call requests to Pronet API
   *
   * This endpoint forwards call requests to the Pronet API with proper authentication.
   * It accepts call request data and proxies it to the Pronet API with checksum authentication.
   */
  static forwardCallRequest = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📞 Forwarding call request to Pronet API');
      console.log('📥 Request body:', JSON.stringify(req.body, null, 2));

      // Validate request body structure
      const callRequest: CallRequest = req.body;

      if (!callRequest.id || !callRequest.type || !callRequest.token || !callRequest.code || !callRequest.time) {
        return res.status(400).json({
          success: false,
          message: 'Invalid call request format. Required fields: id, type, token, code, time',
          timestamp: new Date().toISOString(),
        });
      }

      // Get Pronet credentials
      const credentials = getPronetCredentials();

      // Build the URL for the call-requests endpoint
      // Use isProxy=false to use the full API path: {{HOST}}/api/pronet/v1/call-requests
      const url = buildPronetApiUrl('v1/call-requests', false);

      console.log(`🌐 Making POST request to: ${url}`);

      // Extract headers for forwarding
      const headersToForward = extractHeadersForForwarding(req);

      console.log('📤 Headers being forwarded:', JSON.stringify(headersToForward, null, 2));

      // Forward the request to Pronet API using the client with proper checksum authentication
      // The PronetApiClient automatically handles checksum generation for POST requests
      const response = await pronetApiClient.makeRequest(
        'v1/call-requests',
        'POST',
        callRequest,
        headersToForward,
        false, // isProxy=false to use full API path
      );

      console.log('✅ Call request forwarded successfully');
      console.log('📥 Pronet response:', JSON.stringify(response, null, 2));

      // Return the raw response from Pronet API
      res.json(response);
    } catch (error) {
      console.error('❌ Failed to forward call request:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to forward call request to Pronet API',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });


}
