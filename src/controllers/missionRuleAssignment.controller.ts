import { Request, Response } from 'express';
import { MissionRuleAssignmentService, CreateMissionRuleAssignmentDto, MissionRuleAssignmentQueryParams } from '@/services/missionRuleAssignment.service';
import { MissionService } from '@/services/mission.service';
import { MissionRuleService } from '@/services/missionRule.service';
import { asyncHandler } from '@/utils/asyncHandler';

const missionRuleAssignmentService = new MissionRuleAssignmentService();
const missionService = new MissionService();
const missionRuleService = new MissionRuleService();

export const createMissionRuleAssignment = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { missionId, missionRuleId }: CreateMissionRuleAssignmentDto = req.body;

  // Validation
  if (!missionId || typeof missionId !== 'number' || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Mission ID is required and must be a positive number',
    });
  }

  if (!missionRuleId || typeof missionRuleId !== 'number' || missionRuleId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Mission Rule ID is required and must be a positive number',
    });
  }

  try {
    // Validate foreign key existence - Check if mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(400).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Validate foreign key existence - Check if mission rule exists
    const existingMissionRule = await missionRuleService.findMissionRuleById(missionRuleId);
    if (!existingMissionRule) {
      return res.status(400).json({
        success: false,
        message: 'Mission rule with this ID does not exist',
      });
    }

    // Check if assignment already exists
    const existingAssignment = await missionRuleAssignmentService.findMissionRuleAssignmentByMissionAndRule(missionId, missionRuleId);
    if (existingAssignment) {
      return res.status(409).json({
        success: false,
        message: 'Mission rule assignment already exists for this mission and rule combination',
      });
    }

    const missionRuleAssignment = await missionRuleAssignmentService.createMissionRuleAssignment({
      missionId,
      missionRuleId,
    });

    res.status(201).json({
      success: true,
      message: 'Mission rule assignment created successfully',
      data: missionRuleAssignment,
    });
  } catch (error) {
    console.error('Error creating mission rule assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mission rule assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionRuleAssignments = asyncHandler(async (req: Request, res: Response) => {
  console.log('Query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Helper function to safely parse integers
  const safeParseInt = (value: any): number | undefined => {
    if (!value) return undefined;
    const parsed = parseInt(value as string);
    return isNaN(parsed) ? undefined : parsed;
  };

  const queryParams: MissionRuleAssignmentQueryParams = {};

  // Safely assign only defined values
  const page = safeParseInt(req.query['page']);
  const limit = safeParseInt(req.query['limit']);
  const missionId = safeParseInt(req.query['missionId']);
  const missionRuleId = safeParseInt(req.query['missionRuleId']);
  const createdAtFrom = safeParseInt(req.query['createdAtFrom']);
  const createdAtTo = safeParseInt(req.query['createdAtTo']);
  const updatedAtFrom = safeParseInt(req.query['updatedAtFrom']);
  const updatedAtTo = safeParseInt(req.query['updatedAtTo']);

  if (page !== undefined) queryParams.page = page;
  if (limit !== undefined) queryParams.limit = limit;
  if (req.query['sortBy']) queryParams.sortBy = req.query['sortBy'] as any;
  if (req.query['sortOrder']) queryParams.sortOrder = req.query['sortOrder'] as 'ASC' | 'DESC';
  if (missionId !== undefined) queryParams.missionId = missionId;
  if (missionRuleId !== undefined) queryParams.missionRuleId = missionRuleId;
  if (createdAtFrom !== undefined) queryParams.createdAtFrom = createdAtFrom;
  if (createdAtTo !== undefined) queryParams.createdAtTo = createdAtTo;
  if (updatedAtFrom !== undefined) queryParams.updatedAtFrom = updatedAtFrom;
  if (updatedAtTo !== undefined) queryParams.updatedAtTo = updatedAtTo;
  if (req.query['search']) queryParams.search = req.query['search'] as string;

  try {
    const result = await missionRuleAssignmentService.findMissionRuleAssignmentsWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Mission rule assignments retrieved successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error retrieving mission rule assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission rule assignments',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionRuleAssignmentById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Mission rule assignment ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission rule assignment ID is required',
    });
  }

  try {
    const missionRuleAssignment = await missionRuleAssignmentService.findMissionRuleAssignmentById(id);

    if (!missionRuleAssignment) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule assignment not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission rule assignment retrieved successfully',
      data: missionRuleAssignment,
    });
  } catch (error) {
    console.error('Error retrieving mission rule assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission rule assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateMissionRuleAssignment = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating mission rule assignment ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission rule assignment ID is required',
    });
  }

  // Check if mission rule assignment exists
  const existingMissionRuleAssignment = await missionRuleAssignmentService.findMissionRuleAssignmentById(id);
  if (!existingMissionRuleAssignment) {
    return res.status(404).json({
      success: false,
      message: 'Mission rule assignment not found',
    });
  }

  const { missionId, missionRuleId } = req.body;

  // Validation
  if (missionId !== undefined && (typeof missionId !== 'number' || missionId <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'Mission ID must be a positive number',
    });
  }

  if (missionRuleId !== undefined && (typeof missionRuleId !== 'number' || missionRuleId <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'Mission Rule ID must be a positive number',
    });
  }

  // Validate foreign key existence if missionId is being updated
  if (missionId !== undefined && missionId !== existingMissionRuleAssignment.missionId) {
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(400).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }
  }

  // Validate foreign key existence if missionRuleId is being updated
  if (missionRuleId !== undefined && missionRuleId !== existingMissionRuleAssignment.missionRuleId) {
    const existingMissionRule = await missionRuleService.findMissionRuleById(missionRuleId);
    if (!existingMissionRule) {
      return res.status(400).json({
        success: false,
        message: 'Mission rule with this ID does not exist',
      });
    }
  }

  // Check for unique constraint if missionId or missionRuleId is being updated
  if ((missionId !== undefined && missionId !== existingMissionRuleAssignment.missionId) ||
      (missionRuleId !== undefined && missionRuleId !== existingMissionRuleAssignment.missionRuleId)) {
    const checkMissionId = missionId !== undefined ? missionId : existingMissionRuleAssignment.missionId;
    const checkMissionRuleId = missionRuleId !== undefined ? missionRuleId : existingMissionRuleAssignment.missionRuleId;

    const existingAssignment = await missionRuleAssignmentService.findMissionRuleAssignmentByMissionAndRule(checkMissionId, checkMissionRuleId);
    if (existingAssignment && existingAssignment.id !== id) {
      return res.status(409).json({
        success: false,
        message: 'Mission rule assignment already exists for this mission and rule combination',
      });
    }
  }

  try {
    const updateData: Partial<CreateMissionRuleAssignmentDto> = {};
    if (missionId !== undefined) updateData.missionId = missionId;
    if (missionRuleId !== undefined) updateData.missionRuleId = missionRuleId;

    const updatedMissionRuleAssignment = await missionRuleAssignmentService.updateMissionRuleAssignment(id, updateData);

    res.status(200).json({
      success: true,
      message: 'Mission rule assignment updated successfully',
      data: updatedMissionRuleAssignment,
    });
  } catch (error) {
    console.error('Error updating mission rule assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mission rule assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteMissionRuleAssignment = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting mission rule assignment ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission rule assignment ID is required',
    });
  }

  try {
    // Check if mission rule assignment exists before deletion
    const existingMissionRuleAssignment = await missionRuleAssignmentService.findMissionRuleAssignmentById(id);
    if (!existingMissionRuleAssignment) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule assignment not found',
      });
    }

    const deleted = await missionRuleAssignmentService.deleteMissionRuleAssignment(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule assignment not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission rule assignment deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting mission rule assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mission rule assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Assign rules to a mission (PUT endpoint)
export const assignRuleToMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Assigning rules to mission ID:', req.params['missionId']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');
  const { missionRuleIds } = req.body;

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  if (!missionRuleIds || !Array.isArray(missionRuleIds) || missionRuleIds.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Mission Rule IDs array is required and must not be empty',
    });
  }

  // Validate that all mission rule IDs are positive numbers
  for (const ruleId of missionRuleIds) {
    if (typeof ruleId !== 'number' || ruleId <= 0) {
      return res.status(400).json({
        success: false,
        message: 'All mission rule IDs must be positive numbers',
      });
    }
  }

  try {
    // Validate foreign key existence - Check if mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(400).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Validate that all mission rules exist
    const validationResults = await Promise.all(
      missionRuleIds.map(async (ruleId: number) => {
        const rule = await missionRuleService.findMissionRuleById(ruleId);
        return { ruleId, exists: !!rule };
      })
    );

    const invalidRuleIds = validationResults.filter(result => !result.exists).map(result => result.ruleId);
    if (invalidRuleIds.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Mission rules with IDs [${invalidRuleIds.join(', ')}] do not exist`,
      });
    }

    // Check for existing assignments and create new ones
    const skipped = [];
    const created = [];

    for (const ruleId of missionRuleIds) {
      const existingAssignment = await missionRuleAssignmentService.findMissionRuleAssignmentByMissionAndRule(missionId, ruleId);

      if (existingAssignment) {
        skipped.push({
          missionRuleId: ruleId,
          reason: 'Assignment already exists',
          assignment: existingAssignment
        });
      } else {
        const newAssignment = await missionRuleAssignmentService.createMissionRuleAssignment({
          missionId,
          missionRuleId: ruleId,
        });
        created.push(newAssignment);
      }
    }

    res.status(200).json({
      success: true,
      message: `Processed ${missionRuleIds.length} rule assignments for mission`,
      data: {
        missionId,
        mission: existingMission,
        summary: {
          total: missionRuleIds.length,
          created: created.length,
          skipped: skipped.length,
        },
        created,
        skipped,
      },
    });
  } catch (error) {
    console.error('Error assigning rules to mission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign rules to mission',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get all rules assigned to a specific mission
export const getRulesForMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting rules for mission ID:', req.params['missionId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    // Validate that mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(404).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Get all rule assignments for this mission
    const assignments = await missionRuleAssignmentService.findAssignmentsByMissionId(missionId);

    res.status(200).json({
      success: true,
      message: 'Mission rules retrieved successfully',
      data: {
        missionId,
        mission: existingMission,
        assignments,
        totalRules: assignments.length,
      },
    });
  } catch (error) {
    console.error('Error retrieving mission rules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission rules',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
