import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { pgCasinoTraderAdminHttpClient } from '@/network/pg-ct/PGCasinoTraderApiClient';
import { FreespinBonusPreloadRequest } from '@/network/pg-ct/requests/freespinBonus/FreespinBonusPreloadRequest';
import { FreespinBonusRowSelectRequest } from '@/network/pg-ct/requests/freespinBonus/FreespinBonusRowSelectRequest';
import { FreespinBonusCancelRequest } from '@/network/pg-ct/requests/freespinBonus/FreespinBonusCancelRequest';
import * as FreespinBonusCreate from '@/network/pg-ct/requests/freespinBonus/create';
import { PGCasinoTraderService } from '@/services/thirdparty/pg-ct';

const FREESPIN_BONUS_SCHEMAS: { providerName: string; providerId: number; steps: Record<string, any>[] }[] = [
  {
    providerName: 'Big Time Gaming',
    providerId: 138,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: {
            type: 'float',
            inputType: 'select',
            inputOptions: { name: 'Bet Amount', order: 3 },
          },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Blueprint Gaming',
    providerId: 26,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'EGT Digital',
    providerId: 156,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Evolution',
    providerId: 130,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'NetEnt',
    providerId: 133,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: "Play'n GO",
    providerId: 61,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          lines: { type: 'integer', required: true, inputOptions: { name: 'Lines', order: 6 } },
          coins: { type: 'float', inputOptions: { name: 'Coins', order: 7 } },
          sect: { type: 'float', inputOptions: { name: 'Sect', order: 8 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 9 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Pragmatic Play',
    providerId: 80,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          betPerLine: { type: 'float', inputOptions: { name: 'Bet Per Line', order: 6 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 7 },
          },
        },
      },
    ],
  },
  {
    providerName: 'Red Tiger',
    providerId: 134,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
  {
    providerName: 'VoltEnt',
    providerId: 3,
    steps: [
      {
        schema: {
          gameIds: {
            type: 'array',
            inputType: 'game-picker',
            required: true,
            inputOptions: { name: 'Games', order: 2 },
          },
        },
      },
      {
        schema: {
          currencyId: { type: 'integer', inputOptions: { defaultValue: 1, name: 'Currency Code', order: 1 } },
          nOfFreespins: { type: 'integer', required: true, inputOptions: { name: 'Number of Freespins', order: 2 } },
          betAmount: { type: 'float', inputType: 'select', inputOptions: { name: 'Bet Amount', order: 3 } },
          expiresAt: { type: 'date', required: true, inputOptions: { name: 'Expires At', order: 4 } },
          maxWin: { type: 'float', required: true, inputOptions: { name: 'Max Win', order: 5 } },
          customerIds: {
            type: 'array',
            inputType: 'text',
            required: true,
            inputOptions: { name: 'Customers', order: 6 },
          },
        },
      },
    ],
  },
];

export class FreespinBonusController {
  static listProviders = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎮 Fetching providers');

      res.json({
        success: true,
        message: 'Providers retrieved successfully',
        data: FREESPIN_BONUS_SCHEMAS,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('❌ Failed to fetch providers:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch providers',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  static listProviderBetAmounts = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎮 Fetching providers bet amounts');

      const currencyId = Number(req.query['currencyId'] as string);
      const providerId = Number(req.query['providerId'] as string);
      const gameIds = (Array.isArray(req.query['gameId']) ? req.query['gameId'] : [req.query['gameId']]).map(Number);

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: providerId.toString(),
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      if (Number.isNaN(currencyId) === false) {
        const changeCurrencyCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          field: 'cmbCurrency',
          value: currencyId.toString(),
        });
        const changeCurrencyCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          changeCurrencyCreateFormRequest,
          preloadResult.viewState || '',
        );
        if (!changeCurrencyCreateFormResult.success) {
          throw new Error(changeCurrencyCreateFormResult.message);
        }
      }

      const selectGameRequests = gameIds.map(
        (gameId) =>
          new FreespinBonusCreate.FreespinBonusSelectGameRequest({
            javax: {
              ...preloadResult.data,
              ...enterCreateModeResult.data,
            },
            gameId,
            gameIds,
          }),
      );
      let betAmounts: number[] = [];
      for (const selectGameRequest of selectGameRequests) {
        const selectGameResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          selectGameRequest,
          preloadResult.viewState || '',
        );
        if (!selectGameResult.success) {
          throw new Error(selectGameResult.message);
        }

        betAmounts = selectGameResult.data;
      }

      res.json({
        success: true,
        message: 'Providers bet amounts retrieved successfully',
        data: betAmounts,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('❌ Failed to fetch providers bet amounts:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch providers bet amounts',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  static listProviderGames = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎮 Fetching providers games');

      const { providerId } = req.params;
      const { page, limit, name } = req.query;

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: providerId as string,
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      const gameListRequest = new FreespinBonusCreate.FreespinBonusGameListRequest({
        providerId: Number(providerId),
        name: name as string,
        page: Number(page || '1'),
        limit: Number(limit || '20'),
        javax: enterCreateModeResult.data,
      });
      const gameListResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        gameListRequest,
        preloadResult.viewState || '',
      );
      if (!gameListResult.success) {
        throw new Error(gameListResult.message);
      }

      res.json({
        success: true,
        message: 'Providers games retrieved successfully',
        data: gameListResult.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('❌ Failed to fetch providers games:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch providers games',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  static createFreespinBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎰 Creating freespin bonus');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: req.body.providerId.toString(),
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      const selectGameRequests = req.body.gameIds.map(
        (gameId: number) =>
          new FreespinBonusCreate.FreespinBonusSelectGameRequest({
            javax: {
              ...preloadResult.data,
              ...enterCreateModeResult.data,
            },
            gameId,
            gameIds: req.body.gameIds,
          }),
      );
      for (const selectGameRequest of selectGameRequests) {
        const selectGameResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          selectGameRequest,
          preloadResult.viewState || '',
        );
        if (!selectGameResult.success) {
          throw new Error(selectGameResult.message);
        }
      }

      const tabChangeRequest = new FreespinBonusCreate.FreespinBonusTabChangeRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        tab: 'customers',
      });
      const tabChangeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        tabChangeRequest,
        preloadResult.viewState || '',
      );
      if (!tabChangeResult.success) {
        throw new Error(tabChangeResult.message);
      }

      const selectCustomerRequest = new FreespinBonusCreate.FreespinBonusSelectCustomerRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        customerId: req.body.customerId,
      });
      const customerSearchRequest = new FreespinBonusCreate.FreespinBonusCustomerSearchRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
          ...tabChangeResult.data,
        },
        customerId: req.body.customerId,
      });
      const customerSearchResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        customerSearchRequest,
        preloadResult.viewState || '',
      );
      if (!customerSearchResult.success) {
        throw new Error(customerSearchResult.message);
      }

      const selectCustomerResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        selectCustomerRequest,
        preloadResult.viewState || '',
      );
      if (!selectCustomerResult.success) {
        throw new Error(selectCustomerResult.message);
      }

      // if (req.body.currencyCode) {
      //   const changeCurrencyCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
      //     javax: {
      //       ...preloadResult.data,
      //       ...enterCreateModeResult.data,
      //     },
      //     field: 'cmbCurrency',
      //     value: req.body.currencyCode,
      //   });
      //   const changeCurrencyCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
      //     changeCurrencyCreateFormRequest,
      //     preloadResult.viewState || '',
      //   );
      // }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new FreespinBonusCreate.FreespinBonusCreateRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          betAmount: req.body.betAmount,
          betPerLine: req.body.betPerLine,
          customerId: req.body.customerId,
          expiresAt: new Date(req.body.expiresAt),
          gameIds: req.body.gameIds,
          maxWin: req.body.maxWin,
          nOfFreespins: req.body.nOfFreespins,
          providerId: req.body.providerId,
          currencyId: req.body.currencyId,
        }),
        preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin bonus created successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to create freespin bonus:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to create freespin bonus',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  static cancelFreespinBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      // @todo switch id to internal
      // @todo call search on table before selecting row
      const { id } = req.params;

      console.log(`🎰 Cancelling freespin bonus - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const rowSelectRequest = new FreespinBonusRowSelectRequest({
        id: Number(id),
        javax: preloadResult.data,
      });
      const rowSelectResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        rowSelectRequest,
        preloadResult.viewState || '',
      );
      if (!rowSelectResult.success) {
        throw new Error(rowSelectResult.message);
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new FreespinBonusCancelRequest({
          id: Number(id),
          javax: preloadResult.data,
        }),
        preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin bonus cancelled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to cancel freespin bonus:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to cancel freespin bonus',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  static assignTrialBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      console.log(`🎮 Assigning trial bonus - id: ${id}`);

      const result = await PGCasinoTraderService.assignTrialBonus(
        {
          id: 0,
          externalBonusId: Number(id),
          amount: 10,
          externalBonusName: '',
          bonus: {
            id: 0,
            name: '',
            description: '',
            reward: '',
            type: '',
            isActive: false,
            rules: [],
            expiresAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: null,
          },
          bonusId: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        req.body.customerCode,
      );

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus assigned successfully',
        data: result,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to assign trial bonus:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to assign trial bonus',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });
}
