import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHandler } from '@/utils/asyncHandler';
import { pgCasinoTraderAdminHttpClient } from '@/network/pg-ct/PGCasinoTraderApiClient';
import { TestRequest } from '@/network/pg-ct/requests/TestRequest';

export class TestController {
  /**
   * GET /pg-ct/internal/test - Test PG Casino Trader API
   */
  static test = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🧪 PG Casino Trader test requested');

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(new TestRequest());
      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'PG Casino Trader test completed',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ PG Casino Trader test failed:', error);

      res.status(500).json({
        success: false,
        message: 'PG Casino Trader test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });
}
