import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { asyncHandler } from '@/utils/asyncHandler';
import { pgCasinoTraderAdminHttpClient } from '@/network/pg-ct/PGCasinoTraderApiClient';
import { BonusListRequest, BonusListResponse } from '@/network/pg-ct/requests/bonus/BonusListRequest';
import { Bonus } from '@/network/pg-ct/dto/Bonus';
import { BonusPreloadRequest } from '@/network/pg-ct/requests/bonus/BonusPreloadRequest';
import { BonusGetRequest } from '@/network/pg-ct/requests/bonus/BonusGetRequest';
import { BonusRowSelectRequest } from '@/network/pg-ct/requests/bonus/BonusRowSelectRequest';

export class BonusController {
  /**
   * GET /pg-ct/internal/bonuses - Get bonuses from PG Casino Trader
   */
  static listBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { status, cursor, page, limit } = req.query;

      console.log('🎁 Fetching bonuses from PG Casino Trader');

      const preloadRequest = new BonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new BonusListRequest({
          status: (status as string) || 'active',
          javax: preloadResult.data,
          cursor: (cursor as string) || preloadResult.viewState || '',
          page: Number(page || '1'),
          limit: Number(limit || '20'),
        }),
        (cursor as string) || preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<BonusListResponse> = {
        success: true,
        message: 'Bonuses retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonuses:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch bonuses',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  static getBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      console.log(`🎁 Fetching bonus from PG Casino Trader - id: ${id}`);

      const preloadRequest = new BonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const rowSelectRequest = new BonusRowSelectRequest({
        id: Number(id),
        javax: preloadResult.data,
      });
      const rowSelectResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        rowSelectRequest,
        preloadResult.viewState || '',
      );
      if (!rowSelectResult.success) {
        throw new Error(rowSelectResult.message);
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new BonusGetRequest({
          id: Number(id),
          javax: preloadResult.data,
        }),
        preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<Bonus> = {
        success: true,
        message: 'Bonus retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch bonus',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });
}
