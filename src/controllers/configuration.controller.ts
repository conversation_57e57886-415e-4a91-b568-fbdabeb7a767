import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { ConfigurationsListRequest } from '@/network/ebetlab/requests/configuration/ConfigurationsListRequest';

export class ConfigurationController {
  /**
   * GET /configurations - Get configurations from EbetLab
   */
  static getConfigurations = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('⚙️ Fetching configurations');

      // Use EbetLab API client singleton and make request
      const configRequest = new ConfigurationsListRequest();

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        configRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch configurations: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Configurations retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch configurations:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch configurations: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
