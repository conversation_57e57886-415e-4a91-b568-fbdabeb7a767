import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { WelcomeBonusGetRequest } from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusGetRequest';
import { WelcomeBonusListRequest } from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusListRequest';
import {
  WelcomeBonusCreateRequest,
  WelcomeBonusCreateRequestOptions,
} from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusCreateRequest';
import { WelcomeBonusClaimListRequest } from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusClaimListRequest';
import {
  WelcomeBonusUpdateRequest,
  WelcomeBonusUpdateRequestOptions,
} from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusUpdateRequest';
import { WelcomeBonusCancellationCreateRequest } from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusCancellationCreateRequest';
import { WelcomeBonusDeleteRequest } from '@/network/ebetlab/requests/welcome-bonus/WelcomeBonusDeleteRequest';

export class WelcomeBonusController {
  /**
   * GET /welcome-bonuses - Get welcome bonuses from EbetLab
   */
  static listWelcomeBonuses = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎁 Fetching welcome bonuses - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const welcomeBonusRequest = new WelcomeBonusListRequest({
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        welcomeBonusRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch welcome bonuses: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Welcome bonuses retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch welcome bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch welcome bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /welcome-bonuses/:id - Get a specific welcome bonus by ID
   */
  static getWelcomeBonusById = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🎁 Fetching welcome bonus by ID - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const bonusId = parseInt(id);
      if (isNaN(bonusId) || bonusId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const welcomeBonusRequest = new WelcomeBonusGetRequest({
        id: bonusId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        welcomeBonusRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch welcome bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Welcome bonus retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch welcome bonus by ID:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch welcome bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /welcome-bonuses/:id/claims - Get claims for a specific welcome bonus
   */
  static getWelcomeBonusClaims = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters and query parameters
    const { id } = req.params;
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎁 Fetching welcome bonus claims - id: ${id}, page: ${page}, limit: ${limit}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const bonusId = parseInt(id);
      if (isNaN(bonusId) || bonusId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const claimRequest = new WelcomeBonusClaimListRequest({
        id: bonusId,
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(claimRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch welcome bonus claims: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Welcome bonus claims retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch welcome bonus claims:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch welcome bonus claims: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /welcome-bonuses - Create a new welcome bonus
   */
  static createWelcomeBonus = asyncHandler(
    async (req: Request<any, any, WelcomeBonusCreateRequestOptions>, res: Response) => {
      try {
        console.log('🎁 Creating welcome bonus');
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate required fields
        const requiredFields: (keyof WelcomeBonusCreateRequestOptions)[] = [
          'code',
          'currency_code',
          'usd_amount',
          'from',
          'to',
          'required_wager',
          'amount',
          'required_wager_currency',
          'total',
          'ref_code',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Use EbetLab API client singleton and make request
        const createRequest = new WelcomeBonusCreateRequest(req.body);

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          createRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to create welcome bonus: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Welcome bonus created successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(201).json(response);
      } catch (error) {
        console.error('❌ Failed to create welcome bonus:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to create welcome bonus: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * PATCH /welcome-bonuses/:id - Update a specific welcome bonus
   */
  static updateWelcomeBonus = asyncHandler(
    async (req: Request<{ id: string }, any, WelcomeBonusUpdateRequestOptions>, res: Response) => {
      // Extract ID from route parameters
      const { id } = req.params;

      try {
        console.log(`🎁 Updating welcome bonus - id: ${id}`);
        console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate ID parameter
        if (!id) {
          throw new ValidationError('id parameter is required');
        }

        const bonusId = parseInt(id);
        if (isNaN(bonusId) || bonusId < 1) {
          throw new ValidationError('id must be a positive integer');
        }

        // Validate required fields
        const requiredFields: (keyof WelcomeBonusUpdateRequestOptions)[] = [
          'code',
          'currency_code',
          'currency',
          'total',
          'amount',
          'usd_amount',
          'ref_code',
          'required_wager',
          'required_wager_currency',
          'from',
          'to',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Use EbetLab API client singleton and make request
        const updateRequest = new WelcomeBonusUpdateRequest({
          ...req.body,
          id: bonusId,
        });

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          updateRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to update welcome bonus: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Welcome bonus updated successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);
      } catch (error) {
        console.error('❌ Failed to update welcome bonus:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to update welcome bonus: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * POST /welcome-bonuses/:id/cancellations - Cancel a specific welcome bonus
   */
  static cancelWelcomeBonus = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🚫 Cancelling welcome bonus - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const bonusId = parseInt(id);
      if (isNaN(bonusId) || bonusId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const cancellationRequest = new WelcomeBonusCancellationCreateRequest({
        id: bonusId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        cancellationRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to cancel welcome bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Welcome bonus cancelled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to cancel welcome bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to cancel welcome bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * DELETE /welcome-bonuses/:id - Delete a specific welcome bonus
   */
  static deleteWelcomeBonus = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🗑️ Deleting welcome bonus - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const bonusId = parseInt(id);
      if (isNaN(bonusId) || bonusId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const deleteRequest = new WelcomeBonusDeleteRequest({
        id: bonusId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(deleteRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to delete welcome bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Welcome bonus deleted successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete welcome bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete welcome bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
