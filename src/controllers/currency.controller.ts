import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { CurrencyRateConversionListRequest } from '@/network/ebetlab/requests/currency/CurrencyRateConversionListRequest';

export class CurrencyController {
  /**
   * GET /currency/rates/conversion/:currency - Get currency rate conversion from EbetLab
   */
  static getCurrencyRateConversion = asyncHandler(async (req: Request, res: Response) => {
    // Extract currency from route parameters
    const { currency } = req.params;

    try {
      console.log(`💱 Fetching currency rate conversion - currency: ${currency}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate currency parameter
      if (!currency) {
        throw new ValidationError('currency parameter is required');
      }

      // Validate currency format (should be 3-4 character currency code)
      if (currency.length < 2 || currency.length > 10) {
        throw new ValidationError('currency must be a valid currency code');
      }

      // Use EbetLab API client singleton and make request
      const currencyRequest = new CurrencyRateConversionListRequest({
        currency: currency.toUpperCase(),
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        currencyRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch currency rate conversion: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Currency rate conversion retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch currency rate conversion:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch currency rate conversion: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
