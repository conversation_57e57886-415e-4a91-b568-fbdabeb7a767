import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { CssSettingsListRequest } from '@/network/ebetlab/requests/css-settings/CssSettingsListRequest';
import { CssSettingsShowRequest } from '@/network/ebetlab/requests/css-settings/CssSettingsShowRequest';

export class CssSettingsController {
  /**
   * POST /operator/css-settings/index/:page/:limit - Get CSS settings list from EbetLab
   */
  static getCssSettingsList = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const { page, limit } = req.params;
    
    // Validate required parameters
    if (!page || !limit) {
      throw new ValidationError('Page and limit parameters are required');
    }

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || isNaN(limitNum)) {
      throw new ValidationError('Page and limit must be valid numbers');
    }

    // Log request body for debugging
    console.log('📤 CSS Settings List Request body:', JSON.stringify(req.body, null, 2));

    // Extract filter parameters from request body
    const {
      id,
      username,
      currency,
      operator_id,
      type,
      status_id,
      tx,
      from,
      to,
      sortBy,
      direction,
    } = req.body;

    // Extract authorization header
    const authorization = req.headers.authorization;
    if (!authorization) {
      throw new ValidationError('Authorization header is required');
    }

    // Create request
    const request = new CssSettingsListRequest({
      page: pageNum,
      limit: limitNum,
      id,
      username,
      currency,
      operator_id,
      type,
      status_id,
      tx,
      from,
      to,
      sortBy,
      direction,
    });

    // Make request to EbetLab
    const response = await ebetlabApiClient.makeAuthenticatedRequest(request, authorization);

    // Return response
    const apiResponse: ApiResponse<any> = {
      success: response.success,
      data: response.success ? response.data : undefined,
      message: response.success ? undefined : response.message,
    };

    res.json(apiResponse);
  });

  /**
   * POST /operator/css-settings/show/:id - Get individual CSS setting from EbetLab
   */
  static getCssSettingById = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const { id } = req.params;
    
    // Validate required parameters
    if (!id) {
      throw new ValidationError('ID parameter is required');
    }

    // Log request body for debugging
    console.log('📤 CSS Settings Show Request body:', JSON.stringify(req.body, null, 2));

    // Extract authorization header
    const authorization = req.headers.authorization;
    if (!authorization) {
      throw new ValidationError('Authorization header is required');
    }

    // Create request
    const request = new CssSettingsShowRequest({
      id,
    });

    // Make request to EbetLab
    const response = await ebetlabApiClient.makeAuthenticatedRequest(request, authorization);

    // Return response
    const apiResponse: ApiResponse<any> = {
      success: response.success,
      data: response.success ? response.data : undefined,
      message: response.success ? undefined : response.message,
    };

    res.json(apiResponse);
  });


}
