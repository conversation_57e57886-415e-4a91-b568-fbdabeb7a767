import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { BalanceCorrectionRuleListRequest } from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleListRequest';
import { BalanceCorrectionRuleClaimListByIdRequest } from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleClaimListByIdRequest';
import { BalanceCorrectionRuleGetRequest } from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleGetRequest';
import { BalanceCorrectionRuleCancellationCreateRequest } from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleCancellationCreateRequest';
import { BalanceCorrectionRuleDeleteRequest } from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleDeleteRequest';
import {
  BalanceCorrectionRuleCreateRequest,
  BalanceCorrectionRuleCreateRequestOptions,
} from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleCreateRequest';
import {
  BalanceCorrectionRuleUpdateRequest,
  BalanceCorrectionRuleUpdateRequestOptions,
} from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleUpdateRequest';
import { BalanceCorrectionRuleDownloadsCreateRequest } from '@/network/ebetlab/requests/balance-correction-rule/BalanceCorrectionRuleDownloadsCreateRequest';

export class BalanceCorrectionRuleController {
  /**
   * GET /balance-correction-rules - Get balance correction rules from EbetLab
   */
  static listBalanceCorrectionRules = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`💰 Fetching balance correction rules - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const ruleRequest = new BalanceCorrectionRuleListRequest({
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(ruleRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch balance correction rules: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Balance correction rules retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch balance correction rules:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch balance correction rules: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /balance-correction-rules/:id/claims - Get claims for a specific balance correction rule
   */
  static getBalanceCorrectionRuleClaims = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters and query parameters
    const { id } = req.params;
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`💰 Fetching balance correction rule claims - id: ${id}, page: ${page}, limit: ${limit}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const claimRequest = new BalanceCorrectionRuleClaimListByIdRequest({
        id: id as string,
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(claimRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(
          `Failed to fetch balance correction rule claims: ${result.message || 'Unknown error'}`,
        );
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Balance correction rule claims retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch balance correction rule claims:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch balance correction rule claims: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /balance-correction-rules/:id - Get a specific balance correction rule by ID
   */
  static getBalanceCorrectionRuleById = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`💰 Fetching balance correction rule by ID - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const ruleId = parseInt(id);
      if (isNaN(ruleId) || ruleId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const ruleRequest = new BalanceCorrectionRuleGetRequest({
        id: ruleId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(ruleRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch balance correction rule: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Balance correction rule retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch balance correction rule by ID:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch balance correction rule: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /balance-correction-rules/:id/cancellations - Cancel a specific balance correction rule
   */
  static cancelBalanceCorrectionRule = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🚫 Cancelling balance correction rule - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const ruleId = parseInt(id);
      if (isNaN(ruleId) || ruleId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const cancellationRequest = new BalanceCorrectionRuleCancellationCreateRequest({
        id: ruleId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        cancellationRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to cancel balance correction rule: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Balance correction rule cancelled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to cancel balance correction rule:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to cancel balance correction rule: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * DELETE /balance-correction-rules/:id - Delete a specific balance correction rule
   */
  static deleteBalanceCorrectionRule = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🗑️ Deleting balance correction rule - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const ruleId = parseInt(id);
      if (isNaN(ruleId) || ruleId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const deleteRequest = new BalanceCorrectionRuleDeleteRequest({
        id: ruleId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(deleteRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to delete balance correction rule: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Balance correction rule deleted successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete balance correction rule:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete balance correction rule: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /balance-correction-rules - Create a new balance correction rule
   */
  static createBalanceCorrectionRule = asyncHandler(
    async (req: Request<any, any, BalanceCorrectionRuleCreateRequestOptions>, res: Response) => {
      try {
        console.log('💰 Creating balance correction rule');
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate required fields
        const requiredFields: (keyof BalanceCorrectionRuleCreateRequestOptions)[] = [
          'wager_percentage',
          'bonus_wager_percentage',
          'reference_tag',
          'max_usd_amount',
          'amount_percentage',
          'deposit_order',
          'ref_code',
          'range',
          'name',
          'from',
          'to',
          'limits',
          'bonus_tag',
          'bonus_tag_time_range',
          'redeem_code',
          'currencies',
          'type',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Use EbetLab API client singleton and make request
        const createRequest = new BalanceCorrectionRuleCreateRequest(req.body);

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          createRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to create balance correction rule: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Balance correction rule created successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(201).json(response);
      } catch (error) {
        console.error('❌ Failed to create balance correction rule:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to create balance correction rule: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * PATCH /balance-correction-rules/:id - Update a specific balance correction rule
   */
  static updateBalanceCorrectionRule = asyncHandler(
    async (req: Request<{ id: string }, any, BalanceCorrectionRuleUpdateRequestOptions>, res: Response) => {
      // Extract ID from route parameters
      const { id } = req.params;

      try {
        console.log(`💰 Updating balance correction rule - id: ${id}`);
        console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate ID parameter
        if (!id) {
          throw new ValidationError('id parameter is required');
        }

        const ruleId = parseInt(id);
        if (isNaN(ruleId) || ruleId < 1) {
          throw new ValidationError('id must be a positive integer');
        }

        // Validate required fields
        const requiredFields: (keyof BalanceCorrectionRuleUpdateRequestOptions)[] = [
          'wager_percentage',
          'reference_tag',
          'max_usd_amount',
          'amount_percentage',
          'range',
          'name',
          'min_usd',
          'max_usd',
          'redeem_code',
          'from',
          'to',
          'type',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Use EbetLab API client singleton and make request
        const updateRequest = new BalanceCorrectionRuleUpdateRequest({
          ...req.body,
          id: ruleId,
        });

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          updateRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to update balance correction rule: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Balance correction rule updated successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);
      } catch (error) {
        console.error('❌ Failed to update balance correction rule:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to update balance correction rule: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * POST /balance-correction-rules/:id/claims/downloads - Create download for balance correction rule claims
   */
  static createBalanceCorrectionRuleDownload = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`📥 Creating download for balance correction rule claims - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      // Use EbetLab API client singleton and make request
      const downloadRequest = new BalanceCorrectionRuleDownloadsCreateRequest({
        id: id as string,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        downloadRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to create download: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Download created successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to create download:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create download: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
