import { Request, Response } from 'express';
import { WeeklyLossbackBonusService } from '@/services/pronet/weeklyLossbackBonus.service';
import { asyncHandler } from '@/utils/asyncHandler';
import { ApiResponse } from '@/types/api';

export class WeeklyLossbackBonusController {
  static listWeeklyLossbackBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const isActive = req.query.isActive ? req.query.isActive === 'true' : undefined;

      const bonuses = await new WeeklyLossbackBonusService().list({
        isActive,
        page,
        limit,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Weekly lossback bonuses retrieved successfully',
        data: bonuses,
      };

      res.json(response);
    } catch (error: any) {
      console.error('Error listing weekly lossback bonuses:', error);

      const response: ApiResponse<null> = {
        success: false,
        message: error.message || 'Failed to list weekly lossback bonuses',
        data: null,
      };

      res.status(500).json(response);

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createWeeklyLossbackBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new WeeklyLossbackBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Weekly lossback bonus created successfully',
        data: bonus,
      };

      res.status(201).json(response);
    } catch (error: any) {
      console.error('Error creating weekly lossback bonus:', error);

      const response: ApiResponse<null> = {
        success: false,
        message: error.message || 'Failed to create weekly lossback bonus',
        data: null,
      };

      res.status(500).json(response);

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getWeeklyLossbackBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const bonus = await new WeeklyLossbackBonusService().findById(Number(id));

      if (!bonus) {
        const response: ApiResponse<null> = {
          success: false,
          message: 'Weekly lossback bonus not found',
          data: null,
        };

        return res.status(404).json(response);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Weekly lossback bonus retrieved successfully',
        data: bonus,
      };

      res.json(response);
    } catch (error: any) {
      console.error('Error getting weekly lossback bonus:', error);

      const response: ApiResponse<null> = {
        success: false,
        message: error.message || 'Failed to get weekly lossback bonus',
        data: null,
      };

      res.status(500).json(response);

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listWeeklyLossbackBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const isActive = req.query.isActive ? req.query.isActive === 'true' : undefined;

      const templates = await new WeeklyLossbackBonusService().listTemplates({
        isActive,
        page,
        limit,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Weekly lossback bonus templates retrieved successfully',
        data: templates,
      };

      res.json(response);
    } catch (error: any) {
      console.error('Error listing weekly lossback bonus templates:', error);

      const response: ApiResponse<null> = {
        success: false,
        message: error.message || 'Failed to list weekly lossback bonus templates',
        data: null,
      };

      res.status(500).json(response);

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createWeeklyLossbackBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new WeeklyLossbackBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Weekly lossback bonus template created successfully',
        data: template,
      };

      res.status(201).json(response);
    } catch (error: any) {
      console.error('Error creating weekly lossback bonus template:', error);

      const response: ApiResponse<null> = {
        success: false,
        message: error.message || 'Failed to create weekly lossback bonus template',
        data: null,
      };

      res.status(500).json(response);

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getWeeklyLossbackBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const template = await new WeeklyLossbackBonusService().findTemplateById(Number(id));

      if (!template) {
        const response: ApiResponse<null> = {
          success: false,
          message: 'Weekly lossback bonus template not found',
          data: null,
        };

        return res.status(404).json(response);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Weekly lossback bonus template retrieved successfully',
        data: template,
      };

      res.json(response);
    } catch (error: any) {
      console.error('Error getting weekly lossback bonus template:', error);

      const response: ApiResponse<null> = {
        success: false,
        message: error.message || 'Failed to get weekly lossback bonus template',
        data: null,
      };

      res.status(500).json(response);

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
