import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/utils/asyncHandler';
import { CashBonusService } from '@/services/pronet/cashBonus.service';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { getRabbitMQChannel } from '@/services/thirdparty/rabbitmq';

export class PronetCashBonusController {
  static listCashBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new CashBonusService().list({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch cash bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch cash bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getCashBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new CashBonusService().findById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonus retrieved successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch cash bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch cash bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createCashBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new CashBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonus created successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create cash bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create cash bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listCashBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await new CashBonusService().listTemplates({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonus templates retrieved successfully',
        data: templates,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch cash bonus templates:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch cash bonus templates: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getCashBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new CashBonusService().findTemplateById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonus template retrieved successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch cash bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch cash bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createCashBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new CashBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonus template created successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create cash bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create cash bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createCashBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const cashBonusId = Number(req.params['id']);

      const [channel, closeChannel] = await getRabbitMQChannel('bonus-bulk-assignment');

      const job = await new CashBonusService().createBulkAssignmentJob({
        cashBonusId,
        externalCustomerIds: req.body.externalCustomerIds,
      });

      for (const target of job.targets) {
        const payload = {
          jobId: job.id,
          targetId: target.id,
        };

        if (channel.sendToQueue('bonus-bulk-assignment', Buffer.from(JSON.stringify(payload))) === false) {
          throw new Error('Failed to send message to RabbitMQ');
        }
      }

      await closeChannel();

      const response: ApiResponse<any> = {
        success: true,
        message: 'Cash bonus bulk assignment job created successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create cash bonus bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create cash bonus bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
