import { BonusService } from '@/services/pronet/bonus.service';
import { CustomerService } from '@/services/pronet/customer.service';
import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { asyncHandler } from '@/utils/asyncHandler';
import { Request, Response } from 'express';

export class PronetBonusController {
  static assignBonusToTarget = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { targetId } = req.params;

      const target = await new BonusService().getBonusBulkAssignmentJobTarget(Number(targetId));
      if (!target) {
        throw new Error('Target not found');
      }

      const result = await PgDagurService.getCustomerDetails(target.externalCustomerId.toString());

      const customer = await new CustomerService().put({
        externalId: result.basic.customerId,
        code: result.basic.customerCode,
        username: result.basic.username,
      });

      const claim = await new BonusService().claimForBulkAssignmentJobTarget(Number(targetId), customer.id);

      res.status(201).json({
        success: true,
        message: 'Bonus assigned successfully',
        data: claim,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('❌ Failed to assign bonus:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to assign bonus',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });
}
