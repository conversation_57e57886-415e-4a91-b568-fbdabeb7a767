import { Request, Response } from 'express';
import { async<PERSON><PERSON><PERSON> } from '@/utils/asyncHandler';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { CustomerService } from '@/services/pronet/customer.service';

export class PronetCustomerController {
  static create = asyncHandler(async (req: Request, res: Response) => {
    try {
      const customer = await new CustomerService().create(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Customer created successfully',
        data: customer,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create customer:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create customer: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
