import { pgDagurHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { Request, Response } from 'express';

export class AuthController {
  static loginWithDagur = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🔐 Pronet login attempt');

      const result = await pgDagurHttpClient.loginWithOtp(req.body.username, req.body.password, req.body.otpCode);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Login successful',
        data: {
          session: Buffer.from(JSON.stringify(result.cookies), 'utf8').toString('base64'),
        },
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Pronet login failed:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Pronet login failed: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
