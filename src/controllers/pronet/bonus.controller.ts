import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { CasinoFreespinBonusService } from '@/services/pronet/casinoFreespinBonus.service';
import { TrialBonusService } from '@/services/pronet/trialBonus.service';
import { HappyHoursBonusService } from '@/services/pronet/happyHoursBonus.service';
import { FreebetBonusService } from '@/services/pronet/freebetBonus.service';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { BonusService } from '@/services/pronet/bonus.service';
import { FREESPIN_BONUS_SCHEMAS } from '@/network/pg-ct/constants';
import { FreespinBonusPreloadRequest } from '@/network/pg-ct/requests/freespinBonus/FreespinBonusPreloadRequest';
import { pgCasinoTraderAdminHttpClient } from '@/network/pg-ct/PGCasinoTraderApiClient';
import * as FreespinBonusCreate from '@/network/pg-ct/requests/freespinBonus/create';
import { getRabbitMQChannel } from '@/services/thirdparty/rabbitmq';
import { pgWebHttpClient } from '@/network/pg-web/PGWebHttpClient';
import { CustomerGetRequest } from '@/network/pg-web/requests/customer/CustomerGetRequest';
import { CustomerService } from '@/services/pronet/customer.service';
import { BonusPreloadRequest } from '@/network/pg-ct/requests/bonus/BonusPreloadRequest';
import { BonusListRequest, BonusListResponse } from '@/network/pg-ct/requests/bonus/BonusListRequest';

export class PronetBonusController {
  static listFreespinBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new CasinoFreespinBonusService().list({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createFreespinBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new CasinoFreespinBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino freespin bonus created successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create casino freespin bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create casino freespin bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getFreespinBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new CasinoFreespinBonusService().findById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino freespin bonus retrieved successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch casino freespin bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch casino freespin bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listFreespinBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await new CasinoFreespinBonusService().listTemplates({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus templates retrieved successfully',
        data: templates,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus templates:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus templates: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createFreespinBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new CasinoFreespinBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino freespin bonus template created successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create casino freespin bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create casino freespin bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getFreespinBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new CasinoFreespinBonusService().findTemplateById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino freespin bonus template retrieved successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch casino freespin bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch casino freespin bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static updateBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusId = Number(req.params['id']);
      const bonus = await new BonusService().toggleActive(bonusId, req.body.isActive as boolean);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus updated successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to update bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to update bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static patchBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusId = Number(req.params['id']);
      const { description, reward, isActive } = req.body;

      const bonus = await new BonusService().update(bonusId, {
        description,
        reward,
        isActive,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus updated successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to patch bonus:', error);

      if (error instanceof Error) {
        // Check if it's a validation error
        if (error.message.includes('No fields provided to update') || error.message.includes('Bonus not found')) {
          throw new ValidationError(error.message);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static softDeleteBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusId = Number(req.params['id']);
      const bonus = await new BonusService().softDelete(bonusId);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus deleted successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static softDeleteBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templateId = Number(req.params['id']);
      const template = await new BonusService().softDeleteTemplate(templateId);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus template deleted successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static searchBonusClaims = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusId = Number(req.params['id']);
      const claims = await new BonusService().searchClaims({
        ...req.query,
        bonusId,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus claims retrieved successfully',
        data: claims,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus claims:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus claims: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createBonusPromocode = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id: bonusId } = req.params;

      const promocode = await new BonusService().createPromocode({
        ...req.body,
        bonusId: Number(bonusId),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus promocode created successfully',
        data: promocode,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create bonus promocode:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create bonus promocode: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static activateBonusPromocode = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { code } = req.params;

      const { referer, origin, authorization } = req.headers;
      const url = referer || origin;
      if (!url || !authorization) {
        throw new Error('Referer or Origin, and Authorization headers are required');
      }

      const result = await pgWebHttpClient.makeAuthorizedRequest(new CustomerGetRequest(), url, authorization);
      if (!result.success) {
        throw new Error(result.error);
      }

      const customer = await new CustomerService().put({
        externalId: result.data.customerId,
        code: result.data.code,
        username: result.data.username,
      });

      try {
        const activation = await new BonusService().activatePromocode(code as string, customer.id);

        const response: ApiResponse<any> = {
          success: true,
          message: 'Bonus promocode activated successfully',
          data: activation,
          timestamp: new Date().toISOString(),
        };

        res.status(201).json(response);
      } catch (error) {
        if ((error as Error).message.includes('duplicate')) {
          throw new Error('Promocode already activated');
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('❌ Failed to activate bonus promocode:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to activate bonus promocode: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static searchBonusPromocodes = asyncHandler(async (req: Request, res: Response) => {
    try {
      const promocodes = await new BonusService().searchPromocodes({
        ...req.query,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus promocodes retrieved successfully',
        data: promocodes,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus promocodes:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus promocodes: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static toggleBonusPromocode = asyncHandler(async (req: Request, res: Response) => {
    try {
      const promocodeId = Number(req.params['id']);
      const promocode = await new BonusService().togglePromocode(promocodeId, req.body.isActive as boolean);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus promocode updated successfully',
        data: promocode,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to update bonus promocode:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to update bonus promocode: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static softDeleteBonusPromocode = asyncHandler(async (req: Request, res: Response) => {
    try {
      const promocodeId = Number(req.params['id']);
      const promocode = await new BonusService().softDeletePromocode(promocodeId);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus promocode deleted successfully',
        data: promocode,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete bonus promocode:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete bonus promocode: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static searchBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new BonusService().searchBonuses({
        ...req.query,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static searchBonusPromocodeActivations = asyncHandler(async (req: Request, res: Response) => {
    try {
      const activations = await new BonusService().searchPromocodeActivations({
        ...req.query,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus promocode activations retrieved successfully',
        data: activations,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus promocode activations:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus promocode activations: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static claimBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id: bonusId } = req.params;

      const { referer, origin, authorization } = req.headers;
      const url = referer || origin;
      if (!url || !authorization) {
        throw new Error('Referer or Origin, and Authorization headers are required');
      }

      const result = await pgWebHttpClient.makeAuthorizedRequest(new CustomerGetRequest(), url, authorization);
      if (!result.success) {
        throw new Error(result.error);
      }

      const customer = await new CustomerService().put({
        externalId: result.data.customerId,
        code: result.data.code,
        username: result.data.username,
      });

      const claim = await new BonusService().claim({
        bonusId: Number(bonusId),
        customerId: customer.id,
        source: 'manual',
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus claimed successfully',
        data: claim,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to claim bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to claim bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listFreespinBonusProviders = asyncHandler(async (req: Request, res: Response) => {
    try {
      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin bonus providers retrieved successfully',
        data: FREESPIN_BONUS_SCHEMAS,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freespin bonus providers:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freespin bonus providers: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listFreespinBonusGames = asyncHandler(async (req: Request, res: Response) => {
    try {
      const providerId = req.params['id'];
      const { page, limit, name } = req.query;

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: providerId as string,
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      const gameListRequest = new FreespinBonusCreate.FreespinBonusGameListRequest({
        providerId: Number(providerId),
        name: name as string,
        page: Number(page || '1'),
        limit: Number(limit || '20'),
        javax: enterCreateModeResult.data,
      });
      const gameListResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        gameListRequest,
        preloadResult.viewState || '',
      );
      if (!gameListResult.success) {
        throw new Error(gameListResult.message);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin bonus games retrieved successfully',
        data: gameListResult.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freespin bonus games:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freespin bonus games: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listFreespinBetAmounts = asyncHandler(async (req: Request, res: Response) => {
    try {
      const currencyId = Number(req.query['currencyId'] as string);
      const providerId = Number(req.params['id']);
      const gameIds = (Array.isArray(req.query['gameId']) ? req.query['gameId'] : [req.query['gameId']]).map(Number);

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: providerId.toString(),
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      if (Number.isNaN(currencyId) === false) {
        const changeCurrencyCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          field: 'cmbCurrency',
          value: currencyId.toString(),
        });
        const changeCurrencyCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          changeCurrencyCreateFormRequest,
          preloadResult.viewState || '',
        );
        if (!changeCurrencyCreateFormResult.success) {
          throw new Error(changeCurrencyCreateFormResult.message);
        }
      }

      const selectGameRequests = gameIds.map(
        (gameId) =>
          new FreespinBonusCreate.FreespinBonusSelectGameRequest({
            javax: {
              ...preloadResult.data,
              ...enterCreateModeResult.data,
            },
            gameId,
            gameIds,
          }),
      );
      let betAmounts: number[] = [];
      for (const selectGameRequest of selectGameRequests) {
        const selectGameResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          selectGameRequest,
          preloadResult.viewState || '',
        );
        if (!selectGameResult.success) {
          throw new Error(selectGameResult.message);
        }

        betAmounts = selectGameResult.data;
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin bet amounts retrieved successfully',
        data: betAmounts,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freespin bet amounts:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freespin bet amounts: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listFreespinCurrencies = asyncHandler(async (req: Request, res: Response) => {
    try {
      const providerId = Number(req.params['id']);

      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: providerId.toString(),
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin bet amounts retrieved successfully',
        data: changeVendorCreateFormResult.data.currencies,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freespin bet amounts:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freespin bet amounts: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createFreespinBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id: casinoFreespinBonusId } = req.params;

      const [channel, closeChannel] = await getRabbitMQChannel('bonus-bulk-assignment');

      const job = await new CasinoFreespinBonusService().createBulkAssignmentJob({
        ...req.body,
        casinoFreespinBonusId: Number(casinoFreespinBonusId),
      });

      for (const target of job.targets) {
        const payload = {
          jobId: job.id,
          targetId: target.id,
        };

        if (channel.sendToQueue('bonus-bulk-assignment', Buffer.from(JSON.stringify(payload))) === false) {
          throw new Error('Failed to send message to RabbitMQ');
        }
      }

      await closeChannel();

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bulk assignment job created successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listBonusBulkAssignmentJobs = asyncHandler(async (req: Request, res: Response) => {
    try {
      const jobs = await new BonusService().listBulkAssignmentJobs({
        ...req.query,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bulk assignment jobs retrieved successfully',
        data: jobs,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bulk assignment jobs:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bulk assignment jobs: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const job = await new BonusService().getBulkAssignmentJob(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bulk assignment job retrieved successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static cancelBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const job = await new BonusService().cancelBulkAssignmentJob(Number(id));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bulk assignment job cancelled successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to cancel bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to cancel bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static retryBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const job = await new BonusService().retryBulkAssignmentJob(Number(id));

      const [channel, closeChannel] = await getRabbitMQChannel('bonus-bulk-assignment');

      for (const target of job.targets) {
        if (target.status !== 'pending') {
          continue;
        }

        const payload = {
          jobId: job.id,
          targetId: target.id,
        };

        if (channel.sendToQueue('bonus-bulk-assignment', Buffer.from(JSON.stringify(payload))) === false) {
          throw new Error('Failed to send message to RabbitMQ');
        }
      }

      await closeChannel();

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bulk assignment job retried successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to retry bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to retry bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  // Trial Bonus endpoints
  static listTrialBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new TrialBonusService().list({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch trial bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch trial bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listTrialBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await new TrialBonusService().listTemplates({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus templates retrieved successfully',
        data: templates,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch trial bonus templates:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch trial bonus templates: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getTrialBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusId = Number(req.params['id']);
      const bonus = await new TrialBonusService().findById(bonusId);

      if (!bonus) {
        throw new ValidationError('Trial bonus not found');
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus retrieved successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch trial bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch trial bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getTrialBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templateId = Number(req.params['id']);
      const template = await new TrialBonusService().findTemplateById(templateId);

      if (!template) {
        throw new ValidationError('Trial bonus template not found');
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus template retrieved successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch trial bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch trial bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createTrialBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new TrialBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus created successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create trial bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create trial bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createTrialBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new TrialBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus template created successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create trial bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create trial bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createTrialBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const trialBonusId = Number(req.params['id']);

      const [channel, closeChannel] = await getRabbitMQChannel('bonus-bulk-assignment');

      const job = await new TrialBonusService().createBulkAssignmentJob({
        trialBonusId,
        externalCustomerIds: req.body.externalCustomerIds,
      });

      for (const target of job.targets) {
        const payload = {
          jobId: job.id,
          targetId: target.id,
        };

        if (channel.sendToQueue('bonus-bulk-assignment', Buffer.from(JSON.stringify(payload))) === false) {
          throw new Error('Failed to send message to RabbitMQ');
        }
      }

      await closeChannel();

      const response: ApiResponse<any> = {
        success: true,
        message: 'Trial bonus bulk assignment job created successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create trial bonus bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create trial bonus bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listExternalTrialBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { status, cursor, page, limit } = req.query;

      console.log('🎁 Fetching bonuses from PG Casino Trader');

      const preloadRequest = new BonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new BonusListRequest({
          status: (status as string) || 'active',
          javax: preloadResult.data,
          cursor: (cursor as string) || preloadResult.viewState || '',
          page: Number(page || '1'),
          limit: Number(limit || '20'),
        }),
        (cursor as string) || preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<BonusListResponse['items']> = {
        success: true,
        message: 'Bonuses retrieved successfully',
        data: result.data.items.filter((d) => d.type === 'Manual Bonus'),
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonuses:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch bonuses',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Happy Hours Bonus endpoints
  static listHappyHoursBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new HappyHoursBonusService().list({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch happy hours bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch happy hours bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listHappyHoursBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await new HappyHoursBonusService().listTemplates({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonus templates retrieved successfully',
        data: templates,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch happy hours bonus templates:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch happy hours bonus templates: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getHappyHoursBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusId = Number(req.params['id']);
      const bonus = await new HappyHoursBonusService().findById(bonusId);

      if (!bonus) {
        throw new ValidationError('Happy hours bonus not found');
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonus retrieved successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch happy hours bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch happy hours bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getHappyHoursBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templateId = Number(req.params['id']);
      const template = await new HappyHoursBonusService().findTemplateById(templateId);

      if (!template) {
        throw new ValidationError('Happy hours bonus template not found');
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonus template retrieved successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch happy hours bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch happy hours bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createHappyHoursBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new HappyHoursBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonus created successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create happy hours bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create happy hours bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createHappyHoursBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new HappyHoursBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonus template created successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create happy hours bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create happy hours bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createHappyHoursBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const happyHoursBonusId = Number(req.params['id']);

      const [channel, closeChannel] = await getRabbitMQChannel('bonus-bulk-assignment');

      const job = await new HappyHoursBonusService().createBulkAssignmentJob({
        happyHoursBonusId,
        externalCustomerIds: req.body.externalCustomerIds,
      });

      for (const target of job.targets) {
        const payload = {
          jobId: job.id,
          targetId: target.id,
        };

        if (channel.sendToQueue('bonus-bulk-assignment', Buffer.from(JSON.stringify(payload))) === false) {
          throw new Error('Failed to send message to RabbitMQ');
        }
      }

      await closeChannel();

      const response: ApiResponse<any> = {
        success: true,
        message: 'Happy hours bonus bulk assignment job created successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create happy hours bonus bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create happy hours bonus bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listExternalHappyHoursBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { status, cursor, page, limit } = req.query;

      console.log('🎁 Fetching happy hours bonuses from PG Casino Trader');

      const preloadRequest = new BonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new BonusListRequest({
          status: (status as string) || 'active',
          javax: preloadResult.data,
          cursor: (cursor as string) || preloadResult.viewState || '',
          page: Number(page || '1'),
          limit: Number(limit || '20'),
        }),
        (cursor as string) || preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<BonusListResponse['items']> = {
        success: true,
        message: 'Happy hours bonuses retrieved successfully',
        data: result.data.items.filter((d) => d.type === 'Manual Bonus'),
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to fetch happy hours bonuses:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch happy hours bonuses',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Freebet Bonus endpoints
  static listFreebetBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new FreebetBonusService().list({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freebet bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freebet bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getFreebetBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new FreebetBonusService().findById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonus retrieved successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freebet bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freebet bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listFreebetBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await new FreebetBonusService().listTemplates({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonus templates retrieved successfully',
        data: templates,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freebet bonus templates:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freebet bonus templates: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getFreebetBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new FreebetBonusService().findTemplateById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonus template retrieved successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freebet bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freebet bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createFreebetBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new FreebetBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonus created successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create freebet bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create freebet bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createFreebetBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new FreebetBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonus template created successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create freebet bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create freebet bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createFreebetBonusBulkAssignmentJob = asyncHandler(async (req: Request, res: Response) => {
    try {
      const [channel, closeChannel] = await getRabbitMQChannel('bonus-bulk-assignment');

      const job = await new FreebetBonusService().createBulkAssignmentJob({
        freebetBonusId: Number(req.params['id']),
        externalCustomerIds: req.body.externalCustomerIds,
      });

      for (const target of job.targets) {
        const payload = {
          jobId: job.id,
          targetId: target.id,
        };

        if (channel.sendToQueue('bonus-bulk-assignment', Buffer.from(JSON.stringify(payload))) === false) {
          throw new Error('Failed to send message to RabbitMQ');
        }
      }

      await closeChannel();

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freebet bonus bulk assignment job created successfully',
        data: job,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create freebet bonus bulk assignment job:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create freebet bonus bulk assignment job: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listExternalFreebetBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { status, cursor, page, limit } = req.query;

      console.log('🎁 Fetching freebet bonuses from PG Casino Trader');

      const preloadRequest = new BonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new BonusListRequest({
          status: (status as string) || 'active',
          javax: preloadResult.data,
          cursor: (cursor as string) || preloadResult.viewState || '',
          page: Number(page || '1'),
          limit: Number(limit || '20'),
        }),
        (cursor as string) || preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      const response: ApiResponse<BonusListResponse['items']> = {
        success: true,
        message: 'External freebet bonuses retrieved successfully',
        data: result.data.items.filter((d) => d.type === 'Manual Bonus'), // Same filter as trial bonuses
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      console.error('❌ Failed to fetch external freebet bonuses:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to fetch external freebet bonuses',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });
}
