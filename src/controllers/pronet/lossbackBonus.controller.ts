import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/utils/asyncHandler';
import { CasinoLossbackBonusService } from '@/services/pronet/casinoLossbackBonus.service';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';

export class PronetLossbackBonusController {
  static listLossbackBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonuses = await new CasinoLossbackBonusService().list({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Lossback bonuses retrieved successfully',
        data: bonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch lossback bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch lossback bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createLossbackBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new CasinoLossbackBonusService().create({
        ...req.body,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino lossback bonus created successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create casino lossback bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create casino lossback bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getLossbackBonus = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonus = await new CasinoLossbackBonusService().findById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino lossback bonus retrieved successfully',
        data: bonus,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch casino lossback bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch casino lossback bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listLossbackBonusTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await new CasinoLossbackBonusService().listTemplates({
        isActive: req.query['isActive'] ? req.query['isActive'] === 'true' : undefined,
        page: Number(req.query['page'] || '1'),
        limit: Number(req.query['limit'] || '20'),
      });

      const response: ApiResponse<any> = {
        success: true,
        message: 'Lossback bonus templates retrieved successfully',
        data: templates,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch lossback bonus templates:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch lossback bonus templates: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static createLossbackBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new CasinoLossbackBonusService().createTemplate(req.body);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino lossback bonus template created successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create casino lossback bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create casino lossback bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getLossbackBonusTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      const template = await new CasinoLossbackBonusService().findTemplateById(Number(req.params['id']));

      const response: ApiResponse<any> = {
        success: true,
        message: 'Casino lossback bonus template retrieved successfully',
        data: template,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch casino lossback bonus template:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch casino lossback bonus template: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}