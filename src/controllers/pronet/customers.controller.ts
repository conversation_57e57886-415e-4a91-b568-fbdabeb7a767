import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/utils/asyncHandler';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { CustomerService, ListCustomersOptions } from '@/services/pronet/customer.service';
import { PgDagurService } from '@/services/thirdparty/pg-dagur';

export class PronetCustomersController {
  /**
   * GET /customers - List all customers with pagination
   *
   * Query parameters:
   * - page: number (default: 1)
   * - limit: number (default: 20, max: 100)
   * - sortBy: 'id' | 'username' | 'code' | 'createdAt' | 'updatedAt' (default: 'createdAt')
   * - sortOrder: 'ASC' | 'DESC' (default: 'DESC')
   */
  static list = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📋 Listing customers with query params:', req.query);

      // Extract and validate query parameters
      const page = parseInt(req.query['page'] as string) || 1;
      const limit = parseInt(req.query['limit'] as string) || 20;
      const sortBy = (req.query['sortBy'] as ListCustomersOptions['sortBy']) || 'createdAt';
      const sortOrder = (req.query['sortOrder'] as ListCustomersOptions['sortOrder']) || 'DESC';

      // Validate parameters
      if (page < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (limit < 1 || limit > 100) {
        throw new ValidationError('limit must be between 1 and 100');
      }

      const validSortFields = ['id', 'username', 'code', 'createdAt', 'updatedAt'];
      if (!validSortFields.includes(sortBy)) {
        throw new ValidationError(`sortBy must be one of: ${validSortFields.join(', ')}`);
      }

      const validSortOrders = ['ASC', 'DESC'];
      if (!validSortOrders.includes(sortOrder)) {
        throw new ValidationError('sortOrder must be either ASC or DESC');
      }

      // Get customers from service
      const customerService = new CustomerService();
      const result = await customerService.list({
        page,
        limit,
        sortBy,
        sortOrder,
      });

      console.log(`✅ Retrieved ${result.items.length} customers (${result.total} total)`);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Customers retrieved successfully',
        data: {
          customers: result.items,
          pagination: {
            page: result.page,
            limit: result.limit,
            total: result.total,
            totalPages: result.totalPages,
            hasNext: result.page < result.totalPages,
            hasPrev: result.page > 1,
          },
        },
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to list customers:', error);

      if (error instanceof ValidationError) {
        throw error;
      }

      if (error instanceof Error) {
        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to retrieve customers: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /customers/:id - Get detailed information for a specific customer
   *
   * Path parameters:
   * - id: number - The customer ID
   */
  static getById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const customerId = parseInt(req.params['id']);

      console.log(`🔍 Getting customer details for ID: ${customerId}`);

      // Validate customer ID
      if (isNaN(customerId) || customerId < 1) {
        throw new ValidationError('Customer ID must be a positive integer');
      }

      // Get customer from service
      const customerService = new CustomerService();
      const customer = await customerService.findById(customerId);

      if (!customer) {
        return res.status(404).json({
          success: false,
          message: 'Customer not found',
          timestamp: new Date().toISOString(),
        });
      }

      console.log(`✅ Retrieved customer: ${customer.username} (ID: ${customer.id})`);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Customer retrieved successfully',
        data: customer,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to get customer:', error);

      if (error instanceof ValidationError) {
        throw error;
      }

      if (error instanceof Error) {
        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to retrieve customer: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static getTotalBalances = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🔍 Getting total balances');

      // Get total balances from service
      const balances = await PgDagurService.getTotalBalances(new Date(req.query['endDate'] as string));

      console.log(`✅ Retrieved total balances`);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Total balances retrieved successfully',
        data: balances,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to get total balances:', error);

      if (error instanceof Error) {
        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to retrieve total balances: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
