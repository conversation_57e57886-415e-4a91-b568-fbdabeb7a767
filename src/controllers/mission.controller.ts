import { Request, Response } from 'express';
import { MissionService, CreateMissionDto, MissionQueryParams } from '@/services/mission.service';
import { MissionType } from '@/enums/shared';
import { asyncHandler } from '@/utils/asyncHandler';

const missionService = new MissionService();

export const createMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { name, missionType, reward, description, startDate, endDate, isActive, name_i18n, description_i18n }: CreateMissionDto = req.body;

  // Validation
  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Name is required and must be a non-empty string',
    });
  }

  if (!Object.values(MissionType).includes(missionType)) {
    return res.status(400).json({
      success: false,
      message: `Mission type must be one of: ${Object.values(MissionType).join(', ')}`,
    });
  }

  if (!reward || typeof reward !== 'number' || reward <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Reward is required and must be a positive number',
    });
  }

  if (!description || typeof description !== 'string' || description.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Description is required and must be a non-empty string',
    });
  }

  if (!startDate || typeof startDate !== 'number' || startDate <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Start date is required and must be a positive timestamp',
    });
  }

  if (!endDate || typeof endDate !== 'number' || endDate <= 0) {
    return res.status(400).json({
      success: false,
      message: 'End date is required and must be a positive timestamp',
    });
  }

  if (endDate <= startDate) {
    return res.status(400).json({
      success: false,
      message: 'End date must be after start date',
    });
  }

  try {
    const mission = await missionService.createMission({
      name: name.trim(),
      missionType,
      reward,
      description: description.trim(),
      startDate,
      endDate,
      isActive: isActive !== undefined ? isActive : true,
      name_i18n: name_i18n || {},
      description_i18n: description_i18n || {},
    });

    res.status(201).json({
      success: true,
      message: 'Mission created successfully',
      data: mission,
    });
  } catch (error) {
    console.error('Error creating mission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mission',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissions = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching missions list with query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Parse and validate query parameters
  const queryParams: MissionQueryParams = {};

  // Pagination
  if (req.query['page']) {
    const page = parseInt(req.query['page'] as string);
    if (isNaN(page) || page < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page must be a positive integer',
      });
    }
    queryParams.page = page;
  }

  if (req.query['limit']) {
    const limit = parseInt(req.query['limit'] as string);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
      });
    }
    queryParams.limit = limit;
  }

  // Sorting
  if (req.query['sortBy']) {
    const validSortFields = ['id', 'name', 'missionType', 'reward', 'startDate', 'endDate', 'isActive', 'createdAt', 'updatedAt'];
    const sortBy = req.query['sortBy'] as string;
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({
        success: false,
        message: `sortBy must be one of: ${validSortFields.join(', ')}`,
      });
    }
    queryParams.sortBy = sortBy as any;
  }

  if (req.query['sortOrder']) {
    const sortOrder = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC', 'asc', 'desc'].includes(sortOrder)) {
      return res.status(400).json({
        success: false,
        message: 'sortOrder must be either "ASC", "DESC", "asc", or "desc"',
      });
    }
    queryParams.sortOrder = sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filters Actually Used in Component
  if (req.query['name']) {
    queryParams.name = req.query['name'] as string;
  }

  if (req.query['description']) {
    queryParams.description = req.query['description'] as string;
  }

  if (req.query['missionType']) {
    const missionType = req.query['missionType'] as string;
    if (!Object.values(MissionType).includes(missionType as MissionType)) {
      return res.status(400).json({
        success: false,
        message: `missionType must be one of: ${Object.values(MissionType).join(', ')}`,
      });
    }
    queryParams.missionType = missionType as MissionType;
  }

  if (req.query['status']) {
    const status = req.query['status'] as string;
    if (!['active', 'upcoming', 'expired'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'status must be one of: active, upcoming, expired',
      });
    }
    queryParams.status = status as 'active' | 'upcoming' | 'expired';
  }

  if (req.query['minReward']) {
    const minReward = parseInt(req.query['minReward'] as string);
    if (isNaN(minReward) || minReward < 0) {
      return res.status(400).json({
        success: false,
        message: 'minReward must be a non-negative integer',
      });
    }
    queryParams.minReward = minReward;
  }

  if (req.query['maxReward']) {
    const maxReward = parseInt(req.query['maxReward'] as string);
    if (isNaN(maxReward) || maxReward < 0) {
      return res.status(400).json({
        success: false,
        message: 'maxReward must be a non-negative integer',
      });
    }
    queryParams.maxReward = maxReward;
  }

  if (req.query['isActive']) {
    const isActiveStr = req.query['isActive'] as string;
    if (!['true', 'false'].includes(isActiveStr.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'isActive must be either "true" or "false"',
      });
    }
    queryParams.isActive = isActiveStr.toLowerCase() === 'true';
  }

  // Additional Parameters in Interface (but not used in UI)
  if (req.query['reward']) {
    const reward = parseInt(req.query['reward'] as string);
    if (isNaN(reward) || reward < 0) {
      return res.status(400).json({
        success: false,
        message: 'reward must be a non-negative integer',
      });
    }
    queryParams.reward = reward;
  }

  if (req.query['startDateFrom']) {
    const startDateFrom = parseInt(req.query['startDateFrom'] as string);
    if (isNaN(startDateFrom) || startDateFrom < 0) {
      return res.status(400).json({
        success: false,
        message: 'startDateFrom must be a valid timestamp',
      });
    }
    queryParams.startDateFrom = startDateFrom;
  }

  if (req.query['startDateTo']) {
    const startDateTo = parseInt(req.query['startDateTo'] as string);
    if (isNaN(startDateTo) || startDateTo < 0) {
      return res.status(400).json({
        success: false,
        message: 'startDateTo must be a valid timestamp',
      });
    }
    queryParams.startDateTo = startDateTo;
  }

  if (req.query['endDateFrom']) {
    const endDateFrom = parseInt(req.query['endDateFrom'] as string);
    if (isNaN(endDateFrom) || endDateFrom < 0) {
      return res.status(400).json({
        success: false,
        message: 'endDateFrom must be a valid timestamp',
      });
    }
    queryParams.endDateFrom = endDateFrom;
  }

  if (req.query['endDateTo']) {
    const endDateTo = parseInt(req.query['endDateTo'] as string);
    if (isNaN(endDateTo) || endDateTo < 0) {
      return res.status(400).json({
        success: false,
        message: 'endDateTo must be a valid timestamp',
      });
    }
    queryParams.endDateTo = endDateTo;
  }

  if (req.query['search']) {
    queryParams.search = req.query['search'] as string;
  }

  try {
    const result = await missionService.findMissionsWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Missions retrieved successfully',
      data: result.missions,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching missions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch missions',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching mission by ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    const mission = await missionService.findMissionById(id);

    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission retrieved successfully',
      data: mission,
    });
  } catch (error) {
    console.error('Error fetching mission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch mission',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating mission ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  // Check if mission exists
  const existingMission = await missionService.findMissionById(id);
  if (!existingMission) {
    return res.status(404).json({
      success: false,
      message: 'Mission not found',
    });
  }

  const { name, missionType, reward, description, startDate, endDate, isActive, name_i18n, description_i18n } = req.body;

  // Validate only provided fields (partial update)
  const updateData: Partial<CreateMissionDto> = {};

  if (name !== undefined) {
    if (typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Name must be a non-empty string',
      });
    }
    updateData.name = name.trim();
  }

  if (missionType !== undefined) {
    if (!Object.values(MissionType).includes(missionType)) {
      return res.status(400).json({
        success: false,
        message: `Mission type must be one of: ${Object.values(MissionType).join(', ')}`,
      });
    }
    updateData.missionType = missionType;
  }

  if (reward !== undefined) {
    if (typeof reward !== 'number' || reward <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Reward must be a positive number',
      });
    }
    updateData.reward = reward;
  }

  if (description !== undefined) {
    if (typeof description !== 'string' || description.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Description must be a non-empty string',
      });
    }
    updateData.description = description.trim();
  }

  if (startDate !== undefined) {
    if (typeof startDate !== 'number' || startDate <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Start date must be a positive timestamp',
      });
    }
    updateData.startDate = startDate;
  }

  if (endDate !== undefined) {
    if (typeof endDate !== 'number' || endDate <= 0) {
      return res.status(400).json({
        success: false,
        message: 'End date must be a positive timestamp',
      });
    }
    updateData.endDate = endDate;
  }

  if (isActive !== undefined) {
    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isActive must be a boolean',
      });
    }
    updateData.isActive = isActive;
  }

  if (name_i18n !== undefined) {
    if (typeof name_i18n !== 'object' || name_i18n === null || Array.isArray(name_i18n)) {
      return res.status(400).json({
        success: false,
        message: 'name_i18n must be an object',
      });
    }
    updateData.name_i18n = name_i18n;
  }

  if (description_i18n !== undefined) {
    if (typeof description_i18n !== 'object' || description_i18n === null || Array.isArray(description_i18n)) {
      return res.status(400).json({
        success: false,
        message: 'description_i18n must be an object',
      });
    }
    updateData.description_i18n = description_i18n;
  }

  // Validate date range if both dates are provided or being updated
  const finalStartDate = updateData.startDate || existingMission.startDate;
  const finalEndDate = updateData.endDate || existingMission.endDate;

  if (finalEndDate <= finalStartDate) {
    return res.status(400).json({
      success: false,
      message: 'End date must be after start date',
    });
  }

  try {
    const updatedMission = await missionService.updateMission(id, updateData);

    if (!updatedMission) {
      return res.status(404).json({
        success: false,
        message: 'Mission not found after update',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission updated successfully',
      data: updatedMission,
    });
  } catch (error) {
    console.error('Error updating mission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mission',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting mission ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    // Check if mission exists before deletion
    const existingMission = await missionService.findMissionById(id);
    if (!existingMission) {
      return res.status(404).json({
        success: false,
        message: 'Mission not found',
      });
    }

    const deleted = await missionService.deleteMission(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Mission not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting mission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mission',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
