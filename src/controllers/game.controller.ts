import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient, adminEbetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { GameListByProviderRequest } from '@/network/ebetlab/requests/game/GameListByProviderRequest';
import { GameListRequest } from '@/network/ebetlab/requests/game/GameList';

export class GameController {
  /**
   * GET /games - Get games by provider from EbetLab
   */
  static getGamesByProvider = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { providers, type } = req.query;

    try {
      console.log(`🎮 Fetching games by provider - providers: ${providers}, type: ${type}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Validate required fields
      if (!providers) {
        throw new ValidationError('providers parameter is required');
      }

      if (!type) {
        throw new ValidationError('type parameter is required');
      }

      // Parse providers as array of integers
      let providerIds: number[];
      if (Array.isArray(providers)) {
        // Handle multiple providers: ?providers=1&providers=2
        providerIds = providers.map((p) => {
          const id = parseInt(p as string);
          if (isNaN(id)) {
            throw new ValidationError(`Invalid provider ID: ${p}`);
          }
          return id;
        });
      } else {
        // Handle single provider: ?providers=1
        const id = parseInt(providers as string);
        if (isNaN(id)) {
          throw new ValidationError(`Invalid provider ID: ${providers}`);
        }
        providerIds = [id];
      }

      // Validate type
      if (typeof type !== 'string') {
        throw new ValidationError('type must be a string');
      }

      // Use EbetLab API client singleton and make request
      const gameRequest = new GameListByProviderRequest({
        providers: providerIds,
        type: type,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(gameRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch games: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Games retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch games by provider:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch games by provider: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  static listPublicGames = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('🎮 Fetching games');

      // Use EbetLab API client singleton and make request
      const { merchantId, name, page = '1', limit = '20' } = req.query;
      const gameRequest = new GameListRequest({
        merchantId: merchantId ? parseInt(merchantId as string) : undefined,
        name: name ? (name as string) : undefined,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      });

      const result = await adminEbetlabApiClient.makeAuthenticatedRequest(gameRequest);

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch games: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Games retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch games:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch games: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
