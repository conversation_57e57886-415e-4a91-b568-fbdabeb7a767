import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { SelfExclusionListRequest, SelfExclusionListRequestOptions } from '@/network/ebetlab/requests/self-exclusion/SelfExclusionListRequest';

export class SelfExclusionController {
  /**
   * POST /operator/self-exclusions/index/:page/:limit - Get self-exclusions from EbetLab
   */
  static getSelfExclusions = asyncHandler(async (req: Request, res: Response) => {
    // Extract path parameters
    const page = parseInt(req.params['page'] || '1') || 1;
    const limit = parseInt(req.params['limit'] || '20') || 20;

    // Extract request body as filters
    const filters = req.body;

    // Validate page and limit
    if (page < 1) {
      throw new ValidationError('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    try {
      console.log(`🚫 Fetching self-exclusions - page: ${page}, limit: ${limit}`);
      console.log('🔍 Filters:', JSON.stringify(filters, null, 2));

      // Prepare request options
      const requestOptions: SelfExclusionListRequestOptions = {
        page,
        limit,
        id: filters.id || null,
        username: filters.username || null,
        step: filters.step || null,
        is_active: filters.is_active || null,
        from: filters.from || null,
        to: filters.to || null,
        rt: filters.rt || Math.floor(Date.now() / 1000),
      };

      // Use EbetLab API client singleton and make request
      const selfExclusionRequest = new SelfExclusionListRequest(requestOptions);

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        selfExclusionRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch self-exclusions: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Self-exclusions retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to fetch self-exclusions:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') ||
            error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch self-exclusions: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
