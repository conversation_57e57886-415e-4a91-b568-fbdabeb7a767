import { Request, Response } from 'express';
import {
  MissionParticipationService,
  CreateMissionParticipationDto,
  MissionParticipationQueryParams,
} from '@/services/missionParticipation.service';
import { ExtendedUserService } from '@/services/extendedUser.service';
import { MissionService } from '@/services/mission.service';
import { MissionObjectiveService } from '@/services/missionObjective.service';
import { MissionObjectiveAssignmentService } from '@/services/missionObjectiveAssignment.service';
import { MissionRuleAssignmentService } from '@/services/missionRuleAssignment.service';
import { asyncHandler } from '@/utils/asyncHandler';
import { extractUserIdFromToken, validateJwtToken } from '@/utils/jwtUtils';
import { AppDataSource } from '@/database/connection';
import { RuleType, CompareOperator, VipLevel, MissionType, TransactionCategory } from '@/enums/shared';
import { adminEbetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { CustomerShowRequest } from '@/network/ebetlab/requests/customer/CustomerShowRequest';
import { Customer } from '@/types/api';
import { isWithinCurrentDay, isWithinCurrentWeek, isWithinCurrentMonth, getSecondsUntilNextMissionParticipation, isParticipationWithinCurrentPeriod, getSecondsUntilNextDailyReset, getSecondsUntilNextWeeklyReset, getSecondsUntilNextMonthlyReset, calculateMissionObjectiveEndDate } from '@/utils/periodicValidator';

const missionParticipationService = new MissionParticipationService();
const extendedUserService = new ExtendedUserService();
const missionService = new MissionService();
const missionObjectiveService = new MissionObjectiveService();
const missionObjectiveAssignmentService = new MissionObjectiveAssignmentService();
const missionRuleAssignmentService = new MissionRuleAssignmentService();

// VIP level hierarchy for comparison (index = level rank, higher index = higher rank)
const VIP_LEVEL_HIERARCHY = [
  VipLevel.NO_VIP,
  VipLevel.IRON,
  VipLevel.COPPER,
  VipLevel.BRONZE,
  VipLevel.BRASS,
  VipLevel.NICKEL,
  VipLevel.STEEL,
  VipLevel.COBALT,
  VipLevel.TITANIUM,
  VipLevel.TUNGSTEN,
  VipLevel.SILVER,
  VipLevel.GOLD,
  VipLevel.PLATINUM,
  VipLevel.PALLADIUM,
  VipLevel.RHODIUM,
  VipLevel.OSMIUM,
  VipLevel.IRIDIUM,
  VipLevel.MITHRIL,
  VipLevel.ADAMANTITE,
  VipLevel.ORICHALCUM,
  VipLevel.VIBRANIUM,
  VipLevel.UNOBTANIUM,
  VipLevel.ETERNIUM,
];

// Helper function to get VIP level rank (higher number = higher rank)
const getVipLevelRank = (vipLevel: string): number => {
  const normalizedLevel = vipLevel.toLowerCase().replace(/[-_\s]/g, '-');
  const index = VIP_LEVEL_HIERARCHY.findIndex((level) => level === normalizedLevel);
  return index >= 0 ? index : -1; // Return -1 for unknown levels
};

// Helper function to compare VIP levels
const compareVipLevels = (actualLevel: string, expectedLevel: string, operator: CompareOperator): boolean => {
  const actualRank = getVipLevelRank(actualLevel);
  const expectedRank = getVipLevelRank(expectedLevel);

  // If either level is unknown, fall back to string comparison
  if (actualRank === -1 || expectedRank === -1) {
    console.warn(
      `Unknown VIP level detected: actual="${actualLevel}", expected="${expectedLevel}". Falling back to string comparison.`,
    );
    switch (operator) {
      case CompareOperator.EQUAL:
        return actualLevel.toLowerCase() === expectedLevel.toLowerCase();
      case CompareOperator.NOT_EQUAL:
        return actualLevel.toLowerCase() !== expectedLevel.toLowerCase();
      default:
        return false; // Cannot perform numeric comparison on unknown levels
    }
  }

  // Perform hierarchical comparison
  switch (operator) {
    case CompareOperator.EQUAL:
      return actualRank === expectedRank;
    case CompareOperator.NOT_EQUAL:
      return actualRank !== expectedRank;
    case CompareOperator.GREATER_THAN:
      return actualRank > expectedRank;
    case CompareOperator.LESS_THAN:
      return actualRank < expectedRank;
    case CompareOperator.GREATER_EQUAL:
      return actualRank >= expectedRank;
    case CompareOperator.LESS_EQUAL:
      return actualRank <= expectedRank;
    default:
      return false;
  }
};

// Helper function to fetch customer data using the dedicated admin client
const fetchCustomerData = async (customerId: number): Promise<Customer | null> => {
  try {
    console.log(`🔍 Fetching customer data from EbetLab for customer ID: ${customerId}`);

    const customerRequest = new CustomerShowRequest({
      id: customerId.toString(),
    });

    const result = await adminEbetlabApiClient.makeAuthenticatedRequest(customerRequest);

    if (!result.success || !result.data) {
      throw new Error('Unable to fetch customer data for rule validation');
    }

    console.log(`✅ Fetched customer data from EbetLab for customer ID: ${customerId}`);
    return result.data as Customer;
  } catch (error) {
    console.warn('⚠️ Error fetching customer data from EbetLab:', error);
    return null;
  }
};

// Helper function to validate mission rules against customer data
const validateMissionRules = (customerData: Customer, rules: any[]): { isValid: boolean; failedRules: string[] } => {
  const failedRules: string[] = [];

  for (const ruleAssignment of rules) {
    const rule = ruleAssignment.missionRule;
    const { ruleType, compare, compareValue, minDate, maxDate } = rule;

    let actualValue: any;
    let isValid = false;

    // Extract the actual value from customer data based on rule type
    switch (ruleType) {
      case RuleType.JOINED_AT:
        actualValue = customerData.registration_ts;
        break;
      case RuleType.TOTAL_DEPOSIT:
        actualValue = parseFloat(customerData.summary?.total_in_usd || '0');
        break;
      case RuleType.TOTAL_WITHDRAW:
        actualValue = parseFloat(customerData.summary?.total_out_usd || '0');
        break;
      case RuleType.NET_PROFIT:
        const totalIn = parseFloat(customerData.summary?.total_in_usd || '0');
        const totalOut = parseFloat(customerData.summary?.total_out_usd || '0');
        actualValue = totalIn - totalOut;
        break;
      case RuleType.VIP_RANK:
        actualValue = customerData.rank;
        break;
      case RuleType.COUNTRY:
        actualValue = customerData.registration_country;
        break;
      case RuleType.PHONE_NUMBER:
        actualValue = customerData.phone?.full || '';
        break;
      case RuleType.KYC_LEVEL:
        actualValue = customerData.profile?.verification_level || 0;
        break;
      case RuleType.REFERRER:
        actualValue = customerData.ref_id ? customerData.ref_id.toString() : '';
        break;
      default:
        console.warn(`Unknown rule type: ${ruleType}`);
        continue;
    }

    // Check date range if specified
    if (minDate || maxDate) {
      const currentTime = Math.floor(Date.now() / 1000);
      if (minDate && currentTime < minDate) continue;
      if (maxDate && currentTime > maxDate) continue;
    }

    // Perform comparison based on operator and rule type
    if (ruleType === RuleType.VIP_RANK) {
      // Use VIP level hierarchy comparison for VIP rank rules
      isValid = compareVipLevels(actualValue, compareValue, compare);
      console.log(`🏆 VIP Level comparison: ${actualValue} ${compare} ${compareValue} = ${isValid}`);
    } else {
      // Use standard comparison for other rule types
      const compareValueParsed = isNaN(Number(compareValue)) ? compareValue : Number(compareValue);

      switch (compare) {
        case CompareOperator.EQUAL:
          isValid = actualValue == compareValueParsed;
          break;
        case CompareOperator.NOT_EQUAL:
          isValid = actualValue != compareValueParsed;
          break;
        case CompareOperator.GREATER_THAN:
          isValid = Number(actualValue) > Number(compareValueParsed);
          break;
        case CompareOperator.LESS_THAN:
          isValid = Number(actualValue) < Number(compareValueParsed);
          break;
        case CompareOperator.GREATER_EQUAL:
          isValid = Number(actualValue) >= Number(compareValueParsed);
          break;
        case CompareOperator.LESS_EQUAL:
          isValid = Number(actualValue) <= Number(compareValueParsed);
          break;
        default:
          console.warn(`Unknown compare operator: ${compare}`);
          continue;
      }
    }

    if (!isValid) {
      failedRules.push(`${ruleType} ${compare} ${compareValue} (actual: ${actualValue})`);
    }
  }

  return {
    isValid: failedRules.length === 0,
    failedRules,
  };
};

export const createMissionParticipation = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { userId, missionId, isCompleted }: CreateMissionParticipationDto = req.body;

  // Validation
  if (!userId || typeof userId !== 'number' || userId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'User ID is required and must be a positive number',
    });
  }

  if (!missionId || typeof missionId !== 'number' || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Mission ID is required and must be a positive number',
    });
  }

  if (isCompleted !== undefined && typeof isCompleted !== 'boolean') {
    return res.status(400).json({
      success: false,
      message: 'isCompleted must be a boolean value',
    });
  }

  try {
    // Validate foreign key existence - Check if user exists
    const existingUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this ID does not exist',
      });
    }

    // Validate foreign key existence - Check if mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(400).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }

    // Check if participation already exists
    const existingParticipation = await missionParticipationService.findMissionParticipationByUserAndMission(
      userId,
      missionId,
    );
    if (existingParticipation) {
      return res.status(409).json({
        success: false,
        message: 'Mission participation already exists for this user and mission',
      });
    }

    const missionParticipation = await missionParticipationService.createMissionParticipation({
      userId,
      missionId,
      isCompleted: isCompleted || false,
    });

    res.status(201).json({
      success: true,
      message: 'Mission participation created successfully',
      data: missionParticipation,
    });
  } catch (error) {
    console.error('Error creating mission participation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mission participation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionParticipations = asyncHandler(async (req: Request, res: Response) => {
  console.log('Query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const queryParams: MissionParticipationQueryParams = {
    page: req.query.page ? parseInt(req.query.page as string) : undefined,
    limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
    sortBy: req.query.sortBy as any,
    sortOrder: req.query.sortOrder as 'ASC' | 'DESC',
    userId: req.query.userId ? parseInt(req.query.userId as string) : undefined,
    missionId: req.query.missionId ? parseInt(req.query.missionId as string) : undefined,
    isCompleted: req.query.isCompleted ? req.query.isCompleted === 'true' : undefined,
    createdAtFrom: req.query.createdAtFrom ? parseInt(req.query.createdAtFrom as string) : undefined,
    createdAtTo: req.query.createdAtTo ? parseInt(req.query.createdAtTo as string) : undefined,
    updatedAtFrom: req.query.updatedAtFrom ? parseInt(req.query.updatedAtFrom as string) : undefined,
    updatedAtTo: req.query.updatedAtTo ? parseInt(req.query.updatedAtTo as string) : undefined,
    search: req.query.search as string,
  };

  try {
    const result = await missionParticipationService.findMissionParticipationsWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Mission participations retrieved successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error retrieving mission participations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission participations',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionParticipationById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Mission participation ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission participation ID is required',
    });
  }

  try {
    const missionParticipation = await missionParticipationService.findMissionParticipationById(id);

    if (!missionParticipation) {
      return res.status(404).json({
        success: false,
        message: 'Mission participation not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission participation retrieved successfully',
      data: missionParticipation,
    });
  } catch (error) {
    console.error('Error retrieving mission participation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission participation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateMissionParticipation = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating mission participation ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission participation ID is required',
    });
  }

  // Check if mission participation exists
  const existingMissionParticipation = await missionParticipationService.findMissionParticipationById(id);
  if (!existingMissionParticipation) {
    return res.status(404).json({
      success: false,
      message: 'Mission participation not found',
    });
  }

  const { userId, missionId, isCompleted } = req.body;

  // Validation
  if (userId !== undefined && (typeof userId !== 'number' || userId <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'User ID must be a positive number',
    });
  }

  if (missionId !== undefined && (typeof missionId !== 'number' || missionId <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'Mission ID must be a positive number',
    });
  }

  if (isCompleted !== undefined && typeof isCompleted !== 'boolean') {
    return res.status(400).json({
      success: false,
      message: 'isCompleted must be a boolean value',
    });
  }

  // Validate foreign key existence if userId is being updated
  if (userId !== undefined && userId !== existingMissionParticipation.userId) {
    const existingUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this ID does not exist',
      });
    }
  }

  // Validate foreign key existence if missionId is being updated
  if (missionId !== undefined && missionId !== existingMissionParticipation.missionId) {
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      return res.status(400).json({
        success: false,
        message: 'Mission with this ID does not exist',
      });
    }
  }

  // Check for unique constraint if userId or missionId is being updated
  if (
    (userId !== undefined && userId !== existingMissionParticipation.userId) ||
    (missionId !== undefined && missionId !== existingMissionParticipation.missionId)
  ) {
    const checkUserId = userId !== undefined ? userId : existingMissionParticipation.userId;
    const checkMissionId = missionId !== undefined ? missionId : existingMissionParticipation.missionId;

    const existingParticipation = await missionParticipationService.findMissionParticipationByUserAndMission(
      checkUserId,
      checkMissionId,
    );
    if (existingParticipation && existingParticipation.id !== id) {
      return res.status(409).json({
        success: false,
        message: 'Mission participation already exists for this user and mission combination',
      });
    }
  }

  try {
    const updateData: Partial<CreateMissionParticipationDto> = {};
    if (userId !== undefined) updateData.userId = userId;
    if (missionId !== undefined) updateData.missionId = missionId;
    if (isCompleted !== undefined) updateData.isCompleted = isCompleted;

    const updatedMissionParticipation = await missionParticipationService.updateMissionParticipation(id, updateData);

    res.status(200).json({
      success: true,
      message: 'Mission participation updated successfully',
      data: updatedMissionParticipation,
    });
  } catch (error) {
    console.error('Error updating mission participation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mission participation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteMissionParticipation = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting mission participation ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission participation ID is required',
    });
  }

  try {
    // Check if mission participation exists before deletion
    const existingMissionParticipation = await missionParticipationService.findMissionParticipationById(id);
    if (!existingMissionParticipation) {
      return res.status(404).json({
        success: false,
        message: 'Mission participation not found',
      });
    }

    const deleted = await missionParticipationService.deleteMissionParticipation(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Mission participation not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission participation deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting mission participation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mission participation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get all mission participations for a specific mission
export const getMissionParticipationsByMissionId = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting mission participations for mission ID:', req.params['missionId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    const participations = await missionParticipationService.findParticipationsByMissionId(missionId);

    res.status(200).json({
      success: true,
      message: 'Mission participations retrieved successfully',
      data: {
        missionId,
        participations,
        totalParticipations: participations.length,
      },
    });
  } catch (error) {
    console.error('Error retrieving mission participations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission participations',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get participation statistics for a specific mission
export const getMissionParticipationStats = asyncHandler(async (req: Request, res: Response) => {
  console.log('Getting mission participation stats for mission ID:', req.params['missionId']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const missionId = parseInt(req.params['missionId'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  try {
    // Get all participations for this mission
    const participations = await missionParticipationService.findParticipationsByMissionId(missionId);

    // Get total extended users count
    const totalExtendedUsers = await extendedUserService.findAllExtendedUsers();

    // Calculate statistics
    const totalParticipations = participations.length;
    const completedParticipations = participations.filter((p) => p.isCompleted).length;
    const pendingParticipations = totalParticipations - completedParticipations;
    const completionRate = totalParticipations > 0 ? (completedParticipations / totalParticipations) * 100 : 0;
    const participationRate =
      totalExtendedUsers.length > 0 ? (totalParticipations / totalExtendedUsers.length) * 100 : 0;

    // Get unique user IDs who participated
    const uniqueUserIds = [...new Set(participations.map((p) => p.userId))];
    const uniqueParticipants = uniqueUserIds.length;

    res.status(200).json({
      success: true,
      message: 'Mission participation statistics retrieved successfully',
      data: {
        missionId,
        statistics: {
          totalParticipations,
          completedParticipations,
          pendingParticipations,
          uniqueParticipants,
          totalExtendedUsers: totalExtendedUsers.length,
          completionRate: Math.round(completionRate * 100) / 100, // Round to 2 decimal places
          participationRate: Math.round(participationRate * 100) / 100, // Round to 2 decimal places
        },
        breakdown: {
          completed: {
            count: completedParticipations,
            percentage: Math.round((completedParticipations / totalParticipations) * 10000) / 100 || 0,
          },
          pending: {
            count: pendingParticipations,
            percentage: Math.round((pendingParticipations / totalParticipations) * 10000) / 100 || 0,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error retrieving mission participation statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve mission participation statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint for users to start a mission participation
export const startMissionParticipation = asyncHandler(async (req: Request, res: Response) => {
  console.log('Starting mission participation for mission ID:', req.params['id']);
  console.log('Request body:', req.body);

  const missionId = parseInt(req.params['id'] || '0');

  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  // Extract user ID from JWT token
  const authHeader = req.headers['authorization'];
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from token
  const userId = extractUserIdFromToken(authHeader);
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  try {
    // Check if mission exists
    const existingMission = await missionService.findMissionById(missionId);
    if (!existingMission) {
      throw new Error('Mission with this ID does not exist');
    }

    // Check participation eligibility based on mission type
    const existingParticipation = await missionParticipationService.findMissionParticipationByUserAndMission(
      userId,
      missionId,
    );

    if (existingParticipation) {
      // For custom missions, prevent any duplicate participation
      if (existingMission.missionType === MissionType.CUSTOM) {
        throw new Error('You are already participating in this mission');
      }

      // For periodic missions (daily/weekly/monthly), check if user can start a new participation
      // based on when their last participation was created
      let canStartNewParticipation = false;
      let periodName = '';

      switch (existingMission.missionType) {
        case MissionType.DAILY:
          canStartNewParticipation = !isWithinCurrentDay(existingParticipation.createdAt);
          periodName = 'day';
          break;
        case MissionType.WEEKLY:
          canStartNewParticipation = !isWithinCurrentWeek(existingParticipation.createdAt);
          periodName = 'week';
          break;
        case MissionType.MONTHLY:
          canStartNewParticipation = !isWithinCurrentMonth(existingParticipation.createdAt);
          periodName = 'month';
          break;
        default:
          // This shouldn't happen as CUSTOM is handled above, but just in case
          throw new Error('You are already participating in this mission');
      }

      if (!canStartNewParticipation) {
        throw new Error(`You have already participated in this ${existingMission.missionType} mission within the current ${periodName}. Please wait until the next ${periodName} to participate again.`);
      }

      // If user can start new participation, we need to delete the old one first
      // This allows for fresh participation tracking for the new period
      console.log(`🔄 Removing previous ${existingMission.missionType} participation to allow new participation for current ${periodName}`);

      // First, delete related objective assignments for this user and mission
      const missionObjectives = await missionObjectiveService.findMissionObjectivesByMissionId(missionId);
      for (const objective of missionObjectives) {
        const existingAssignment = await missionObjectiveAssignmentService.findMissionObjectiveAssignmentByUserAndObjective(
          userId,
          objective.id
        );
        if (existingAssignment) {
          console.log(`🗑️ Deleting objective assignment ${existingAssignment.id} for objective ${objective.id}`);
          await missionObjectiveAssignmentService.deleteMissionObjectiveAssignment(existingAssignment.id);
        }
      }

      // Then delete the participation
      await missionParticipationService.deleteMissionParticipation(existingParticipation.id);
    }

    // Get mission rules and validate user eligibility
    const missionRuleAssignments = await missionRuleAssignmentService.findAssignmentsByMissionId(missionId);

    if (missionRuleAssignments.length > 0) {
      // Fetch customer data using the dedicated admin client
      const customerData = await fetchCustomerData(userId);

      if (!customerData) {
        throw new Error('Unable to fetch customer data for rule validation');
      }

      // Validate mission rules
      const ruleValidation = validateMissionRules(customerData, missionRuleAssignments);
      if (!ruleValidation.isValid) {
        throw new Error(`You do not meet the mission requirements: ${ruleValidation.failedRules.join(', ')}`);
      }
    }

    // Use transaction to ensure atomicity for database operations
    await AppDataSource.transaction(async () => {
      // Get or create extended user
      let extendedUser = await extendedUserService.findExtendedUserByExternalId(userId);
      if (!extendedUser) {
        extendedUser = await extendedUserService.createExtendedUser({
          externalId: userId,
          points: 0,
        });
      }

      // Create mission participation
      const missionParticipation = await missionParticipationService.createMissionParticipation({
        userId,
        missionId,
        isCompleted: false,
      });

      // Get all objectives for this mission
      const missionObjectives = await missionObjectiveService.findMissionObjectivesByMissionId(missionId);

      // Create mission objective assignments for each objective
      const objectiveAssignments = [];
      const currentTimestamp = Math.floor(Date.now() / 1000);

      for (const objective of missionObjectives) {
        // Check if assignment already exists (safety check)
        const existingAssignment =
          await missionObjectiveAssignmentService.findMissionObjectiveAssignmentByUserAndObjective(
            userId,
            objective.id,
          );
        if (!existingAssignment) {
          // Calculate end date based on mission type
          const endDate = calculateMissionObjectiveEndDate(existingMission.missionType, currentTimestamp);

          const assignment = await missionObjectiveAssignmentService.createMissionObjectiveAssignment({
            userId,
            missionObjectiveId: objective.id,
            progress: 0,
            lastCheckedRecordTimestamp: currentTimestamp,
            startDate: currentTimestamp,
            endDate: endDate,
          });
          objectiveAssignments.push(assignment);
        }
      }

      return { missionParticipation, objectiveAssignments };
    });

    res.status(201).json({
      success: true,
      message: 'Mission participation started successfully',
      data: {
        missionId,
        userId,
        message: 'You have successfully joined the mission and all objectives have been assigned to you.',
      },
    });
  } catch (error) {
    console.error('Error starting mission participation:', error);

    // Handle specific error messages
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (errorMessage.includes('already participating')) {
      return res.status(409).json({
        success: false,
        message: errorMessage,
      });
    }

    if (errorMessage.includes('does not exist')) {
      return res.status(404).json({
        success: false,
        message: errorMessage,
      });
    }

    if (errorMessage.includes('do not meet the mission requirements')) {
      return res.status(403).json({
        success: false,
        message: errorMessage,
      });
    }

    if (errorMessage.includes('Unable to fetch customer data')) {
      return res.status(502).json({
        success: false,
        message: errorMessage,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to start mission participation',
      error: errorMessage,
    });
  }
});

// Debug endpoint to test OTP generation
export const testOtpGeneration = asyncHandler(async (req: Request, res: Response) => {
  console.log('🧪 Testing OTP generation endpoint called');

  try {
    const otpSecret = process.env['API_OTP_SECRET'];
    const username = process.env['API_USERNAME'];
    const password = process.env['API_PASSWORD'];

    if (!otpSecret) {
      return res.status(500).json({
        success: false,
        message: 'API_OTP_SECRET not configured',
      });
    }

    if (!username || !password) {
      return res.status(500).json({
        success: false,
        message: 'API_USERNAME or API_PASSWORD not configured',
      });
    }

    console.log('🔐 Environment variables found, testing admin client...');

    // Test admin client authentication
    console.log('🔑 Testing admin client authentication...');
    try {
      // Test fetching customer data with a dummy ID to verify admin client works
      const testCustomerId = 1; // Use a test customer ID
      const customerData = await fetchCustomerData(testCustomerId);

      res.status(200).json({
        success: true,
        message: 'Admin client test completed successfully',
        data: {
          adminClientWorking: true,
          customerDataFetched: customerData !== null,
          testCustomerId,
        },
      });
    } catch (adminError) {
      console.error('❌ Admin client test failed:', adminError);

      res.status(200).json({
        success: true,
        message: 'Admin client test completed, but authentication failed',
        data: {
          adminClientWorking: false,
          adminClientError: adminError instanceof Error ? adminError.message : 'Unknown error',
        },
      });
    }
  } catch (error) {
    console.error('❌ OTP test failed:', error);
    res.status(500).json({
      success: false,
      message: 'OTP generation test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint to get all missions a user is eligible to start
export const getUserEligibleMissions = asyncHandler(async (req: Request, res: Response) => {
  console.log('🎯 Getting eligible missions for user');

  // Extract JWT token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  // Validate JWT token
  const tokenValidation = validateJwtToken(authHeader);
  if (!tokenValidation.valid) {
    return res.status(401).json({
      success: false,
      message: tokenValidation.reason || 'Invalid token',
    });
  }

  // Extract user ID from token
  const userId = extractUserIdFromToken(authHeader);
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log(`👤 User ID extracted from token: ${userId}`);

  try {
    // Get current timestamp for active mission filtering
    const currentTimestamp = Math.floor(Date.now() / 1000);

    // Get all active missions
    const activeMissions = await missionService.findActiveMissions(currentTimestamp);
    console.log(`📋 Found ${activeMissions.length} active missions`);

    // Get all user's mission participations
    const userParticipations = await missionParticipationService.findParticipationsByUserId(userId);
    console.log(`👤 Found ${userParticipations.length} user participations`);

    if (activeMissions.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No active missions found',
        data: {
          eligibleMissions: [],
          totalEligible: 0,
          totalActive: 0,
        },
      });
    }

    // Cache user data to avoid multiple API calls
    let cachedUserData: any = null;
    let userDataFetched = false;

    const eligibleMissions = [];

    // Check each mission for eligibility
    for (const mission of activeMissions) {
      console.log(`🔍 Checking eligibility for mission: ${mission.name} (ID: ${mission.id})`);

      // Get mission rules
      const missionRuleAssignments = await missionRuleAssignmentService.findAssignmentsByMissionId(mission.id);

      if (missionRuleAssignments.length === 0) {
        console.log(`✅ Mission ${mission.id} has no rules - user is eligible`);

        // Get mission objectives and user's assignments
        const objectives = await missionObjectiveService.findMissionObjectivesByMissionId(mission.id);
        const userAssignments = await missionObjectiveAssignmentService.findAssignmentsByUserId(userId);

        // Find the latest participation for this mission that is within the current period
        const missionParticipations = userParticipations
          .filter(p => p.missionId === mission.id)
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        const latestParticipation = missionParticipations.find(p =>
          isParticipationWithinCurrentPeriod(p.createdAt, mission.missionType)
        );

        // Filter assignments to only include those for this mission's objectives
        let missionAssignments = userAssignments.filter((assignment) =>
          objectives.some((obj) => obj.id === assignment.missionObjectiveId),
        );

        // For periodic missions, only show assignments if there's a current participation
        // and the assignments are within the current period
        if (mission.missionType !== MissionType.CUSTOM) {
          if (latestParticipation) {
            // Only show assignments created within the current participation's timeframe
            missionAssignments = missionAssignments.filter(assignment =>
              assignment.createdAt >= latestParticipation.createdAt
            );
          } else {
            // No current participation means no assignments should be shown
            missionAssignments = [];
          }
        }

        // Calculate next allowed participation seconds if there's a participation
        let participationWithNextAllowed = null;
        if (latestParticipation) {
          const nextAllowedParticipationSeconds = getSecondsUntilNextMissionParticipation(
            latestParticipation.createdAt,
            mission.missionType
          );
          participationWithNextAllowed = {
            ...latestParticipation,
            nextAllowedParticipationSeconds,
          };
        }

        eligibleMissions.push({
          ...mission,
          objectives,
          userAssignments: missionAssignments,
          latestParticipation: participationWithNextAllowed,
        });
        continue;
      }

      // Fetch user data only if we haven't already and there are rules to check
      if (!userDataFetched) {
        console.log(`🔐 Fetching user data for rule validation...`);

        try {
          cachedUserData = await fetchCustomerData(userId);

          if (!cachedUserData) {
            throw new Error('Unable to fetch customer data for rule validation');
          }

          userDataFetched = true;
          console.log(`✅ User data cached successfully`);
        } catch (error) {
          console.error('❌ Error fetching user data:', error);
          return res.status(502).json({
            success: false,
            message: 'Unable to fetch customer data for rule validation',
          });
        }
      }

      // Validate mission rules against cached user data
      const ruleValidation = validateMissionRules(cachedUserData, missionRuleAssignments);

      if (ruleValidation.isValid) {
        console.log(`✅ Mission ${mission.id} - user meets all requirements`);

        // Get mission objectives and user's assignments
        const objectives = await missionObjectiveService.findMissionObjectivesByMissionId(mission.id);
        const userAssignments = await missionObjectiveAssignmentService.findAssignmentsByUserId(userId);

        // Find the latest participation for this mission that is within the current period
        const missionParticipations = userParticipations
          .filter(p => p.missionId === mission.id)
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        const latestParticipation = missionParticipations.find(p =>
          isParticipationWithinCurrentPeriod(p.createdAt, mission.missionType)
        );

        // Filter assignments to only include those for this mission's objectives
        let missionAssignments = userAssignments.filter((assignment) =>
          objectives.some((obj) => obj.id === assignment.missionObjectiveId),
        );

        // For periodic missions, only show assignments if there's a current participation
        // and the assignments are within the current period
        if (mission.missionType !== MissionType.CUSTOM) {
          if (latestParticipation) {
            // Only show assignments created within the current participation's timeframe
            missionAssignments = missionAssignments.filter(assignment =>
              assignment.createdAt >= latestParticipation.createdAt
            );
          } else {
            // No current participation means no assignments should be shown
            missionAssignments = [];
          }
        }

        // Calculate next allowed participation seconds if there's a participation
        let participationWithNextAllowed = null;
        if (latestParticipation) {
          const nextAllowedParticipationSeconds = getSecondsUntilNextMissionParticipation(
            latestParticipation.createdAt,
            mission.missionType
          );
          participationWithNextAllowed = {
            ...latestParticipation,
            nextAllowedParticipationSeconds,
          };
        }

        eligibleMissions.push({
          ...mission,
          objectives,
          userAssignments: missionAssignments,
          latestParticipation: participationWithNextAllowed,
        });
      } else {
        console.log(
          `❌ Mission ${mission.id} - user does not meet requirements: ${ruleValidation.failedRules.join(', ')}`,
        );
      }
    }

    console.log(`🎯 User is eligible for ${eligibleMissions.length} out of ${activeMissions.length} active missions`);

    res.status(200).json({
      success: true,
      message: 'Eligible missions retrieved successfully',
      data: {
        eligibleMissions,
        totalEligible: eligibleMissions.length,
        totalActive: activeMissions.length,
      },
    });
  } catch (error) {
    console.error('❌ Error getting eligible missions:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (errorMessage.includes('Unable to fetch customer data')) {
      return res.status(502).json({
        success: false,
        message: errorMessage,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to get eligible missions',
      error: errorMessage,
    });
  }
});



// Public endpoint for users to complete a mission
export const completeMission = asyncHandler(async (req: Request, res: Response) => {
  console.log('Completing mission with mission ID:', req.params['missionId']);

  const missionId = parseInt(req.params['missionId'] || '0');

  // Validation
  if (!missionId || isNaN(missionId) || missionId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission ID is required',
    });
  }

  // Extract user ID from JWT token
  const authHeader = req.headers['authorization'];
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header is required',
    });
  }

  const userId = extractUserIdFromToken(authHeader);
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Unable to extract user ID from token',
    });
  }

  console.log('Extracted user ID from token:', userId);

  try {
    // Find the extended user
    const extendedUser = await extendedUserService.findExtendedUserByExternalId(userId);
    if (!extendedUser) {
      return res.status(404).json({
        success: false,
        message: 'Extended user not found',
      });
    }

    // Find the mission
    const mission = await missionService.findMissionById(missionId);
    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission not found',
      });
    }

    // Check if user has a participation for this mission
    const participation = await missionParticipationService.findLatestMissionParticipationByUserAndMission(userId, missionId);
    if (!participation) {
      return res.status(404).json({
        success: false,
        message: 'Mission participation not found. User must start the mission first.',
      });
    }

    // For periodic missions, check if the latest participation is still within the current period
    if (mission.missionType !== MissionType.CUSTOM) {
      let isWithinCurrentPeriod = false;
      let periodName = '';

      switch (mission.missionType) {
        case MissionType.DAILY:
          isWithinCurrentPeriod = isWithinCurrentDay(participation.createdAt);
          periodName = 'day';
          break;
        case MissionType.WEEKLY:
          isWithinCurrentPeriod = isWithinCurrentWeek(participation.createdAt);
          periodName = 'week';
          break;
        case MissionType.MONTHLY:
          isWithinCurrentPeriod = isWithinCurrentMonth(participation.createdAt);
          periodName = 'month';
          break;
      }

      if (!isWithinCurrentPeriod) {
        return res.status(409).json({
          success: false,
          message: `Your latest participation for this ${mission.missionType} mission has expired. Please start a new participation for the current ${periodName}.`,
        });
      }
    }

    // Check if mission is already completed
    if (participation.isCompleted) {
      return res.status(409).json({
        success: false,
        message: 'Mission is already completed',
      });
    }

    // Get all mission objectives for this mission
    const objectives = await missionObjectiveService.findMissionObjectivesByMissionId(missionId);

    // Get all objective assignments for this user and mission
    const allUserAssignments = await missionObjectiveAssignmentService.findAssignmentsByUserId(userId);
    let missionAssignments = allUserAssignments.filter(assignment =>
      objectives.some(obj => obj.id === assignment.missionObjectiveId)
    );

    // For periodic missions, filter assignments to only include those within the current participation's timeframe
    if (mission.missionType !== MissionType.CUSTOM) {
      missionAssignments = missionAssignments.filter(assignment => {
        // Only consider assignments created after or at the same time as the current participation
        return assignment.createdAt >= participation.createdAt;
      });
    }

    // Check if all objective assignments are complete
    const allObjectivesComplete = objectives.length > 0 &&
      objectives.every(objective => {
        const assignment = missionAssignments.find(a => a.missionObjectiveId === objective.id);
        return assignment && assignment.isCompleted;
      });

    if (!allObjectivesComplete) {
      return res.status(400).json({
        success: false,
        message: 'Not all mission objectives are completed yet',
        data: {
          totalObjectives: objectives.length,
          completedObjectives: missionAssignments.filter(a => a.isCompleted).length,
        },
      });
    }

    // Use transaction to ensure atomicity
    await AppDataSource.transaction(async (tx) => {
      // Award mission reward points with proper transaction logging
      await extendedUserService.depositPoints(
        extendedUser.externalId,
        mission.reward,
        TransactionCategory.MISSION_REWARD,
        {
          missionId: mission.id,
          missionName: mission.name,
          missionType: mission.missionType,
          participationId: participation.id,
          rewardAmount: mission.reward,
        },
        tx,
      );

      // Mark mission participation as complete
      await missionParticipationService.markParticipationAsCompleted(participation.id);
    });

    res.status(200).json({
      success: true,
      message: 'Mission completed successfully',
      data: {
        missionId,
        userId,
        pointsAwarded: mission.reward,
        newPointsTotal: extendedUser.points + mission.reward,
      },
    });
  } catch (error) {
    console.error('Error completing mission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete mission',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Public endpoint to get mission reset times
export const getMissionResetTimes = asyncHandler(async (req: Request, res: Response) => {
  console.log('🕐 Getting mission reset times');

  try {
    const dailyResetSeconds = getSecondsUntilNextDailyReset();
    const weeklyResetSeconds = getSecondsUntilNextWeeklyReset();
    const monthlyResetSeconds = getSecondsUntilNextMonthlyReset();

    res.status(200).json({
      success: true,
      message: 'Mission reset times retrieved successfully',
      data: {
        daily: {
          secondsUntilReset: dailyResetSeconds,
          resetTime: new Date(Date.now() + dailyResetSeconds * 1000).toISOString(),
        },
        weekly: {
          secondsUntilReset: weeklyResetSeconds,
          resetTime: new Date(Date.now() + weeklyResetSeconds * 1000).toISOString(),
        },
        monthly: {
          secondsUntilReset: monthlyResetSeconds,
          resetTime: new Date(Date.now() + monthlyResetSeconds * 1000).toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('❌ Error getting mission reset times:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get mission reset times',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
