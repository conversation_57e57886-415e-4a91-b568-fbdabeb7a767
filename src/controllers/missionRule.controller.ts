import { Request, Response } from 'express';
import { MissionRuleService, CreateMissionRuleDto, MissionRuleQueryParams } from '@/services/missionRule.service';
import { RuleType, CompareOperator } from '@/enums/shared';
import { asyncHandler } from '@/utils/asyncHandler';

const missionRuleService = new MissionRuleService();

export const createMissionRule = asyncHandler(async (req: Request, res: Response) => {
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const { ruleType, compare, compareValue, minDate, maxDate }: CreateMissionRuleDto = req.body;

  // Validation
  if (!ruleType || !Object.values(RuleType).includes(ruleType)) {
    return res.status(400).json({
      success: false,
      message: `Rule type is required and must be one of: ${Object.values(RuleType).join(', ')}`,
    });
  }

  if (!compare || !Object.values(CompareOperator).includes(compare)) {
    return res.status(400).json({
      success: false,
      message: `Compare operator is required and must be one of: ${Object.values(CompareOperator).join(', ')}`,
    });
  }

  if (!compareValue || typeof compareValue !== 'string' || compareValue.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Compare value is required and must be a non-empty string',
    });
  }

  if (minDate !== undefined && minDate !== null && (typeof minDate !== 'number' || minDate < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Min date must be a valid timestamp or null',
    });
  }

  if (maxDate !== undefined && maxDate !== null && (typeof maxDate !== 'number' || maxDate < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Max date must be a valid timestamp or null',
    });
  }

  // Validate date range if both are provided
  if (minDate !== null && maxDate !== null && minDate !== undefined && maxDate !== undefined && maxDate <= minDate) {
    return res.status(400).json({
      success: false,
      message: 'Max date must be after min date',
    });
  }

  try {
    const missionRule = await missionRuleService.createMissionRule({
      ruleType,
      compare,
      compareValue: compareValue.trim(),
      minDate: minDate || null,
      maxDate: maxDate || null,
    });

    res.status(201).json({
      success: true,
      message: 'Mission rule created successfully',
      data: missionRule,
    });
  } catch (error) {
    console.error('Error creating mission rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create mission rule',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionRules = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching mission rules list with query params:', req.query);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  // Parse and validate query parameters
  const queryParams: MissionRuleQueryParams = {};

  // Pagination
  if (req.query['page']) {
    const page = parseInt(req.query['page'] as string);
    if (isNaN(page) || page < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page must be a positive integer',
      });
    }
    queryParams.page = page;
  }

  if (req.query['limit']) {
    const limit = parseInt(req.query['limit'] as string);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
      });
    }
    queryParams.limit = limit;
  }

  // Sorting
  if (req.query['sortBy']) {
    const validSortFields = ['id', 'ruleType', 'compare', 'compareValue', 'minDate', 'maxDate', 'createdAt', 'updatedAt'];
    const sortBy = req.query['sortBy'] as string;
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({
        success: false,
        message: `sortBy must be one of: ${validSortFields.join(', ')}`,
      });
    }
    queryParams.sortBy = sortBy as any;
  }

  if (req.query['sortOrder']) {
    const sortOrder = req.query['sortOrder'] as string;
    if (!['ASC', 'DESC', 'asc', 'desc'].includes(sortOrder)) {
      return res.status(400).json({
        success: false,
        message: 'sortOrder must be either "ASC", "DESC", "asc", or "desc"',
      });
    }
    queryParams.sortOrder = sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filters
  if (req.query['ruleType']) {
    const ruleType = req.query['ruleType'] as string;
    if (!Object.values(RuleType).includes(ruleType as RuleType)) {
      return res.status(400).json({
        success: false,
        message: `ruleType must be one of: ${Object.values(RuleType).join(', ')}`,
      });
    }
    queryParams.ruleType = ruleType as RuleType;
  }

  if (req.query['compare']) {
    const compare = req.query['compare'] as string;
    if (!Object.values(CompareOperator).includes(compare as CompareOperator)) {
      return res.status(400).json({
        success: false,
        message: `compare must be one of: ${Object.values(CompareOperator).join(', ')}`,
      });
    }
    queryParams.compare = compare as CompareOperator;
  }

  if (req.query['compareValue']) {
    queryParams.compareValue = req.query['compareValue'] as string;
  }

  if (req.query['search']) {
    queryParams.search = req.query['search'] as string;
  }

  // Date range filters
  if (req.query['minDateFrom']) {
    const minDateFrom = parseInt(req.query['minDateFrom'] as string);
    if (isNaN(minDateFrom) || minDateFrom < 0) {
      return res.status(400).json({
        success: false,
        message: 'minDateFrom must be a valid timestamp',
      });
    }
    queryParams.minDateFrom = minDateFrom;
  }

  if (req.query['minDateTo']) {
    const minDateTo = parseInt(req.query['minDateTo'] as string);
    if (isNaN(minDateTo) || minDateTo < 0) {
      return res.status(400).json({
        success: false,
        message: 'minDateTo must be a valid timestamp',
      });
    }
    queryParams.minDateTo = minDateTo;
  }

  if (req.query['maxDateFrom']) {
    const maxDateFrom = parseInt(req.query['maxDateFrom'] as string);
    if (isNaN(maxDateFrom) || maxDateFrom < 0) {
      return res.status(400).json({
        success: false,
        message: 'maxDateFrom must be a valid timestamp',
      });
    }
    queryParams.maxDateFrom = maxDateFrom;
  }

  if (req.query['maxDateTo']) {
    const maxDateTo = parseInt(req.query['maxDateTo'] as string);
    if (isNaN(maxDateTo) || maxDateTo < 0) {
      return res.status(400).json({
        success: false,
        message: 'maxDateTo must be a valid timestamp',
      });
    }
    queryParams.maxDateTo = maxDateTo;
  }

  try {
    const result = await missionRuleService.findMissionRulesWithQuery(queryParams);

    res.status(200).json({
      success: true,
      message: 'Mission rules retrieved successfully',
      data: result.missionRules,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching mission rules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch mission rules',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const getMissionRuleById = asyncHandler(async (req: Request, res: Response) => {
  console.log('Fetching mission rule by ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission rule ID is required',
    });
  }

  try {
    const missionRule = await missionRuleService.findMissionRuleById(id);

    if (!missionRule) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission rule retrieved successfully',
      data: missionRule,
    });
  } catch (error) {
    console.error('Error fetching mission rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch mission rule',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const updateMissionRule = asyncHandler(async (req: Request, res: Response) => {
  console.log('Updating mission rule ID:', req.params['id']);
  console.log('Request body:', req.body);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission rule ID is required',
    });
  }

  // Check if mission rule exists
  const existingMissionRule = await missionRuleService.findMissionRuleById(id);
  if (!existingMissionRule) {
    return res.status(404).json({
      success: false,
      message: 'Mission rule not found',
    });
  }

  const { ruleType, compare, compareValue, minDate, maxDate } = req.body;

  // Validate only provided fields (partial update)
  const updateData: Partial<CreateMissionRuleDto> = {};

  if (ruleType !== undefined) {
    if (!Object.values(RuleType).includes(ruleType)) {
      return res.status(400).json({
        success: false,
        message: `Rule type must be one of: ${Object.values(RuleType).join(', ')}`,
      });
    }
    updateData.ruleType = ruleType;
  }

  if (compare !== undefined) {
    if (!Object.values(CompareOperator).includes(compare)) {
      return res.status(400).json({
        success: false,
        message: `Compare operator must be one of: ${Object.values(CompareOperator).join(', ')}`,
      });
    }
    updateData.compare = compare;
  }

  if (compareValue !== undefined) {
    if (typeof compareValue !== 'string' || compareValue.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Compare value must be a non-empty string',
      });
    }
    updateData.compareValue = compareValue.trim();
  }

  if (minDate !== undefined) {
    if (minDate !== null && (typeof minDate !== 'number' || minDate < 0)) {
      return res.status(400).json({
        success: false,
        message: 'Min date must be a valid timestamp or null',
      });
    }
    updateData.minDate = minDate;
  }

  if (maxDate !== undefined) {
    if (maxDate !== null && (typeof maxDate !== 'number' || maxDate < 0)) {
      return res.status(400).json({
        success: false,
        message: 'Max date must be a valid timestamp or null',
      });
    }
    updateData.maxDate = maxDate;
  }

  // Validate date range if both dates are provided or being updated
  const finalMinDate = updateData.minDate !== undefined ? updateData.minDate : existingMissionRule.minDate;
  const finalMaxDate = updateData.maxDate !== undefined ? updateData.maxDate : existingMissionRule.maxDate;

  if (finalMinDate !== null && finalMaxDate !== null && finalMaxDate <= finalMinDate) {
    return res.status(400).json({
      success: false,
      message: 'Max date must be after min date',
    });
  }

  try {
    const updatedMissionRule = await missionRuleService.updateMissionRule(id, updateData);

    if (!updatedMissionRule) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule not found after update',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission rule updated successfully',
      data: updatedMissionRule,
    });
  } catch (error) {
    console.error('Error updating mission rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mission rule',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export const deleteMissionRule = asyncHandler(async (req: Request, res: Response) => {
  console.log('Deleting mission rule ID:', req.params['id']);
  console.log('Authenticated admin user:', req.adminUser?.email || 'Unknown');

  const id = parseInt(req.params['id'] || '0');

  if (!id || isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid mission rule ID is required',
    });
  }

  try {
    // Check if mission rule exists before deletion
    const existingMissionRule = await missionRuleService.findMissionRuleById(id);
    if (!existingMissionRule) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule not found',
      });
    }

    const deleted = await missionRuleService.deleteMissionRule(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Mission rule not found or already deleted',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Mission rule deleted successfully',
      data: { id, deletedAt: new Date().toISOString() },
    });
  } catch (error) {
    console.error('Error deleting mission rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete mission rule',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
