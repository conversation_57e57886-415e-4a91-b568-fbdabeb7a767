import { Request, Response } from 'express';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { asyncHandler } from '@/utils/asyncHandler';
import { ebetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { BonusGetRequest } from '@/network/ebetlab/requests/bonus/BonusGetRequest';
import { BonusListRequest } from '@/network/ebetlab/requests/bonus/BonusListRequest';
import { BonusCreateRequest, BonusCreateRequestOptions } from '@/network/ebetlab/requests/bonus/BonusCreateRequest';
import { BonusPatchRequest, BonusPatchRequestOptions } from '@/network/ebetlab/requests/bonus/BonusPatchRequest';
import { BonusFreespinRedeemsListRequest } from '@/network/ebetlab/requests/bonus/BonusFreespinRedeemsListRequest';
import { BonusCancellationCreateRequest } from '@/network/ebetlab/requests/bonus/BonusCancellationCreate';
import { BonusDeleteRequest } from '@/network/ebetlab/requests/bonus/BonusDeleteRequest';

export class BonusController {
  /**
   * GET /bonuses - Get bonuses from EbetLab
   */
  static listBonuses = asyncHandler(async (req: Request, res: Response) => {
    // Extract query parameters
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎁 Fetching bonuses - page: ${page}, limit: ${limit}`);
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          // Handle special conversions
          if (key === 'operator_id' && value) {
            filters[key] = parseInt(value as string);
          } else if (key === 'is_active' && value) {
            filters[key] = value === 'true' || value === '1';
          } else if (['to_start', 'to_end', 'from_start', 'from_end'].includes(key) && value) {
            filters[key] = parseInt(value as string);
          } else {
            filters[key] = value;
          }
        }
      });

      // Use EbetLab API client singleton and make request
      const bonusRequest = new BonusListRequest({
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(bonusRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonuses: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonuses retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonuses:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonuses - Create a new bonus in EbetLab
   */
  static createBonus = asyncHandler(async (req: Request<{}, any, BonusCreateRequestOptions>, res: Response) => {
    try {
      console.log('🎁 Creating new bonus...');
      console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

      // Validate required fields
      const requiredFields: (keyof BonusCreateRequestOptions)[] = [
        'provider_id',
        'provider',
        'type',
        'name',
        'from',
        'to',
        'currency',
        'product',
        'expire_type',
        'expire_unit',
      ];
      const missingFields = requiredFields.filter((field) => req.body[field] === undefined || req.body[field] === null);

      if (missingFields.length > 0) {
        throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Use EbetLab API client singleton and make request
      const bonusRequest = new BonusCreateRequest(req.body);

      const result = await ebetlabApiClient.makeAuthenticatedRequest(bonusRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to create bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus created successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('❌ Failed to create bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to create bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /bonuses/:id - Get a specific bonus by ID from EbetLab
   */
  static getBonusById = asyncHandler(async (req: Request, res: Response) => {
    // Extract ID from route parameters
    const { id } = req.params;

    try {
      console.log(`🎁 Fetching bonus by ID - id: ${id}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate ID parameter
      if (!id) {
        throw new ValidationError('id parameter is required');
      }

      const bonusId = parseInt(id);
      if (isNaN(bonusId) || bonusId < 1) {
        throw new ValidationError('id must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const bonusRequest = new BonusGetRequest({
        id: bonusId,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(bonusRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch bonus by ID:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * PATCH /bonuses/:id - Update a specific bonus by ID in EbetLab
   */
  static updateBonus = asyncHandler(
    async (req: Request<{ id: string }, any, BonusPatchRequestOptions>, res: Response) => {
      // Extract ID from route parameters
      const { id } = req.params;

      try {
        console.log(`🎁 Updating bonus by ID - id: ${id}`);
        console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
        console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

        // Validate ID parameter
        if (!id) {
          throw new ValidationError('id parameter is required');
        }

        const bonusId = parseInt(id);
        if (isNaN(bonusId) || bonusId < 1) {
          throw new ValidationError('id must be a positive integer');
        }

        // Validate required fields
        const requiredFields: (keyof BonusPatchRequestOptions)[] = [
          'type',
          'name',
          'from',
          'to',
          'currency',
          'product',
          'expire_type',
          'expire_unit',
        ];
        const missingFields = requiredFields.filter(
          (field) => req.body[field] === undefined || req.body[field] === null,
        );

        if (missingFields.length > 0) {
          throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Use EbetLab API client singleton and make request
        const bonusRequest = new BonusPatchRequest({
          ...req.body,
          id: bonusId,
        });

        const result = await ebetlabApiClient.makeAuthenticatedRequest(
          bonusRequest,
          req.headers['authorization'] || '',
        );

        // Check if the request was successful
        if (!result.success) {
          throw new ValidationError(`Failed to update bonus: ${result.message || 'Unknown error'}`);
        }

        const response: ApiResponse<any> = {
          success: true,
          message: 'Bonus updated successfully',
          data: result.data,
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);
      } catch (error) {
        console.error('❌ Failed to update bonus by ID:', error);

        if (error instanceof Error) {
          // Check if it's an authentication error
          if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
            throw new ValidationError(`Failed to update bonus: ${error.message}`);
          }
        }

        // Re-throw the error to be handled by the global error handler
        throw error;
      }
    },
  );

  /**
   * GET /bonuses/:bonusId/freespins - Get freespin redeems for a specific bonus
   */
  static getBonusFreespins = asyncHandler(async (req: Request, res: Response) => {
    // Extract bonusId from route parameters and query parameters
    const { bonusId } = req.params;
    const { page = 1, limit = 20, ...queryParams } = req.query;

    try {
      console.log(`🎰 Fetching freespin redeems for bonus - bonusId: ${bonusId}, page: ${page}, limit: ${limit}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));
      console.log('📋 Request query:', JSON.stringify(req.query, null, 2));

      // Validate bonusId parameter
      if (!bonusId) {
        throw new ValidationError('bonusId parameter is required');
      }

      const bonusIdNum = parseInt(bonusId);
      if (isNaN(bonusIdNum) || bonusIdNum < 1) {
        throw new ValidationError('bonusId must be a positive integer');
      }

      // Convert page and limit to numbers
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Validate pagination parameters
      if (isNaN(pageNum) || pageNum < 1) {
        throw new ValidationError('page must be a positive integer');
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        throw new ValidationError('limit must be a positive integer between 1 and 100');
      }

      // Convert query parameters to filters
      const filters: Record<string, any> = {};
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          filters[key] = value;
        }
      });

      // Use EbetLab API client singleton and make request
      const freespinRequest = new BonusFreespinRedeemsListRequest({
        bonusId: bonusIdNum,
        page: pageNum,
        limit: limitNum,
        ...filters,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        freespinRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to fetch freespin redeems: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Freespin redeems retrieved successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to fetch freespin redeems:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to fetch freespin redeems: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * POST /bonuses/:bonusId/cancellations - Cancel a specific bonus by ID
   */
  static cancelBonus = asyncHandler(async (req: Request, res: Response) => {
    // Extract bonusId from route parameters
    const { bonusId } = req.params;

    try {
      console.log(`🚫 Cancelling bonus - bonusId: ${bonusId}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate bonusId parameter
      if (!bonusId) {
        throw new ValidationError('bonusId parameter is required');
      }

      const bonusIdNum = parseInt(bonusId);
      if (isNaN(bonusIdNum) || bonusIdNum < 1) {
        throw new ValidationError('bonusId must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const cancellationRequest = new BonusCancellationCreateRequest({
        id: bonusIdNum,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(
        cancellationRequest,
        req.headers['authorization'] || '',
      );

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to cancel bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus cancelled successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to cancel bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to cancel bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * DELETE /bonuses/:bonusId - Delete a specific bonus by ID
   */
  static deleteBonus = asyncHandler(async (req: Request, res: Response) => {
    // Extract bonusId from route parameters
    const { bonusId } = req.params;

    try {
      console.log(`🗑️ Deleting bonus - bonusId: ${bonusId}`);
      console.log('📋 Request params:', JSON.stringify(req.params, null, 2));

      // Validate bonusId parameter
      if (!bonusId) {
        throw new ValidationError('bonusId parameter is required');
      }

      const bonusIdNum = parseInt(bonusId);
      if (isNaN(bonusIdNum) || bonusIdNum < 1) {
        throw new ValidationError('bonusId must be a positive integer');
      }

      // Use EbetLab API client singleton and make request
      const deleteRequest = new BonusDeleteRequest({
        id: bonusIdNum,
      });

      const result = await ebetlabApiClient.makeAuthenticatedRequest(deleteRequest, req.headers['authorization'] || '');

      // Check if the request was successful
      if (!result.success) {
        throw new ValidationError(`Failed to delete bonus: ${result.message || 'Unknown error'}`);
      }

      const response: ApiResponse<any> = {
        success: true,
        message: 'Bonus deleted successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to delete bonus:', error);

      if (error instanceof Error) {
        // Check if it's an authentication error
        if (error.message.includes('Request failed:') || error.message.includes('Authentication failed')) {
          throw new ValidationError(`Failed to delete bonus: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
