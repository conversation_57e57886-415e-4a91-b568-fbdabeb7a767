import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateMissionParticipations1703000000004 implements MigrationInterface {
  name = 'CreateMissionParticipations1703000000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'mission_participations',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'userId',
            type: 'integer',
          },
          {
            name: 'missionId',
            type: 'integer',
          },
          {
            name: 'isCompleted',
            type: 'boolean',
            default: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create unique index on userId and missionId (one participation per user per mission)
    await queryRunner.createIndex(
      'mission_participations',
      new TableIndex({
        name: 'IDX_MISSION_PARTICIPATIONS_USER_MISSION_UNIQUE',
        columnNames: ['userId', 'missionId'],
        isUnique: true,
      }),
    );

    // Create index on userId for faster user-based queries
    await queryRunner.createIndex(
      'mission_participations',
      new TableIndex({
        name: 'IDX_MISSION_PARTICIPATIONS_USER_ID',
        columnNames: ['userId'],
      }),
    );

    // Create index on missionId for faster mission-based queries
    await queryRunner.createIndex(
      'mission_participations',
      new TableIndex({
        name: 'IDX_MISSION_PARTICIPATIONS_MISSION_ID',
        columnNames: ['missionId'],
      }),
    );

    // Create index on isCompleted for filtering completed/pending participations
    await queryRunner.createIndex(
      'mission_participations',
      new TableIndex({
        name: 'IDX_MISSION_PARTICIPATIONS_IS_COMPLETED',
        columnNames: ['isCompleted'],
      }),
    );

    // Create composite index for user + completion status queries
    await queryRunner.createIndex(
      'mission_participations',
      new TableIndex({
        name: 'IDX_MISSION_PARTICIPATIONS_USER_COMPLETED',
        columnNames: ['userId', 'isCompleted'],
      }),
    );

    // Create foreign key constraint to missions table
    await queryRunner.createForeignKey(
      'mission_participations',
      new TableForeignKey({
        columnNames: ['missionId'],
        referencedTableName: 'missions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_PARTICIPATIONS_MISSION_ID',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('mission_participations', 'FK_MISSION_PARTICIPATIONS_MISSION_ID');
    await queryRunner.dropIndex('mission_participations', 'IDX_MISSION_PARTICIPATIONS_USER_COMPLETED');
    await queryRunner.dropIndex('mission_participations', 'IDX_MISSION_PARTICIPATIONS_IS_COMPLETED');
    await queryRunner.dropIndex('mission_participations', 'IDX_MISSION_PARTICIPATIONS_MISSION_ID');
    await queryRunner.dropIndex('mission_participations', 'IDX_MISSION_PARTICIPATIONS_USER_ID');
    await queryRunner.dropIndex('mission_participations', 'IDX_MISSION_PARTICIPATIONS_USER_MISSION_UNIQUE');
    await queryRunner.dropTable('mission_participations');
  }
}
