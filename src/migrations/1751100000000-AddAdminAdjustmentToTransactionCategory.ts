import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdminAdjustmentToTransactionCategory1751100000000 implements MigrationInterface {
  name = 'AddAdminAdjustmentToTransactionCategory1751100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the enum type exists and what values it has
    const enumCheck = await queryRunner.query(`
      SELECT enumlabel
      FROM pg_enum
      WHERE enumtypid = (
        SELECT oid
        FROM pg_type
        WHERE typname = 'transactions_category_enum'
      );
    `);

    const existingValues = enumCheck.map((row: any) => row.enumlabel);
    console.log('Existing enum values:', existingValues);

    // Only add the value if it doesn't exist
    if (!existingValues.includes('admin_adjustment')) {
      console.log('Adding admin_adjustment to transactions_category_enum');
      await queryRunner.query(`
        ALTER TYPE transactions_category_enum ADD VALUE 'admin_adjustment';
      `);
    } else {
      console.log('admin_adjustment already exists in transactions_category_enum');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum and updating all references
    // For safety, we'll leave the enum value in place during rollback
    console.log('Warning: Cannot remove enum value admin_adjustment from transactions_category_enum');
    console.log('PostgreSQL does not support removing enum values. Manual intervention required for rollback.');
  }
}
