import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusBulkAssignmentJobTargetTable1753194436008 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'bonus_bulk_assignment_job_targets',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusBulkAssignmentJobId',
            type: 'integer',
          },
          {
            name: 'externalCustomerId',
            type: 'integer',
          },
          {
            name: 'events',
            type: 'json',
          },
          {
            name: 'status',
            type: 'text',
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'processedAt',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationships
    await queryRunner.createForeignKey(
      'pronet.bonus_bulk_assignment_job_targets',
      new TableForeignKey({
        name: 'FK_bonus_bulk_assignment_job_targets_bonusBulkAssignmentJobId',
        columnNames: ['bonusBulkAssignmentJobId'],
        referencedTableName: 'bonus_bulk_assignment_jobs',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'pronet.bonus_bulk_assignment_job_targets',
      new TableForeignKey({
        name: 'FK_bonus_bulk_assignment_job_targets_externalCustomerId',
        columnNames: ['externalCustomerId'],
        referencedTableName: 'customers',
        referencedSchema: 'pronet',
        referencedColumnNames: ['externalId'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_job_targets_jobId',
        columnNames: ['bonusBulkAssignmentJobId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_job_targets_externalCustomerId',
        columnNames: ['externalCustomerId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_job_targets_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_job_targets_processedAt',
        columnNames: ['processedAt'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      'IDX_bonus_bulk_assignment_job_targets_processedAt',
    );
    await queryRunner.dropIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      'IDX_bonus_bulk_assignment_job_targets_status',
    );
    await queryRunner.dropIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      'IDX_bonus_bulk_assignment_job_targets_externalCustomerId',
    );
    await queryRunner.dropIndex(
      'pronet.bonus_bulk_assignment_job_targets',
      'IDX_bonus_bulk_assignment_job_targets_jobId',
    );

    // Drop foreign keys
    await queryRunner.dropForeignKey(
      'pronet.bonus_bulk_assignment_job_targets',
      'FK_bonus_bulk_assignment_job_targets_externalCustomerId',
    );
    await queryRunner.dropForeignKey(
      'pronet.bonus_bulk_assignment_job_targets',
      'FK_bonus_bulk_assignment_job_targets_bonusBulkAssignmentJobId',
    );

    // Drop table
    await queryRunner.dropTable('pronet.bonus_bulk_assignment_job_targets');
  }
}
