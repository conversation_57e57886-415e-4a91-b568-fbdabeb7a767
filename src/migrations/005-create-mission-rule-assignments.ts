import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateMissionRuleAssignments1703000000005 implements MigrationInterface {
  name = 'CreateMissionRuleAssignments1703000000005';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'mission_rule_assignments',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'missionId',
            type: 'integer',
          },
          {
            name: 'missionRuleId',
            type: 'integer',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create unique index on missionId and missionRuleId (one assignment per mission-rule pair)
    await queryRunner.createIndex(
      'mission_rule_assignments',
      new TableIndex({
        name: 'IDX_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_UNIQUE',
        columnNames: ['missionId', 'missionRuleId'],
        isUnique: true,
      }),
    );

    // Create index on missionId for faster mission-based queries
    await queryRunner.createIndex(
      'mission_rule_assignments',
      new TableIndex({
        name: 'IDX_MISSION_RULE_ASSIGNMENTS_MISSION_ID',
        columnNames: ['missionId'],
      }),
    );

    // Create index on missionRuleId for faster rule-based queries
    await queryRunner.createIndex(
      'mission_rule_assignments',
      new TableIndex({
        name: 'IDX_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_ID',
        columnNames: ['missionRuleId'],
      }),
    );

    // Create foreign key constraint to missions table
    await queryRunner.createForeignKey(
      'mission_rule_assignments',
      new TableForeignKey({
        columnNames: ['missionId'],
        referencedTableName: 'missions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_RULE_ASSIGNMENTS_MISSION_ID',
      }),
    );

    // Create foreign key constraint to mission_rules table
    await queryRunner.createForeignKey(
      'mission_rule_assignments',
      new TableForeignKey({
        columnNames: ['missionRuleId'],
        referencedTableName: 'mission_rules',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_ID',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('mission_rule_assignments', 'FK_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_ID');
    await queryRunner.dropForeignKey('mission_rule_assignments', 'FK_MISSION_RULE_ASSIGNMENTS_MISSION_ID');
    await queryRunner.dropIndex('mission_rule_assignments', 'IDX_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_ID');
    await queryRunner.dropIndex('mission_rule_assignments', 'IDX_MISSION_RULE_ASSIGNMENTS_MISSION_ID');
    await queryRunner.dropIndex('mission_rule_assignments', 'IDX_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_UNIQUE');
    await queryRunner.dropTable('mission_rule_assignments');
  }
}
