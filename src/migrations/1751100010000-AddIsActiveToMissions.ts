import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsActiveToMissions1751100010000 implements MigrationInterface {
  name = 'AddIsActiveToMissions1751100010000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add isActive boolean field to missions table with default value true
    await queryRunner.query(`
      ALTER TABLE "missions" 
      ADD COLUMN "isActive" boolean NOT NULL DEFAULT true
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the isActive column
    await queryRunner.query(`
      ALTER TABLE "missions" 
      DROP COLUMN "isActive"
    `);
  }
}
