import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHappyHoursBoostPercentageToLossbackBonus1753194440000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add happyHoursBoostPercentage column to csn_lossback_bonuses table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonuses" 
      ADD COLUMN "happyHoursBoostPercentage" DECIMAL(5,2) NOT NULL DEFAULT 2.00
    `);

    // Add happyHoursBoostPercentage column to csn_lossback_bonus_templates table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonus_templates" 
      ADD COLUMN "happyHoursBoostPercentage" DECIMAL(5,2) NOT NULL DEFAULT 2.00
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove happyHoursBoostPercentage column from csn_lossback_bonuses table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonuses" 
      DROP COLUMN "happyHoursBoostPercentage"
    `);

    // Remove happyHoursBoostPercentage column from csn_lossback_bonus_templates table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonus_templates" 
      DROP COLUMN "happyHoursBoostPercentage"
    `);
  }
}
