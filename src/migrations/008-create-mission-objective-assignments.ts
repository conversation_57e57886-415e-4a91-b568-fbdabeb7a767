import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateMissionObjectiveAssignments1703000000008 implements MigrationInterface {
  name = 'CreateMissionObjectiveAssignments1703000000008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'mission_objective_assignments',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'userId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'progress',
            type: 'integer',
            default: 0,
            isNullable: false,
          },
          {
            name: 'lastCheckedRecordTimestamp',
            type: 'bigint',
          },
          {
            name: 'missionObjectiveId',
            type: 'integer',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create unique index for mission-objective pair
    await queryRunner.createIndex(
      'mission_objective_assignments',
      new TableIndex({
        name: 'IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_UNIQUE',
        columnNames: ['userId', 'missionObjectiveId'],
        isUnique: true,
      }),
    );

    // Create index on userId for faster mission-based queries
    await queryRunner.createIndex(
      'mission_objective_assignments',
      new TableIndex({
        name: 'IDX_MISSION_OBJECTIVE_ASSIGNMENTS_USER_ID',
        columnNames: ['userId'],
      }),
    );

    // Create index on missionObjectiveId for faster objective-based queries
    await queryRunner.createIndex(
      'mission_objective_assignments',
      new TableIndex({
        name: 'IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_ID',
        columnNames: ['missionObjectiveId'],
      }),
    );

    // Create foreign key constraint to mission_objectives table
    await queryRunner.createForeignKey(
      'mission_objective_assignments',
      new TableForeignKey({
        columnNames: ['missionObjectiveId'],
        referencedTableName: 'mission_objectives',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_ID',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey(
      'mission_objective_assignments',
      'FK_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_ID',
    );
    await queryRunner.dropIndex(
      'mission_objective_assignments',
      'IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_ID',
    );
    await queryRunner.dropIndex('mission_objective_assignments', 'IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_ID');
    await queryRunner.dropIndex(
      'mission_objective_assignments',
      'IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_UNIQUE',
    );
    await queryRunner.dropTable('mission_objective_assignments');
  }
}
