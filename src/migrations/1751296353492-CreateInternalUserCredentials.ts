import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateInternalUserCredentials1751296353492 implements MigrationInterface {
  name = 'CreateInternalUserCredentials1751296353492';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the users_credentials table in the internal schema
    await queryRunner.createTable(
      new Table({
        name: 'internal.users_credentials',
        columns: [
          {
            name: 'user_id',
            type: 'integer',
            isPrimary: true,
          },
          {
            name: 'panel_name',
            type: 'text',
            isPrimary: true,
          },
          {
            name: 'login',
            type: 'text',
          },
          {
            name: 'password',
            type: 'text',
          },
          {
            name: 'otp_secret',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key constraint to reference internal.users
    await queryRunner.createForeignKey(
      'internal.users_credentials',
      new TableForeignKey({
        name: 'FK_INTERNAL_USER_CREDENTIALS_USER_ID',
        columnNames: ['user_id'],
        referencedTableName: 'internal.users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create index for faster lookups by user_id
    await queryRunner.createIndex(
      'internal.users_credentials',
      new TableIndex({
        name: 'IDX_INTERNAL_USER_CREDENTIALS_USER_ID',
        columnNames: ['user_id'],
      }),
    );

    // Create index for faster lookups by panel_name
    await queryRunner.createIndex(
      'internal.users_credentials',
      new TableIndex({
        name: 'IDX_INTERNAL_USER_CREDENTIALS_PANEL_NAME',
        columnNames: ['panel_name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.dropIndex('internal.users_credentials', 'IDX_INTERNAL_USER_CREDENTIALS_USER_ID');
    await queryRunner.dropIndex('internal.users_credentials', 'IDX_INTERNAL_USER_CREDENTIALS_PANEL_NAME');

    // Drop foreign key
    await queryRunner.dropForeignKey('internal.users_credentials', 'FK_INTERNAL_USER_CREDENTIALS_USER_ID');

    // Drop the table
    await queryRunner.dropTable('internal.users_credentials');
  }
}
