import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusTemplateRuleTable1753039786437 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create bonus_template_rules table
    await queryRunner.createTable(
      new Table({
        name: 'bonus_template_rules',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusTemplateId',
            type: 'integer',
          },
          {
            name: 'criterium',
            type: 'text',
          },
          {
            name: 'firstOperand',
            type: 'text',
          },
          {
            name: 'secondOperand',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'operator',
            type: 'text',
          },
          {
            name: 'startsInSeconds',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'endsInSeconds',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationship
    await queryRunner.createForeignKey(
      'pronet.bonus_template_rules',
      new TableForeignKey({
        name: 'FK_bonus_template_rules_bonus_template_id',
        columnNames: ['bonusTemplateId'],
        referencedTableName: 'bonus_templates',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_template_rules',
      new TableIndex({
        name: 'IDX_bonus_template_rules_bonus_template_id',
        columnNames: ['bonusTemplateId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_template_rules',
      new TableIndex({
        name: 'IDX_bonus_template_rules_criterium',
        columnNames: ['criterium'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_template_rules',
      new TableIndex({
        name: 'IDX_bonus_template_rules_timing',
        columnNames: ['startsInSeconds', 'endsInSeconds'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('pronet.bonus_template_rules', 'IDX_bonus_template_rules_timing');
    await queryRunner.dropIndex('pronet.bonus_template_rules', 'IDX_bonus_template_rules_criterium');
    await queryRunner.dropIndex('pronet.bonus_template_rules', 'IDX_bonus_template_rules_bonus_template_id');

    await queryRunner.dropForeignKey('pronet.bonus_template_rules', 'FK_bonus_template_rules_bonus_template_id');

    await queryRunner.dropTable('pronet.bonus_template_rules');
  }
}
