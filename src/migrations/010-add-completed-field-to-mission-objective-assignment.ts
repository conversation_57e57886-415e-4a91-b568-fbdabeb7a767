import { MigrationInterface, QueryRunner } from 'typeorm';

export class AdCompletedFieldToMissionObjectiveAssignment1703000000010 implements MigrationInterface {
  name = 'AdCompletedFieldToMissionObjectiveAssignment1703000000010';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraint from mission_objective_assignments.userId to extended_users.externalId
    // This clarifies that userId in assignments refers to externalId in extended_users
    await queryRunner.query(
      `ALTER TABLE "mission_objective_assignments" ADD "isCompleted" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "mission_objective_assignments" DROP COLUMN "isCompleted"`);
  }
}
