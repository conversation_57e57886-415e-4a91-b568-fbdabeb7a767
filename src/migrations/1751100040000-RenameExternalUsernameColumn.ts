import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameExternalUsernameColumn1751100040000 implements MigrationInterface {
  name = 'RenameExternalUsernameColumn1751100040000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Rename external_username column to externalUsername for casing consistency
    await queryRunner.query(`
      ALTER TABLE "extended_users" 
      RENAME COLUMN "external_username" TO "externalUsername"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert the column name back to external_username
    await queryRunner.query(`
      ALTER TABLE "extended_users" 
      RENAME COLUMN "externalUsername" TO "external_username"
    `);
  }
}
