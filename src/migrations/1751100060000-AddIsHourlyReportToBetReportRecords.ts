import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsHourlyReportToBetReportRecords1751100060000 implements MigrationInterface {
  name = 'AddIsHourlyReportToBetReportRecords1751100060000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "bet_report_records" ADD "is_hourly_report" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bet_report_records" DROP COLUMN "is_hourly_report"`);
  }
}
