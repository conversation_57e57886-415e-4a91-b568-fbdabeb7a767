import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey, TableIndex } from 'typeorm';

export class CreateSiteClaimableBonusesTable1753200000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create site_claimable_bonuses table
    await queryRunner.createTable(
      new Table({
        name: 'site_claimable_bonuses',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'slotName',
            type: 'text',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create unique index on slotName
    await queryRunner.createIndex(
      'pronet.site_claimable_bonuses',
      new TableIndex({
        name: 'IDX_site_claimable_bonuses_slotName',
        columnNames: ['slotName'],
        isUnique: true,
      }),
    );

    // Create index on isActive
    await queryRunner.createIndex(
      'pronet.site_claimable_bonuses',
      new TableIndex({
        name: 'IDX_site_claimable_bonuses_isActive',
        columnNames: ['isActive'],
      }),
    );

    // Create foreign key to bonuses table
    await queryRunner.createForeignKey(
      'pronet.site_claimable_bonuses',
      new TableForeignKey({
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Insert initial 3 slots without bonus IDs (will be set later via API)
    // We'll use a dummy bonus ID that we'll create first, or handle this differently
    // For now, let's create the slots without foreign key constraints initially

    // First, let's check if any bonuses exist and get the first one, or create a placeholder
    const bonusResult = await queryRunner.query(`
      SELECT id FROM pronet.bonuses WHERE "deletedAt" IS NULL ORDER BY id LIMIT 1;
    `);

    let defaultBonusId = 1;
    if (bonusResult && bonusResult.length > 0) {
      defaultBonusId = bonusResult[0].id;
    } else {
      // Create a placeholder bonus for initial setup
      const insertResult = await queryRunner.query(`
        INSERT INTO pronet.bonuses (name, type, "isActive", "expiresAt", "deletedAt")
        VALUES ('Placeholder Bonus', 'placeholder', false, NULL, NULL)
        RETURNING id;
      `);
      defaultBonusId = insertResult[0].id;
    }

    // Insert initial 3 slots with the available bonus ID (inactive by default)
    await queryRunner.query(
      `
      INSERT INTO pronet.site_claimable_bonuses ("slotName", "bonusId", "isActive") VALUES
      ('time_1', $1, false),
      ('time_2', $1, false),
      ('happy_1', $1, false),
      ('happy_2', $1, false),
      ('happy_3', $1, false),
      ('happy_4', $1, false),
      ('happy_5', $1, false),
      ('happy_6', $1, false),
      ('welcome', $1, false)
      ON CONFLICT ("slotName") DO NOTHING;
    `,
      [defaultBonusId],
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key first
    const table = await queryRunner.getTable('pronet.site_claimable_bonuses');
    if (table) {
      const foreignKey = table.foreignKeys.find((fk) => fk.columnNames.indexOf('bonusId') !== -1);
      if (foreignKey) {
        await queryRunner.dropForeignKey('pronet.site_claimable_bonuses', foreignKey);
      }
    }

    // Drop indexes
    await queryRunner.dropIndex('pronet.site_claimable_bonuses', 'IDX_site_claimable_bonuses_isActive');
    await queryRunner.dropIndex('pronet.site_claimable_bonuses', 'IDX_site_claimable_bonuses_slotName');

    // Drop table
    await queryRunner.dropTable('pronet.site_claimable_bonuses');
  }
}
