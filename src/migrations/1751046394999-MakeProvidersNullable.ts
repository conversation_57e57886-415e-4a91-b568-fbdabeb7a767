import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeProvidersNullable1751046394999 implements MigrationInterface {
  name = 'MakeProvidersNullable1751046394999';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Make providers column nullable in market_products table
    await queryRunner.query(`
      ALTER TABLE "market_products" 
      ALTER COLUMN "providers" DROP NOT NULL
    `);

    // Make providers column nullable in market_product_requests table
    await queryRunner.query(`
      ALTER TABLE "market_product_requests" 
      ALTER COLUMN "providers" DROP NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert providers column to NOT NULL in market_product_requests table
    // First, update any NULL values to empty objects
    await queryRunner.query(`
      UPDATE "market_product_requests" 
      SET "providers" = '{}' 
      WHERE "providers" IS NULL
    `);
    
    await queryRunner.query(`
      ALTER TABLE "market_product_requests" 
      ALTER COLUMN "providers" SET NOT NULL
    `);

    // Revert providers column to NOT NULL in market_products table
    // First, update any NULL values to empty objects
    await queryRunner.query(`
      UPDATE "market_products" 
      SET "providers" = '{}' 
      WHERE "providers" IS NULL
    `);
    
    await queryRunner.query(`
      ALTER TABLE "market_products" 
      ALTER COLUMN "providers" SET NOT NULL
    `);
  }
}
