import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHistoryFieldToMissionObjectiveAssignment1703000000011 implements MigrationInterface {
  name = 'AddHistoryFieldToMissionObjectiveAssignment1703000000011';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraint from mission_objective_assignments.userId to extended_users.externalId
    // This clarifies that userId in assignments refers to externalId in extended_users
    await queryRunner.query(`ALTER TABLE "mission_objective_assignments" ADD "history" json NOT NULL DEFAULT '[]'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "mission_objective_assignments" DROP COLUMN "history"`);
  }
}
