import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateBonusTable1753039113542 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create bonuses table
    await queryRunner.createTable(
      new Table({
        name: 'bonuses',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'text',
          },
          {
            name: 'type',
            type: 'text',
          },
          {
            name: 'isActive',
            type: 'boolean',
          },
          {
            name: 'expiresAt',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonuses',
      new TableIndex({
        name: 'IDX_bonuses_type',
        columnNames: ['type'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonuses',
      new TableIndex({
        name: 'IDX_bonuses_isActive',
        columnNames: ['isActive'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.bonuses', 'IDX_bonuses_isActive');
    await queryRunner.dropIndex('pronet.bonuses', 'IDX_bonuses_type');

    // Drop tables
    await queryRunner.dropTable('pronet.bonuses');
  }
}
