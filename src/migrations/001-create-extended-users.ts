import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateExtendedUsers1703000000001 implements MigrationInterface {
  name = 'CreateExtendedUsers1703000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'extended_users',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'externalId',
            type: 'integer',
            isUnique: true,
          },
          {
            name: 'points',
            type: 'integer',
            default: 0,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create index on externalId for faster lookups
    await queryRunner.createIndex(
      'extended_users',
      new TableIndex({
        name: 'IDX_EXTENDED_USERS_EXTERNAL_ID',
        columnNames: ['externalId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('extended_users', 'IDX_EXTENDED_USERS_EXTERNAL_ID');
    await queryRunner.dropTable('extended_users');
  }
}
