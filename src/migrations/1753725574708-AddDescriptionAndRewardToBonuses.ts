import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDescriptionAndRewardToBonuses1753725574708 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add description column
    await queryRunner.addColumn(
      'pronet.bonuses',
      new TableColumn({
        name: 'description',
        type: 'text',
        isNullable: false,
        default: "''",
      }),
    );

    // Add reward column
    await queryRunner.addColumn(
      'pronet.bonuses',
      new TableColumn({
        name: 'reward',
        type: 'text',
        isNullable: false,
        default: "''",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove reward column
    await queryRunner.dropColumn('pronet.bonuses', 'reward');

    // Remove description column
    await queryRunner.dropColumn('pronet.bonuses', 'description');
  }
}
