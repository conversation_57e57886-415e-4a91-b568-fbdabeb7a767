import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class AddTrialBonus1753737420308 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create trial_bonuses table
    await queryRunner.createTable(
      new Table({
        name: 'trial_bonuses',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'externalBonusName',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'externalBonusId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
    );

    // Create trial_bonus_templates table
    await queryRunner.createTable(
      new Table({
        name: 'trial_bonus_templates',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusTemplateId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'externalBonusName',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'externalBonusId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
    );

    // Add foreign key for trial_bonuses.bonusId -> bonuses.id
    await queryRunner.createForeignKey(
      'pronet.trial_bonuses',
      new TableForeignKey({
        name: 'FK_trial_bonuses_bonusId',
        columnNames: ['bonusId'],
        referencedTableName: 'pronet.bonuses',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Add foreign key for trial_bonus_templates.bonusTemplateId -> bonus_templates.id
    await queryRunner.createForeignKey(
      'pronet.trial_bonus_templates',
      new TableForeignKey({
        name: 'FK_trial_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
        referencedTableName: 'pronet.bonus_templates',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first
    await queryRunner.dropForeignKey('pronet.trial_bonus_templates', 'FK_trial_bonus_templates_bonusTemplateId');
    await queryRunner.dropForeignKey('pronet.trial_bonuses', 'FK_trial_bonuses_bonusId');

    // Drop tables
    await queryRunner.dropTable('pronet.trial_bonus_templates');
    await queryRunner.dropTable('pronet.trial_bonuses');
  }
}
