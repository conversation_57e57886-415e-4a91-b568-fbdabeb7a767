import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateBonusTemplateTable1753039423570 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create bonus_templates table
    await queryRunner.createTable(
      new Table({
        name: 'bonus_templates',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'text',
          },
          {
            name: 'type',
            type: 'text',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonus_templates',
      new TableIndex({
        name: 'IDX_bonus_templates_type',
        columnNames: ['type'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.bonus_templates', 'IDX_bonus_templates_type');

    // Drop tables
    await queryRunner.dropTable('pronet.bonus_templates');
  }
}
