import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateMissionObjectives170300000006 implements MigrationInterface {
  name = 'CreateMissionObjectives1703000000006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'mission_objectives',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'missionId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'objectiveType',
            type: 'enum',
            enum: ['slot', 'liveCasino', 'deposit', 'withdraw', 'turnover'],
          },
          {
            name: 'subtype',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'operator',
            type: 'enum',
            enum: ['eq', 'ne', 'gt', 'lt', 'ge', 'le'],
          },
          {
            name: 'targetValue',
            type: 'text',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'timeframeStart',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'timeframeEnd',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      'mission_objectives',
      new TableForeignKey({
        columnNames: ['missionId'],
        referencedTableName: 'missions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_OBJECTIVES_MISSION_ID',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('mission_objectives', 'FK_MISSION_OBJECTIVES_MISSION_ID');
    await queryRunner.dropTable('mission_objectives');
  }
}
