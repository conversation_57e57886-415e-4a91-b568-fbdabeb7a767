import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusBulkAssignmentJobTable1753194325952 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'bonus_bulk_assignment_jobs',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'bonusValues',
            type: 'json',
          },
          {
            name: 'bonusValuesVersion',
            type: 'integer',
          },
          {
            name: 'status',
            type: 'text',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationship
    await queryRunner.createForeignKey(
      'pronet.bonus_bulk_assignment_jobs',
      new TableForeignKey({
        name: 'FK_bonus_bulk_assignment_jobs_bonusId',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_jobs',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_jobs_bonusId',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_jobs',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_jobs_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_bulk_assignment_jobs',
      new TableIndex({
        name: 'IDX_bonus_bulk_assignment_jobs_createdAt',
        columnNames: ['createdAt'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.bonus_bulk_assignment_jobs', 'IDX_bonus_bulk_assignment_jobs_createdAt');
    await queryRunner.dropIndex('pronet.bonus_bulk_assignment_jobs', 'IDX_bonus_bulk_assignment_jobs_status');
    await queryRunner.dropIndex('pronet.bonus_bulk_assignment_jobs', 'IDX_bonus_bulk_assignment_jobs_bonusId');

    // Drop foreign key
    await queryRunner.dropForeignKey('pronet.bonus_bulk_assignment_jobs', 'FK_bonus_bulk_assignment_jobs_bonusId');

    // Drop table
    await queryRunner.dropTable('pronet.bonus_bulk_assignment_jobs');
  }
}
