import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExternalUsernameToExtendedUsers1703000000012 implements MigrationInterface {
  name = 'AddExternalUsernameToExtendedUsers1703000000012';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "extended_users" ADD "external_username" VARCHAR(32) NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "extended_users" DROP COLUMN "external_username"`);
  }
}
