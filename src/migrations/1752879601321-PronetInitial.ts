import { MigrationInterface, QueryRunner } from 'typeorm';

export class PronetInitial1752879601321 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the pronet schema if it doesn't exist
    await queryRunner.query(`CREATE SCHEMA IF NOT EXISTS "pronet"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP SCHEMA IF EXISTS "pronet" CASCADE`);
  }
}
