import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey, TableIndex } from 'typeorm';

export class CreateMarketProductRequestsTable1751046393999 implements MigrationInterface {
  name = 'CreateMarketProductRequestsTable1751046393999';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the market_product_requests table
    await queryRunner.createTable(
      new Table({
        name: 'market_product_requests',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['rejected', 'refunded', 'pending', 'completed'],
            isNullable: false,
          },
          {
            name: 'currency',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'providers',
            type: 'json',
            isNullable: false,
          },
          {
            name: 'userId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'productId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'history',
            type: 'json',
            default: "'[]'",
            isNullable: false,
          },
          {
            name: 'rejectReason',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'market_product_requests',
      new TableIndex({
        name: 'IDX_MARKET_PRODUCT_REQUESTS_USER_ID',
        columnNames: ['userId'],
      }),
    );

    await queryRunner.createIndex(
      'market_product_requests',
      new TableIndex({
        name: 'IDX_MARKET_PRODUCT_REQUESTS_STATUS',
        columnNames: ['status'],
      }),
    );

    // Create foreign key constraint
    await queryRunner.createForeignKey(
      'market_product_requests',
      new TableForeignKey({
        name: 'FK_MARKET_PRODUCT_REQUESTS_USER',
        columnNames: ['userId'],
        referencedTableName: 'extended_users',
        referencedColumnNames: ['externalId'],
      }),
    );

    await queryRunner.createForeignKey(
      'market_product_requests',
      new TableForeignKey({
        name: 'FK_MARKET_PRODUCT_REQUESTS_PRODUCT',
        columnNames: ['productId'],
        referencedTableName: 'market_products',
        referencedColumnNames: ['id'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    await queryRunner.dropForeignKey('market_product_requests', 'FK_MARKET_PRODUCT_REQUESTS_PRODUCT');
    await queryRunner.dropForeignKey('market_product_requests', 'FK_MARKET_PRODUCT_REQUESTS_USER');

    // Drop indexes
    await queryRunner.dropIndex('market_product_requests', 'IDX_MARKET_PRODUCT_REQUESTS_STATUS');
    await queryRunner.dropIndex('market_product_requests', 'IDX_MARKET_PRODUCT_REQUESTS_USER_ID');

    // Drop the table
    await queryRunner.dropTable('market_product_requests');
  }
}
