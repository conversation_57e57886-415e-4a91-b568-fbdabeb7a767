import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusPromocodeTable1753039860708 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'bonus_promocodes',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'code',
            type: 'text',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'activations',
            type: 'integer',
            default: 0,
          },
          {
            name: 'maxActivations',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create foreign key relationship
    await queryRunner.createForeignKey(
      'pronet.bonus_promocodes',
      new TableForeignKey({
        name: 'FK_bonus_promocodes_bonusId',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonus_promocodes',
      new TableIndex({
        name: 'IDX_bonus_promocodes_code',
        columnNames: ['code'],
        isUnique: true,
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_promocodes',
      new TableIndex({
        name: 'IDX_bonus_promocodes_bonusId',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_promocodes',
      new TableIndex({
        name: 'IDX_bonus_promocodes_isActive',
        columnNames: ['isActive'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.bonus_promocodes', 'IDX_bonus_promocodes_isActive');
    await queryRunner.dropIndex('pronet.bonus_promocodes', 'IDX_bonus_promocodes_bonusId');
    await queryRunner.dropIndex('pronet.bonus_promocodes', 'IDX_bonus_promocodes_code');

    // Drop foreign key
    await queryRunner.dropForeignKey('pronet.bonus_promocodes', 'FK_bonus_promocodes_bonusId');

    // Drop table
    await queryRunner.dropTable('pronet.bonus_promocodes');
  }
}
