import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveFKConstraintFromBonusBulkJobs1753702338349 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey(
      'pronet.bonus_bulk_assignment_job_targets',
      'FK_bonus_bulk_assignment_job_targets_externalCustomerId',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
