import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey, TableIndex } from 'typeorm';

export class CreateCasinoFreespinBonusTable1753040439529 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create csn_freespin_bonuses table
    await queryRunner.createTable(
      new Table({
        name: 'csn_freespin_bonuses',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'vendorId',
            type: 'integer',
          },
          {
            name: 'vendorName',
            type: 'text',
          },
          {
            name: 'values',
            type: 'json',
          },
          {
            name: 'gameIds',
            type: 'integer',
            isArray: true,
            default: 'ARRAY[]::integer[]',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create csn_freespin_bonus_templates table
    await queryRunner.createTable(
      new Table({
        name: 'csn_freespin_bonus_templates',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'vendorId',
            type: 'integer',
          },
          {
            name: 'vendorName',
            type: 'text',
          },
          {
            name: 'values',
            type: 'json',
          },
          {
            name: 'gameIds',
            type: 'integer',
            isArray: true,
            default: 'ARRAY[]::integer[]',
          },
          {
            name: 'validForDays',
            type: 'integer',
          },
          {
            name: 'bonusTemplateId',
            type: 'integer',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationships
    await queryRunner.createForeignKey(
      'pronet.csn_freespin_bonuses',
      new TableForeignKey({
        name: 'FK_csn_freespin_bonuses_bonus_id',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'pronet.csn_freespin_bonus_templates',
      new TableForeignKey({
        name: 'FK_csn_freespin_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
        referencedTableName: 'bonus_templates',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.csn_freespin_bonuses',
      new TableIndex({
        name: 'IDX_csn_freespin_bonuses_bonus_id',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.csn_freespin_bonuses',
      new TableIndex({
        name: 'IDX_csn_freespin_bonuses_vendorId',
        columnNames: ['vendorId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.csn_freespin_bonus_templates',
      new TableIndex({
        name: 'IDX_csn_freespin_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.csn_freespin_bonus_templates',
      new TableIndex({
        name: 'IDX_csn_freespin_bonus_templates_vendorId',
        columnNames: ['vendorId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.csn_freespin_bonus_templates', 'IDX_csn_freespin_bonus_templates_vendorId');
    await queryRunner.dropIndex(
      'pronet.csn_freespin_bonus_templates',
      'IDX_csn_freespin_bonus_templates_bonusTemplateId',
    );
    await queryRunner.dropIndex('pronet.csn_freespin_bonuses', 'IDX_csn_freespin_bonuses_vendorId');
    await queryRunner.dropIndex('pronet.csn_freespin_bonuses', 'IDX_csn_freespin_bonuses_bonus_id');

    // Drop foreign keys
    await queryRunner.dropForeignKey(
      'pronet.csn_freespin_bonus_templates',
      'FK_csn_freespin_bonus_templates_bonusTemplateId',
    );
    await queryRunner.dropForeignKey('pronet.csn_freespin_bonuses', 'FK_csn_freespin_bonuses_bonus_id');

    // Drop tables
    await queryRunner.dropTable('pronet.csn_freespin_bonus_templates');
    await queryRunner.dropTable('pronet.csn_freespin_bonuses');
  }
}
