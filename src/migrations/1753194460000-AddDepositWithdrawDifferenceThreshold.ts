import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDepositWithdrawDifferenceThreshold1753194460000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add depositWithDrawDifferenceThreshold column to csn_lossback_bonuses table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonuses" 
      ADD COLUMN "depositWithDrawDifferenceThreshold" DECIMAL(10,2) NOT NULL DEFAULT 100.00
    `);

    // Add depositWithDrawDifferenceThreshold column to csn_lossback_bonus_templates table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonus_templates" 
      ADD COLUMN "depositWithDrawDifferenceThreshold" DECIMAL(10,2) NOT NULL DEFAULT 100.00
    `);

    // Add depositWithDrawDifferenceThreshold column to csn_weekly_lossback_bonuses table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_weekly_lossback_bonuses" 
      ADD COLUMN "depositWithDrawDifferenceThreshold" DECIMAL(10,2) NOT NULL DEFAULT 100.00
    `);

    // Add depositWithDrawDifferenceThreshold column to csn_weekly_lossback_bonus_templates table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_weekly_lossback_bonus_templates" 
      ADD COLUMN "depositWithDrawDifferenceThreshold" DECIMAL(10,2) NOT NULL DEFAULT 100.00
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove depositWithDrawDifferenceThreshold column from csn_lossback_bonuses table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonuses" 
      DROP COLUMN "depositWithDrawDifferenceThreshold"
    `);

    // Remove depositWithDrawDifferenceThreshold column from csn_lossback_bonus_templates table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_lossback_bonus_templates" 
      DROP COLUMN "depositWithDrawDifferenceThreshold"
    `);

    // Remove depositWithDrawDifferenceThreshold column from csn_weekly_lossback_bonuses table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_weekly_lossback_bonuses" 
      DROP COLUMN "depositWithDrawDifferenceThreshold"
    `);

    // Remove depositWithDrawDifferenceThreshold column from csn_weekly_lossback_bonus_templates table
    await queryRunner.query(`
      ALTER TABLE "pronet"."csn_weekly_lossback_bonus_templates" 
      DROP COLUMN "depositWithDrawDifferenceThreshold"
    `);
  }
}
