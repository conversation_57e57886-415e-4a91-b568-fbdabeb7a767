import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey, TableIndex } from 'typeorm';

export class CreateCasinoLossbackBonus1753040450000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create csn_lossback_bonuses table
    await queryRunner.createTable(
      new Table({
        name: 'csn_lossback_bonuses',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'maxBalance',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'lossbackPercentage',
            type: 'decimal',
            precision: 5,
            scale: 2,
          },
          {
            name: 'happyHoursStart',
            type: 'time',
            isNullable: true,
          },
          {
            name: 'happyHoursEnd',
            type: 'time',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create csn_lossback_bonus_templates table
    await queryRunner.createTable(
      new Table({
        name: 'csn_lossback_bonus_templates',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusTemplateId',
            type: 'integer',
          },
          {
            name: 'maxBalance',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'lossbackPercentage',
            type: 'decimal',
            precision: 5,
            scale: 2,
          },
          {
            name: 'happyHoursStart',
            type: 'time',
            isNullable: true,
          },
          {
            name: 'happyHoursEnd',
            type: 'time',
            isNullable: true,
          },
          {
            name: 'validForDays',
            type: 'integer',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationships
    await queryRunner.createForeignKey(
      'pronet.csn_lossback_bonuses',
      new TableForeignKey({
        name: 'FK_csn_lossback_bonuses_bonusId',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'pronet.csn_lossback_bonus_templates',
      new TableForeignKey({
        name: 'FK_csn_lossback_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
        referencedTableName: 'bonus_templates',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.csn_lossback_bonuses',
      new TableIndex({
        name: 'IDX_csn_lossback_bonuses_bonus_id',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.csn_lossback_bonus_templates',
      new TableIndex({
        name: 'IDX_csn_lossback_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex(
      'pronet.csn_lossback_bonus_templates',
      'IDX_csn_lossback_bonus_templates_bonusTemplateId',
    );
    await queryRunner.dropIndex('pronet.csn_lossback_bonuses', 'IDX_csn_lossback_bonuses_bonus_id');

    // Drop foreign keys
    await queryRunner.dropForeignKey(
      'pronet.csn_lossback_bonus_templates',
      'FK_csn_lossback_bonus_templates_bonusTemplateId',
    );
    await queryRunner.dropForeignKey('pronet.csn_lossback_bonuses', 'FK_csn_lossback_bonuses_bonusId');

    // Drop tables
    await queryRunner.dropTable('pronet.csn_lossback_bonus_templates');
    await queryRunner.dropTable('pronet.csn_lossback_bonuses');
  }
}
