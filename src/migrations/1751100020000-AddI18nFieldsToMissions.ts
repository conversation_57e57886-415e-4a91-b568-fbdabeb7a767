import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddI18nFieldsToMissions1751100020000 implements MigrationInterface {
  name = 'AddI18nFieldsToMissions1751100020000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add name_i18n JSON field to missions table with default empty object
    await queryRunner.query(`
      ALTER TABLE "missions" 
      ADD COLUMN "name_i18n" json NOT NULL DEFAULT '{}'
    `);

    // Add description_i18n JSON field to missions table with default empty object
    await queryRunner.query(`
      ALTER TABLE "missions" 
      ADD COLUMN "description_i18n" json NOT NULL DEFAULT '{}'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the i18n columns
    await queryRunner.query(`
      ALTER TABLE "missions" 
      DROP COLUMN "description_i18n"
    `);

    await queryRunner.query(`
      ALTER TABLE "missions" 
      DROP COLUMN "name_i18n"
    `);
  }
}
