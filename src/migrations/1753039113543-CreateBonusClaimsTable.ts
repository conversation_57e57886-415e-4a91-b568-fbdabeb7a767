import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusClaimsTable1753039113543 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create bonus_claims table
    await queryRunner.createTable(
      new Table({
        name: 'bonus_claims',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'customerId',
            type: 'integer',
          },
          {
            name: 'source',
            type: 'text',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationships
    await queryRunner.createForeignKey(
      'pronet.bonus_claims',
      new TableForeignKey({
        name: 'FK_bonus_claims_bonusId',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'pronet.bonus_claims',
      new TableForeignKey({
        name: 'FK_bonus_claims_customerId',
        columnNames: ['customerId'],
        referencedTableName: 'customers',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_claims',
      new TableIndex({
        name: 'IDX_bonus_claims_bonusId',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_claims',
      new TableIndex({
        name: 'IDX_bonus_claims_customerId',
        columnNames: ['customerId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('pronet.bonus_claims', 'IDX_bonus_claims_customerId');
    await queryRunner.dropIndex('pronet.bonus_claims', 'IDX_bonus_claims_bonusId');

    await queryRunner.dropForeignKey('pronet.bonus_claims', 'FK_bonus_claims_customerId');
    await queryRunner.dropForeignKey('pronet.bonus_claims', 'FK_bonus_claims_bonusId');

    await queryRunner.dropTable('pronet.bonus_claims');
  }
}
