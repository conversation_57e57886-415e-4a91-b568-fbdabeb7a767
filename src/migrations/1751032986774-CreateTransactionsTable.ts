import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON><PERSON>, TableIndex } from 'typeorm';

export class CreateTransactionsTable1751032986774 implements MigrationInterface {
  name = 'CreateTransactionsTable1751032986774';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the transactions table
    await queryRunner.createTable(
      new Table({
        name: 'transactions',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'fromUserId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'toUserId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['deposit', 'withdrawal', 'charge', 'refund'],
            isNullable: false,
          },
          {
            name: 'category',
            type: 'enum',
            enum: ['mission_reward', 'market_purchase', 'admin_adjustment'],
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 20,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'metadata',
            type: 'json',
            default: "'{}'",
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'transactions',
      new TableIndex({
        name: 'IDX_TRANSACTIONS_FROM_USER_ID',
        columnNames: ['fromUserId'],
      }),
    );

    await queryRunner.createIndex(
      'transactions',
      new TableIndex({
        name: 'IDX_TRANSACTIONS_TO_USER_ID',
        columnNames: ['toUserId'],
      }),
    );

    // Create foreign key constraints (assuming users table exists)
    const fromUserForeignKey = new TableForeignKey({
      name: 'FK_TRANSACTIONS_FROM_USER',
      columnNames: ['fromUserId'],
      referencedTableName: 'extended_users',
      referencedColumnNames: ['externalId'],
    });

    const toUserForeignKey = new TableForeignKey({
      name: 'FK_TRANSACTIONS_TO_USER',
      columnNames: ['toUserId'],
      referencedTableName: 'extended_users',
      referencedColumnNames: ['externalId'],
    });

    await queryRunner.createForeignKey('transactions', fromUserForeignKey);
    await queryRunner.createForeignKey('transactions', toUserForeignKey);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.dropForeignKey('transactions', 'FK_TRANSACTIONS_TO_USER');
    await queryRunner.dropForeignKey('transactions', 'FK_TRANSACTIONS_FROM_USER');

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS IDX_TRANSACTIONS_TO_USER_ID`);
    await queryRunner.query(`DROP INDEX IF EXISTS IDX_TRANSACTIONS_FROM_USER_ID`);

    // Drop the table
    await queryRunner.dropTable('transactions');
  }
}
