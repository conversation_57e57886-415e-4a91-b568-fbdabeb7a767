import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateFinalMissionClaims1703000000013 implements MigrationInterface {
  name = 'CreateFinalMissionClaims1703000000013';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'final_mission_claims',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'userId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'claimType',
            type: 'varchar',
            length: '10',
            isNullable: false,
          },
          {
            name: 'grantedReward',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'NOW()',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'NOW()',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Add check constraint for claimType
    await queryRunner.query(
      `ALTER TABLE "final_mission_claims" ADD CONSTRAINT "CHK_FINAL_MISSION_CLAIMS_CLAIM_TYPE" CHECK ("claimType" IN ('daily', 'weekly', 'monthly'))`,
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'final_mission_claims',
      new TableForeignKey({
        name: 'FK_FINAL_MISSION_CLAIMS_USER_ID',
        columnNames: ['userId'],
        referencedTableName: 'extended_users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('final_mission_claims', 'FK_FINAL_MISSION_CLAIMS_USER_ID');
    await queryRunner.dropTable('final_mission_claims');
  }
}
