import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateMarketProductsTable1751035513171 implements MigrationInterface {
  name = 'CreateMarketProductsTable1751035513171';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the market_products table
    await queryRunner.createTable(
      new Table({
        name: 'market_products',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'slug',
            type: 'text',
            isNullable: false,
            isUnique: true,
          },
          {
            name: 'name_i18n',
            type: 'json',
            default: "'{}'",
            isNullable: false,
          },
          {
            name: 'description_i18n',
            type: 'json',
            default: "'{}'",
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['general', 'slots'],
            isNullable: false,
          },
          {
            name: 'category',
            type: 'enum',
            enum: ['free_spins', 'cash', 'reload', 'scatter'],
            isNullable: false,
          },
          {
            name: 'availableAmount',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'isMultiPerBuyer',
            type: 'boolean',
            default: false,
            isNullable: false,
          },
          {
            name: 'photoUrl',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'price',
            type: 'float',
            isNullable: false,
          },
          {
            name: 'currencies',
            type: 'json',
            default: "'[]'",
            isNullable: false,
          },
          {
            name: 'providers',
            type: 'json',
            default: "'{}'",
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'market_products',
      new TableIndex({
        name: 'IDX_MARKET_PRODUCTS_TYPE',
        columnNames: ['type'],
      }),
    );

    await queryRunner.createIndex(
      'market_products',
      new TableIndex({
        name: 'IDX_MARKET_PRODUCTS_CATEGORY',
        columnNames: ['category'],
      }),
    );

    await queryRunner.createIndex(
      'market_products',
      new TableIndex({
        name: 'IDX_MARKET_PRODUCTS_SLUG',
        columnNames: ['slug'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.dropIndex('market_products', 'IDX_MARKET_PRODUCTS_SLUG');
    await queryRunner.dropIndex('market_products', 'IDX_MARKET_PRODUCTS_CATEGORY');
    await queryRunner.dropIndex('market_products', 'IDX_MARKET_PRODUCTS_TYPE');

    // Drop the table
    await queryRunner.dropTable('market_products');
  }
}
