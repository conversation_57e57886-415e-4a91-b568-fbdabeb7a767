import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateBetReportRecordsTable1751100050000 implements MigrationInterface {
  name = 'CreateBetReportRecordsTable1751100050000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'bet_report_records',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'total_coupons',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'total_play_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_open_coupons',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'total_open_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_open_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_win_coupons',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'total_win_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_win_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_lose_coupons',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'total_lose_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_void_coupons',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'total_void_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_void_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_part_cashout_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_part_cashout_count',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'total_comp_cashout_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'total_comp_cashout_count',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'tot_real_bal_play_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'tot_bon_bal_play_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'tot_free_bal_play_amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'tot_real_bal_win_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'tot_bon_bal_win_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'tot_real_bal_void_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'tot_bon_bal_void_return',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'ngr',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'ggr',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'transaction_currency',
            type: 'varchar',
            length: '10',
            isNullable: true,
          },
          {
            name: 'start_date',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'end_date',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes for better query performance
    await queryRunner.createIndex(
      'bet_report_records',
      new TableIndex({
        name: 'IDX_BET_REPORT_RECORDS_START_DATE',
        columnNames: ['start_date'],
      }),
    );

    await queryRunner.createIndex(
      'bet_report_records',
      new TableIndex({
        name: 'IDX_BET_REPORT_RECORDS_END_DATE',
        columnNames: ['end_date'],
      }),
    );

    await queryRunner.createIndex(
      'bet_report_records',
      new TableIndex({
        name: 'IDX_BET_REPORT_RECORDS_DATE_RANGE',
        columnNames: ['start_date', 'end_date'],
      }),
    );

    await queryRunner.createIndex(
      'bet_report_records',
      new TableIndex({
        name: 'IDX_BET_REPORT_RECORDS_CREATED_AT',
        columnNames: ['created_at'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('bet_report_records', 'IDX_BET_REPORT_RECORDS_CREATED_AT');
    await queryRunner.dropIndex('bet_report_records', 'IDX_BET_REPORT_RECORDS_DATE_RANGE');
    await queryRunner.dropIndex('bet_report_records', 'IDX_BET_REPORT_RECORDS_END_DATE');
    await queryRunner.dropIndex('bet_report_records', 'IDX_BET_REPORT_RECORDS_START_DATE');
    await queryRunner.dropTable('bet_report_records');
  }
}
