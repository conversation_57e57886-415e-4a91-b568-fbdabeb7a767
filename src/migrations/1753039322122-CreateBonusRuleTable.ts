import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusRuleTable1753039322122 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'bonus_rules',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'criterium',
            type: 'text',
          },
          {
            name: 'firstOperand',
            type: 'text',
          },
          {
            name: 'secondOperand',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'operator',
            type: 'text',
          },
          {
            name: 'startsAt',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'endsAt',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationship
    await queryRunner.createForeignKey(
      'pronet.bonus_rules',
      new TableForeignKey({
        name: 'FK_bonus_rules_bonus_id',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonus_rules',
      new TableIndex({
        name: 'IDX_bonus_rules_bonus_id',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_rules',
      new TableIndex({
        name: 'IDX_bonus_rules_criterium',
        columnNames: ['criterium'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_rules',
      new TableIndex({
        name: 'IDX_bonus_rules_startsAt_endsAt',
        columnNames: ['startsAt', 'endsAt'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.bonus_rules', 'IDX_bonus_rules_startsAt_endsAt');
    await queryRunner.dropIndex('pronet.bonus_rules', 'IDX_bonus_rules_criterium');
    await queryRunner.dropIndex('pronet.bonus_rules', 'IDX_bonus_rules_bonus_id');

    // Drop foreign key
    await queryRunner.dropForeignKey('pronet.bonus_rules', 'FK_bonus_rules_bonus_id');

    // Drop table
    await queryRunner.dropTable('pronet.bonus_rules');
  }
}
