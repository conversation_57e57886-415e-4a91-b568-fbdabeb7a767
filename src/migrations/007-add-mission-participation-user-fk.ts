import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class AddMissionParticipationUserFk1703000000007 implements MigrationInterface {
  name = 'AddMissionParticipationUserFk1703000000007';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraint from mission_participations.userId to extended_users.externalId
    // This clarifies that userId in participations refers to externalId in extended_users
    await queryRunner.createForeignKey(
      'mission_participations',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedTableName: 'extended_users',
        referencedColumnNames: ['externalId'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_PARTICIPATIONS_USER_EXTERNAL_ID',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('mission_participations', 'FK_MISSION_PARTICIPATIONS_USER_EXTERNAL_ID');
  }
}
