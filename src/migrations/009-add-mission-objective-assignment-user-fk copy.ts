import { MigrationInterface, QueryRunner, TableFore<PERSON><PERSON><PERSON> } from 'typeorm';

export class AddMissionObjectiveAssignmentUserFk1703000000009 implements MigrationInterface {
  name = 'AddMissionObjectiveAssignmentUserFk1703000000009';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraint from mission_objective_assignments.userId to extended_users.externalId
    // This clarifies that userId in assignments refers to externalId in extended_users
    await queryRunner.createForeignKey(
      'mission_objective_assignments',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedTableName: 'extended_users',
        referencedColumnNames: ['externalId'],
        onDelete: 'CASCADE',
        name: 'FK_MISSION_OBJECTIVE_ASSIGNMENTS_USER_EXTERNAL_ID',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('mission_objective_assignments', 'FK_MISSION_OBJECTIVE_ASSIGNMENTS_USER_EXTERNAL_ID');
  }
}
