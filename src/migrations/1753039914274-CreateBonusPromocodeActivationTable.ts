import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateBonusPromocodeActivationTable1753039914274 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'bonus_promocode_activations',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'promocodeId',
            type: 'integer',
          },
          {
            name: 'customerId',
            type: 'integer',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationships
    await queryRunner.createForeignKey(
      'pronet.bonus_promocode_activations',
      new TableForeignKey({
        name: 'FK_bonus_promocode_activations_promocodeId',
        columnNames: ['promocodeId'],
        referencedTableName: 'bonus_promocodes',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'pronet.bonus_promocode_activations',
      new TableForeignKey({
        name: 'FK_bonus_promocode_activations_customerId',
        columnNames: ['customerId'],
        referencedTableName: 'customers',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes
    await queryRunner.createIndex(
      'pronet.bonus_promocode_activations',
      new TableIndex({
        name: 'IDX_bonus_promocode_activations_promocodeId',
        columnNames: ['promocodeId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_promocode_activations',
      new TableIndex({
        name: 'IDX_bonus_promocode_activations_customerId',
        columnNames: ['customerId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.bonus_promocode_activations',
      new TableIndex({
        name: 'IDX_bonus_promocode_activations_unique',
        columnNames: ['promocodeId', 'customerId'],
        isUnique: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.bonus_promocode_activations', 'IDX_bonus_promocode_activations_unique');
    await queryRunner.dropIndex('pronet.bonus_promocode_activations', 'IDX_bonus_promocode_activations_customerId');
    await queryRunner.dropIndex('pronet.bonus_promocode_activations', 'IDX_bonus_promocode_activations_promocodeId');

    // Drop foreign keys
    await queryRunner.dropForeignKey('pronet.bonus_promocode_activations', 'FK_bonus_promocode_activations_customerId');
    await queryRunner.dropForeignKey(
      'pronet.bonus_promocode_activations',
      'FK_bonus_promocode_activations_promocodeId',
    );

    // Drop table
    await queryRunner.dropTable('pronet.bonus_promocode_activations');
  }
}
