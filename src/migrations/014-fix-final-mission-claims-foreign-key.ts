import { MigrationInterface, QueryRunner, TableFore<PERSON><PERSON><PERSON> } from 'typeorm';

export class FixFinalMissionClaimsForeignKey1703000000014 implements MigrationInterface {
  name = 'FixFinalMissionClaimsForeignKey1703000000014';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing incorrect foreign key constraint
    // The original constraint references extended_users.id but should reference extended_users.externalId
    await queryRunner.dropForeignKey('final_mission_claims', 'FK_FINAL_MISSION_CLAIMS_USER_ID');

    // Add the correct foreign key constraint
    // This matches the pattern used in mission_participations and mission_objective_assignments
    // where userId references extended_users.externalId
    await queryRunner.createForeignKey(
      'final_mission_claims',
      new TableForeignKey({
        name: 'FK_FINAL_MISSION_CLAIMS_USER_ID',
        columnNames: ['userId'],
        referencedTableName: 'extended_users',
        referencedColumnNames: ['externalId'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the corrected foreign key constraint
    await queryRunner.dropForeignKey('final_mission_claims', 'FK_FINAL_MISSION_CLAIMS_USER_ID');

    // Restore the original (incorrect) foreign key constraint
    await queryRunner.createForeignKey(
      'final_mission_claims',
      new TableForeignKey({
        name: 'FK_FINAL_MISSION_CLAIMS_USER_ID',
        columnNames: ['userId'],
        referencedTableName: 'extended_users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }
}
