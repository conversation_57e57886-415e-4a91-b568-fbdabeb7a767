import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStartEndDateToMissionObjectiveAssignments1751100030000 implements MigrationInterface {
  name = 'AddStartEndDateToMissionObjectiveAssignments1751100030000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add startDate bigint field to mission_objective_assignments table
    await queryRunner.query(`
      ALTER TABLE "mission_objective_assignments" 
      ADD COLUMN "startDate" bigint
    `);

    // Add endDate bigint field to mission_objective_assignments table
    await queryRunner.query(`
      ALTER TABLE "mission_objective_assignments" 
      ADD COLUMN "endDate" bigint
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the date columns
    await queryRunner.query(`
      ALTER TABLE "mission_objective_assignments" 
      DROP COLUMN "endDate"
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_objective_assignments" 
      DROP COLUMN "startDate"
    `);
  }
}
