import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey, TableIndex } from 'typeorm';

export class CreateCashBonus1753800000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create csn_cash_bonuses table
    await queryRunner.createTable(
      new Table({
        name: 'csn_cash_bonuses',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusId',
            type: 'integer',
          },
          {
            name: 'cashAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create csn_cash_bonus_templates table
    await queryRunner.createTable(
      new Table({
        name: 'csn_cash_bonus_templates',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'bonusTemplateId',
            type: 'integer',
          },
          {
            name: 'cashAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create foreign key relationships
    await queryRunner.createForeignKey(
      'pronet.csn_cash_bonuses',
      new TableForeignKey({
        name: 'FK_csn_cash_bonuses_bonusId',
        columnNames: ['bonusId'],
        referencedTableName: 'bonuses',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'pronet.csn_cash_bonus_templates',
      new TableForeignKey({
        name: 'FK_csn_cash_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
        referencedTableName: 'bonus_templates',
        referencedSchema: 'pronet',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create indexes for better performance
    await queryRunner.createIndex(
      'pronet.csn_cash_bonuses',
      new TableIndex({
        name: 'IDX_csn_cash_bonuses_bonus_id',
        columnNames: ['bonusId'],
      }),
    );

    await queryRunner.createIndex(
      'pronet.csn_cash_bonus_templates',
      new TableIndex({
        name: 'IDX_csn_cash_bonus_templates_bonusTemplateId',
        columnNames: ['bonusTemplateId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex(
      'pronet.csn_cash_bonus_templates',
      'IDX_csn_cash_bonus_templates_bonusTemplateId',
    );
    await queryRunner.dropIndex('pronet.csn_cash_bonuses', 'IDX_csn_cash_bonuses_bonus_id');

    // Drop foreign keys
    await queryRunner.dropForeignKey(
      'pronet.csn_cash_bonus_templates',
      'FK_csn_cash_bonus_templates_bonusTemplateId',
    );
    await queryRunner.dropForeignKey('pronet.csn_cash_bonuses', 'FK_csn_cash_bonuses_bonusId');

    // Drop tables
    await queryRunner.dropTable('pronet.csn_cash_bonus_templates');
    await queryRunner.dropTable('pronet.csn_cash_bonuses');
  }
}
