import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateMissions1703000000003 implements MigrationInterface {
  name = 'CreateMissions1703000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'missions',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'missionType',
            type: 'enum',
            enum: ['daily', 'weekly', 'monthly', 'custom'],
          },
          {
            name: 'reward',
            type: 'integer',
          },
          {
            name: 'description',
            type: 'text',
          },
          {
            name: 'startDate',
            type: 'bigint',
          },
          {
            name: 'endDate',
            type: 'bigint',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create index on missionType for faster lookups
    await queryRunner.createIndex(
      'missions',
      new TableIndex({
        name: 'IDX_MISSIONS_MISSION_TYPE',
        columnNames: ['missionType'],
      }),
    );

    // Create index on startDate for ordering and date range queries
    await queryRunner.createIndex(
      'missions',
      new TableIndex({
        name: 'IDX_MISSIONS_START_DATE',
        columnNames: ['startDate'],
      }),
    );

    // Create index on endDate for date range queries
    await queryRunner.createIndex(
      'missions',
      new TableIndex({
        name: 'IDX_MISSIONS_END_DATE',
        columnNames: ['endDate'],
      }),
    );

    // Create composite index for active mission queries
    await queryRunner.createIndex(
      'missions',
      new TableIndex({
        name: 'IDX_MISSIONS_DATE_RANGE',
        columnNames: ['startDate', 'endDate'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('missions', 'IDX_MISSIONS_DATE_RANGE');
    await queryRunner.dropIndex('missions', 'IDX_MISSIONS_END_DATE');
    await queryRunner.dropIndex('missions', 'IDX_MISSIONS_START_DATE');
    await queryRunner.dropIndex('missions', 'IDX_MISSIONS_MISSION_TYPE');
    await queryRunner.dropTable('missions');
  }
}
