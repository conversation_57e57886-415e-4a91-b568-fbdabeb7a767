import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateInternalSchema1751296353491 implements MigrationInterface {
  name = 'CreateInternalSchema1751296353491';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the internal schema
    await queryRunner.query(`CREATE SCHEMA IF NOT EXISTS "internal"`);

    // Create the users table in the internal schema
    await queryRunner.createTable(
      new Table({
        name: 'internal.users',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'email',
            type: 'text',
            isUnique: true,
          },
          {
            name: 'password_hash',
            type: 'text',
          },
          {
            name: 'otp_secret',
            type: 'text',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes for unique columns
    await queryRunner.createIndex(
      'internal.users',
      new TableIndex({
        name: 'IDX_INTERNAL_USERS_EMAIL',
        columnNames: ['email'],
      }),
    );

    await queryRunner.createIndex(
      'internal.users',
      new TableIndex({
        name: 'IDX_INTERNAL_USERS_PASSWORD_HASH',
        columnNames: ['password_hash'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.dropIndex('internal.users', 'IDX_INTERNAL_USERS_EMAIL');
    await queryRunner.dropIndex('internal.users', 'IDX_INTERNAL_USERS_PASSWORD_HASH');

    // Drop the table
    await queryRunner.dropTable('internal.users');

    // Drop the schema (only if empty)
    await queryRunner.query(`DROP SCHEMA IF EXISTS "internal" CASCADE`);
  }
}
