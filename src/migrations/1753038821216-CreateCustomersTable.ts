import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCustomersTable1753038821216 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'customers',
        schema: 'pronet',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'externalId',
            type: 'integer',
          },
          {
            name: 'code',
            type: 'text',
          },
          {
            name: 'username',
            type: 'text',
          },
          {
            name: 'deletedAt',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create unique index for externalId
    await queryRunner.createIndex(
      'pronet.customers',
      new TableIndex({
        name: 'IDX_customers_externalId',
        columnNames: ['externalId'],
        isUnique: true,
      }),
    );

    // Create index for username
    await queryRunner.createIndex(
      'pronet.customers',
      new TableIndex({
        name: 'IDX_customers_username',
        columnNames: ['username'],
      }),
    );

    // Create index for code
    await queryRunner.createIndex(
      'pronet.customers',
      new TableIndex({
        name: 'IDX_customers_code',
        columnNames: ['code'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('pronet.customers', 'IDX_customers_code');
    await queryRunner.dropIndex('pronet.customers', 'IDX_customers_username');
    await queryRunner.dropIndex('pronet.customers', 'IDX_customers_externalId');

    // Then drop the table
    await queryRunner.dropTable('pronet.customers');
  }
}
