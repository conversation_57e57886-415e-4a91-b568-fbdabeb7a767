import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddShortDescriptionToBonuses1753956914000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add shortDescription column
    await queryRunner.addColumn(
      'pronet.bonuses',
      new TableColumn({
        name: 'shortDescription',
        type: 'text',
        isNullable: false,
        default: "''",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove shortDescription column
    await queryRunner.dropColumn('pronet.bonuses', 'shortDescription');
  }
}
