import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateMissionRules1703000000002 implements MigrationInterface {
  name = 'CreateMissionRules1703000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'mission_rules',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'ruleType',
            type: 'enum',
            enum: [
              'joinedAt',
              'totalDeposit',
              'totalWithdraw',
              'netProfit',
              'vipRank',
              'country',
              'phoneNumber',
              'kycLevel',
              'referrer',
            ],
          },
          {
            name: 'compare',
            type: 'enum',
            enum: ['eq', 'ne', 'gt', 'lt', 'ge', 'le'],
          },
          {
            name: 'compareValue',
            type: 'text',
          },
          {
            name: 'minDate',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'maxDate',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create index on ruleType for faster lookups
    await queryRunner.createIndex(
      'mission_rules',
      new TableIndex({
        name: 'IDX_MISSION_RULES_RULE_TYPE',
        columnNames: ['ruleType'],
      }),
    );

    // Create index on createdAt for ordering
    await queryRunner.createIndex(
      'mission_rules',
      new TableIndex({
        name: 'IDX_MISSION_RULES_CREATED_AT',
        columnNames: ['createdAt'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('mission_rules', 'IDX_MISSION_RULES_CREATED_AT');
    await queryRunner.dropIndex('mission_rules', 'IDX_MISSION_RULES_RULE_TYPE');
    await queryRunner.dropTable('mission_rules');
  }
}
