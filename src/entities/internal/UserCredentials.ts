import { <PERSON><PERSON><PERSON>, <PERSON>umn, CreateDateColumn, UpdateDateColumn, PrimaryColumn } from 'typeorm';

@Entity('users_credentials', { schema: 'internal' })
export class UserCredentials {
  @PrimaryColumn({ type: 'integer', name: 'user_id' })
  userId!: number;

  @PrimaryColumn({ type: 'text', name: 'panel_name' })
  panelName!: string;

  @Column({ type: 'text' })
  login!: string;

  @Column({ type: 'text' })
  password!: string;

  @Column({ type: 'text', nullable: true, name: 'otp_secret' })
  otpSecret?: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
