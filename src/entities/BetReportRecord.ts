import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('bet_report_records')
@Index('IDX_BET_REPORT_RECORDS_START_DATE', ['startDate'])
@Index('IDX_BET_REPORT_RECORDS_END_DATE', ['endDate'])
@Index('IDX_BET_REPORT_RECORDS_DATE_RANGE', ['startDate', 'endDate'])
@Index('IDX_BET_REPORT_RECORDS_CREATED_AT', ['createdAt'])
export class BetReportRecord {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ name: 'total_coupons', type: 'integer' })
  totalCoupons!: number;

  @Column({ name: 'total_play_amount', type: 'decimal', precision: 15, scale: 2 })
  totalPlayAmount!: number;

  @Column({ name: 'total_open_coupons', type: 'integer' })
  totalOpenCoupons!: number;

  @Column({ name: 'total_open_return', type: 'decimal', precision: 15, scale: 2 })
  totalOpenReturn!: number;

  @Column({ name: 'total_open_amount', type: 'decimal', precision: 15, scale: 2 })
  totalOpenAmount!: number;

  @Column({ name: 'total_win_coupons', type: 'integer' })
  totalWinCoupons!: number;

  @Column({ name: 'total_win_amount', type: 'decimal', precision: 15, scale: 2 })
  totalWinAmount!: number;

  @Column({ name: 'total_win_return', type: 'decimal', precision: 15, scale: 2 })
  totalWinReturn!: number;

  @Column({ name: 'total_lose_coupons', type: 'integer' })
  totalLoseCoupons!: number;

  @Column({ name: 'total_lose_amount', type: 'decimal', precision: 15, scale: 2 })
  totalLoseAmount!: number;

  @Column({ name: 'total_void_coupons', type: 'integer' })
  totalVoidCoupons!: number;

  @Column({ name: 'total_void_amount', type: 'decimal', precision: 15, scale: 2 })
  totalVoidAmount!: number;

  @Column({ name: 'total_void_return', type: 'decimal', precision: 15, scale: 2 })
  totalVoidReturn!: number;

  @Column({ name: 'total_part_cashout_amount', type: 'decimal', precision: 15, scale: 2 })
  totalPartCashoutAmount!: number;

  @Column({ name: 'total_part_cashout_count', type: 'integer' })
  totalPartCashoutCount!: number;

  @Column({ name: 'total_comp_cashout_amount', type: 'decimal', precision: 15, scale: 2 })
  totalCompCashoutAmount!: number;

  @Column({ name: 'total_comp_cashout_count', type: 'integer' })
  totalCompCashoutCount!: number;

  @Column({ name: 'tot_real_bal_play_amount', type: 'decimal', precision: 15, scale: 2 })
  totRealBalPlayAmount!: number;

  @Column({ name: 'tot_bon_bal_play_amount', type: 'decimal', precision: 15, scale: 2 })
  totBonBalPlayAmount!: number;

  @Column({ name: 'tot_free_bal_play_amount', type: 'decimal', precision: 15, scale: 2 })
  totFreeBalPlayAmount!: number;

  @Column({ name: 'tot_real_bal_win_return', type: 'decimal', precision: 15, scale: 2 })
  totRealBalWinReturn!: number;

  @Column({ name: 'tot_bon_bal_win_return', type: 'decimal', precision: 15, scale: 2 })
  totBonBalWinReturn!: number;

  @Column({ name: 'tot_real_bal_void_return', type: 'decimal', precision: 15, scale: 2 })
  totRealBalVoidReturn!: number;

  @Column({ name: 'tot_bon_bal_void_return', type: 'decimal', precision: 15, scale: 2 })
  totBonBalVoidReturn!: number;

  @Column({ name: 'ngr', type: 'decimal', precision: 15, scale: 2 })
  ngr!: number;

  @Column({ name: 'ggr', type: 'decimal', precision: 15, scale: 2 })
  ggr!: number;

  @Column({ name: 'transaction_currency', type: 'varchar', length: 10, nullable: true })
  transactionCurrency!: string | null;

  @Column({ name: 'start_date', type: 'timestamp' })
  startDate!: Date;

  @Column({ name: 'end_date', type: 'timestamp' })
  endDate!: Date;

  @Column({ name: 'is_hourly_report', type: 'boolean', default: false })
  isHourlyReport!: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;
}
