import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { RuleType, CompareOperator } from '@/enums/shared';

@Entity('mission_rules')
@Index('IDX_MISSION_RULES_RULE_TYPE', ['ruleType'])
@Index('IDX_MISSION_RULES_CREATED_AT', ['createdAt'])
export class MissionRule {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({
    type: 'enum',
    enum: RuleType,
  })
  ruleType!: RuleType;

  @Column({
    type: 'enum',
    enum: CompareOperator,
  })
  compare!: CompareOperator;

  @Column({ type: 'text' })
  compareValue!: string;

  @Column({ type: 'bigint', nullable: true })
  minDate!: number | null;

  @Column({ type: 'bigint', nullable: true })
  maxDate!: number | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
