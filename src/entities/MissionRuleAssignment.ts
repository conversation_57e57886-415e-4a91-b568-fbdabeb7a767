import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Mission } from './Mission';
import { MissionRule } from './MissionRule';

@Entity('mission_rule_assignments')
@Index('IDX_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_UNIQUE', ['missionId', 'missionRuleId'], { unique: true })
@Index('IDX_MISSION_RULE_ASSIGNMENTS_MISSION_ID', ['missionId'])
@Index('IDX_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_ID', ['missionRuleId'])
export class MissionRuleAssignment {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  missionId!: number;

  @Column({ type: 'integer' })
  missionRuleId!: number;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationships
  @ManyToOne(() => Mission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'missionId' })
  mission!: Mission;

  @ManyToOne(() => MissionRule, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'missionRuleId' })
  missionRule!: MissionRule;
}
