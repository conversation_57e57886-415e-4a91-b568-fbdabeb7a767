import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Mission } from './Mission';
import { ObjectiveType, CompareOperator } from '@/enums/shared';

@Entity('mission_objectives')
@Index('IDX_MISSION_OBJECTIVES_MISSION_ID', ['missionId'])
@Index('IDX_MISSION_OBJECTIVES_OBJECTIVE_TYPE', ['objectiveType'])
@Index('IDX_MISSION_OBJECTIVES_CREATED_AT', ['createdAt'])
export class MissionObjective {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  missionId!: number;

  @Column({
    type: 'enum',
    enum: ObjectiveType,
  })
  objectiveType!: ObjectiveType;

  @Column({ type: 'varchar', length: 255, nullable: true })
  subtype!: string | null;

  @Column({
    type: 'enum',
    enum: CompareOperator,
  })
  operator!: CompareOperator;

  @Column({ type: 'text' })
  targetValue!: string;

  @Column({ type: 'text', nullable: true })
  description!: string | null;

  @Column({ type: 'bigint', nullable: true })
  timeframeStart!: number | null;

  @Column({ type: 'bigint', nullable: true })
  timeframeEnd!: number | null;

  @Column({ type: 'json', nullable: true })
  metadata!: any;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationship
  @ManyToOne(() => Mission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'missionId' })
  mission!: Mission;
}
