import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('extended_users')
@Index('IDX_EXTENDED_USERS_EXTERNAL_ID', ['externalId'])
export class ExtendedUser {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer', unique: true })
  externalId!: number;

  @Column({ type: 'integer', default: 0 })
  points!: number;

  @Column({ type: 'varchar', length: 32, default: '' })
  externalUsername!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
