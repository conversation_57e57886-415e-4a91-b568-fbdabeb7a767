import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ExtendedUser } from './ExtendedUser';
import { MissionType } from '@/enums/shared';

/**
 * Final Mission Claim Entity
 * 
 * Tracks when users complete ALL missions of a specific type (daily/weekly/monthly)
 * within a time period and claim a bonus reward.
 */
@Entity('final_mission_claims')
@Index('IDX_FINAL_MISSION_CLAIMS_USER_ID', ['userId'])
@Index('IDX_FINAL_MISSION_CLAIMS_CLAIM_TYPE', ['claimType'])
@Index('IDX_FINAL_MISSION_CLAIMS_USER_CLAIM_TYPE', ['userId', 'claimType'])
@Index('IDX_FINAL_MISSION_CLAIMS_CREATED_AT', ['createdAt'])
export class FinalMissionClaim {
  @PrimaryGeneratedColumn()
  id!: number;

  /**
   * User ID that references ExtendedUser.externalId
   * This represents the external user ID from the main system
   */
  @Column({ type: 'integer' })
  userId!: number;

  /**
   * Type of missions that were completed to earn this claim
   * Can be 'daily', 'weekly', or 'monthly'
   */
  @Column({
    type: 'enum',
    enum: [MissionType.DAILY, MissionType.WEEKLY, MissionType.MONTHLY],
  })
  claimType!: MissionType.DAILY | MissionType.WEEKLY | MissionType.MONTHLY;

  /**
   * Number of points awarded for completing all missions of this type
   * Calculated as: 10 * N for daily, 30 * N for weekly, 100 * N for monthly
   * where N is the number of completed missions
   */
  @Column({ type: 'integer' })
  grantedReward!: number;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationship to ExtendedUser
  @ManyToOne(() => ExtendedUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId', referencedColumnName: 'externalId' })
  user!: ExtendedUser;
}
