import { MarketProductCategory, MarketProductType } from '@/enums/shared';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('market_products')
@Index('IDX_MARKET_PRODUCTS_TYPE', ['type'])
@Index('IDX_MARKET_PRODUCTS_SLUG', ['slug'])
@Index('IDX_MARKET_PRODUCTS_CATEGORY', ['category'])
export class MarketProduct {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  name!: string;

  @Column({ type: 'text' })
  slug!: string;

  @Column({ type: 'json' })
  name_i18n!: Record<string, string>;

  @Column({ type: 'json' })
  description_i18n!: Record<string, string>;

  @Column({
    type: 'enum',
    enum: MarketProductType,
  })
  type!: MarketProductType;

  @Column({
    type: 'enum',
    enum: MarketProductCategory,
  })
  category!: MarketProductCategory;

  @Column({ type: 'integer' })
  availableAmount!: number | null;

  @Column({ type: 'boolean' })
  isMultiPerBuyer!: boolean;

  @Column({ type: 'text' })
  photoUrl!: string;

  @Column({ type: 'float' })
  price!: number;

  @Column({ type: 'json' })
  currencies!: string[];

  @Column({ type: 'json', nullable: true })
  providers!: { provider: Record<string, any>; games: Record<string, any>[] }[] | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
