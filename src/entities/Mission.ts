import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { MissionType } from '@/enums/shared';

@Entity('missions')
@Index('IDX_MISSIONS_MISSION_TYPE', ['missionType'])
@Index('IDX_MISSIONS_START_DATE', ['startDate'])
@Index('IDX_MISSIONS_END_DATE', ['endDate'])
@Index('IDX_MISSIONS_DATE_RANGE', ['startDate', 'endDate'])
@Index('IDX_MISSIONS_IS_ACTIVE', ['isActive'])
@Index('IDX_MISSIONS_ACTIVE_TYPE', ['isActive', 'missionType'])
export class Mission {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'varchar', length: 255 })
  name!: string;

  @Column({
    type: 'enum',
    enum: MissionType,
  })
  missionType!: MissionType;

  @Column({ type: 'integer' })
  reward!: number;

  @Column({ type: 'text' })
  description!: string;

  @Column({ type: 'bigint' })
  startDate!: number;

  @Column({ type: 'bigint' })
  endDate!: number;

  @Column({ type: 'boolean', default: true })
  isActive!: boolean;

  @Column({ type: 'json' })
  name_i18n!: Record<string, string>;

  @Column({ type: 'json' })
  description_i18n!: Record<string, string>;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
