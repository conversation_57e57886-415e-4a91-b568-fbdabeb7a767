import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { TransactionType, TransactionCategory } from '@/enums/shared';
import { ExtendedUser } from './ExtendedUser';

@Entity('transactions')
@Index('IDX_TRANSACTIONS_FROM_USER_ID', ['fromUserId'])
@Index('IDX_TRANSACTIONS_TO_USER_ID', ['toUserId'])
export class Transaction {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  fromUserId!: number | null;

  @Column({ type: 'integer' })
  toUserId!: number;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type!: TransactionType;

  @Column({
    type: 'enum',
    enum: TransactionCategory,
  })
  category!: TransactionCategory;

  @Column({ type: 'decimal' })
  amount!: number;

  @Column({ type: 'json' })
  metadata!: any;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationship
  @ManyToOne(() => ExtendedUser)
  @JoinColumn({ name: 'fromUserId' })
  fromUser!: ExtendedUser | null;

  @ManyToOne(() => ExtendedUser)
  @JoinColumn({ name: 'toUserId' })
  toUser!: ExtendedUser;
}
