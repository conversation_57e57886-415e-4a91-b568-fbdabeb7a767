import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Bonus } from './Bonus';

@Entity('csn_freespin_bonuses', { schema: 'pronet' })
export class CasinoFreespinBonus {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  vendorId!: number;

  @Column({ type: 'text' })
  vendorName!: string;

  @Column({ type: 'json' })
  values!: Record<string, any>;

  @Column({ type: 'integer', array: true, default: () => 'ARRAY[]::integer[]' })
  gameIds!: number[];

  @Column({ type: 'integer' })
  bonusId!: number;

  @ManyToOne('Bonus')
  @JoinColumn({ name: 'bonusId', referencedColumnName: 'id' })
  bonus!: Bonus;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
