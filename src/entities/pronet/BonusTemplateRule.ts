import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BonusTemplate } from './BonusTemplate';

@Entity('bonus_template_rules', { schema: 'pronet' })
export class BonusTemplateRule {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusTemplateId!: number;

  @ManyToOne('BonusTemplate')
  @JoinColumn({ name: 'bonusTemplateId', referencedColumnName: 'id' })
  bonusTemplate!: BonusTemplate;

  @Column({ type: 'text' })
  criterium!: string;

  @Column({ type: 'text' })
  firstOperand!: string;

  @Column({ type: 'text' })
  secondOperand!: string;

  @Column({ type: 'text' })
  operator!: string;

  @Column({ type: 'integer' })
  startsInSeconds!: number;

  @Column({ type: 'integer' })
  endsInSeconds!: number;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
