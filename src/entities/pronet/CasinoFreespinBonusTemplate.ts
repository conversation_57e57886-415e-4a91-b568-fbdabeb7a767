import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BonusTemplate } from './BonusTemplate';

@Entity('csn_freespin_bonus_templates', { schema: 'pronet' })
export class CasinoFreespinBonusTemplate {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  vendorId!: number;

  @Column({ type: 'text' })
  vendorName!: string;

  @Column({ type: 'json' })
  values!: Record<string, any>;

  @Column({ type: 'integer', array: true, default: () => 'ARRAY[]::integer[]' })
  gameIds!: number[];

  @Column({ type: 'integer' })
  validForDays!: number;

  @Column({ type: 'integer' })
  bonusTemplateId!: number;

  @ManyToOne('BonusTemplate')
  @JoinColumn({ name: 'bonusTemplateId', referencedColumnName: 'id' })
  bonusTemplate!: BonusTemplate;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
