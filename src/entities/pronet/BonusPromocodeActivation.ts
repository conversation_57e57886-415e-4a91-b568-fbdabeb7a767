import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Customer } from './Customer';
import { BonusPromocode } from './BonusPromocode';

@Entity('bonus_promocode_activations', { schema: 'pronet' })
export class BonusPromocodeActivation {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  promocodeId!: number;

  @ManyToOne('BonusPromocode')
  @JoinColumn({ name: 'promocodeId', referencedColumnName: 'id' })
  promocode!: BonusPromocode;

  @Column({ type: 'integer' })
  customerId!: number;

  @ManyToOne('Customer')
  @JoinColumn({ name: 'customerId', referencedColumnName: 'id' })
  customer!: Customer;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
