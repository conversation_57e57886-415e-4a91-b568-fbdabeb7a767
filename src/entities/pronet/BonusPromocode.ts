import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Bonus } from './Bonus';

@Entity('bonus_promocodes', { schema: 'pronet' })
export class BonusPromocode {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  code!: string;

  @Column({ type: 'integer' })
  bonusId!: number;

  @ManyToOne('Bonus')
  @JoinColumn({ name: 'bonusId', referencedColumnName: 'id' })
  bonus!: Bonus;

  @Column({ type: 'integer' })
  activations!: number;

  @Column({ type: 'integer' })
  maxActivations!: number;

  @Column({ type: 'boolean' })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ type: 'timestamptz' })
  deletedAt!: Date | null;
}
