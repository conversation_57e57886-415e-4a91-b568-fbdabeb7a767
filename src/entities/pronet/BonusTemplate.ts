import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { BonusTemplateRule } from './BonusTemplateRule';

@Entity('bonus_templates', { schema: 'pronet' })
export class BonusTemplate {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  name!: string;

  @Column({ type: 'text' })
  type!: string;

  @OneToMany('BonusTemplateRule', 'bonusTemplate')
  rules!: BonusTemplateRule[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ type: 'timestamptz' })
  deletedAt!: Date | null;
}
