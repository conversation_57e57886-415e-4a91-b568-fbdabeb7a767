import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BonusTemplate } from './BonusTemplate';

@Entity('csn_weekly_lossback_bonus_templates', { schema: 'pronet' })
export class WeeklyLossbackBonusTemplate {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusTemplateId!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  maxBalance!: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  lossbackPercentage!: number;

  @Column({ type: 'time', nullable: true })
  happyHoursStart!: string | null;

  @Column({ type: 'time', nullable: true })
  happyHoursEnd!: string | null;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 2.00 })
  happyHoursBoostPercentage!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 100.00 })
  depositWithDrawDifferenceThreshold!: number;

  @Column({
    type: 'integer',
    comment: 'Start of request window in seconds from start of week (Monday 00:00)'
  })
  requestWindowStartSeconds!: number;

  @Column({ 
    type: 'integer',
    comment: 'End of request window in seconds from start of week (Monday 00:00)'
  })
  requestWindowEndSeconds!: number;

  @Column({ type: 'integer' })
  validForDays!: number;

  @ManyToOne('BonusTemplate')
  @JoinColumn({ name: 'bonusTemplateId', referencedColumnName: 'id' })
  bonusTemplate!: BonusTemplate;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
