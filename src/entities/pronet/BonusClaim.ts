import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Bonus } from './Bonus';
import { Customer } from './Customer';

@Entity('bonus_claims', { schema: 'pronet' })
export class BonusClaim {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusId!: number;

  @Column({ type: 'integer' })
  customerId!: number;

  @ManyToOne('Customer')
  @JoinColumn({ name: 'customerId', referencedColumnName: 'id' })
  customer!: Customer;

  @ManyToOne('Bonus')
  @JoinColumn({ name: 'bonusId', referencedColumnName: 'id' })
  bonus!: Bonus;

  @Column({ type: 'text' })
  source!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
