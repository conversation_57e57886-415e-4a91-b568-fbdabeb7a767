import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  JoinColumn,
  ManyToOne,
  UpdateDateColumn,
} from 'typeorm';
import { Customer } from './Customer';
import { BonusBulkAssignmentJob } from './BonusBulkAssignmentJob';

@Entity('bonus_bulk_assignment_job_targets', { schema: 'pronet' })
export class BonusBulkAssignmentJobTarget {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusBulkAssignmentJobId!: number;

  @ManyToOne('BonusBulkAssignmentJob')
  @JoinColumn({ name: 'bonusBulkAssignmentJobId', referencedColumnName: 'id' })
  bonusBulkAssignmentJob!: BonusBulkAssignmentJob;

  @Column({ type: 'integer' })
  externalCustomerId!: number;

  @ManyToOne('Customer')
  @JoinColumn({ name: 'externalCustomerId', referencedColumnName: 'externalId' })
  customer!: Customer | null;

  @Column({ type: 'json' })
  events!: Record<string, any>[];

  @Column({ type: 'text' })
  status!: 'pending' | 'processing' | 'processed' | 'timeout' | 'failed' | 'cancelled';
  // status!: string;

  @Column({ type: 'text', nullable: true })
  errorMessage!: string | null;

  @Column({ type: 'timestamptz', nullable: true })
  processedAt!: Date | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
