import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { BonusRule } from './BonusRule';

@Entity('bonuses', { schema: 'pronet' })
export class Bonus {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  name!: string;

  @Column({ type: 'text' })
  description!: string;

  @Column({ type: 'text' })
  reward!: string;

  @Column({ type: 'text' })
  type!: string;

  @Column({ type: 'boolean' })
  isActive!: boolean;

  @OneToMany('BonusRule', 'bonus')
  rules!: BonusRule[];

  @Column({ type: 'timestamptz' })
  expiresAt!: Date | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ type: 'timestamptz' })
  deletedAt!: Date | null;
}
