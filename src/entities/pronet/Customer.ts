import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('customers', { schema: 'pronet' })
export class Customer {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  externalId!: number;

  @Column({ type: 'text' })
  code!: string;

  @Column({ type: 'text' })
  username!: string;

  @Column({ type: 'timestamptz' })
  deletedAt!: Date | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
