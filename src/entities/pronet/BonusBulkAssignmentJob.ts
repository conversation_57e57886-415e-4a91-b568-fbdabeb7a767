import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Bonus } from './Bonus';
import { BonusBulkAssignmentJobTarget } from './BonusBulkAssignmentJobTarget';

@Entity('bonus_bulk_assignment_jobs', { schema: 'pronet' })
export class BonusBulkAssignmentJob {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusId!: number;

  @ManyToOne('Bonus')
  @JoinColumn({ name: 'bonusId', referencedColumnName: 'id' })
  bonus!: Bonus;

  @OneToMany('BonusBulkAssignmentJobTarget', 'bonusBulkAssignmentJob')
  targets!: BonusBulkAssignmentJobTarget[];

  @Column({ type: 'json' })
  bonusValues!: Record<string, any>;

  @Column({ type: 'integer' })
  bonusValuesVersion!: number;

  @Column({ type: 'text' })
  status!: 'pending' | 'processing' | 'cancelled' | 'timeout' | 'processed' | 'failed';
  // status!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
