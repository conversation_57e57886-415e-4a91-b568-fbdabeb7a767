import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BonusTemplate } from './BonusTemplate';

@Entity('happy_hours_bonus_templates', { schema: 'pronet' })
export class HappyHoursBonusTemplate {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusTemplateId!: number;

  @ManyToOne('BonusTemplate')
  @JoinColumn({ name: 'bonusTemplateId', referencedColumnName: 'id' })
  bonusTemplate!: BonusTemplate;

  @Column({ type: 'text' })
  externalBonusName!: string;

  @Column({ type: 'integer' })
  externalBonusId!: number;

  @Column({ type: 'integer' })
  amount!: number;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
