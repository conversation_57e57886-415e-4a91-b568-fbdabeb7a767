import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { Bonus } from './Bonus';

@Entity('bonus_rules', { schema: 'pronet' })
export class BonusRule {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusId!: number;

  @ManyToOne('Bonus')
  @JoinColumn({ name: 'bonusId', referencedColumnName: 'id' })
  bonus!: Bonus;

  @Column({ type: 'text' })
  criterium!: string;

  @Column({ type: 'text' })
  firstOperand!: string;

  @Column({ type: 'text' })
  secondOperand!: string;

  @Column({ type: 'text' })
  operator!: string;

  @Column({ type: 'timestamptz' })
  startsAt!: Date;

  @Column({ type: 'timestamptz' })
  endsAt!: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
