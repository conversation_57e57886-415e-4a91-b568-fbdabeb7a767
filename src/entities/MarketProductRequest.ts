import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ExtendedUser } from './ExtendedUser';
import { MarketProductRequestStatus } from '@/enums/shared';
import { MarketProduct } from './MarketProduct';

@Entity('market_product_requests')
@Index('IDX_MARKET_PRODUCT_REQUESTS_USER_ID', ['userId'])
export class MarketProductRequest {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({
    type: 'enum',
    enum: MarketProductRequestStatus,
  })
  status!: MarketProductRequestStatus;

  @Column({ type: 'text' })
  currency!: string;

  @Column({ type: 'json', nullable: true })
  providers!: Record<string, number[]> | null;

  @Column({ type: 'integer' })
  userId!: number;

  @Column({ type: 'integer' })
  productId!: number;

  @Column({ type: 'json' })
  history!: { status: string; timestamp: number; message: string }[];

  @Column({ type: 'text', nullable: true })
  rejectReason!: string | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @ManyToOne(() => ExtendedUser)
  @JoinColumn({ name: 'userId', referencedColumnName: 'externalId' })
  user!: ExtendedUser;

  @ManyToOne(() => MarketProduct)
  @JoinColumn({ name: 'productId' })
  product!: MarketProduct;
}
