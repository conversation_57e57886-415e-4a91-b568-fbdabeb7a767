import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Mission } from './Mission';
import { ExtendedUser } from './ExtendedUser';

@Entity('mission_participations')
@Index('IDX_MISSION_PARTICIPATIONS_USER_MISSION_UNIQUE', ['userId', 'missionId'], { unique: true })
@Index('IDX_MISSION_PARTICIPATIONS_USER_ID', ['userId'])
@Index('IDX_MISSION_PARTICIPATIONS_MISSION_ID', ['missionId'])
@Index('IDX_MISSION_PARTICIPATIONS_IS_COMPLETED', ['isCompleted'])
@Index('IDX_MISSION_PARTICIPATIONS_USER_COMPLETED', ['userId', 'isCompleted'])
export class MissionParticipation {
  @PrimaryGeneratedColumn()
  id!: number;

  /**
   * User ID that references ExtendedUser.externalId
   * This represents the external user ID from the main system
   */
  @Column({ type: 'integer' })
  userId!: number;

  @Column({ type: 'integer' })
  missionId!: number;

  @Column({ type: 'boolean', default: false })
  isCompleted!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationship
  @ManyToOne(() => Mission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'missionId' })
  mission!: Mission;

  @ManyToOne(() => ExtendedUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId', referencedColumnName: 'externalId' })
  user!: ExtendedUser;
}
