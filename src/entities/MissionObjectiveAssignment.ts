import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { MissionObjective } from './MissionObjective';
import { ExtendedUser } from './ExtendedUser';

@Entity('mission_objective_assignments')
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_UNIQUE', ['userId', 'missionObjectiveId'], { unique: true })
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_USER_ID', ['userId'])
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_MISSION_OBJECTIVE_ID', ['missionObjectiveId'])
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_PROGRESS', ['progress'])
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_LAST_CHECKED', ['lastCheckedRecordTimestamp'])
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_START_DATE', ['startDate'])
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_END_DATE', ['endDate'])
@Index('IDX_MISSION_OBJECTIVE_ASSIGNMENTS_DATE_RANGE', ['startDate', 'endDate'])
export class MissionObjectiveAssignment {
  @PrimaryGeneratedColumn()
  id!: number;

  /**
   * User ID that references ExtendedUser.externalId
   * This represents the external user ID from the main system
   */
  @Column({ type: 'integer' })
  userId!: number;

  @Column({ type: 'integer' })
  missionObjectiveId!: number;

  @Column({ type: 'integer', default: 0 })
  progress!: number;

  @Column({ type: 'bigint', nullable: true })
  lastCheckedRecordTimestamp!: number | null;

  @Column({ type: 'boolean', default: false })
  isCompleted!: boolean;

  @Column({ type: 'bigint', nullable: true })
  startDate!: number | null;

  @Column({ type: 'bigint', nullable: true })
  endDate!: number | null;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationships
  @ManyToOne(() => MissionObjective, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'missionObjectiveId' })
  missionObjective!: MissionObjective;

  @ManyToOne(() => ExtendedUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId', referencedColumnName: 'externalId' })
  user!: ExtendedUser;
}
