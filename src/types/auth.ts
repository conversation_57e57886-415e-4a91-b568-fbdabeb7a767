export interface LoginRequest {
  username: string;
  password: string;
  otp: string;
  fingerprint?: any;
}

export interface AuthData {
  cf_clearance: string;
  x_fingerprint: string;
  signed_challenge: string;
}

export interface LoginResponse {
  token: string;
  [key: string]: any;
}

export interface AgentInfo {
  browserName: string;
  isMobile: boolean;
  isTablet: boolean;
  isBrowser: boolean;
  browserVersion: string;
  osVersion: string;
  engineVersion: string;
  osName: string;
  engineName: string;
}

export interface EbetLabLoginPayload {
  email: string;
  password: string;
  agent: AgentInfo;
  otp: string;
  sms: string;
  fingerprint: any;
  rt: number;
  secret: string;
}
