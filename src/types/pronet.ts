/**
 * Pronet API Types
 * 
 * This file contains TypeScript interfaces for Pronet API requests and responses.
 */

/**
 * Bonus and Free Bet item from Pronet API
 */
export interface BonusAndFreeBet {
  id: number;
  name: string;
  product: string;
  type: string;
  endDate: string; // ISO 8601 date string, e.g., "2021-04-22T12:51:17"
  validityInDays: number;
  currencyId: number;
}

/**
 * Response type for getBonusesAndFreeBets endpoint
 */
export type BonusesAndFreeBetsResponse = BonusAndFreeBet[];

/**
 * Call request data structure for Pronet API
 */
export interface CallRequest {
  id: number;
  type: number;
  token: string; // session token
  code: string;
  time: string;
}

/**
 * Generic Pronet API response wrapper
 */
export interface PronetApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

/**
 * Pronet API error response
 */
export interface PronetApiError {
  success: false;
  message: string;
  error?: string;
  timestamp: string;
}
