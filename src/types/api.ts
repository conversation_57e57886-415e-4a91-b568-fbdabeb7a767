export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  timestamp?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface HealthCheckResponse {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services?: {
    database?: 'connected' | 'disconnected';
    redis?: 'connected' | 'disconnected';
    [key: string]: string | undefined;
  };
}

export interface WidgetStatsRequest {
  from: number;
  to: number;
}

export interface WidgetStatsResponse {
  total_deposit: string;
  total_deposit_count: number;
  total_withdraw: string;
  total_withdraw_count: number;
  players_logged_in: number;
  registered_players: number;
  deposit_players: number;
  withdraw_players: number;
}

export interface CustomersRequest {
  id?: string;
  affiliator?: string;
  referrer?: string | null;
  username?: string;
  name?: string;
  birthday?: string;
  surname?: string;
  ref_code?: string;
  identity_no?: string;
  verification_level?: string;
  email?: string;
  ip?: string;
  status_id?: string | null;
  operator_id?: string | null;
  registration_country?: string;
  language?: string | null;
  rank?: string;
  phone?: string;
  register_start?: number;
  register_end?: number;
  first_deposit_start?: number;
  first_deposit_end?: number;
  last_deposit_start?: number;
  last_deposit_end?: number;
  last_login_start?: number;
  last_login_end?: number;
  total_reload_min?: string;
  total_reload_max?: string;
  total_rain_min?: string;
  total_rain_max?: string;
  total_deposit_greater?: string;
  total_deposit_lower?: string;
  total_withdraw_greater?: string;
  total_withdraw_lower?: string;
  total_bonus_drop_min?: string;
  total_bonus_drop_max?: string;
  total_turnover_greater?: string;
  total_turnover_lower?: string;
  net_percentage_min?: string;
  net_percentage_max?: string;
  rakebackMin?: string;
  rakebackMax?: string;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface CustomerProfile {
  id: number;
  name: string;
  surname: string;
  customer_id: number;
  verification_level: number;
  birthday: string;
  country_id: number;
  identity_no: string;
}

export interface CustomerDeposit {
  id: number;
  customer_id: number;
  timestamp: number;
  amount: string;
  currency: string;
  type: number;
  provider: string;
  payment_provider: string | null;
}

export interface CustomerSummary {
  id: number;
  customer_id: number;
  total_in_usd: string;
  total_out_usd: string;
  percentage: string;
  total_rakeback_usd: string;
  total_reload_usd: string;
  total_bonus_drop_usd: string;
  total_rain_usd: string;
}

export interface CustomerStatus {
  id: number;
  name: string;
}

export interface CustomerAffiliator {
  id: number;
  name: string;
}

export interface CustomerPhone {
  id: number;
  full: string;
  code: string;
  number: string;
  customer_id: number;
}

export interface Customer {
  id: number;
  username: string;
  last_online_at: number;
  last_ip: string;
  ref_id: number | null;
  affiliator_id: number;
  registration_country: string;
  ref_code: string;
  registration_ts: number;
  operator_id: number | null;
  status_id: number;
  is_public: boolean;
  lang: string;
  rank: string;
  rank_percentage: string;
  total_turnover: string;
  next_wager_limit: string;
  email: string;
  masked_username: string;
  last_action: string | null;
  profile: CustomerProfile;
  last_deposit: CustomerDeposit;
  first_deposit: CustomerDeposit;
  summary: CustomerSummary;
  status: CustomerStatus;
  operator: any | null;
  affiliator: CustomerAffiliator;
  referrer: any | null;
  phone: CustomerPhone;
}

export interface CustomersResponse {
  total: number;
  data: Customer[];
}

export interface CustomerShowRequest {
  id: string;
}

export interface CustomerDetailProfile {
  id: number;
  name: string;
  surname: string;
  customer_id: number;
  verification_level: number;
  birthday: string;
  country_id: number | null;
  verified_at: string | null;
  verified_by: string | null;
  residential: string | null;
  city: string;
  postal_code: string | null;
  occupation: string | null;
  ghost_mode: boolean;
  hide_statistics: boolean;
  hide_race_statistics: boolean;
  exclude_rain: boolean;
  receive_marketing_mails: boolean;
  identity_no: string;
  country: any | null;
}

export interface CustomerDetail {
  id: number;
  merchant_id: number;
  affiliator_id: number | null;
  phone_verified_at: string;
  excluded_till: string | null;
  otp_step: boolean;
  username: string;
  ref_id: number | null;
  is_active: boolean;
  is_suspended: boolean;
  last_country: string;
  registration_country: string;
  registration_ip: string;
  last_online_at: number;
  last_ip: string;
  email_verified_at: string;
  registration_ts: number;
  website_id: number;
  operator_id: number | null;
  status_id: number;
  is_public: boolean;
  lang: string;
  rank: string;
  rank_percentage: string;
  ref_code: string;
  total_turnover: string;
  next_wager_limit: string;
  authenticator_enabled: boolean;
  social_auth: number;
  email: string;
  masked_username: string;
  last_action: number;
  operator: any | null;
  referrer: any | null;
  affiliator: any | null;
  profile: CustomerDetailProfile;
  phone: CustomerPhone;
}

export interface SessionCheckRequest {
  id: string;
}

export interface SessionConflict {
  ip: string;
  usernames: string[];
}

export interface SessionCheckResponse {
  data: SessionConflict[];
}

export interface CustomerSummaryRangeRequest {
  id: string;
}

export interface SummaryRangeData {
  deposit: number;
  withdraw: number;
}

export interface CustomerSummaryRangeResponse {
  day: SummaryRangeData;
  week: SummaryRangeData;
  month: SummaryRangeData;
  year: SummaryRangeData;
}

export interface CustomerInfoRequest {
  customer_id: string;
  id: string;
}

export interface CustomerLastTransaction {
  id: number;
  amount: string;
  currency: string;
  timestamp: number;
  provider: string;
  payment_provider: string | null;
}

export interface CustomerLastDiscount {
  id: number;
  timestamp: number;
  code: string;
  before_balance: string;
  amount: string;
  after_balance: string;
  depositAmount: string;
}

export interface CustomerInfoResponse {
  last_deposit: CustomerLastTransaction;
  last_withdraw: CustomerLastTransaction;
  last_discount: CustomerLastDiscount;
  corrections: any[];
  last_bonus: any | null;
}

export interface GeneralLimitsRequest {
  id: string;
}

export interface GeneralLimitsResponse {
  login_disabled: boolean;
  bets_disabled: boolean;
  bonus_disabled: boolean;
  aml_disabled: boolean;
  local_aml_disabled: boolean;
  withdraw_disabled: boolean;
  chat_disabled: boolean;
  tip_disabled: boolean;
  rakeback_disabled: boolean;
  raffle_disabled: boolean;
  race_disabled: boolean;
  casino_bets_disabled: boolean;
  live_casino_bets_disabled: boolean;
  sportsbook_disabled: boolean;
  withdrawal_bypass_otp: boolean;
  instant_discount_disabled: number;
  weekly_discount_disabled: number;
  monthly_discount_disabled: number;
  pokerklas_enabled: number;
}

export interface GeneralLimitsApplyRequest {
  full_access: boolean;
  bonus_disabled: boolean;
  login_disabled: boolean;
  withdraw_disabled: boolean;
  chat_disabled: boolean;
  tip_disabled: boolean;
  rakeback_disabled: boolean;
  raffle_disabled: boolean;
  bets_disabled: boolean;
  casino_bets_disabled: boolean;
  live_casino_bets_disabled: boolean;
  sportsbook_disabled: boolean;
  race_disabled: boolean;
  aml_disabled: boolean;
  local_aml_disabled: boolean;
  withdrawal_bypass_otp: boolean;
  instant_discount_disabled: boolean;
  weekly_discount_disabled: boolean;
  monthly_discount_disabled: boolean;
  pokerklas_enabled: boolean;
  id: string;
  rt: number;
}

export interface GeneralLimitsApplyResponse {
  data: boolean;
}

export interface TransactionsRequest {
  id?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: number | string | null; // Support both number (2 for withdrawal) and string ("withdrawal")
  status_id?: number | null;
  tx?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  is_manuel?: boolean | string | null; // Support both boolean and string ("all")
  page?: number;
  limit?: number;
  rt?: number;
}

export interface TransactionStatus {
  id: number;
  name: string;
  key: string;
}

export interface TransactionDiscountDetails {
  t: number;
  rt: number;
  code: string;
  pre_t: number;
  deposit: string;
  real_ip: string;
  withdraw: number;
  difference: number;
  return_url: string;
  website_id: number;
  deposit_url: string;
  merchant_id: number;
  website_domain: string;
  payment_provider_id: number;
}

export interface TransactionDiscount {
  id: number;
  merchant_id: number;
  website_id: number;
  customer_id: number;
  deposit_id: number;
  timestamp: number;
  before_balance: string;
  amount: string;
  after_balance: string;
  details: TransactionDiscountDetails;
  code: string;
  wallet_id: number;
  percentage: string;
  note: string | null;
  depositAmount: string;
}

export interface TransactionCustomerProfile {
  id: number;
  name: string;
  surname: string;
  customer_id: number;
}

export interface TransactionCustomerAffiliator {
  id: number;
  name: string;
}

export interface TransactionCustomer {
  id: number;
  username: string;
  affiliator_id: number;
  ref_id: number | null;
  ref_code: string;
  masked_username: string;
  last_action: number;
  profile: TransactionCustomerProfile;
  affiliator: TransactionCustomerAffiliator;
}

export interface TransactionPaymentProvider {
  id: number;
  name: string;
}

export interface Transaction {
  id: number;
  method: string;
  payment_provider_id: number;
  discount_able: boolean;
  approve_details: any | null;
  network: string | null;
  description: string | null;
  customer_id: number;
  is_manuel: number;
  completed_at: number;
  target_address: string | null;
  unique_id: string;
  operator_id: number | null;
  currency: string;
  wallet_id: number;
  amount: string;
  usd_amount: string;
  status_id: number;
  timestamp: number;
  type: number;
  tx_id: string | null;
  aml_met: boolean;
  provider: string;
  status: TransactionStatus;
  discount: TransactionDiscount;
  operator: any | null;
  last_action: any | null;
  customer: TransactionCustomer;
  payment_provider: TransactionPaymentProvider;
}

export interface TransactionsResponse {
  total: number;
  data: Transaction[];
}

export interface CasinoDebitsRequest {
  id?: string | null;
  game?: string | null;
  provider_id?: string | null;
  type?: string | null;
  status?: string | null;
  currency?: string | null;
  wallet_currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | null;
  to?: number | null;
  amount_min?: string | null;
  amount_max?: string | null;
  multiplier_min?: string | null;
  multiplier_max?: string | null;
  income_min?: string | null;
  income_max?: string | null;
  net_min?: string | null;
  net_max?: string | null;
  income_usd_min?: string | null;
  income_usd_max?: string | null;
  wallet_amount_min?: string | null;
  wallet_amount_max?: string | null;
  win_wallet_amount_min?: string | null;
  win_wallet_amount_max?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface CasinoDebit {
  id: number;
  game_id: string;
  customer_id: number;
  is_win: boolean;
  timestamp: number;
  amount: string;
  income: string;
  multiplier: string;
  wallet_currency: string;
  wallet_amount: string;
  game_name: string;
  amount_usd: string;
  finished: boolean;
  game_provider_id: number;
  external_id: string;
  player_id: number;
}

export interface CasinoDebitsStats {
  bet: number;
  win: number;
  bet_total: number;
  win_total: number;
}

export interface CasinoDebitsResponse {
  total: number;
  data: CasinoDebit[];
  stats: CasinoDebitsStats;
}

export interface CustomerBigWinLoseDebitsRequest {
  customer_id: string;
  range?: string;
  from?: number;
  to?: number;
  [key: string]: any;
}

export interface CustomerBigWinLoseCustomer {
  id: number;
  username: string;
  is_public: boolean;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface CustomerBigWinLoseDebit {
  id: number;
  customer_id: number;
  game_id: string;
  game_name: string;
  timestamp: number;
  amount: string;
  income: string;
  net: string;
  customer: CustomerBigWinLoseCustomer;
}

export interface GameProvider {
  id: number;
  identifier: string;
  name: string;
  currencies: string[];
  restrictions: string[];
  system_id: number;
}

export interface GameWebsite {
  id: number;
  website_id: number;
  game_id: number;
  image: string;
  name: string;
  is_active: boolean;
  sort: number;
  color: string;
}

export interface Game {
  id: number;
  name: string;
  slug: string;
  merchant_id: number;
  provider: string;
  blacklist: string[];
  game_website: GameWebsite;
  game_provider: GameProvider;
}

export interface CustomerBigWinLoseLastDebit {
  id: number;
  game_id: number;
  timestamp: number;
  customer: any | null;
  game: Game;
}

export interface CustomerBigWinLoseDebitsResponse {
  win: CustomerBigWinLoseDebit[];
  lose: CustomerBigWinLoseDebit[];
  last: CustomerBigWinLoseLastDebit[];
}

export interface CustomerGameWinLoseDebitsRequest {
  customer_id: string;
  range?: string;
  from?: number;
  to?: number;
  [key: string]: any;
}

export interface CustomerGameWinLoseDebitsResponse {
  win: CustomerBigWinLoseDebit[];
  lose: CustomerBigWinLoseDebit[];
  last: CustomerBigWinLoseLastDebit[];
}

export interface SportsbookPlayersRequest {
  customer_id: string;
  page: number;
  limit: number;
  rt: number;
}

export interface SportsbookPlayerCustomer {
  id: number;
  username: string;
  ranki: string;
  rankc: string;
  masked_username: string;
  last_action: number;
}

export interface SportsbookPlayer {
  id: number;
  currency: string;
  wallet_id: number;
  customer_id: number;
  sportsbook_id: string;
  customer: SportsbookPlayerCustomer;
}

export interface SportsbookPlayersResponse {
  total: number;
  data: SportsbookPlayer[];
}

export interface WalletsRequest {
  limit: number;
  page: number;
  customer_id: string;
  rt: number;
}

export interface WalletNetwork {
  id: number;
  name: string;
  key: string;
  pname: string;
}

export interface WalletCurrency {
  id: number;
  payment_provider_id: number;
  network_id: number;
  currency_id: number | null;
  name: string;
  alias: string;
  precision: number;
  min_transfer: string;
  is_active: boolean;
  network: WalletNetwork;
}

export interface Wallet {
  id: number;
  code: string;
  balance: string;
  address: string | null;
  network_id: number | null;
  currency_id: number;
  currency: WalletCurrency;
}

export interface WalletsResponse {
  total: number;
  data: Wallet[];
}

export interface SportsbookDebitsRequest {
  id?: string | null;
  username?: string | null;
  game?: string | null;
  provider_id?: string | null;
  type?: string | null;
  status?: string | null;
  currency?: string | null;
  wallet_currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | null;
  to?: number | null;
  amount_min?: string | null;
  amount_max?: string | null;
  multiplier_min?: string | null;
  multiplier_max?: string | null;
  income_min?: string | null;
  income_max?: string | null;
  net_min?: string | null;
  net_max?: string | null;
  income_usd_min?: string | null;
  income_usd_max?: string | null;
  wallet_amount_min?: string | null;
  wallet_amount_max?: string | null;
  win_wallet_amount_min?: string | null;
  win_wallet_amount_max?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  odd_min?: string | null;
  odd_max?: string | null;
  page?: number;
  limit?: number;
  customer_id?: string | null;
  timezone?: number;
  rt?: number;
}

export interface SportsbookEvent {
  id: number;
  market_name: string;
  outcome_name: string;
  match_name: string;
  match_start_date: string;
}

export interface SportsbookDebit {
  id: number;
  customer_id: number;
  timestamp: string;
  wallet_currency: string;
  currency: string;
  finished: boolean;
  amount: string;
  possible_win: string;
  wallet_amount: string;
  is_active: boolean;
  income: string;
  type: string;
  state: string;
  odds: string;
  events: SportsbookEvent[];
}

export interface SportsbookDebitsStats {
  bet: number;
  win: number;
  bet_total: number;
  win_total: number;
}

export interface SportsbookDebitsResponse {
  total: number;
  data: SportsbookDebit[];
  stats: SportsbookDebitsStats;
}

export interface DiscountsRequest {
  username?: string | null;
  currency?: string | null;
  from?: number | null;
  to?: number | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface DiscountTransaction {
  id: number;
  amount: string;
  usd_amount: string;
  timestamp: number;
  provider: string;
  payment_provider: any | null;
}

export interface DiscountCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number;
}

export interface Discount {
  id: number;
  timestamp: number;
  customer_id: number;
  before_balance: string;
  amount: string;
  after_balance: string;
  code: string;
  deposit_id: number;
  depositAmount: string;
  transaction: DiscountTransaction;
  customer: DiscountCustomer;
}

export interface DiscountsResponse {
  total: number;
  data: Discount[];
}

export interface NotificationsRequest {
  page: number;
  limit: number;
  customer_id: string;
}

export interface NotificationData {
  currency: string;
  amount: string;
}

export interface Notification {
  id: number;
  customer_id: number;
  type: string;
  lang_key: string;
  timestamp: string;
  data: NotificationData;
}

export interface NotificationsResponse {
  total: number;
  data: Notification[];
}

export interface VaultsRequest {
  id?: string | null;
  sender?: string | null;
  taker?: string | null;
  currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface Vault {
  id: number;
  currency: string;
  balance: string;
}

export interface VaultsResponse {
  data: Vault[];
}

export interface VipStateChangesRequest {
  limit: number;
  page: number;
  customer_id: string;
  rt: number;
}

export interface VipStateChangeCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface VipStateChange {
  id: number;
  customer_id: number;
  timestamp: string;
  total_wager: string;
  from: string;
  to: string;
  customer: VipStateChangeCustomer;
}

export interface VipStateChangesResponse {
  page: string;
  total: number;
  data: VipStateChange[];
}

export interface PlayerActionsRequest {
  timestamp_start?: number | null;
  timestamp_end?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface PlayerActionDetails {
  t?: number;
  rt?: number;
  code?: string;
  pre_t?: number;
  deposit?: string;
  real_ip?: string;
  withdraw?: number;
  difference?: number;
  return_url?: string;
  website_id?: number;
  deposit_url?: string;
  merchant_id?: number;
  website_domain?: string;
  payment_provider_id?: number;
  [key: string]: any; // Allow for additional dynamic properties
}

export interface PlayerActionResult {
  status: string;
  message: string;
  [key: string]: any; // Allow for additional dynamic properties
}

export interface PlayerAction {
  id: number;
  merchant_id: number;
  website_id: number;
  wallet_currency: string | null;
  customer_id: number;
  wallet_id: number | null;
  timestamp: number;
  requested: string;
  details: PlayerActionDetails;
  result: PlayerActionResult;
}

export interface PlayerActionsResponse {
  total: number;
  data: PlayerAction[];
}

export interface CommitsRequest {
  id?: string | null;
  model?: string[] | null;
  way?: string | null;
  currency?: string | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  amount_max?: number | null;
  amount_min?: number | null;
  customer_id?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface CommitCustomer {
  id: number;
  username: string;
  email: string;
  masked_username: string;
  last_action: number;
}

export interface Commit {
  id: number;
  customer_id: number;
  timestamp: number;
  wallet_currency: string;
  way: string;
  model: string;
  amount: string;
  before_balance: string;
  after_balance: string;
  commitable_id: number;
  commitable_type: string;
  customer: CommitCustomer;
  commitable: any | null;
}

export interface CommitsResponse {
  total: number;
  data: Commit[];
}

export interface VipRankRequest {
  slug: string;
  add_gift: boolean;
  customer_id: string;
}

export interface VipRankResponse {
  data: boolean;
  status: number;
  success: boolean;
}

export interface ProfileUpdateRequest {
  id: number;
  username?: string;
  phone?: string;
  country_code?: string;
  email?: string;
  name?: string;
  surname?: string;
  occupation?: string;
  identity_no?: string;
  residential?: string;
  birthday?: string;
  city?: string;
  ghost_mode?: boolean;
  hide_statistics?: boolean;
  hide_race_statistics?: boolean;
  exclude_rain?: boolean;
  receive_marketing_mails?: boolean;
}

export interface ProfileUpdateResponse {
  data: string;
  status: number;
  success: boolean;
}

// Rakeback types
export interface RakebackUsagesRequest {
  from: number;
  to: number;
  limit: number;
  page: number;
  customer_id: string;
  rt: number;
}

export interface RakebackUsageCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: string | null;
}

export interface RakebackUsage {
  id: number;
  before_balance: string;
  after_balance: string;
  amount: string;
  amount_usd: string;
  customer_id: number;
  wallet_id: number;
  timestamp: number;
  customer: RakebackUsageCustomer;
}

export interface RakebackUsagesResponse {
  total: number;
  data: RakebackUsage[];
}

export interface RakebackAvailablesRequest {
  limit: number;
  page: number;
  customer_id: string;
  rt: number;
}

export interface RakebackAvailableCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: string | null;
}

export interface RakebackAvailable {
  id: number;
  customer_id: number;
  lasttime: number;
  usd_amount: string;
  wallet_amount: string;
  wallet_currency: string;
  customer: RakebackAvailableCustomer;
}

export interface RakebackAvailablesResponse {
  page: string;
  total: number;
  data: RakebackAvailable[];
}

export interface TipsRequest {
  id?: string | null;
  sender?: string | null;
  taker?: string | null;
  currency?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  page: number;
  limit: number;
  rt: number;
}

export interface TipCustomer {
  id: number;
  username: string;
  email?: string;
  rank: string;
  masked_username: string;
  last_action: string | null;
}

export interface Tip {
  id: number;
  customer_id: number;
  tipped_customer_id: number;
  tipped_wallet_id: number;
  amount: string;
  usd_amount: string;
  timestamp: string;
  code: string;
  customer: TipCustomer;
  tipped: TipCustomer;
}

export interface TipsResponse {
  total: number;
  data: Tip[];
}

export interface FinancialLimitsRequest {
  id: string;
  rt?: number;
}

export interface TradeDebitsRequest {
  id?: string | null;
  game?: string | null;
  provider_id?: string | null;
  type?: string | null;
  status?: string | null;
  currency?: string | null;
  wallet_currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | null;
  to?: number | null;
  amount_min?: string | null;
  amount_max?: string | null;
  multiplier_min?: string | null;
  multiplier_max?: string | null;
  income_min?: string | null;
  income_max?: string | null;
  net_min?: string | null;
  net_max?: string | null;
  income_usd_min?: string | null;
  income_usd_max?: string | null;
  wallet_amount_min?: string | null;
  wallet_amount_max?: string | null;
  win_wallet_amount_min?: string | null;
  win_wallet_amount_max?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface TradeDebitCustomer {
  id: number;
  username: string;
  nickname: string | null;
  email: string;
  is_public: boolean;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface TradeDebit {
  id: number;
  customer_id: number;
  timestamp: number;
  amount: string;
  income: string;
  multiplier: string;
  wallet_currency: string;
  wallet_amount: string;
  wallet_income: string;
  game_id: string;
  amount_usd: string;
  income_usd: string;
  currency: string;
  finished: boolean;
  customer: TradeDebitCustomer;
}

export interface TradeDebitsStats {
  bet: number;
  win: number;
  bet_total: number;
  win_total: number;
}

export interface TradeDebitsResponse {
  total: number;
  data: TradeDebit[];
  stats: TradeDebitsStats;
}

export interface SessionsRequest {
  from?: number | null;
  to?: number | null;
  customer_id?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface SessionCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface Session {
  id: number;
  customer_id: number;
  ip: string;
  is_active: boolean;
  timestamp: number;
  country: string;
  browser: string;
  customer: SessionCustomer;
}

export interface SessionsResponse {
  total: number;
  data: Session[];
}

export interface BonusRedeemsRequest {
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  is_active?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  amount_min?: string | null;
  amount_max?: string | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
  type?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface BonusRedeemOperator {
  id: number;
  name: string;
  email: string;
  role_id: number;
}

export interface BonusRedeemGame {
  id: number;
  percentage: number;
}

export interface BonusRedeemTypeable {
  id: number;
  quantity: number;
  bet_level: number;
  game_merchant_id: number;
  aggregator_id: number;
  game_merchant_name: string;
  max_win: string;
}

export interface BonusRedeemBonus {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  model: string;
  currency: string;
  currency_id: number;
  name: string;
  description: string;
  note: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  request_able: boolean;
  typeable_id: number;
  typeable_type: string;
  is_active: boolean;
  expire_type: string;
  expire_unit: string;
  games: BonusRedeemGame[];
  block_balance: number;
  is_deleted: boolean;
  typeable: BonusRedeemTypeable;
}

export interface BonusRedeemCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number | null;
}

export interface BonusRedeem {
  id: number;
  merchant_id: number;
  website_id: number;
  bonus_id: number;
  customer_id: number;
  wallet_id: number;
  timestamp: string;
  amount: string;
  multiplier: string;
  required: string;
  current: string;
  is_completed: number;
  operator_id: number;
  note: string;
  deactivated_at: string | null;
  deactivated_by: string | null;
  is_active: number;
  earn_amount: string | null;
  operator: BonusRedeemOperator;
  bonus: BonusRedeemBonus;
  customer: BonusRedeemCustomer;
}

export interface BonusRedeemsResponse {
  total: number;
  data: BonusRedeem[];
}

export interface FinancialLimitsData {
  id: number;
  merchant_id: number;
  website_id: number;
  customer_id: number;
  withdraw_able: boolean;
  auto_withdraw_limit: string;
  min_withdraw: string;
  max_withdraw: string;
  rakeback_able: boolean;
  reload_able: boolean;
  tips_able: boolean;
  affiliate_funds_able: boolean;
  boost_able: boolean;
  race_able: boolean;
  raffle_able: boolean;
}

export interface FinancialLimitsResponse {
  data: FinancialLimitsData;
  status: number;
  success: boolean;
}

export interface FinancialLimitsUpdateRequest {
  id: string;
  merchant_id: number;
  website_id: number;
  customer_id: number;
  withdraw_able: boolean;
  auto_withdraw_limit: string;
  min_withdraw: string;
  max_withdraw: string;
  rakeback_able: boolean;
  reload_able: boolean;
  tips_able: boolean;
  affiliate_funds_able: boolean;
  boost_able: boolean;
  race_able: boolean;
  raffle_able: boolean;
  rt: number;
}

export interface PasswordUpdateRequest {
  password: string;
  id: number;
  rt?: number;
}

export interface PasswordUpdateResponse {
  data: boolean;
  status: number;
  success: boolean;
}

// FTD (First Time Deposit) types
export interface FtdTransactionsRequest {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  is_manuel?: boolean | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface FtdTransaction {
  customer_id: number;
  username: string;
  registration_ts: number;
  currency: string;
  unique_id: string;
  first_deposit_date: number;
  first_deposit_id: number;
  first_deposit_amount: string;
  first_deposit_currency: string;
  first_deposit_amount_usd: string;
  total_deposit_amount: string;
  total_usd_amount: string;
  phone_full: string | null;
}

export interface FtdTransactionsResponse {
  total_count: number;
  total_sum: string;
  data: FtdTransaction[];
}

// FTW (First Time Withdrawal) types
export interface FtwTransactionsRequest {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  is_manuel?: boolean | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface FtwTransaction {
  customer_id: number;
  username: string;
  registration_ts: number;
  currency: string;
  first_withdrawal_date: number;
  first_withdrawal_id: number;
  first_withdrawal_amount: string;
  first_withdrawal_currency: string;
  first_withdrawal_amount_usd: string;
  total_withdrawal_amount: string;
  total_usd_amount: string;
  phone_full: string | null;
}

export interface FtwTransactionsResponse {
  total_count: number;
  total_sum: string;
  data: FtwTransaction[];
}

// Fiat transactions (waiting deposits) types
export interface FiatTransactionsRequest {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: number | null;
  is_manuel?: boolean | null;
  status_id?: number | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface FiatTransactionCustomerProfile {
  id: number;
  name: string;
  surname: string;
  customer_id: number;
  identity_no: string;
}

export interface FiatTransactionCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number;
  profile: FiatTransactionCustomerProfile;
}

export interface FiatTransactionPaymentProvider {
  id: number;
  name: string;
}

export interface FiatTransactionStatus {
  id: number;
  name: string;
  key: string;
}

export interface FiatTransaction {
  id: number;
  method: string;
  customer_id: number;
  rate: string;
  amount: string;
  usd_amount: string;
  payment_provider_id: number;
  currency: string;
  timestamp: string;
  status: FiatTransactionStatus;
  provider: string;
  customer: FiatTransactionCustomer;
  payment_provider: FiatTransactionPaymentProvider;
}

export interface FiatTransactionsResponse {
  total: number;
  data: FiatTransaction[];
}

// Transaction Summary types
export interface TransactionsSummaryRequest {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  is_manuel?: boolean | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  amount_min?: number | null;
  amount_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

export interface TransactionsSummaryResponse {
  deposit: {
    deposit: string;
    total: number;
    provider: string;
    payment_provider: any;
  };
  withdraw: {
    withdraw: string;
    total: number;
    provider: string;
    payment_provider: any;
  };
  currency: string;
}

export interface AffiliateSetRequest {
  id: number;
  code: string;
}

export interface AffiliateSetResponse {
  data: string;
}
