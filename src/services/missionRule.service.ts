import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { MissionRule } from '@/entities/MissionRule';
import { RuleType, CompareOperator } from '@/enums/shared';

export interface CreateMissionRuleDto {
  ruleType: RuleType;
  compare: CompareOperator;
  compareValue: string;
  minDate?: number | null;
  maxDate?: number | null;
}

export interface MissionRuleQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'ruleType' | 'compare' | 'compareValue' | 'minDate' | 'maxDate' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';
  
  // Filters Actually Used in Component
  ruleType?: RuleType; // Filter by rule type
  compare?: CompareOperator; // Filter by compare operator
  compareValue?: string; // Filter by compare value (partial match)
  
  // Additional Parameters
  minDateFrom?: number; // Min date range filtering
  minDateTo?: number; // Min date range filtering
  maxDateFrom?: number; // Max date range filtering
  maxDateTo?: number; // Max date range filtering
  search?: string; // General search term (searches compareValue)
}

export interface MissionRuleListResponse {
  missionRules: MissionRule[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class MissionRuleService {
  private missionRuleRepository: Repository<MissionRule>;

  constructor() {
    this.missionRuleRepository = AppDataSource.getRepository(MissionRule);
  }

  async createMissionRule(createMissionRuleDto: CreateMissionRuleDto): Promise<MissionRule> {
    const missionRule = this.missionRuleRepository.create(createMissionRuleDto);
    return await this.missionRuleRepository.save(missionRule);
  }

  async findMissionRuleById(id: number): Promise<MissionRule | null> {
    return await this.missionRuleRepository.findOne({ where: { id } });
  }

  async findAllMissionRules(): Promise<MissionRule[]> {
    return await this.missionRuleRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findMissionRulesWithQuery(queryParams: MissionRuleQueryParams): Promise<MissionRuleListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      // Filters
      ruleType,
      compare,
      compareValue,
      minDateFrom,
      minDateTo,
      maxDateFrom,
      maxDateTo,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.missionRuleRepository.createQueryBuilder('missionRule');

    // Apply filters
    if (ruleType) {
      queryBuilder.andWhere('missionRule.ruleType = :ruleType', { ruleType });
    }

    if (compare) {
      queryBuilder.andWhere('missionRule.compare = :compare', { compare });
    }

    if (compareValue) {
      queryBuilder.andWhere('missionRule.compareValue ILIKE :compareValue', { 
        compareValue: `%${compareValue}%` 
      });
    }

    // Date range filters
    if (minDateFrom !== undefined) {
      queryBuilder.andWhere('missionRule.minDate >= :minDateFrom', { minDateFrom });
    }

    if (minDateTo !== undefined) {
      queryBuilder.andWhere('missionRule.minDate <= :minDateTo', { minDateTo });
    }

    if (maxDateFrom !== undefined) {
      queryBuilder.andWhere('missionRule.maxDate >= :maxDateFrom', { maxDateFrom });
    }

    if (maxDateTo !== undefined) {
      queryBuilder.andWhere('missionRule.maxDate <= :maxDateTo', { maxDateTo });
    }

    // General search term (searches compareValue)
    if (search) {
      queryBuilder.andWhere('missionRule.compareValue ILIKE :search', { 
        search: `%${search}%` 
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`missionRule.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const missionRules = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      missionRules,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findMissionRulesByType(ruleType: RuleType): Promise<MissionRule[]> {
    return await this.missionRuleRepository.find({
      where: { ruleType },
      order: { createdAt: 'DESC' },
    });
  }

  async updateMissionRule(id: number, updateData: Partial<CreateMissionRuleDto>): Promise<MissionRule | null> {
    await this.missionRuleRepository.update(id, updateData);
    return await this.findMissionRuleById(id);
  }

  async deleteMissionRule(id: number): Promise<boolean> {
    const result = await this.missionRuleRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
