import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { MissionObjective } from '@/entities/MissionObjective';
import { ObjectiveType, CompareOperator } from '@/enums/shared';

export interface CreateMissionObjectiveDto {
  missionId: number;
  objectiveType: ObjectiveType;
  subtype?: string | null;
  operator: CompareOperator;
  targetValue: string;
  description?: string | null;
  timeframeStart?: number | null;
  timeframeEnd?: number | null;
  metadata?: any;
}

export interface MissionObjectiveQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'missionId' | 'objectiveType' | 'operator' | 'targetValue' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters
  missionId?: number;
  objectiveType?: ObjectiveType;
  subtype?: string;
  operator?: CompareOperator;
  targetValue?: string;
  description?: string;
  timeframeStartFrom?: number;
  timeframeStartTo?: number;
  timeframeEndFrom?: number;
  timeframeEndTo?: number;
  search?: string;
}

export interface MissionObjectiveListResponse {
  missionObjectives: MissionObjective[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class MissionObjectiveService {
  private missionObjectiveRepository: Repository<MissionObjective>;

  constructor() {
    this.missionObjectiveRepository = AppDataSource.getRepository(MissionObjective);
  }

  async createMissionObjective(createMissionObjectiveDto: CreateMissionObjectiveDto): Promise<MissionObjective> {
    const missionObjective = this.missionObjectiveRepository.create(createMissionObjectiveDto);
    return await this.missionObjectiveRepository.save(missionObjective);
  }

  async findMissionObjectiveById(id: number): Promise<MissionObjective | null> {
    return await this.missionObjectiveRepository.findOne({ 
      where: { id },
      relations: ['mission']
    });
  }

  async findAllMissionObjectives(): Promise<MissionObjective[]> {
    return await this.missionObjectiveRepository.find({
      relations: ['mission'],
      order: { createdAt: 'DESC' },
    });
  }

  async findMissionObjectivesWithQuery(queryParams: MissionObjectiveQueryParams): Promise<MissionObjectiveListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      missionId,
      objectiveType,
      subtype,
      operator,
      targetValue,
      description,
      timeframeStartFrom,
      timeframeStartTo,
      timeframeEndFrom,
      timeframeEndTo,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.missionObjectiveRepository.createQueryBuilder('missionObjective')
      .leftJoinAndSelect('missionObjective.mission', 'mission');

    // Apply filters
    if (missionId) {
      queryBuilder.andWhere('missionObjective.missionId = :missionId', { missionId });
    }

    if (objectiveType) {
      queryBuilder.andWhere('missionObjective.objectiveType = :objectiveType', { objectiveType });
    }

    if (subtype) {
      queryBuilder.andWhere('missionObjective.subtype ILIKE :subtype', { subtype: `%${subtype}%` });
    }

    if (operator) {
      queryBuilder.andWhere('missionObjective.operator = :operator', { operator });
    }

    if (targetValue) {
      queryBuilder.andWhere('missionObjective.targetValue ILIKE :targetValue', { targetValue: `%${targetValue}%` });
    }

    if (description) {
      queryBuilder.andWhere('missionObjective.description ILIKE :description', { description: `%${description}%` });
    }

    // Timeframe filters
    if (timeframeStartFrom) {
      queryBuilder.andWhere('missionObjective.timeframeStart >= :timeframeStartFrom', { timeframeStartFrom });
    }

    if (timeframeStartTo) {
      queryBuilder.andWhere('missionObjective.timeframeStart <= :timeframeStartTo', { timeframeStartTo });
    }

    if (timeframeEndFrom) {
      queryBuilder.andWhere('missionObjective.timeframeEnd >= :timeframeEndFrom', { timeframeEndFrom });
    }

    if (timeframeEndTo) {
      queryBuilder.andWhere('missionObjective.timeframeEnd <= :timeframeEndTo', { timeframeEndTo });
    }

    // General search term (searches in description and targetValue)
    if (search) {
      queryBuilder.andWhere(
        '(missionObjective.description ILIKE :search OR missionObjective.targetValue ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`missionObjective.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const missionObjectives = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      missionObjectives,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findMissionObjectivesByMissionId(missionId: number): Promise<MissionObjective[]> {
    return await this.missionObjectiveRepository.find({
      where: { missionId },
      relations: ['mission'],
      order: { createdAt: 'DESC' },
    });
  }

  async findMissionObjectivesByType(objectiveType: ObjectiveType): Promise<MissionObjective[]> {
    return await this.missionObjectiveRepository.find({
      where: { objectiveType },
      relations: ['mission'],
      order: { createdAt: 'DESC' },
    });
  }

  async updateMissionObjective(id: number, updateData: Partial<CreateMissionObjectiveDto>): Promise<MissionObjective | null> {
    await this.missionObjectiveRepository.update(id, updateData);
    return await this.findMissionObjectiveById(id);
  }

  async deleteMissionObjective(id: number): Promise<boolean> {
    const result = await this.missionObjectiveRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
