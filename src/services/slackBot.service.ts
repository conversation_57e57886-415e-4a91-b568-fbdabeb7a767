import { WebClient } from '@slack/web-api';
import { Request, Response } from 'express';

/**
 * Slack Bot Validator Function Type
 * 
 * Validates incoming request data and returns validation result
 */
export type SlackBotValidator = (req: Request) => {
  isValid: boolean;
  error?: string;
};

/**
 * Slack Bot Handler Function Type
 *
 * Processes validated request and returns message to send to Slack
 */
export type SlackBotHandler = (req: Request, res: Response) => Promise<{
  shouldSend: boolean;
  message?: string;
  error?: string;
  originalResponse?: any;
  originalStatusCode?: number;
}>;

/**
 * Slack Bot Configuration
 */
export interface SlackBotConfig {
  route: string;
  channel: string;
  validator: SlackBotValidator;
  handler: SlackBotHandler;
}

/**
 * Registered Slack Bot Instance
 */
export interface RegisteredSlackBot extends SlackBotConfig {
  id: string;
  registeredAt: Date;
}

/**
 * Slack Bot Registry and Management Service
 *
 * This service provides the core infrastructure for registering and managing
 * multiple Slack bots with different routes, channels, validators, and handlers.
 *
 * Key Features:
 * - Bot registration with route conflict detection
 * - Automatic Slack API integration
 * - Request processing pipeline (validation -> handling -> messaging)
 * - Registry management and statistics
 *
 * Usage:
 * 1. Register bots using registerSlackBot() function
 * 2. Routes are automatically created by slackBotRoutes middleware
 * 3. Requests are processed through processBotRequest() method
 *
 * @example
 * ```typescript
 * registerSlackBot('/api/v1/my-bot', 'my-channel', validator, handler);
 * ```
 */
export class SlackBotService {
  private client: WebClient | null = null;
  private botRegistry: Map<string, RegisteredSlackBot> = new Map();
  private routeRegistry: Map<string, RegisteredSlackBot> = new Map();

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize Slack client with environment variables
   */
  private initializeClient(): void {
    try {
      const token = process.env['SLACK_BOT_TOKEN'];

      if (!token) {
        console.warn('⚠️ SLACK_BOT_TOKEN not found in environment variables');
        return;
      }

      this.client = new WebClient(token);
      console.log('✅ Slack bot service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Slack bot service:', error);
    }
  }

  /**
   * Register a new Slack bot
   * 
   * @param route - The POST route that triggers the bot
   * @param channel - The Slack channel to send messages to
   * @param validator - Function to validate incoming requests
   * @param handler - Function to process requests and generate messages
   * @returns The registered bot ID
   */
  registerBot(
    route: string,
    channel: string,
    validator: SlackBotValidator,
    handler: SlackBotHandler
  ): string {
    // Generate unique bot ID
    const botId = `bot_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Check for route conflicts
    if (this.routeRegistry.has(route)) {
      throw new Error(`Route ${route} is already registered by another bot`);
    }

    // Create bot configuration
    const botConfig: RegisteredSlackBot = {
      id: botId,
      route,
      channel,
      validator,
      handler,
      registeredAt: new Date(),
    };

    // Register the bot
    this.botRegistry.set(botId, botConfig);
    this.routeRegistry.set(route, botConfig);

    console.log(`✅ Slack bot registered: ${botId}`);
    console.log(`📍 Route: POST ${route}`);
    console.log(`📢 Channel: ${channel}`);

    return botId;
  }

  /**
   * Get all registered bots
   */
  getAllBots(): RegisteredSlackBot[] {
    return Array.from(this.botRegistry.values());
  }

  /**
   * Get bot by route
   */
  getBotByRoute(route: string): RegisteredSlackBot | undefined {
    return this.routeRegistry.get(route);
  }

  /**
   * Get bot by ID
   */
  getBotById(botId: string): RegisteredSlackBot | undefined {
    return this.botRegistry.get(botId);
  }

  /**
   * Send a message to a specific Slack channel
   * 
   * @param channel - The Slack channel to send to
   * @param text - The message text
   * @returns Promise<boolean> - true if successful
   */
  async sendMessage(channel: string, text: string): Promise<boolean> {
    try {
      if (!this.client) {
        console.warn('⚠️ Slack client not initialized - skipping message');
        return false;
      }

      console.log('📤 Sending Slack message...');
      console.log(`📢 Channel: ${channel}`);
      console.log(`💬 Message: ${text}`);

      const result = await this.client.chat.postMessage({
        channel: channel,
        text: text,
      });

      console.log(`✅ Slack message sent successfully: ${result.ts} in channel ${result.channel}`);
      return true;

    } catch (error) {
      console.error('❌ Failed to send Slack message:', error);
      return false;
    }
  }

  /**
   * Process a bot request (used by auto-generated routes)
   * 
   * @param route - The route that was called
   * @param req - Express request object
   * @param res - Express response object
   */
  async processBotRequest(route: string, req: Request, res: Response): Promise<void> {
    try {
      const bot = this.getBotByRoute(route);
      
      if (!bot) {
        console.error(`❌ No bot registered for route: ${route}`);
        return res.status(404).json({
          success: false,
          message: `No Slack bot registered for route: ${route}`,
        });
      }

      console.log(`🤖 Processing Slack bot request for: ${bot.id}`);
      console.log(`📍 Route: ${route}`);
      console.log(`📢 Channel: ${bot.channel}`);

      // Validate the request
      const validationResult = bot.validator(req);
      
      if (!validationResult.isValid) {
        console.log(`❌ Validation failed: ${validationResult.error}`);
        return res.status(400).json({
          success: false,
          message: validationResult.error || 'Request validation failed',
        });
      }

      console.log('✅ Request validation passed');

      // Process the request with the handler
      const handlerResult = await bot.handler(req, res);

      // Always return the original response first
      const statusCode = handlerResult.originalStatusCode || 200;
      const responseData = handlerResult.originalResponse || { success: true };

      if (!handlerResult.shouldSend) {
        console.log(`⏭️ Handler decided not to send message: ${handlerResult.error || 'No reason provided'}`);
        return res.status(statusCode).json(responseData);
      }

      if (!handlerResult.message) {
        console.error('❌ Handler returned shouldSend=true but no message');
        return res.status(statusCode).json(responseData);
      }

      // Send the Slack message (async, don't wait for it)
      this.sendMessage(bot.channel, handlerResult.message).then(messageSent => {
        if (messageSent) {
          console.log('✅ Slack message sent successfully');
        } else {
          console.error('❌ Failed to send Slack message');
        }
      }).catch(error => {
        console.error('❌ Error sending Slack message:', error);
      });

      // Return the original response immediately
      console.log('✅ Returning original response to client');
      res.status(statusCode).json(responseData);

    } catch (error) {
      console.error('❌ Error processing Slack bot request:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error processing Slack bot request',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Check if Slack client is properly configured
   */
  isConfigured(): boolean {
    return this.client !== null;
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    totalBots: number;
    registeredRoutes: string[];
    isClientConfigured: boolean;
  } {
    return {
      totalBots: this.botRegistry.size,
      registeredRoutes: Array.from(this.routeRegistry.keys()),
      isClientConfigured: this.isConfigured(),
    };
  }
}

// Export singleton instance
export const slackBotService = new SlackBotService();

/**
 * Main registration function - this is the public API
 * 
 * @param route - The POST route that triggers the bot
 * @param channel - The Slack channel to send messages to  
 * @param validator - Function to validate incoming requests
 * @param handler - Function to process requests and generate messages
 * @returns The registered bot ID
 */
export function registerSlackBot(
  route: string,
  channel: string,
  validator: SlackBotValidator,
  handler: SlackBotHandler
): string {
  return slackBotService.registerBot(route, channel, validator, handler);
}
