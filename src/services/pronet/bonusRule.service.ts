import { BonusRule } from '@/entities/pronet/BonusRule';
import { BonusTemplateRule } from '@/entities/pronet/BonusTemplateRule';
import { EntityManager } from 'typeorm';

export interface BonusRuleCreate {
  bonusId: number;
  criterium: string;
  firstOperand: string;
  secondOperand: string;
  operator: string;
  startsAt: Date;
  endsAt: Date;
}

export interface BonusTemplateRuleCreate {
  bonusTemplateId: number;
  criterium: string;
  firstOperand: string;
  secondOperand: string;
  operator: string;
  startsInSeconds: number;
  endsInSeconds: number;
}

export class BonusRuleService {
  async createBonusRuleTx(data: BonusRuleCreate, tx: EntityManager): Promise<BonusRule> {
    const rule = tx.create(BonusRule, {
      bonusId: data.bonusId,
      criterium: data.criterium,
      firstOperand: data.firstOperand,
      secondOperand: data.secondOperand,
      operator: data.operator,
      startsAt: data.startsAt,
      endsAt: data.endsAt,
    });

    return await tx.save(rule);
  }

  async createBonusTemplateRuleTx(data: BonusTemplateRuleCreate, tx: EntityManager): Promise<BonusTemplateRule> {
    const rule = tx.create(BonusTemplateRule, {
      bonusTemplateId: data.bonusTemplateId,
      criterium: data.criterium,
      firstOperand: data.firstOperand,
      secondOperand: data.secondOperand,
      operator: data.operator,
      startsInSeconds: data.startsInSeconds,
      endsInSeconds: data.endsInSeconds,
    });

    return await tx.save(rule);
  }
}
