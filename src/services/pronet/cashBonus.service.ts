import { AppDataSource } from '@/database/data-source';
import { CashBonus } from '@/entities/pronet/CashBonus';
import { CashBonusTemplate } from '@/entities/pronet/CashBonusTemplate';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';
import { BonusRuleService, BonusRuleCreate, BonusTemplateRuleCreate } from './bonusRule.service';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';

export interface ListCashBonusOptions {
  isActive?: boolean | undefined;
  page: number;
  limit: number;
}

export interface ListCashBonusTemplateOptions {
  isActive?: boolean | undefined;
  page: number;
  limit: number;
}

export interface CashBonusCreate {
  name: string;
  cashAmount: number;
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface CashBonusTemplateCreate {
  name: string;
  cashAmount: number;
  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export interface BonusBulkAssignmentJobCreate {
  cashBonusId: number;
  externalCustomerIds: number[];
}

export class CashBonusService {
  private bonusRuleService = new BonusRuleService();

  async list(options: ListCashBonusOptions) {
    const { isActive, page, limit } = options;

    const queryBuilder = AppDataSource.getRepository(CashBonus)
      .createQueryBuilder('cashBonus')
      .leftJoinAndSelect('cashBonus.bonus', 'bonus')
      .leftJoinAndSelect('bonus.rules', 'rules');

    if (isActive !== undefined) {
      queryBuilder.andWhere('bonus.isActive = :isActive', { isActive });
    }

    queryBuilder
      .orderBy('cashBonus.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findById(id: number): Promise<CashBonus | null> {
    return await AppDataSource.getRepository(CashBonus).findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  async listTemplates(options: ListCashBonusTemplateOptions) {
    const { isActive, page, limit } = options;

    const queryBuilder = AppDataSource.getRepository(CashBonusTemplate)
      .createQueryBuilder('cashBonusTemplate')
      .leftJoinAndSelect('cashBonusTemplate.bonusTemplate', 'bonusTemplate')
      .leftJoinAndSelect('bonusTemplate.rules', 'rules');

    if (isActive !== undefined) {
      queryBuilder.andWhere('bonusTemplate.deletedAt IS NULL');
    }

    queryBuilder
      .orderBy('cashBonusTemplate.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findTemplateById(id: number): Promise<CashBonusTemplate | null> {
    return await AppDataSource.getRepository(CashBonusTemplate).findOne({
      where: { id },
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
    });
  }

  async create(data: CashBonusCreate): Promise<CashBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        description: `Cash bonus of ${data.cashAmount} TRY`,
        reward: `${data.cashAmount} TRY Cash`,
        type: 'cash',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const cashBonus = manager.create(CashBonus, {
        bonusId: bonus.id,
        cashAmount: data.cashAmount,
      });

      await manager.save(cashBonus);

      return cashBonus.id;
    });

    const createdCashBonus = await this.findById(result);
    if (!createdCashBonus) {
      throw new Error('Failed to create cash bonus');
    }

    return createdCashBonus;
  }

  async createTemplate(data: CashBonusTemplateCreate): Promise<CashBonusTemplate> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'cash',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const cashBonusTemplate = manager.create(CashBonusTemplate, {
        bonusTemplateId: bonusTemplate.id,
        cashAmount: data.cashAmount,
      });

      await manager.save(cashBonusTemplate);

      return cashBonusTemplate.id;
    });

    const createdTemplate = await this.findTemplateById(result);
    if (!createdTemplate) {
      throw new Error('Failed to create cash bonus template');
    }

    return createdTemplate;
  }

  async createBulkAssignmentJob(data: BonusBulkAssignmentJobCreate): Promise<BonusBulkAssignmentJob> {
    return await AppDataSource.transaction(async (manager) => {
      const cashBonusRepository = manager.getRepository(CashBonus);
      const cashBonus = await cashBonusRepository.findOne({
        where: { id: data.cashBonusId },
        relations: ['bonus'],
      });

      if (!cashBonus) {
        throw new Error('Cash bonus not found');
      }

      if (!cashBonus.bonus.isActive) {
        throw new Error('Cash bonus is not active');
      }

      const values = {
        base: {
          expiresAt: cashBonus.bonus.expiresAt,
        },
        cash: {
          cashAmount: cashBonus.cashAmount,
        },
      };

      const createdJob = manager.create(BonusBulkAssignmentJob, {
        bonusId: cashBonus.bonus.id,
        bonusValues: values,
        bonusValuesVersion: 1,
        status: 'pending',
      });

      await manager.save(createdJob);

      for (const externalCustomerId of data.externalCustomerIds) {
        const target = manager.create(BonusBulkAssignmentJobTarget, {
          bonusBulkAssignmentJobId: createdJob.id,
          externalCustomerId,
          status: 'pending',
          events: [],
        });

        await manager.save(target);
      }

      const job = await manager.findOne(BonusBulkAssignmentJob, {
        where: { id: createdJob.id },
        relations: ['targets', 'bonus'],
      });
      if (!job) {
        throw new Error('Failed to create bulk assignment job');
      }

      return job;
    });
  }
}
