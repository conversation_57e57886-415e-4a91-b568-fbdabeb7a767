import { TrialBonus } from '@/entities/pronet/TrialBonus';
import { Bonus } from '@/entities/pronet/Bonus';
import { AppDataSource } from '@/database/data-source';
import { BonusRuleCreate, BonusRuleService, BonusTemplateRuleCreate } from './bonusRule.service';
import { TrialBonusTemplate } from '@/entities/pronet/TrialBonusTemplate';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { FindOptionsWhere, IsNull, Not } from 'typeorm';
import { paginate, Paginated } from '../shared';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';

export interface ListTrialBonusOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface ListTrialBonusTemplateOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface TrialBonusCreate {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface TrialBonusTemplateCreate {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;

  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export interface BonusBulkAssignmentJobCreate {
  trialBonusId: number;
  externalCustomerIds: number[];
}

export class TrialBonusService {
  private bonusRuleService: BonusRuleService;

  constructor() {
    this.bonusRuleService = new BonusRuleService();
  }

  async list(options: ListTrialBonusOptions): Promise<Paginated<TrialBonus>> {
    const bonusFilter: FindOptionsWhere<Bonus> = {
      deletedAt: IsNull(),
    };

    if (options.isActive !== undefined) {
      bonusFilter.isActive = options.isActive;
    }

    const [items, total] = await AppDataSource.getRepository(TrialBonus).findAndCount({
      relations: ['bonus', 'bonus.rules'],
      where: {
        bonus: bonusFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async listTemplates(options: ListTrialBonusTemplateOptions): Promise<Paginated<TrialBonusTemplate>> {
    const bonusTemplateFilter: FindOptionsWhere<BonusTemplate> = {};

    if (options.isActive !== undefined) {
      bonusTemplateFilter.deletedAt = options.isActive ? IsNull() : Not(IsNull());
    }

    const [items, total] = await AppDataSource.getRepository(TrialBonusTemplate).findAndCount({
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
      where: {
        bonusTemplate: bonusTemplateFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async findById(id: number): Promise<TrialBonus | null> {
    return await AppDataSource.getRepository(TrialBonus).findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  async findTemplateById(id: number): Promise<TrialBonusTemplate | null> {
    return await AppDataSource.getRepository(TrialBonusTemplate).findOne({
      where: { id },
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
    });
  }

  async create(data: TrialBonusCreate): Promise<TrialBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        type: 'trial',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const trialBonus = manager.create(TrialBonus, {
        externalBonusName: data.externalBonusName,
        externalBonusId: data.externalBonusId,
        amount: data.amount,
        bonusId: bonus.id,
      });

      return await manager.save(trialBonus);
    });

    const bonus = await this.findById(result.id);
    if (!bonus) {
      throw new Error('Failed to create trial bonus');
    }

    return bonus;
  }

  async createTemplate(data: TrialBonusTemplateCreate): Promise<TrialBonusTemplate> {
    const id = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'trial',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const trialBonusTemplate = manager.create(TrialBonusTemplate, {
        externalBonusName: data.externalBonusName,
        externalBonusId: data.externalBonusId,
        amount: data.amount,
        bonusTemplateId: bonusTemplate.id,
      });

      await manager.save(trialBonusTemplate);

      return trialBonusTemplate.id;
    });

    const bonusTemplate = await this.findTemplateById(id);
    if (!bonusTemplate) {
      throw new Error('Failed to create trial bonus template');
    }

    return bonusTemplate;
  }

  async createBulkAssignmentJob(data: BonusBulkAssignmentJobCreate): Promise<BonusBulkAssignmentJob> {
    return await AppDataSource.transaction(async (manager) => {
      const trialBonusRepository = manager.getRepository(TrialBonus);
      const trialBonus = await trialBonusRepository.findOne({
        where: { id: data.trialBonusId },
        relations: ['bonus'],
      });

      if (!trialBonus) {
        throw new Error('Trial bonus not found');
      }

      if (!trialBonus.bonus.isActive) {
        throw new Error('Trial bonus is not active');
      }

      const values = {
        base: {
          expiresAt: trialBonus.bonus.expiresAt,
        },
        trial: {
          externalBonusName: trialBonus.externalBonusName,
          externalBonusId: trialBonus.externalBonusId,
          amount: trialBonus.amount,
        },
      };

      const createdJob = manager.create(BonusBulkAssignmentJob, {
        bonusId: trialBonus.bonus.id,
        bonusValues: values,
        bonusValuesVersion: 1,
        status: 'pending',
      });

      await manager.save(createdJob);

      for (const externalCustomerId of data.externalCustomerIds) {
        const target = manager.create(BonusBulkAssignmentJobTarget, {
          bonusBulkAssignmentJobId: createdJob.id,
          externalCustomerId,
          status: 'pending',
          events: [],
        });

        await manager.save(target);
      }

      const job = await manager.findOne(BonusBulkAssignmentJob, {
        where: { id: createdJob.id },
        relations: ['targets', 'bonus'],
      });
      if (!job) {
        throw new Error('Failed to create bulk assignment job');
      }

      return job;
    });
  }
}
