import { CasinoLossbackBonus } from '@/entities/pronet/CasinoLossbackBonus';
import { CasinoLossbackBonusTemplate } from '@/entities/pronet/CasinoLossbackBonusTemplate';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { AppDataSource } from '@/database/data-source';
import { BonusRuleCreate, BonusRuleService, BonusTemplateRuleCreate } from './bonusRule.service';
import { FindOptionsWhere, IsNull } from 'typeorm';
import { paginate, Paginated } from '../shared';

export interface ListCasinoLossbackBonusOptions {
  isActive?: boolean | undefined;
  page: number;
  limit: number;
}

export interface ListCasinoLossbackBonusTemplateOptions {
  isActive?: boolean | undefined;
  page: number;
  limit: number;
}

export interface CasinoLossbackBonusCreate {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart?: string | null;
  happyHoursEnd?: string | null;
  happyHoursBoostPercentage?: number;
  depositWithDrawDifferenceThreshold?: number;
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface CasinoLossbackBonusTemplateCreate {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart?: string | null;
  happyHoursEnd?: string | null;
  happyHoursBoostPercentage?: number;
  depositWithDrawDifferenceThreshold?: number;
  validForDays: number;
  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export class CasinoLossbackBonusService {
  private bonusRuleService: BonusRuleService;

  constructor() {
    this.bonusRuleService = new BonusRuleService();
  }

  async list(options: ListCasinoLossbackBonusOptions): Promise<Paginated<CasinoLossbackBonus>> {
    const bonusFilter: FindOptionsWhere<Bonus> = {
      deletedAt: IsNull(),
    };

    if (options.isActive !== undefined) {
      bonusFilter.isActive = options.isActive;
    }

    const [items, total] = await AppDataSource.getRepository(CasinoLossbackBonus).findAndCount({
      relations: ['bonus', 'bonus.rules'],
      where: {
        bonus: bonusFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async listTemplates(
    options: ListCasinoLossbackBonusTemplateOptions,
  ): Promise<Paginated<CasinoLossbackBonusTemplate>> {
    const bonusTemplateFilter: FindOptionsWhere<BonusTemplate> = {
      deletedAt: IsNull(),
    };

    const [items, total] = await AppDataSource.getRepository(CasinoLossbackBonusTemplate).findAndCount({
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
      where: {
        bonusTemplate: bonusTemplateFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async findById(id: number): Promise<CasinoLossbackBonus | null> {
    return await AppDataSource.getRepository(CasinoLossbackBonus).findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  async findTemplateById(id: number): Promise<CasinoLossbackBonusTemplate | null> {
    return await AppDataSource.getRepository(CasinoLossbackBonusTemplate).findOne({
      where: { id },
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
    });
  }

  async create(data: CasinoLossbackBonusCreate): Promise<CasinoLossbackBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        type: 'lossback',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const lossbackBonus = manager.create(CasinoLossbackBonus, {
        bonusId: bonus.id,
        maxBalance: data.maxBalance,
        lossbackPercentage: data.lossbackPercentage,
        happyHoursStart: data.happyHoursStart ?? null,
        happyHoursEnd: data.happyHoursEnd ?? null,
        happyHoursBoostPercentage: data.happyHoursBoostPercentage ?? 2.00,
        depositWithDrawDifferenceThreshold: data.depositWithDrawDifferenceThreshold ?? 100.00,
      });

      await manager.save(lossbackBonus);

      return lossbackBonus.id;
    });

    const bonus = await this.findById(result);
    if (!bonus) {
      throw new Error('Failed to create casino lossback bonus');
    }

    return bonus;
  }

  async createTemplate(data: CasinoLossbackBonusTemplateCreate): Promise<CasinoLossbackBonusTemplate> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'lossback',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const lossbackBonusTemplate = manager.create(CasinoLossbackBonusTemplate, {
        bonusTemplateId: bonusTemplate.id,
        maxBalance: data.maxBalance,
        lossbackPercentage: data.lossbackPercentage,
        happyHoursStart: data.happyHoursStart ?? null,
        happyHoursEnd: data.happyHoursEnd ?? null,
        happyHoursBoostPercentage: data.happyHoursBoostPercentage ?? 2.00,
        depositWithDrawDifferenceThreshold: data.depositWithDrawDifferenceThreshold ?? 100.00,
        validForDays: data.validForDays,
      });

      await manager.save(lossbackBonusTemplate);

      return lossbackBonusTemplate.id;
    });

    const template = await this.findTemplateById(result);
    if (!template) {
      throw new Error('Failed to create casino lossback bonus template');
    }

    return template;
  }
}
