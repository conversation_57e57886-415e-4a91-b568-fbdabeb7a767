import { CasinoFreespinBonus } from '@/entities/pronet/CasinoFreespinBonus';
import { Bonus } from '@/entities/pronet/Bonus';
import { AppDataSource } from '@/database/data-source';
import { BonusRuleCreate, BonusRuleService, BonusTemplateRuleCreate } from './bonusRule.service';
import { CasinoFreespinBonusTemplate } from '@/entities/pronet/CasinoFreespinBonusTemplate';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { FindOptionsWhere, IsNull, Not } from 'typeorm';
import { paginate, Paginated } from '../shared';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';

export interface ListCasinoFreespinBonusOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface ListCasinoFreespinBonusTemplateOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface CasinoFreespinBonusCreate {
  name: string;
  vendorId: number;
  vendorName: string;
  values: Record<string, any>;
  gameIds: number[];
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface CasinoFreespinBonusTemplateCreate {
  name: string;
  vendorId: number;
  vendorName: string;
  values: Record<string, any>;
  gameIds: number[];
  validForDays: number;

  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export interface BonusBulkAssignmentJobCreate {
  casinoFreespinBonusId: number;
  externalCustomerIds: number[];
}

export class CasinoFreespinBonusService {
  private bonusRuleService: BonusRuleService;

  constructor() {
    this.bonusRuleService = new BonusRuleService();
  }

  async list(options: ListCasinoFreespinBonusOptions): Promise<Paginated<CasinoFreespinBonus>> {
    const bonusFilter: FindOptionsWhere<Bonus> = {
      deletedAt: IsNull(),
    };

    if (options.isActive !== undefined) {
      bonusFilter.isActive = options.isActive;
    }

    const [items, total] = await AppDataSource.getRepository(CasinoFreespinBonus).findAndCount({
      relations: ['bonus', 'bonus.rules'],
      where: {
        bonus: bonusFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async listTemplates(
    options: ListCasinoFreespinBonusTemplateOptions,
  ): Promise<Paginated<CasinoFreespinBonusTemplate>> {
    const bonusTemplateFilter: FindOptionsWhere<BonusTemplate> = {};

    if (options.isActive !== undefined) {
      bonusTemplateFilter.deletedAt = options.isActive ? IsNull() : Not(IsNull());
    }

    const [items, total] = await AppDataSource.getRepository(CasinoFreespinBonusTemplate).findAndCount({
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
      where: {
        bonusTemplate: bonusTemplateFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async findById(id: number): Promise<CasinoFreespinBonus | null> {
    return await AppDataSource.getRepository(CasinoFreespinBonus).findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  async findTemplateById(id: number): Promise<CasinoFreespinBonusTemplate | null> {
    return await AppDataSource.getRepository(CasinoFreespinBonusTemplate).findOne({
      where: { id },
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
    });
  }

  async create(data: CasinoFreespinBonusCreate): Promise<CasinoFreespinBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        type: 'freespin',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const freespinBonus = manager.create(CasinoFreespinBonus, {
        vendorId: data.vendorId,
        vendorName: data.vendorName,
        values: data.values,
        gameIds: data.gameIds,
        bonusId: bonus.id,
      });

      return await manager.save(freespinBonus);
    });

    const bonus = await this.findById(result.id);
    if (!bonus) {
      throw new Error('Failed to create casino freespin bonus');
    }

    return bonus;
  }

  async createTemplate(data: CasinoFreespinBonusTemplateCreate): Promise<CasinoFreespinBonusTemplate> {
    const id = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'freespin',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const freespinBonusTemplate = manager.create(CasinoFreespinBonusTemplate, {
        vendorId: data.vendorId,
        vendorName: data.vendorName,
        values: data.values,
        gameIds: data.gameIds,
        validForDays: data.validForDays,
        bonusTemplateId: bonusTemplate.id,
      });

      await manager.save(freespinBonusTemplate);

      return freespinBonusTemplate.id;
    });

    const bonusTemplate = await this.findTemplateById(id);
    if (!bonusTemplate) {
      throw new Error('Failed to create casino freespin bonus template');
    }

    return bonusTemplate;
  }

  async createBulkAssignmentJob(data: BonusBulkAssignmentJobCreate): Promise<BonusBulkAssignmentJob> {
    return await AppDataSource.transaction(async (manager) => {
      const freespinBonusRepository = manager.getRepository(CasinoFreespinBonus);
      const freespinBonus = await freespinBonusRepository.findOne({
        where: { id: data.casinoFreespinBonusId },
        relations: ['bonus'],
      });

      if (!freespinBonus) {
        throw new Error('Freespin bonus not found');
      }

      if (!freespinBonus.bonus.isActive) {
        throw new Error('Freespin bonus is not active');
      }

      const values = {
        base: {
          expiresAt: freespinBonus.bonus.expiresAt,
        },
        vendor: freespinBonus.values,
      };

      const createdJob = manager.create(BonusBulkAssignmentJob, {
        bonusId: freespinBonus.bonus.id,
        bonusValues: values,
        bonusValuesVersion: 1,
        status: 'pending',
      });

      await manager.save(createdJob);

      for (const externalCustomerId of data.externalCustomerIds) {
        const target = manager.create(BonusBulkAssignmentJobTarget, {
          bonusBulkAssignmentJobId: createdJob.id,
          externalCustomerId,
          status: 'pending',
          events: [],
        });

        await manager.save(target);
      }

      const job = await manager.findOne(BonusBulkAssignmentJob, {
        where: { id: createdJob.id },
        relations: ['targets', 'bonus'],
      });
      if (!job) {
        throw new Error('Failed to create bulk assignment job');
      }

      return job;
    });
  }
}
