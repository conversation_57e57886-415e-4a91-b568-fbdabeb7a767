import { WeeklyLossbackBonus } from '@/entities/pronet/WeeklyLossbackBonus';
import { WeeklyLossbackBonusTemplate } from '@/entities/pronet/WeeklyLossbackBonusTemplate';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { AppDataSource } from '@/database/data-source';
import { BonusRuleCreate, BonusRuleService, BonusTemplateRuleCreate } from './bonusRule.service';
import { FindOptionsWhere, IsNull, Not } from 'typeorm';
import { paginate, Paginated } from '../shared';

export interface ListWeeklyLossbackBonusOptions {
  isActive?: boolean | undefined;
  page: number;
  limit: number;
}

export interface ListWeeklyLossbackBonusTemplateOptions {
  isActive?: boolean | undefined;
  page: number;
  limit: number;
}

export interface WeeklyLossbackBonusCreate {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart?: string | null;
  happyHoursEnd?: string | null;
  happyHoursBoostPercentage?: number;
  depositWithDrawDifferenceThreshold?: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface WeeklyLossbackBonusTemplateCreate {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart?: string | null;
  happyHoursEnd?: string | null;
  happyHoursBoostPercentage?: number;
  depositWithDrawDifferenceThreshold?: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  validForDays: number;
  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export class WeeklyLossbackBonusService {
  private bonusRuleService = new BonusRuleService();

  async list(options: ListWeeklyLossbackBonusOptions): Promise<Paginated<WeeklyLossbackBonus>> {
    const repository = AppDataSource.getRepository(WeeklyLossbackBonus);

    const where: FindOptionsWhere<WeeklyLossbackBonus> = {};
    if (options.isActive !== undefined) {
      where.bonus = {
        isActive: options.isActive,
      };
    }

    const queryBuilder = repository
      .createQueryBuilder('weeklyLossbackBonus')
      .leftJoinAndSelect('weeklyLossbackBonus.bonus', 'bonus')
      .leftJoinAndSelect('bonus.rules', 'rules');

    if (Object.keys(where).length > 0) {
      queryBuilder.where(where);
    }

    queryBuilder.orderBy('weeklyLossbackBonus.createdAt', 'DESC');

    // Add pagination
    const offset = (options.page - 1) * options.limit;
    queryBuilder.skip(offset).take(options.limit);

    // Execute query
    const [items, total] = await queryBuilder.getManyAndCount();

    return paginate(items, total, options.page, options.limit);
  }

  async listTemplates(
    options: ListWeeklyLossbackBonusTemplateOptions,
  ): Promise<Paginated<WeeklyLossbackBonusTemplate>> {
    const repository = AppDataSource.getRepository(WeeklyLossbackBonusTemplate);

    const where: FindOptionsWhere<WeeklyLossbackBonusTemplate> = {};
    if (options.isActive !== undefined) {
      where.bonusTemplate = {
        deletedAt: options.isActive ? IsNull() : Not(IsNull()),
      };
    }

    const queryBuilder = repository
      .createQueryBuilder('weeklyLossbackBonusTemplate')
      .leftJoinAndSelect('weeklyLossbackBonusTemplate.bonusTemplate', 'bonusTemplate')
      .leftJoinAndSelect('bonusTemplate.rules', 'rules');

    if (Object.keys(where).length > 0) {
      queryBuilder.where(where);
    }

    queryBuilder.orderBy('weeklyLossbackBonusTemplate.createdAt', 'DESC');

    // Add pagination
    const offset = (options.page - 1) * options.limit;
    queryBuilder.skip(offset).take(options.limit);

    // Execute query
    const [items, total] = await queryBuilder.getManyAndCount();

    return paginate(items, total, options.page, options.limit);
  }

  async findById(id: number): Promise<WeeklyLossbackBonus | null> {
    return await AppDataSource.getRepository(WeeklyLossbackBonus).findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  async findTemplateById(id: number): Promise<WeeklyLossbackBonusTemplate | null> {
    return await AppDataSource.getRepository(WeeklyLossbackBonusTemplate).findOne({
      where: { id },
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
    });
  }

  async create(data: WeeklyLossbackBonusCreate): Promise<WeeklyLossbackBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        type: 'weekly_lossback',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const weeklyLossbackBonus = manager.create(WeeklyLossbackBonus, {
        bonusId: bonus.id,
        maxBalance: data.maxBalance,
        lossbackPercentage: data.lossbackPercentage,
        happyHoursStart: data.happyHoursStart ?? null,
        happyHoursEnd: data.happyHoursEnd ?? null,
        happyHoursBoostPercentage: data.happyHoursBoostPercentage ?? 2.0,
        depositWithDrawDifferenceThreshold: data.depositWithDrawDifferenceThreshold ?? 100.0,
        requestWindowStartSeconds: data.requestWindowStartSeconds,
        requestWindowEndSeconds: data.requestWindowEndSeconds,
      });

      await manager.save(weeklyLossbackBonus);

      return weeklyLossbackBonus.id;
    });

    const bonus = await this.findById(result);
    if (!bonus) {
      throw new Error('Failed to create weekly lossback bonus');
    }

    return bonus;
  }

  async createTemplate(data: WeeklyLossbackBonusTemplateCreate): Promise<WeeklyLossbackBonusTemplate> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'weekly_lossback',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const weeklyLossbackBonusTemplate = manager.create(WeeklyLossbackBonusTemplate, {
        bonusTemplateId: bonusTemplate.id,
        maxBalance: data.maxBalance,
        lossbackPercentage: data.lossbackPercentage,
        happyHoursStart: data.happyHoursStart ?? null,
        happyHoursEnd: data.happyHoursEnd ?? null,
        happyHoursBoostPercentage: data.happyHoursBoostPercentage ?? 2.0,
        depositWithDrawDifferenceThreshold: data.depositWithDrawDifferenceThreshold ?? 100.0,
        requestWindowStartSeconds: data.requestWindowStartSeconds,
        requestWindowEndSeconds: data.requestWindowEndSeconds,
        validForDays: data.validForDays,
      });

      await manager.save(weeklyLossbackBonusTemplate);

      return weeklyLossbackBonusTemplate.id;
    });

    const template = await this.findTemplateById(result);
    if (!template) {
      throw new Error('Failed to create weekly lossback bonus template');
    }

    return template;
  }
}
