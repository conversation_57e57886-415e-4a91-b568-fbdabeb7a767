import { AppDataSource } from '@/database/data-source';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusRule } from '@/entities/pronet/BonusRule';

// Import all available checkers
import { LossbackChecker } from './pipelines/instantLossback';
import { WeeklyLossbackChecker } from './pipelines/weeklyLossback';
import { CashChecker } from './pipelines/cash';
import { TrialChecker } from './pipelines/trial';
import { HappyHoursChecker } from './pipelines/happyHours';
import { FreespinChecker } from './pipelines/freespin';
import { FreebetChecker } from './pipelines/freebet';

// Import all available rule criteria validators
import { validateJoinedAt } from './ruleCriteriaValidators/joinedAt';
import { validateLastDepositDate } from './ruleCriteriaValidators/lastDepositDate';
import { validateIpConflictCount } from './ruleCriteriaValidators/ipConflictCount';
import { validateTotalDeposit } from './ruleCriteriaValidators/totalDeposit';
import { validateTotalWithdraw } from './ruleCriteriaValidators/totalWithdraw';
import { validateKycVerification } from './ruleCriteriaValidators/kycVerification';

export interface BonusRuleCheckResult {
  isEligible: boolean;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface BonusRuleChecker {
  checkEligibility(userId: number, bonus: Bonus): Promise<BonusRuleCheckResult>;
}

export class BonusRuleCheckerService {
  private checkers: Map<string, BonusRuleChecker> = new Map();
  private validators: Map<string, Function> = new Map();

  constructor() {
    this.checkers.set('lossback', new LossbackChecker());
    this.checkers.set('weekly_lossback', new WeeklyLossbackChecker());
    this.checkers.set('cash', new CashChecker());
    this.checkers.set('trial', new TrialChecker());
    this.checkers.set('happyHours', new HappyHoursChecker());
    this.checkers.set('freespin', new FreespinChecker());
    this.checkers.set('freebet', new FreebetChecker());

    this.validators.set('joinedAt', validateJoinedAt);
    this.validators.set('lastDepositDate', validateLastDepositDate);
    this.validators.set('ipConflictCount', validateIpConflictCount);
    this.validators.set('totalDeposit', validateTotalDeposit);
    this.validators.set('totalWithdraw', validateTotalWithdraw);
    this.validators.set('kycVerification', validateKycVerification);
  }

  async checkBonusEligibility(userId: number, bonusId: number): Promise<BonusRuleCheckResult> {
    console.log(`🎯 Starting bonus eligibility check for user ${userId} and bonus ${bonusId}`);

    const bonusRepository = AppDataSource.getRepository(Bonus);
    const bonus = await bonusRepository.findOne({
      where: {
        id: bonusId,
        isActive: true
      }
    });

    if (!bonus) {
      throw new Error('Bonus not found');
    }

    console.log(`📋 Found bonus: ${bonus.name} (type: ${bonus.type})`);

    const checker = this.checkers.get(bonus.type);

    if (!checker) {
      throw new Error(`No checker registered for bonus type: ${bonus.type}`);
    }

    // Retrieve all bonus rules for the bonus
    const bonusRulesRepository = AppDataSource.getRepository(BonusRule);
    const bonusRules = await bonusRulesRepository.find({
      where: {
        bonusId: bonus.id
      },
      order: {
        createdAt: 'ASC'
      }
    });

    console.log(`📝 Found ${bonusRules.length} rule criteria to validate`);

    // Apply correct validators for each rule criteria - return immediately on first failure
    for (const rule of bonusRules) {
      console.log(`🔍 Validating rule: ${rule.criterium} ${rule.operator} ${rule.firstOperand}`);

      const validator = this.validators.get(rule.criterium);

      if (!validator) {
        throw new Error(`No validator registered for rule criterium: ${rule.criterium}`);
      }

      const isValid = await validator(rule.firstOperand, rule.secondOperand, rule.operator, userId.toString(), rule.startsAt, rule.endsAt);

      if (!isValid) {
        // Return immediately upon first ineligibility
        return {
          isEligible: false,
          reason: `Failed rule: ${rule.criterium} ${rule.operator} ${rule.firstOperand}`,
          metadata: {
            failedRule: rule,
            bonusRules
          }
        };
      }
    }

    // All rules passed - now run the specific bonus type checker
    console.log(`✅ All rule criteria passed for user ${userId} and bonus ${bonusId}. Running specific ${bonus.type} checker...`);

    const checkerResult = await checker.checkEligibility(userId, bonus);

    // Merge metadata from rule validation and specific checker
    const result: BonusRuleCheckResult = {
      isEligible: checkerResult.isEligible,
      metadata: {
        bonusRules,
        checkerMetadata: checkerResult.metadata,
        passedAllRuleCriteria: true
      }
    };

    // Only add reason if it exists
    if (checkerResult.reason) {
      result.reason = checkerResult.reason;
    }

    console.log(`🏁 Final eligibility result for user ${userId} and bonus ${bonusId}: ${result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'}`);
    if (result.reason) {
      console.log(`📝 Reason: ${result.reason}`);
    }

    return result;
  }

  getSupportedBonusTypes(): string[] {
    return Array.from(this.checkers.keys());
  }

  registerChecker(bonusType: string, checker: BonusRuleChecker): void {
    this.checkers.set(bonusType, checker);
  }
}