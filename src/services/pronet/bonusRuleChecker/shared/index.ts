/**
 * Shared types and utilities for Bonus Rule Checker pipelines
 */

// Re-export main interfaces from core service
export { BonusR<PERSON><PERSON><PERSON><PERSON>, BonusRuleCheckResult } from '../bonusRuleChecker.service';
import { Bonus } from '@/entities/pronet/Bonus';

/**
 * Common bonus rule validation context
 * Contains shared data that all pipeline checkers might need
 */
export interface BonusValidationContext {
  userId: number;
  bonus: Bonus;
  bonusType: string;
  currentTime: Date;
  userTimezone?: string;
}

/**
 * Standard bonus rule criteria types
 * Based on the BonusRule entity criterium field
 */
export enum BonusRuleCriterium {
  // Implemented criteria
  JOINED_AT = 'joinedAt',
  LAST_DEPOSIT_DATE = 'lastDepositDate',
  IP_CONFLICT_COUNT = 'ipConflictCount',
  TOTAL_DEPOSIT = 'totalDeposit',
  TOTAL_WITHDRAW = 'totalWithdraw',
  KYC_VERIFICATION = 'kycVerification',
}

/**
 * Standard comparison operators
 * Based on the BonusRule entity operator field
 */
export enum BonusRuleOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  LESS_THAN = 'lt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN_OR_EQUAL = 'lte',
  BETWEEN = 'btw',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  IN = 'in',
  NOT_IN = 'not_in'
}

/**
 * Parsed bonus rule for easier validation
 */
export interface ParsedBonusRule {
  id: number;
  criterium: BonusRuleCriterium;
  operator: BonusRuleOperator;
  firstOperand: string;
  secondOperand: string;
  startsAt: Date;
  endsAt: Date;
}

/**
 * User data structure for rule validation
 * Contains all user information needed for bonus rule checking
 */
export interface UserValidationData {
  id: number;
  externalId: number;
  username: string;
  email?: string;
  phoneNumber?: string;
  country?: string;
  vipRank?: number;
  kycLevel?: number;
  referrer?: string;
  joinedAt: Date;
  lastLogin?: Date;
  
  // Financial data
  totalDeposit: number;
  totalWithdraw: number;
  currentBalance: number;
  
  // Activity data
  totalBets: number;
  casinoLosses: number;
  sportsBets: number;
  lastDepositDate?: Date;
}

/**
 * Base class for bonus rule validation utilities
 */
export class BonusRuleValidator {
  /**
   * Validate a single rule against user data
   * 
   * @param rule - Parsed bonus rule to validate
   * @param userData - User data for validation
   * @param context - Validation context
   * @returns boolean - Whether the rule passes
   */
  static validateRule(rule: ParsedBonusRule, userData: UserValidationData, context: BonusValidationContext): boolean {
    // TODO: Implement rule validation logic
    // TODO: 1. Check if current time is within rule's date range
    // TODO: 2. Extract the value from userData based on criterium
    // TODO: 3. Apply the operator comparison with operands
    // TODO: 4. Handle different data types (string, number, date)
    // TODO: 5. Handle special cases for each criterium type
    
    console.log(`Validating rule ${rule.id} for user ${userData.id}`);
    
    // Placeholder - to be implemented by user
    return true;
  }

  /**
   * Check if current time is within rule's active period
   * 
   * @param rule - Bonus rule with date range
   * @param currentTime - Current timestamp
   * @returns boolean - Whether rule is currently active
   */
  static isRuleActive(rule: ParsedBonusRule, currentTime: Date): boolean {
    return currentTime >= rule.startsAt && currentTime <= rule.endsAt;
  }

  /**
   * Parse string value to appropriate type based on criterium
   * 
   * @param value - String value to parse
   * @param criterium - Rule criterium type
   * @returns any - Parsed value in appropriate type
   */
  static parseValue(value: string, criterium: BonusRuleCriterium): any {
    // TODO: Implement value parsing based on criterium type
    // TODO: Handle numbers, dates, strings, arrays, etc.
    
    switch (criterium) {
      case BonusRuleCriterium.IP_CONFLICT_COUNT:
        return parseInt(value, 10);

      case BonusRuleCriterium.TOTAL_DEPOSIT:
        return parseFloat(value);

      case BonusRuleCriterium.JOINED_AT:
      case BonusRuleCriterium.LAST_DEPOSIT_DATE:
        return new Date(value);

      default:
        return value;
    }
  }
}
