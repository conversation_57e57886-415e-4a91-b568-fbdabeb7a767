import { BonusRule<PERSON><PERSON><PERSON>, BonusRuleCheckResult } from '../bonusRuleChecker.service';
import { Bonus } from '@/entities/pronet/Bonus';

export class Happy<PERSON><PERSON><PERSON>hecker implements BonusRuleChecker {
  async checkEligibility(userId: number, bonus: Bonus): Promise<BonusRuleCheckResult> {
    console.log(`Checking happy hours bonus eligibility for user ${userId}, bonus ${bonus.id}`);

    const bonusType = bonus.type;

    if (bonusType !== 'happyHours') {
      throw new Error('Bonus type mismatch');
    }

    // Happy hours bonus has no additional eligibility checks beyond the standard bonus rules
    // All checks are handled by the base BonusRuleCheckerService
    // This is a blank checker pipeline as requested
    
    return {
      isEligible: true,
      metadata: {
        bonusType: 'happyHours',
        userId,
        bonusId: bonus.id,
        checkedAt: new Date().toISOString(),
      }
    };
  }
}
