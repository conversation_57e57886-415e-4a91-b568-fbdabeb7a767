import { BonusRule<PERSON><PERSON><PERSON>, BonusRuleCheckResult } from '../bonusRuleChecker.service';
import { Bonus } from '@/entities/pronet/Bonus';
import { PgDagurService } from '@/services/thirdparty/pg-dagur';

export class Trial<PERSON>hecker implements BonusRuleChecker {
  async checkEligibility(userId: number, bonus: Bonus): Promise<BonusRuleCheckResult> {
    console.log(`Checking trial bonus eligibility for user ${userId}, bonus ${bonus.id}`);

    const bonusType = bonus.type;

    if (bonusType !== 'trial') {
      throw new Error('Bonus type mismatch');
    }

    // Retrieve user statistics from PG Dagur to check lifetimeTurnover
    let userStatistics: any = null;
    try {
      console.log(`Fetching user statistics for customer ${userId}...`);
      userStatistics = await PgDagurService.getCustomerStatistics(userId.toString());
      console.log(`✅ Successfully retrieved user statistics for customer ${userId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve user statistics for customer ${userId}:`, error);
      // For trial bonuses, we need statistics to check eligibility
      return {
        isEligible: false,
        reason: 'Unable to retrieve user statistics for trial bonus eligibility check',
        metadata: {
          bonusType: 'trial',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          failureReason: 'statistics_unavailable'
        }
      };
    }

    // Extract lifetimeTurnover from user statistics
    const lifetimeTurnover = userStatistics?.statistics?.gameStatistics?.lifetimeTurnover;

    console.log(`User ${userId} lifetimeTurnover: ${lifetimeTurnover}`);

    // Check if lifetimeTurnover is 0 (eligible for trial bonus)
    if (lifetimeTurnover !== 0) {
      return {
        isEligible: false,
        reason: `Customer has lifetime turnover of ${lifetimeTurnover} TRY, not eligible for trial bonus`,
        metadata: {
          bonusType: 'trial',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          lifetimeTurnover: lifetimeTurnover,
          userStatistics: userStatistics || null,
          failureReason: 'has_lifetime_turnover'
        }
      };
    }

    // User is eligible - lifetimeTurnover is 0
    return {
      isEligible: true,
      metadata: {
        bonusType: 'trial',
        userId,
        bonusId: bonus.id,
        checkedAt: new Date().toISOString(),
        lifetimeTurnover: lifetimeTurnover,
        hasLifetimeTurnover: lifetimeTurnover !== 0,
        userStatistics: userStatistics || null
      }
    };
  }
}
