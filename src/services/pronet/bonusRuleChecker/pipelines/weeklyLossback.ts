import { AppDataSource } from '@/database/data-source';
import { Bonus<PERSON><PERSON><PERSON><PERSON><PERSON>, BonusRuleCheckResult } from '../bonusRuleChecker.service';
import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { Bonus } from '@/entities/pronet/Bonus';
import { WeeklyLossbackBonus } from '@/entities/pronet/WeeklyLossbackBonus';

export class Weekly<PERSON>ossbackChecker implements BonusRuleChecker {
  async checkEligibility(userId: number, bonus: Bonus): Promise<BonusRuleCheckResult> {
    console.log(`Checking weekly lossback eligibility for user ${userId}, bonus ${bonus.id}`);

    const bonusType = bonus.type;

    if (bonusType !== 'weekly_lossback') {
      throw new Error('Bonus type mismatch');
    }

    // Retrieve weekly lossback bonus details
    const weeklyLossbackBonusRepository = AppDataSource.getRepository(WeeklyLossbackBonus);
    const weeklyLossbackBonus = await weeklyLossbackBonusRepository.findOne({
      where: {
        bonusId: bonus.id
      }
    });

    if (!weeklyLossbackBonus) {
      throw new Error('Weekly lossback bonus not found');
    }

    // Initialize eligibility tracking variables
    let isWithinHappyHours = false;
    let isWithinRequestWindow = false;

    // Check if the request was made during the allowed weekly window
    const currentTime = new Date();
    isWithinRequestWindow = this.isWithinWeeklyRequestWindow(weeklyLossbackBonus, currentTime);

    if (!isWithinRequestWindow) {
      return {
        isEligible: false,
        reason: 'Request made outside of allowed weekly window',
        metadata: {
          bonusType: 'weekly_lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          isWithinRequestWindow: false,
          requestWindowStartSeconds: weeklyLossbackBonus.requestWindowStartSeconds,
          requestWindowEndSeconds: weeklyLossbackBonus.requestWindowEndSeconds,
          currentWeekSeconds: this.getCurrentWeekSeconds(currentTime),
          secondsFromPreviousWeekStart: this.getSecondsFromPreviousWeekStart(currentTime),
          failureReason: 'outside_request_window'
        }
      };
    }

    // Retrieve user statistics from PG Dagur
    let userStatistics: any = null;
    try {
      console.log(`Fetching user statistics for customer ${userId}...`);
      userStatistics = await PgDagurService.getCustomerStatistics(userId.toString());
      console.log(`✅ Successfully retrieved user statistics for customer ${userId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve user statistics for customer ${userId}:`, error);
      // Continue without statistics - don't fail the entire eligibility check
    }

    // Retrieve customer summary data from PG Dagur
    let customerSummary: any = null;
    try {
      console.log(`Fetching customer summary for customer ${userId}...`);
      customerSummary = await PgDagurService.getCustomerSummary(userId.toString());
      console.log(`✅ Successfully retrieved customer summary for customer ${userId}`);
      console.log(`👤 Customer info:`, customerSummary.customer);
      console.log(`📋 Customer details:`, customerSummary.customerDetail);
      console.log(`💰 Customer balances:`, customerSummary.balances);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve customer summary for customer ${userId}:`, error);
      // Continue without summary - don't fail the entire eligibility check
    }

    const username = customerSummary?.customer?.username;

    // If customer balance exceeds threshold, they are not eligible - return immediately
    if (customerSummary?.calculatedData?.sportsFullBalance > weeklyLossbackBonus.maxBalance) {
      return {
        isEligible: false,
        reason: `Customer has more than ${weeklyLossbackBonus.maxBalance} TRY in their sports full balance`,
        metadata: {
          bonusType: 'weekly_lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          isWithinRequestWindow: true,
          username: username || null,
          customerSummary: customerSummary || null,
          currentWeekSeconds: this.getCurrentWeekSeconds(currentTime),
          secondsFromPreviousWeekStart: this.getSecondsFromPreviousWeekStart(currentTime),
          requestWindowStartSeconds: weeklyLossbackBonus.requestWindowStartSeconds,
          requestWindowEndSeconds: weeklyLossbackBonus.requestWindowEndSeconds,
          failureReason: 'balance_exceeded'
        }
      };
    }

    // Get transactions for the previous week (week A - the week in question)
    const { startDate: weekStartDate, endDate: weekEndDate } = this.getPreviousWeekDateRange(currentTime);

    let weeklyTransactions: any = null;
    try {
      console.log(`Fetching weekly transactions for username: ${username} from ${weekStartDate.toISOString()} to ${weekEndDate.toISOString()}...`);

      weeklyTransactions = await PgDagurService.listTransactions({
        startDate: weekStartDate,
        endDate: weekEndDate,
        status: ['C'], // Completed transactions
        username: username,
        page: 1,
        limit: 1000, // Higher limit for weekly data
        loadSubtotals: false, // Don't need subtotals for bonus rule checking
      });

    } catch (error) {
      console.warn(`⚠️ Failed to retrieve weekly transactions for ${username}:`, error);
      return {
        isEligible: false,
        reason: 'Unable to retrieve weekly transaction data for eligibility check',
        metadata: {
          bonusType: 'weekly_lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          isWithinRequestWindow: true,
          username: username || null,
          weekStartDate: weekStartDate.toISOString(),
          weekEndDate: weekEndDate.toISOString(),
          currentWeekSeconds: this.getCurrentWeekSeconds(currentTime),
          secondsFromPreviousWeekStart: this.getSecondsFromPreviousWeekStart(currentTime),
          requestWindowStartSeconds: weeklyLossbackBonus.requestWindowStartSeconds,
          requestWindowEndSeconds: weeklyLossbackBonus.requestWindowEndSeconds,
          failureReason: 'no_weekly_transactions'
        }
      };
    }

    // Calculate weekly deposit-withdrawal difference from actual transactions
    const weeklyDepositAmount = weeklyTransactions?.transactions?.items
      ?.filter((t: any) => t.masterCode === 'D')
      .reduce((sum: number, t: any) => sum + (t.amount || 0), 0) || 0;
    const weeklyWithdrawAmount = weeklyTransactions?.transactions?.items
      ?.filter((t: any) => t.masterCode === 'W')
      .reduce((sum: number, t: any) => sum + (t.amount || 0), 0) || 0;
    const weeklyDepositWithdrawDiff = weeklyDepositAmount - weeklyWithdrawAmount;

    // If the weekly difference is less than the threshold, they are not eligible
    if (weeklyDepositWithdrawDiff < weeklyLossbackBonus.depositWithDrawDifferenceThreshold) {
      return {
        isEligible: false,
        reason: `Customer has less than ${weeklyLossbackBonus.depositWithDrawDifferenceThreshold} TRY difference between weekly deposits and withdrawals`,
        metadata: {
          bonusType: 'weekly_lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          isWithinRequestWindow: true,
          username: username || null,
          weeklyDepositWithdrawDiff: weeklyDepositWithdrawDiff,
          weeklyDepositAmount: weeklyDepositAmount,
          weeklyWithdrawAmount: weeklyWithdrawAmount,
          weeklyTransactions: weeklyTransactions?.transactions?.items || null,
          weekStartDate: weekStartDate.toISOString(),
          weekEndDate: weekEndDate.toISOString(),
          customerSummary: customerSummary || null,
          currentWeekSeconds: this.getCurrentWeekSeconds(currentTime),
          secondsFromPreviousWeekStart: this.getSecondsFromPreviousWeekStart(currentTime),
          requestWindowStartSeconds: weeklyLossbackBonus.requestWindowStartSeconds,
          requestWindowEndSeconds: weeklyLossbackBonus.requestWindowEndSeconds,
          failureReason: 'insufficient_weekly_deposit_withdraw_diff'
        }
      };
    }

    // Check if the request was made during happy hours
    if (this.requestedDuringHappyHours(weeklyLossbackBonus, currentTime)) {
      isWithinHappyHours = true;
    }

    // All checks passed - user is eligible
    return {
      isEligible: true,
      metadata: {
        bonusType: 'weekly_lossback',
        userId,
        bonusId: bonus.id,
        checkedAt: new Date().toISOString(),
        isWithinHappyHours: isWithinHappyHours,
        isWithinRequestWindow: true,
        username: username || null,
        weeklyDepositWithdrawDiff: weeklyDepositWithdrawDiff,
        weeklyDepositAmount: weeklyDepositAmount,
        weeklyWithdrawAmount: weeklyWithdrawAmount,
        weeklyTransactions: weeklyTransactions?.transactions?.items || null,
        weekStartDate: weekStartDate.toISOString(),
        weekEndDate: weekEndDate.toISOString(),
        userStatistics: userStatistics || null,
        customerSummary: customerSummary || null,
        latestDeposit: { amount: weeklyDepositAmount }, // For compatibility with bonus claim logic
        currentWeekSeconds: this.getCurrentWeekSeconds(currentTime),
        secondsFromPreviousWeekStart: this.getSecondsFromPreviousWeekStart(currentTime),
        requestWindowStartSeconds: weeklyLossbackBonus.requestWindowStartSeconds,
        requestWindowEndSeconds: weeklyLossbackBonus.requestWindowEndSeconds,
      }
    };
  }

  /**
   * Check if the request was made during the allowed weekly window
   * The request window is defined in seconds from the start of the current week (Monday 00:00)
   */
  private isWithinWeeklyRequestWindow(bonus: WeeklyLossbackBonus, currentTime: Date): boolean {
    const currentWeekSeconds = this.getCurrentWeekSeconds(currentTime);

    // Ensure all values are numbers to avoid any type comparison issues
    const startSeconds = Number(bonus.requestWindowStartSeconds);
    const endSeconds = Number(bonus.requestWindowEndSeconds);
    const currentSeconds = Number(currentWeekSeconds);

    return currentSeconds >= startSeconds && currentSeconds <= endSeconds;
  }

  /**
   * Get the date range for the previous week (Monday 00:00 to Sunday 23:59:59)
   * This represents "week A" - the week in question for which the lossback is being requested
   */
  private getPreviousWeekDateRange(currentTime: Date): { startDate: Date; endDate: Date } {
    // Get the start of the previous week (Monday 00:00)
    const startOfPreviousWeek = new Date(currentTime);
    const dayOfWeek = startOfPreviousWeek.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert Sunday to 6, others to dayOfWeek - 1

    // Go back to the start of current week, then subtract 7 more days to get to previous week
    startOfPreviousWeek.setDate(startOfPreviousWeek.getDate() - daysFromMonday - 7);
    startOfPreviousWeek.setHours(0, 0, 0, 0);

    // Get the end of the previous week (Sunday 23:59:59)
    const endOfPreviousWeek = new Date(startOfPreviousWeek);
    endOfPreviousWeek.setDate(endOfPreviousWeek.getDate() + 6); // Add 6 days to get to Sunday
    endOfPreviousWeek.setHours(23, 59, 59, 999);

    return {
      startDate: startOfPreviousWeek,
      endDate: endOfPreviousWeek
    };
  }

  /**
   * Get current time in seconds from the start of the previous week (Monday 00:00)
   * This method is kept for backward compatibility and metadata reporting
   */
  private getSecondsFromPreviousWeekStart(currentTime: Date): number {
    // Get the start of the previous week (Monday 00:00 of the week before current week)
    const startOfPreviousWeek = new Date(currentTime);
    const dayOfWeek = startOfPreviousWeek.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert Sunday to 6, others to dayOfWeek - 1

    // Go back to the start of current week, then subtract 7 more days to get to previous week
    startOfPreviousWeek.setDate(startOfPreviousWeek.getDate() - daysFromMonday - 7);
    startOfPreviousWeek.setHours(0, 0, 0, 0);

    // Calculate seconds from start of previous week
    const diffInMs = currentTime.getTime() - startOfPreviousWeek.getTime();
    return Math.floor(diffInMs / 1000);
  }

  /**
   * Get current time in seconds from the start of the current week (Monday 00:00)
   * This is used to check if we're in the request window defined for the current week
   */
  private getCurrentWeekSeconds(currentTime: Date): number {
    // Get the start of the current week (Monday 00:00)
    const startOfWeek = new Date(currentTime);
    const dayOfWeek = startOfWeek.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert Sunday to 6, others to dayOfWeek - 1

    startOfWeek.setDate(startOfWeek.getDate() - daysFromMonday);
    startOfWeek.setHours(0, 0, 0, 0);

    // Calculate seconds from start of week
    const diffInMs = currentTime.getTime() - startOfWeek.getTime();
    return Math.floor(diffInMs / 1000);
  }

  /**
   * Check if the request was made during happy hours
   */
  private requestedDuringHappyHours(bonus: WeeklyLossbackBonus, currentTime: Date): boolean {
    const happyHoursStart = bonus.happyHoursStart;
    const happyHoursEnd = bonus.happyHoursEnd;

    // If happy hours are not defined, return false
    if (!happyHoursStart || !happyHoursEnd) {
      return false;
    }

    // Convert time strings to Date objects for comparison
    // Time strings are in format "HH:MM:SS" (e.g., "09:30:00", "18:00:00")
    const today = new Date(currentTime);
    today.setHours(0, 0, 0, 0); // Reset to start of day

    const startTime = new Date(today);
    const [startHour, startMinute, startSecond] = happyHoursStart.split(':').map(Number);
    startTime.setHours(startHour || 0, startMinute || 0, startSecond || 0);

    const endTime = new Date(today);
    const [endHour, endMinute, endSecond] = happyHoursEnd.split(':').map(Number);
    endTime.setHours(endHour || 0, endMinute || 0, endSecond || 0);

    // Handle case where happy hours span midnight (e.g., 22:00 to 06:00)
    if (endTime <= startTime) {
      // Happy hours span midnight
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      // Normal case: happy hours within the same day
      return currentTime >= startTime && currentTime <= endTime;
    }
  }
}