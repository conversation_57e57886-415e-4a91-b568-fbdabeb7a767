import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { BonusRuleOperator } from '../shared';

export async function validateJoinedAt(
    firstOperand: string | null,
    secondOperand: string | null,
    operator: string,
    customerId?: string,
    startsAt?: Date,
    endsAt?: Date,
): Promise<boolean> {
    try {
        console.log(`🔍 Validating JoinedAt criteria: ${firstOperand} ${operator} ${secondOperand} for customer ${customerId}`);

        // Handle missing customer ID
        if (!customerId) {
            console.warn('⚠️ JoinedAt validation: Customer ID is required');
            return false;
        }

        // Handle nullable first operand (always required)
        if (!firstOperand) {
            console.warn('⚠️ JoinedAt validation: Missing first operand (date)');
            return false;
        }

        // Parse first operand as date (always required)
        const firstDate = new Date(firstOperand.trim());
        if (isNaN(firstDate.getTime())) {
            console.warn('⚠️ JoinedAt validation: Invalid first operand date format');
            return false;
        }

        // For btw operator, validate second operand
        let secondDate: Date | null = null;
        if (operator === BonusRuleOperator.BETWEEN) {
            if (!secondOperand) {
                console.warn('⚠️ JoinedAt validation: Missing second operand for between operator');
                return false;
            }
            secondDate = new Date(secondOperand.trim());
            if (isNaN(secondDate.getTime())) {
                console.warn('⚠️ JoinedAt validation: Invalid second operand date format');
                return false;
            }
        }

        // Retrieve customer summary data from PG Dagur
        console.log(`📋 Fetching customer summary for customer ${customerId}...`);
        const customerSummary = await PgDagurService.getCustomerSummary(customerId);

        // Extract register date from customer details
        const registerDateStr = customerSummary.customerDetail?.registerDate;
        if (!registerDateStr) {
            console.warn('⚠️ JoinedAt validation: No register date found in customer summary');
            return false;
        }

        // Parse the register date (expected format from PG Dagur: "YYYY-MM-DD")
        const registerDate = new Date(registerDateStr);
        if (isNaN(registerDate.getTime())) {
            console.warn('⚠️ JoinedAt validation: Invalid register date format from PG Dagur');
            return false;
        }

        console.log(`📅 Customer ${customerId} registered on: ${registerDate.toISOString()}`);
        console.log(`📅 First operand date: ${firstDate.toISOString()}`);
        if (secondDate) {
            console.log(`📅 Second operand date: ${secondDate.toISOString()}`);
        }

        // Perform comparison based on operator
        let result = false;
        switch (operator) {
            case BonusRuleOperator.GREATER_THAN:
                result = registerDate > firstDate;
                break;
            case BonusRuleOperator.LESS_THAN:
                result = registerDate < firstDate;
                break;
            case BonusRuleOperator.GREATER_THAN_OR_EQUAL:
                result = registerDate >= firstDate;
                break;
            case BonusRuleOperator.LESS_THAN_OR_EQUAL:
                result = registerDate <= firstDate;
                break;
            case BonusRuleOperator.EQUALS:
                // Compare dates by day (ignore time)
                result = registerDate.toDateString() === firstDate.toDateString();
                break;
            case BonusRuleOperator.NOT_EQUALS:
                // Compare dates by day (ignore time)
                result = registerDate.toDateString() !== firstDate.toDateString();
                break;
            case BonusRuleOperator.BETWEEN:
                // Handle between operator - firstOperand = start date, secondOperand = end date
                if (!secondDate) {
                    console.warn('⚠️ JoinedAt validation: Between operator requires second operand');
                    return false;
                }

                result = registerDate >= firstDate && registerDate <= secondDate;
                console.log(`📅 Between comparison: ${firstDate.toISOString()} <= ${registerDate.toISOString()} <= ${secondDate.toISOString()} = ${result}`);
                break;
            default:
                console.warn(`⚠️ JoinedAt validation: Unsupported operator: ${operator}`);
                return false;
        }

        console.log(`✅ JoinedAt validation result: ${registerDate.toISOString()} ${operator} ${firstDate.toISOString()}${secondDate ? ` and ${secondDate.toISOString()}` : ''} = ${result}`);
        return result;

    } catch (error) {
        console.error('❌ Error in JoinedAt validation:', error);
        return false;
    }
}
