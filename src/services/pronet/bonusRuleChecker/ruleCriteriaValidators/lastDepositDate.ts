import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { BonusRuleOperator } from '../shared';

export async function validateLastDepositDate(
    firstOperand: string | null,
    secondOperand: string | null,
    operator: string,
    customerId?: string,
    startsAt?: Date,
    endsAt?: Date,
): Promise<boolean> {
    try {
        console.log(`🔍 Validating LastDepositDate criteria: ${firstOperand} ${operator} ${secondOperand} for customer ${customerId}`);

        // Handle missing customer ID
        if (!customerId) {
            console.warn('⚠️ LastDepositDate validation: Customer ID is required');
            return false;
        }

        // Handle nullable first operand (always required)
        if (!firstOperand) {
            console.warn('⚠️ LastDepositDate validation: Missing first operand (date)');
            return false;
        }

        // Parse first operand as date (always required)
        const firstDate = new Date(firstOperand.trim());
        if (isNaN(firstDate.getTime())) {
            console.warn('⚠️ LastDepositDate validation: Invalid first operand date format');
            return false;
        }

        // For btw operator, validate second operand
        let secondDate: Date | null = null;
        if (operator === BonusRuleOperator.BETWEEN) {
            if (!secondOperand) {
                console.warn('⚠️ LastDepositDate validation: Missing second operand for between operator');
                return false;
            }
            secondDate = new Date(secondOperand.trim());
            if (isNaN(secondDate.getTime())) {
                console.warn('⚠️ LastDepositDate validation: Invalid second operand date format');
                return false;
            }
        }

        // Retrieve customer statistics data from PG Dagur
        console.log(`📊 Fetching customer statistics for customer ${customerId}...`);
        const userStatistics = await PgDagurService.getCustomerStatistics(customerId);

        // Extract last deposit date from monthly statistics
        const lastDepositDateStr = userStatistics.statistics?.monthlyStatistics?.lastDepositDate;
        if (!lastDepositDateStr) {
            console.warn('⚠️ LastDepositDate validation: No last deposit date found in customer statistics');
            return false;
        }

        // Parse the last deposit date
        const lastDepositDate = new Date(lastDepositDateStr);
        if (isNaN(lastDepositDate.getTime())) {
            console.warn('⚠️ LastDepositDate validation: Invalid last deposit date format from PG Dagur');
            return false;
        }

        console.log(`📅 Customer ${customerId} last deposit date: ${lastDepositDate.toISOString()}`);
        console.log(`📅 First operand date: ${firstDate.toISOString()}`);
        if (secondDate) {
            console.log(`📅 Second operand date: ${secondDate.toISOString()}`);
        }

        // Perform comparison based on operator
        let result = false;
        switch (operator) {
            case BonusRuleOperator.GREATER_THAN:
                result = lastDepositDate > firstDate;
                break;
            case BonusRuleOperator.LESS_THAN:
                result = lastDepositDate < firstDate;
                break;
            case BonusRuleOperator.GREATER_THAN_OR_EQUAL:
                result = lastDepositDate >= firstDate;
                break;
            case BonusRuleOperator.LESS_THAN_OR_EQUAL:
                result = lastDepositDate <= firstDate;
                break;
            case BonusRuleOperator.EQUALS:
                // Compare dates by day (ignore time)
                result = lastDepositDate.toDateString() === firstDate.toDateString();
                break;
            case BonusRuleOperator.NOT_EQUALS:
                // Compare dates by day (ignore time)
                result = lastDepositDate.toDateString() !== firstDate.toDateString();
                break;
            case BonusRuleOperator.BETWEEN:
                // Handle between operator - firstOperand = start date, secondOperand = end date
                if (!secondDate) {
                    console.warn('⚠️ LastDepositDate validation: Between operator requires second operand');
                    return false;
                }

                result = lastDepositDate >= firstDate && lastDepositDate <= secondDate;
                console.log(`📅 Between comparison: ${firstDate.toISOString()} <= ${lastDepositDate.toISOString()} <= ${secondDate.toISOString()} = ${result}`);
                break;
            default:
                console.warn(`⚠️ LastDepositDate validation: Unsupported operator: ${operator}`);
                return false;
        }

        console.log(`✅ LastDepositDate validation result: ${lastDepositDate.toISOString()} ${operator} ${firstDate.toISOString()}${secondDate ? ` and ${secondDate.toISOString()}` : ''} = ${result}`);
        return result;

    } catch (error) {
        console.error('❌ Error in LastDepositDate validation:', error);
        return false;
    }
}
