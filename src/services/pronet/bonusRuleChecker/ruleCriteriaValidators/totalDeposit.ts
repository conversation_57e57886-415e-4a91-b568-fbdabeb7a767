import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { BonusRuleOperator } from '../shared';

export async function validateTotalDeposit(
  firstOperand: string | null,
  secondOperand: string | null,
  operator: string,
  customerId?: string,
  startsAt?: Date,
  endsAt?: Date,
): Promise<boolean> {
  try {
    console.log(
      `🔍 Validating TotalDeposit criteria: ${firstOperand} ${operator} ${secondOperand} for customer ${customerId} between ${startsAt?.toISOString()} and ${endsAt?.toISOString()}`,
    );

    // Handle missing customer ID
    if (!customerId) {
      console.warn('⚠️ TotalDeposit validation: Customer ID is required');
      return false;
    }

    // Handle missing date range
    if (!startsAt || !endsAt) {
      console.warn('⚠️ TotalDeposit validation: startsAt and endsAt dates are required');
      return false;
    }

    // Handle nullable first operand (always required)
    if (!firstOperand) {
      console.warn('⚠️ TotalDeposit validation: Missing first operand (amount)');
      return false;
    }

    // Parse first operand as float (always required)
    const firstAmount = parseFloat(firstOperand.trim());
    if (isNaN(firstAmount)) {
      console.warn('⚠️ TotalDeposit validation: Invalid first operand float format');
      return false;
    }

    // For btw operator, validate second operand
    let secondAmount: number | null = null;
    if (operator === BonusRuleOperator.BETWEEN) {
      if (!secondOperand) {
        console.warn('⚠️ TotalDeposit validation: Missing second operand for between operator');
        return false;
      }
      secondAmount = parseFloat(secondOperand.trim());
      if (isNaN(secondAmount)) {
        console.warn('⚠️ TotalDeposit validation: Invalid second operand float format');
        return false;
      }
    }

    // Retrieve customer summary to get username
    let customerSummary: any = null;
    try {
      console.log(`📋 Fetching customer summary for customer ${customerId}...`);
      customerSummary = await PgDagurService.getCustomerSummary(customerId);
      console.log(`✅ Successfully retrieved customer summary for customer ${customerId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve customer summary for customer ${customerId}:`, error);
      return false; // Can't proceed without username
    }

    const username = customerSummary?.customer?.username;
    if (!username) {
      console.warn('⚠️ TotalDeposit validation: No username found in customer summary');
      return false;
    }

    // Retrieve customer transactions from PG Dagur
    console.log(
      `📋 Fetching customer transactions for username ${username} between ${startsAt.toISOString()} and ${endsAt.toISOString()}...`,
    );
    const transactionsData = await PgDagurService.listTransactions({
      startDate: startsAt,
      endDate: endsAt,
      status: ['C'], // Only confirmed transactions
      masterType: 'D', // Only deposits
      username: username, // Use the username from customer summary
      page: 1,
      limit: 1000, // Get a large number to ensure we capture all deposits
      loadSubtotals: false, // Don't need subtotals for rule validation
    });

    // Calculate total deposit amount
    const totalDepositAmount =
      transactionsData.transactions?.items
        ?.filter((t: any) => t.masterCode === 'D')
        ?.reduce((sum: number, t: any) => sum + (parseFloat(t.amount) || 0), 0) || 0;

    console.log(
      `💰 Customer ${customerId} has total deposits of ${totalDepositAmount} between ${startsAt.toISOString()} and ${endsAt.toISOString()}`,
    );

    // Perform comparison based on operator
    let result = false;
    switch (operator) {
      case BonusRuleOperator.GREATER_THAN:
        result = totalDepositAmount > firstAmount;
        break;
      case BonusRuleOperator.LESS_THAN:
        result = totalDepositAmount < firstAmount;
        break;
      case BonusRuleOperator.GREATER_THAN_OR_EQUAL:
        result = totalDepositAmount >= firstAmount;
        break;
      case BonusRuleOperator.LESS_THAN_OR_EQUAL:
        result = totalDepositAmount <= firstAmount;
        break;
      case BonusRuleOperator.EQUALS:
        result = Math.abs(totalDepositAmount - firstAmount) < 0.01; // Float comparison with small tolerance
        break;
      case BonusRuleOperator.NOT_EQUALS:
        result = Math.abs(totalDepositAmount - firstAmount) >= 0.01; // Float comparison with small tolerance
        break;
      case BonusRuleOperator.BETWEEN:
        // Handle between operator - firstOperand = min amount, secondOperand = max amount
        if (secondAmount === null) {
          console.warn('⚠️ TotalDeposit validation: Between operator requires second operand');
          return false;
        }

        result = totalDepositAmount >= firstAmount && totalDepositAmount <= secondAmount;
        console.log(`💰 Between comparison: ${firstAmount} <= ${totalDepositAmount} <= ${secondAmount} = ${result}`);
        break;
      default:
        console.warn(`⚠️ TotalDeposit validation: Unsupported operator: ${operator}`);
        return false;
    }

    console.log(
      `✅ TotalDeposit validation result: ${totalDepositAmount} ${operator} ${firstAmount}${
        secondAmount !== null ? ` and ${secondAmount}` : ''
      } = ${result}`,
    );
    return result;
  } catch (error) {
    console.error('❌ TotalDeposit validation error:', error);
    return false;
  }
}
