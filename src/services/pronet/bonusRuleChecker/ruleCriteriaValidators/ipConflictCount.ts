import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { BonusRuleOperator } from '../shared';

export async function validateIpConflictCount(
    firstOperand: string | null,
    secondOperand: string | null,
    operator: string,
    customerId?: string,
    startsAt?: Date,
    endsAt?: Date,
): Promise<boolean> {
    try {
        console.log(`🔍 Validating IpConflictCount criteria: ${firstOperand} ${operator} ${secondOperand} for customer ${customerId}`);

        // Handle missing customer ID
        if (!customerId) {
            console.warn('⚠️ IpConflictCount validation: Customer ID is required');
            return false;
        }

        // Handle nullable first operand (always required)
        if (!firstOperand) {
            console.warn('⚠️ IpConflictCount validation: Missing first operand (count)');
            return false;
        }

        // Parse first operand as integer (always required)
        const firstCount = parseInt(firstOperand.trim(), 10);
        if (isNaN(firstCount)) {
            console.warn('⚠️ IpConflictCount validation: Invalid first operand integer format');
            return false;
        }

        // For btw operator, validate second operand
        let secondCount: number | null = null;
        if (operator === BonusRuleOperator.BETWEEN) {
            if (!secondOperand) {
                console.warn('⚠️ IpConflictCount validation: Missing second operand for between operator');
                return false;
            }
            secondCount = parseInt(secondOperand.trim(), 10);
            if (isNaN(secondCount)) {
                console.warn('⚠️ IpConflictCount validation: Invalid second operand integer format');
                return false;
            }
        }

        // Retrieve customer IP conflicts data from PG Dagur
        console.log(`📋 Fetching customer IP conflicts for customer ${customerId}...`);
        const ipConflictsData = await PgDagurService.getCustomerIpConflicts(customerId);

        // Extract conflicts count
        const actualConflictCount = ipConflictsData.conflicts?.length || 0;
        console.log(`📊 Customer ${customerId} has ${actualConflictCount} IP conflicts`);

        // Perform comparison based on operator
        let result = false;
        switch (operator) {
            case BonusRuleOperator.GREATER_THAN:
                result = actualConflictCount > firstCount;
                break;
            case BonusRuleOperator.LESS_THAN:
                result = actualConflictCount < firstCount;
                break;
            case BonusRuleOperator.GREATER_THAN_OR_EQUAL:
                result = actualConflictCount >= firstCount;
                break;
            case BonusRuleOperator.LESS_THAN_OR_EQUAL:
                result = actualConflictCount <= firstCount;
                break;
            case BonusRuleOperator.EQUALS:
                result = actualConflictCount === firstCount;
                break;
            case BonusRuleOperator.NOT_EQUALS:
                result = actualConflictCount !== firstCount;
                break;
            case BonusRuleOperator.BETWEEN:
                // Handle between operator - firstOperand = min count, secondOperand = max count
                if (secondCount === null) {
                    console.warn('⚠️ IpConflictCount validation: Between operator requires second operand');
                    return false;
                }

                result = actualConflictCount >= firstCount && actualConflictCount <= secondCount;
                console.log(`🔢 Between comparison: ${firstCount} <= ${actualConflictCount} <= ${secondCount} = ${result}`);
                break;
            default:
                console.warn(`⚠️ IpConflictCount validation: Unsupported operator: ${operator}`);
                return false;
        }

        console.log(`✅ IpConflictCount validation result: ${actualConflictCount} ${operator} ${firstCount}${secondCount !== null ? ` and ${secondCount}` : ''} = ${result}`);
        return result;

    } catch (error) {
        console.error('❌ IpConflictCount validation error:', error);
        return false;
    }
}
