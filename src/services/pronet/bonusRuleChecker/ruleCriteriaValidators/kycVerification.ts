import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { BonusRuleOperator } from '../shared';

export async function validateKycVerification(
    firstOperand: string | null,
    secondOperand: string | null,
    operator: string,
    customerId?: string,
    startsAt?: Date,
    endsAt?: Date,
): Promise<boolean> {
    try {
        console.log(`🔍 Validating KycVerification criteria: ${firstOperand} ${operator} ${secondOperand} for customer ${customerId}`);

        // Handle missing customer ID
        if (!customerId) {
            console.warn('⚠️ KycVerification validation: Customer ID is required');
            return false;
        }

        // Handle nullable first operand (always required)
        if (!firstOperand) {
            console.warn('⚠️ KycVerification validation: Missing first operand (binary string)');
            return false;
        }

        // Validate operator - only eq and gte are supported
        if (operator !== BonusRuleOperator.EQUALS && operator !== BonusRuleOperator.GREATER_THAN_OR_EQUAL) {
            console.warn(`⚠️ KycVerification validation: Unsupported operator: ${operator}. Only 'eq' and 'gte' are supported`);
            return false;
        }

        // Validate binary string format (should be exactly 4 characters of 0s and 1s)
        const binaryPattern = /^[01]{4}$/;
        if (!binaryPattern.test(firstOperand.trim())) {
            console.warn(`⚠️ KycVerification validation: Invalid binary string format: ${firstOperand}. Must be exactly 4 characters of 0s and 1s`);
            return false;
        }

        const expectedPattern = firstOperand.trim();
        console.log(`🔍 Expected KYC pattern: ${expectedPattern} (Email:${expectedPattern[0]}, Phone:${expectedPattern[1]}, Identity:${expectedPattern[2]}, Address:${expectedPattern[3]})`);

        // Retrieve customer details to get KYC information
        let customerDetails: any = null;
        try {
            console.log(`📋 Fetching customer details for customer ${customerId}...`);
            customerDetails = await PgDagurService.getCustomerDetails(customerId);
            console.log(`✅ Successfully retrieved customer details for customer ${customerId}`);
        } catch (error) {
            console.warn(`⚠️ Failed to retrieve customer details for customer ${customerId}:`, error);
            return false; // Can't proceed without customer details
        }

        // Extract KYC verification status
        const kycData = customerDetails?.kyc;
        if (!kycData) {
            console.warn('⚠️ KycVerification validation: No KYC data found in customer details');
            return false;
        }

        // Convert KYC boolean values to binary string
        // Order: kycEmail, kycPhone, kycIdentity, kycAddress
        const actualKycPattern = [
            kycData.kycEmail ? '1' : '0',
            kycData.kycPhone ? '1' : '0', 
            kycData.kycIdentity ? '1' : '0',
            kycData.kycAddress ? '1' : '0'
        ].join('');

        console.log(`💳 Customer ${customerId} KYC status:`);
        console.log(`   📧 Email: ${kycData.kycEmail ? '✅' : '❌'}`);
        console.log(`   📱 Phone: ${kycData.kycPhone ? '✅' : '❌'}`);
        console.log(`   🆔 Identity: ${kycData.kycIdentity ? '✅' : '❌'}`);
        console.log(`   🏠 Address: ${kycData.kycAddress ? '✅' : '❌'}`);
        console.log(`   🔢 Binary pattern: ${actualKycPattern}`);

        // Perform comparison based on operator
        let result = false;
        switch (operator) {
            case BonusRuleOperator.EQUALS:
                // Exact match required
                result = actualKycPattern === expectedPattern;
                console.log(`🔍 Exact match comparison: ${actualKycPattern} === ${expectedPattern} = ${result}`);
                break;

            case BonusRuleOperator.GREATER_THAN_OR_EQUAL:
                // Check if customer has at least the required KYC verifications
                result = true;
                for (let i = 0; i < 4; i++) {
                    if (expectedPattern[i] === '1' && actualKycPattern[i] === '0') {
                        result = false;
                        break;
                    }
                }
                console.log(`🔍 At-least comparison: Customer has at least required KYC verifications = ${result}`);
                
                // Log detailed comparison for debugging
                const kycTypes = ['Email', 'Phone', 'Identity', 'Address'];
                for (let i = 0; i < 4; i++) {
                    if (expectedPattern[i] === '1') {
                        const hasRequired = actualKycPattern[i] === '1';
                        console.log(`   ${kycTypes[i]}: Required ✓, Customer has ${hasRequired ? '✅' : '❌'}`);
                    }
                }
                break;

            default:
                console.warn(`⚠️ KycVerification validation: Unsupported operator: ${operator}`);
                return false;
        }

        console.log(`✅ KycVerification validation result: ${actualKycPattern} ${operator} ${expectedPattern} = ${result}`);
        return result;

    } catch (error) {
        console.error('❌ KycVerification validation error:', error);
        return false;
    }
}
