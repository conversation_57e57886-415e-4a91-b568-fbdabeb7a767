import { AppDataSource } from '@/database/data-source';
import { FreebetBonus } from '@/entities/pronet/FreebetBonus';
import { FreebetBonusTemplate } from '@/entities/pronet/FreebetBonusTemplate';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';
import { BonusRuleService, BonusRuleCreate, BonusTemplateRuleCreate } from './bonusRule.service';
import { FindOptionsWhere, IsNull } from 'typeorm';
import { paginate, Paginated } from '../shared';

export interface ListFreebetBonusOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface ListFreebetBonusTemplateOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface FreebetBonusCreate {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface FreebetBonusTemplateCreate {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;

  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export interface BonusBulkAssignmentJobCreate {
  freebetBonusId: number;
  externalCustomerIds: number[];
}

export class FreebetBonusService {
  private bonusRuleService: BonusRuleService;

  constructor() {
    this.bonusRuleService = new BonusRuleService();
  }

  async list(options: ListFreebetBonusOptions): Promise<Paginated<FreebetBonus>> {
    const bonusFilter: FindOptionsWhere<Bonus> = {
      deletedAt: IsNull(),
    };

    if (options.isActive !== undefined) {
      bonusFilter.isActive = options.isActive;
    }

    const [items, total] = await AppDataSource.getRepository(FreebetBonus).findAndCount({
      relations: ['bonus', 'bonus.rules'],
      where: {
        bonus: bonusFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async listTemplates(options: ListFreebetBonusTemplateOptions): Promise<Paginated<FreebetBonusTemplate>> {
    const bonusTemplateFilter: FindOptionsWhere<BonusTemplate> = {
      deletedAt: IsNull(),
    };

    const [items, total] = await AppDataSource.getRepository(FreebetBonusTemplate).findAndCount({
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
      where: {
        bonusTemplate: bonusTemplateFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async findById(id: number): Promise<FreebetBonus | null> {
    return await AppDataSource.getRepository(FreebetBonus).findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  async findTemplateById(id: number): Promise<FreebetBonusTemplate | null> {
    return await AppDataSource.getRepository(FreebetBonusTemplate).findOne({
      where: { id },
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
    });
  }

  async create(data: FreebetBonusCreate): Promise<FreebetBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        description: `Freebet bonus: ${data.name}`,
        reward: `${data.amount} freebet`,
        type: 'freebet',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const freebetBonus = manager.create(FreebetBonus, {
        externalBonusName: data.externalBonusName,
        externalBonusId: data.externalBonusId,
        amount: data.amount,
        bonusId: bonus.id,
      });

      return await manager.save(freebetBonus);
    });

    const bonus = await this.findById(result.id);
    if (!bonus) {
      throw new Error('Failed to create freebet bonus');
    }

    return bonus;
  }

  async createTemplate(data: FreebetBonusTemplateCreate): Promise<FreebetBonusTemplate> {
    const id = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'freebet',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const freebetBonusTemplate = manager.create(FreebetBonusTemplate, {
        externalBonusName: data.externalBonusName,
        externalBonusId: data.externalBonusId,
        amount: data.amount,
        bonusTemplateId: bonusTemplate.id,
      });

      await manager.save(freebetBonusTemplate);

      return freebetBonusTemplate.id;
    });

    const template = await this.findTemplateById(id);
    if (!template) {
      throw new Error('Failed to create freebet bonus template');
    }

    return template;
  }

  async createBulkAssignmentJob(data: BonusBulkAssignmentJobCreate): Promise<BonusBulkAssignmentJob> {
    return await AppDataSource.transaction(async (manager) => {
      const freebetBonusRepository = manager.getRepository(FreebetBonus);
      const freebetBonus = await freebetBonusRepository.findOne({
        where: { id: data.freebetBonusId },
        relations: ['bonus'],
      });

      if (!freebetBonus) {
        throw new Error('Freebet bonus not found');
      }

      if (!freebetBonus.bonus.isActive) {
        throw new Error('Freebet bonus is not active');
      }

      const values = {
        base: {
          expiresAt: freebetBonus.bonus.expiresAt,
        },
        freebet: {
          externalBonusName: freebetBonus.externalBonusName,
          externalBonusId: freebetBonus.externalBonusId,
          amount: freebetBonus.amount,
        },
      };

      const createdJob = manager.create(BonusBulkAssignmentJob, {
        bonusId: freebetBonus.bonus.id,
        bonusValues: values,
        bonusValuesVersion: 1,
        status: 'pending',
      });

      await manager.save(createdJob);

      for (const externalCustomerId of data.externalCustomerIds) {
        const target = manager.create(BonusBulkAssignmentJobTarget, {
          bonusBulkAssignmentJobId: createdJob.id,
          externalCustomerId,
          status: 'pending',
          events: [],
        });

        await manager.save(target);
      }

      const job = await manager.findOne(BonusBulkAssignmentJob, {
        where: { id: createdJob.id },
        relations: ['targets', 'bonus'],
      });
      if (!job) {
        throw new Error('Failed to create bulk assignment job');
      }

      return job;
    });
  }
}
