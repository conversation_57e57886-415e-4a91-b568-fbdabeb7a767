import { AppDataSource } from '@/database/data-source';
import { Customer } from '@/entities/pronet/Customer';
import { pgWebHttpClient } from '@/network/pg-web/PGWebHttpClient';
import { Repository, IsNull } from 'typeorm';
import { paginate, Paginated } from '../shared';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';

export interface CustomerCreateInput {
  externalId: number;
  code: string;
  username: string;
}

export interface CustomerPatchInput {
  username?: string;
}

export interface ListCustomersOptions {
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'username' | 'code' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';
}

export class CustomerService {
  private customerRepository: Repository<Customer>;

  constructor() {
    this.customerRepository = AppDataSource.getRepository(Customer);
  }

  async create(payload: CustomerCreateInput): Promise<Customer> {
    return await this.customerRepository.save({
      externalId: payload.externalId,
      code: payload.code,
      username: payload.username,
    });
  }

  async put(payload: CustomerCreateInput): Promise<Customer> {
    let customer = await this.findByExternalId(payload.externalId);
    if (!customer) {
      customer = await this.create(payload);
    }

    return customer;
  }

  async findById(id: number): Promise<Customer | null> {
    return await this.customerRepository.findOne({ where: { id } });
  }

  async findByExternalId(externalId: number): Promise<Customer | null> {
    return await this.customerRepository.findOne({ where: { externalId } });
  }

  async findByCode(code: string): Promise<Customer | null> {
    return await this.customerRepository.findOne({ where: { code } });
  }

  async findByUsername(username: string): Promise<Customer | null> {
    return await this.customerRepository.findOne({ where: { username } });
  }

  async patch(id: number, payload: CustomerPatchInput): Promise<Customer | null> {
    await this.customerRepository.update(id, payload);
    return await this.findById(id);
  }

  async list(options: ListCustomersOptions = {}): Promise<Paginated<Customer>> {
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = options;

    // Validate pagination parameters
    if (page < 1) {
      throw new Error('page must be a positive integer');
    }

    if (limit < 1 || limit > 100) {
      throw new Error('limit must be between 1 and 100');
    }

    // Build query to exclude soft-deleted customers
    const queryBuilder = this.customerRepository.createQueryBuilder('customer').where('customer.deletedAt IS NULL');

    // Add ordering
    const validSortFields = ['id', 'username', 'code', 'createdAt', 'updatedAt'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    queryBuilder.orderBy(`customer.${sortField}`, sortOrder);

    // Add pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [customers, total] = await queryBuilder.getManyAndCount();

    return paginate(customers, total, page, limit);
  }
}
