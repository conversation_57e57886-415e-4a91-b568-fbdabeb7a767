import { AppDataSource } from '@/database/data-source';
import { SiteClaimableBonus } from '@/entities/pronet/SiteClaimableBonus';
import { Bonus } from '@/entities/pronet/Bonus';
import { Repository, IsNull } from 'typeorm';

export interface SiteClaimableBonusUpdateInput {
  bonusId: number;
  isActive?: boolean;
}

export interface SiteClaimableBonusCreateInput {
  slotName: string;
  bonusId: number;
  isActive?: boolean;
}

export class SiteClaimableBonusService {
  private siteClaimableBonusRepository: Repository<SiteClaimableBonus>;
  private bonusRepository: Repository<Bonus>;

  constructor() {
    this.siteClaimableBonusRepository = AppDataSource.getRepository(SiteClaimableBonus);
    this.bonusRepository = AppDataSource.getRepository(Bonus);
  }

  /**
   * Get all claimable bonus configurations
   */
  async list(): Promise<SiteClaimableBonus[]> {
    return await this.siteClaimableBonusRepository.find({
      relations: ['bonus', 'bonus.rules'],
      order: {
        slotName: 'ASC',
      },
    });
  }

  /**
   * Get configuration for a specific slot
   */
  async findBySlotName(slotName: string): Promise<SiteClaimableBonus | null> {
    return await this.siteClaimableBonusRepository.findOne({
      where: { slotName },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  /**
   * Get configuration by ID
   */
  async findById(id: number): Promise<SiteClaimableBonus | null> {
    return await this.siteClaimableBonusRepository.findOne({
      where: { id },
      relations: ['bonus', 'bonus.rules'],
    });
  }

  /**
   * Update which bonus is assigned to a slot
   */
  async update(slotName: string, data: SiteClaimableBonusUpdateInput): Promise<SiteClaimableBonus> {
    // Validate that the slot exists
    const existingSlot = await this.findBySlotName(slotName);
    if (!existingSlot) {
      throw new Error(`Slot '${slotName}' not found`);
    }

    // Validate that the bonus exists and is active
    const bonus = await this.bonusRepository.findOne({
      where: { 
        id: data.bonusId,
        deletedAt: IsNull(),
      },
      relations: ['rules'],
    });

    if (!bonus) {
      throw new Error(`Bonus with ID ${data.bonusId} not found or has been deleted`);
    }

    if (!bonus.isActive) {
      throw new Error(`Bonus with ID ${data.bonusId} is not active`);
    }

    // Check if bonus has expired
    if (bonus.expiresAt && bonus.expiresAt < new Date()) {
      throw new Error(`Bonus with ID ${data.bonusId} has expired`);
    }

    // Update the slot configuration
    const updateData: Partial<SiteClaimableBonus> = {
      bonusId: data.bonusId,
    };

    if (data.isActive !== undefined) {
      updateData.isActive = data.isActive;
    }

    const result = await this.siteClaimableBonusRepository.update(
      { slotName },
      updateData
    );

    if (result.affected !== 1) {
      throw new Error(`Failed to update slot '${slotName}'`);
    }

    // Return the updated slot configuration
    const updatedSlot = await this.findBySlotName(slotName);
    if (!updatedSlot) {
      throw new Error(`Failed to retrieve updated slot '${slotName}'`);
    }

    return updatedSlot;
  }

  /**
   * Check if a bonus is currently claimable via any active slot
   */
  async isClaimable(bonusId: number): Promise<boolean> {
    const claimableSlot = await this.siteClaimableBonusRepository.findOne({
      where: {
        bonusId,
        isActive: true,
      },
      relations: ['bonus'],
    });

    if (!claimableSlot) {
      return false;
    }

    // Additional validation: check if the bonus itself is still active and not expired
    const bonus = claimableSlot.bonus;
    if (!bonus.isActive || bonus.deletedAt) {
      return false;
    }

    if (bonus.expiresAt && bonus.expiresAt < new Date()) {
      return false;
    }

    return true;
  }

  /**
   * Get all currently claimable bonuses (active slots with active, non-expired bonuses)
   */
  async getClaimableBonuses(): Promise<SiteClaimableBonus[]> {
    const slots = await this.siteClaimableBonusRepository.find({
      where: {
        isActive: true,
      },
      relations: ['bonus', 'bonus.rules'],
      order: {
        slotName: 'ASC',
      },
    });

    // Filter out slots with inactive or expired bonuses
    const now = new Date();
    return slots.filter(slot => {
      const bonus = slot.bonus;
      return bonus.isActive && 
             !bonus.deletedAt && 
             (!bonus.expiresAt || bonus.expiresAt > now);
    });
  }

  /**
   * Validate that a bonus can be claimed (used by bonus claiming logic)
   */
  async validateBonusClaimable(bonusId: number): Promise<void> {
    const isClaimable = await this.isClaimable(bonusId);
    if (!isClaimable) {
      throw new Error(`Bonus with ID ${bonusId} is not currently claimable via the rewards page`);
    }
  }

  /**
   * Toggle the active status of a slot
   */
  async toggleActive(slotName: string, isActive: boolean): Promise<SiteClaimableBonus> {
    const existingSlot = await this.findBySlotName(slotName);
    if (!existingSlot) {
      throw new Error(`Slot '${slotName}' not found`);
    }

    const result = await this.siteClaimableBonusRepository.update(
      { slotName },
      { isActive }
    );

    if (result.affected !== 1) {
      throw new Error(`Failed to toggle active status for slot '${slotName}'`);
    }

    const updatedSlot = await this.findBySlotName(slotName);
    if (!updatedSlot) {
      throw new Error(`Failed to retrieve updated slot '${slotName}'`);
    }

    return updatedSlot;
  }

  /**
   * Create a new claimable bonus slot
   */
  async create(data: SiteClaimableBonusCreateInput): Promise<SiteClaimableBonus> {
    // Check if slot name already exists
    const existingSlot = await this.findBySlotName(data.slotName);
    if (existingSlot) {
      throw new Error(`Slot '${data.slotName}' already exists`);
    }

    // Validate that the bonus exists and is active
    const bonus = await this.bonusRepository.findOne({
      where: {
        id: data.bonusId,
        deletedAt: IsNull(),
      },
      relations: ['rules'],
    });

    if (!bonus) {
      throw new Error(`Bonus with ID ${data.bonusId} not found or has been deleted`);
    }

    if (!bonus.isActive) {
      throw new Error(`Bonus with ID ${data.bonusId} is not active`);
    }

    // Check if bonus has expired
    if (bonus.expiresAt && bonus.expiresAt < new Date()) {
      throw new Error(`Bonus with ID ${data.bonusId} has expired`);
    }

    // Create the new slot
    const newSlot = this.siteClaimableBonusRepository.create({
      slotName: data.slotName,
      bonusId: data.bonusId,
      isActive: data.isActive !== undefined ? data.isActive : true,
    });

    const savedSlot = await this.siteClaimableBonusRepository.save(newSlot);

    // Return the created slot with relations
    const createdSlot = await this.findById(savedSlot.id);
    if (!createdSlot) {
      throw new Error(`Failed to retrieve created slot '${data.slotName}'`);
    }

    return createdSlot;
  }

  /**
   * Delete a claimable bonus slot by slot name
   */
  async deleteBySlotName(slotName: string): Promise<void> {
    const existingSlot = await this.findBySlotName(slotName);
    if (!existingSlot) {
      throw new Error(`Slot '${slotName}' not found`);
    }

    const result = await this.siteClaimableBonusRepository.delete({ slotName });

    if (result.affected !== 1) {
      throw new Error(`Failed to delete slot '${slotName}'`);
    }
  }

  /**
   * Delete a claimable bonus slot by ID
   */
  async deleteById(id: number): Promise<void> {
    const existingSlot = await this.findById(id);
    if (!existingSlot) {
      throw new Error(`Slot with ID ${id} not found`);
    }

    const result = await this.siteClaimableBonusRepository.delete({ id });

    if (result.affected !== 1) {
      throw new Error(`Failed to delete slot with ID ${id}`);
    }
  }
}
