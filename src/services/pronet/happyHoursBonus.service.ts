import { AppDataSource } from '@/database/data-source';
import { HappyHoursBonus } from '@/entities/pronet/HappyHoursBonus';
import { HappyHoursBonusTemplate } from '@/entities/pronet/HappyHoursBonusTemplate';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';
import { FindOptionsWhere, IsNull, Not } from 'typeorm';
import { paginate, Paginated } from '../shared';
import { BonusRuleService, BonusRuleCreate, BonusTemplateRuleCreate } from './bonusRule.service';

export interface ListHappyHoursBonusOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface ListHappyHoursBonusTemplateOptions {
  isActive?: boolean | undefined;

  page: number;
  limit: number;
}

export interface HappyHoursBonusCreate {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  expiresAt?: Date | null;
  rules: Omit<BonusRuleCreate, 'bonusId'>[];
}

export interface HappyHoursBonusTemplateCreate {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;

  rules: Omit<BonusTemplateRuleCreate, 'bonusTemplateId'>[];
}

export interface BonusBulkAssignmentJobCreate {
  happyHoursBonusId: number;
  externalCustomerIds: number[];
}

export class HappyHoursBonusService {
  private bonusRuleService: BonusRuleService;

  constructor() {
    this.bonusRuleService = new BonusRuleService();
  }

  async list(options: ListHappyHoursBonusOptions): Promise<Paginated<HappyHoursBonus>> {
    const bonusFilter: FindOptionsWhere<Bonus> = {
      deletedAt: IsNull(),
    };

    if (options.isActive !== undefined) {
      bonusFilter.isActive = options.isActive;
    }

    const [items, total] = await AppDataSource.getRepository(HappyHoursBonus).findAndCount({
      relations: ['bonus', 'bonus.rules'],
      where: {
        bonus: bonusFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async listTemplates(options: ListHappyHoursBonusTemplateOptions): Promise<Paginated<HappyHoursBonusTemplate>> {
    const bonusTemplateFilter: FindOptionsWhere<BonusTemplate> = {};

    if (options.isActive !== undefined) {
      bonusTemplateFilter.deletedAt = options.isActive ? IsNull() : Not(IsNull());
    }

    const [items, total] = await AppDataSource.getRepository(HappyHoursBonusTemplate).findAndCount({
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
      where: {
        bonusTemplate: bonusTemplateFilter,
      },
      skip: (options.page - 1) * options.limit,
      take: options.limit,
    });

    return paginate(items, total, options.page, options.limit);
  }

  async findById(id: number): Promise<HappyHoursBonus | null> {
    return await AppDataSource.getRepository(HappyHoursBonus).findOne({
      relations: ['bonus', 'bonus.rules'],
      where: { id },
    });
  }

  async findTemplateById(id: number): Promise<HappyHoursBonusTemplate | null> {
    return await AppDataSource.getRepository(HappyHoursBonusTemplate).findOne({
      relations: ['bonusTemplate', 'bonusTemplate.rules'],
      where: { id },
    });
  }

  async create(data: HappyHoursBonusCreate): Promise<HappyHoursBonus> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonus = manager.create(Bonus, {
        name: data.name,
        type: 'happyHours',
        isActive: true,
        expiresAt: data.expiresAt ?? null,
      });

      await manager.save(bonus);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusRuleTx(
          {
            bonusId: bonus.id,
            ...rule,
          },
          manager,
        );
      }

      const happyHoursBonus = manager.create(HappyHoursBonus, {
        externalBonusName: data.externalBonusName,
        externalBonusId: data.externalBonusId,
        amount: data.amount,
        bonusId: bonus.id,
      });

      return await manager.save(happyHoursBonus);
    });

    const bonus = await this.findById(result.id);
    if (!bonus) {
      throw new Error('Failed to create happy hours bonus');
    }

    return bonus;
  }

  async createTemplate(data: HappyHoursBonusTemplateCreate): Promise<HappyHoursBonusTemplate> {
    const result = await AppDataSource.transaction(async (manager) => {
      const bonusTemplate = manager.create(BonusTemplate, {
        name: data.name,
        type: 'happyHours',
      });

      await manager.save(bonusTemplate);

      for (const rule of data.rules) {
        await this.bonusRuleService.createBonusTemplateRuleTx(
          {
            bonusTemplateId: bonusTemplate.id,
            ...rule,
          },
          manager,
        );
      }

      const happyHoursBonusTemplate = manager.create(HappyHoursBonusTemplate, {
        externalBonusName: data.externalBonusName,
        externalBonusId: data.externalBonusId,
        amount: data.amount,
        bonusTemplateId: bonusTemplate.id,
      });

      return await manager.save(happyHoursBonusTemplate);
    });

    const bonusTemplate = await this.findTemplateById(result.id);
    if (!bonusTemplate) {
      throw new Error('Failed to create happy hours bonus template');
    }

    return bonusTemplate;
  }

  async createBulkAssignmentJob(data: BonusBulkAssignmentJobCreate): Promise<BonusBulkAssignmentJob> {
    return await AppDataSource.transaction(async (manager) => {
      const happyHoursBonusRepository = manager.getRepository(HappyHoursBonus);
      const happyHoursBonus = await happyHoursBonusRepository.findOne({
        where: { id: data.happyHoursBonusId },
        relations: ['bonus'],
      });

      if (!happyHoursBonus) {
        throw new Error('Happy hours bonus not found');
      }

      if (!happyHoursBonus.bonus.isActive) {
        throw new Error('Happy hours bonus is not active');
      }

      const values = {
        base: {
          expiresAt: happyHoursBonus.bonus.expiresAt,
        },
        happyHours: {
          externalBonusName: happyHoursBonus.externalBonusName,
          externalBonusId: happyHoursBonus.externalBonusId,
          amount: happyHoursBonus.amount,
        },
      };

      const createdJob = manager.create(BonusBulkAssignmentJob, {
        bonusId: happyHoursBonus.bonus.id,
        bonusValues: values,
        bonusValuesVersion: 1,
        status: 'pending',
      });

      await manager.save(createdJob);

      for (const externalCustomerId of data.externalCustomerIds) {
        const target = manager.create(BonusBulkAssignmentJobTarget, {
          bonusBulkAssignmentJobId: createdJob.id,
          externalCustomerId,
          status: 'pending',
          events: [],
        });

        await manager.save(target);
      }

      const job = await manager.findOne(BonusBulkAssignmentJob, {
        where: { id: createdJob.id },
        relations: ['targets', 'bonus'],
      });
      if (!job) {
        throw new Error('Failed to create bulk assignment job');
      }

      return job;
    });
  }
}
