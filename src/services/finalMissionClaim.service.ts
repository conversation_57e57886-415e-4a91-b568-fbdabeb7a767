import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { FinalMissionClaim } from '@/entities/FinalMissionClaim';
import { MissionType } from '@/enums/shared';

export interface CreateFinalMissionClaimDto {
  userId: number;
  claimType: MissionType.DAILY | MissionType.WEEKLY | MissionType.MONTHLY;
  grantedReward: number;
}

export interface FinalMissionClaimQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'userId' | 'claimType' | 'grantedReward' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters
  userId?: number;
  claimType?: MissionType.DAILY | MissionType.WEEKLY | MissionType.MONTHLY;
  createdAtFrom?: Date;
  createdAtTo?: Date;
  minReward?: number;
  maxReward?: number;
}

export interface FinalMissionClaimListResponse {
  finalMissionClaims: FinalMissionClaim[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class FinalMissionClaimService {
  private finalMissionClaimRepository: Repository<FinalMissionClaim>;

  constructor() {
    this.finalMissionClaimRepository = AppDataSource.getRepository(FinalMissionClaim);
  }

  async createFinalMissionClaim(createFinalMissionClaimDto: CreateFinalMissionClaimDto): Promise<FinalMissionClaim> {
    const finalMissionClaim = this.finalMissionClaimRepository.create(createFinalMissionClaimDto);
    return await this.finalMissionClaimRepository.save(finalMissionClaim);
  }

  async findFinalMissionClaimById(id: number): Promise<FinalMissionClaim | null> {
    return await this.finalMissionClaimRepository.findOne({
      where: { id },
      relations: ['user']
    });
  }

  async findAllFinalMissionClaims(): Promise<FinalMissionClaim[]> {
    return await this.finalMissionClaimRepository.find({
      order: { createdAt: 'DESC' },
      relations: ['user']
    });
  }

  async findFinalMissionClaimsWithQuery(queryParams: FinalMissionClaimQueryParams): Promise<FinalMissionClaimListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      userId,
      claimType,
      createdAtFrom,
      createdAtTo,
      minReward,
      maxReward,
    } = queryParams;

    // Build query
    const queryBuilder = this.finalMissionClaimRepository.createQueryBuilder('finalMissionClaim')
      .leftJoinAndSelect('finalMissionClaim.user', 'user');

    // Apply filters
    if (userId !== undefined) {
      queryBuilder.andWhere('finalMissionClaim.userId = :userId', { userId });
    }

    if (claimType) {
      queryBuilder.andWhere('finalMissionClaim.claimType = :claimType', { claimType });
    }

    if (createdAtFrom) {
      queryBuilder.andWhere('finalMissionClaim.createdAt >= :createdAtFrom', { createdAtFrom });
    }

    if (createdAtTo) {
      queryBuilder.andWhere('finalMissionClaim.createdAt <= :createdAtTo', { createdAtTo });
    }

    if (minReward !== undefined) {
      queryBuilder.andWhere('finalMissionClaim.grantedReward >= :minReward', { minReward });
    }

    if (maxReward !== undefined) {
      queryBuilder.andWhere('finalMissionClaim.grantedReward <= :maxReward', { maxReward });
    }

    // Apply sorting
    queryBuilder.orderBy(`finalMissionClaim.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const finalMissionClaims = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      finalMissionClaims,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Find the most recent final mission claim for a user and claim type
   * This is used to check if the user can claim again based on the time period
   */
  async findLatestClaimByUserAndType(
    userId: number, 
    claimType: MissionType.DAILY | MissionType.WEEKLY | MissionType.MONTHLY
  ): Promise<FinalMissionClaim | null> {
    return await this.finalMissionClaimRepository.findOne({
      where: { userId, claimType },
      order: { createdAt: 'DESC' },
      relations: ['user']
    });
  }

  /**
   * Find all claims for a user, ordered by most recent
   */
  async findClaimsByUserId(userId: number): Promise<FinalMissionClaim[]> {
    return await this.finalMissionClaimRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      relations: ['user']
    });
  }

  /**
   * Find all claims of a specific type, ordered by most recent
   */
  async findClaimsByType(claimType: MissionType.DAILY | MissionType.WEEKLY | MissionType.MONTHLY): Promise<FinalMissionClaim[]> {
    return await this.finalMissionClaimRepository.find({
      where: { claimType },
      order: { createdAt: 'DESC' },
      relations: ['user']
    });
  }

  async updateFinalMissionClaim(id: number, updateData: Partial<CreateFinalMissionClaimDto>): Promise<FinalMissionClaim | null> {
    await this.finalMissionClaimRepository.update(id, updateData);
    return await this.findFinalMissionClaimById(id);
  }

  async deleteFinalMissionClaim(id: number): Promise<boolean> {
    const result = await this.finalMissionClaimRepository.delete(id);
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }

  /**
   * Get statistics for final mission claims
   */
  async getClaimStatistics(): Promise<{
    totalClaims: number;
    dailyClaims: number;
    weeklyClaims: number;
    monthlyClaims: number;
    totalRewardsGranted: number;
  }> {
    const totalClaims = await this.finalMissionClaimRepository.count();
    
    const dailyClaims = await this.finalMissionClaimRepository.count({
      where: { claimType: MissionType.DAILY }
    });
    
    const weeklyClaims = await this.finalMissionClaimRepository.count({
      where: { claimType: MissionType.WEEKLY }
    });
    
    const monthlyClaims = await this.finalMissionClaimRepository.count({
      where: { claimType: MissionType.MONTHLY }
    });

    const rewardSum = await this.finalMissionClaimRepository
      .createQueryBuilder('claim')
      .select('SUM(claim.grantedReward)', 'total')
      .getRawOne();

    return {
      totalClaims,
      dailyClaims,
      weeklyClaims,
      monthlyClaims,
      totalRewardsGranted: parseInt(rewardSum?.total || '0'),
    };
  }
}
