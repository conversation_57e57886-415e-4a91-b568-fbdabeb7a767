import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SelectQueryBuilder } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { Transaction } from '@/entities/Transaction';
import { TransactionType, TransactionCategory } from '@/enums/shared';

export interface CreateTransactionOptions {
  fromUserId?: number;
  toUserId: number;
  type: TransactionType;
  category: TransactionCategory;
  amount: number;
  metadata?: Record<string, any>;
}

export interface ListTransactionsFilters {
  fromUserId?: number;
  toUserId?: number;
  type?: TransactionType;
  category?: TransactionCategory;
}

export interface ListTransactionsOptions {
  filters?: ListTransactionsFilters;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}



export interface AdminTransactionFilters extends ListTransactionsFilters {
  id?: number;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
  createdAfter?: Date;
  createdBefore?: Date;
}

export interface PaginatedTransactions {
  data: Transaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TransactionStats {
  totalCount: number;
  totalVolume: number;
  depositCount: number;
  depositVolume: number;
  chargeCount: number;
  chargeVolume: number;
  refundCount: number;
  refundVolume: number;
  withdrawCount: number;
  withdrawVolume: number;
}

export class TransactionsService {
  /**
   * Get transaction statistics
   */
  async getTransactionStats(): Promise<TransactionStats> {
    try {
      console.log('📊 Getting transaction statistics');

      const transactionRepository = AppDataSource.manager.getRepository(Transaction);

      // Get total count and volume
      const totalStats = await transactionRepository
        .createQueryBuilder('transaction')
        .select('COUNT(*)', 'count')
        .addSelect('COALESCE(SUM(transaction.amount), 0)', 'volume')
        .getRawOne();

      // Get stats by transaction type
      const depositStats = await transactionRepository
        .createQueryBuilder('transaction')
        .select('COUNT(*)', 'count')
        .addSelect('COALESCE(SUM(transaction.amount), 0)', 'volume')
        .where('transaction.type = :type', { type: TransactionType.DEPOSIT })
        .getRawOne();

      const chargeStats = await transactionRepository
        .createQueryBuilder('transaction')
        .select('COUNT(*)', 'count')
        .addSelect('COALESCE(SUM(transaction.amount), 0)', 'volume')
        .where('transaction.type = :type', { type: TransactionType.CHARGE })
        .getRawOne();

      const refundStats = await transactionRepository
        .createQueryBuilder('transaction')
        .select('COUNT(*)', 'count')
        .addSelect('COALESCE(SUM(transaction.amount), 0)', 'volume')
        .where('transaction.type = :type', { type: TransactionType.REFUND })
        .getRawOne();

      const withdrawStats = await transactionRepository
        .createQueryBuilder('transaction')
        .select('COUNT(*)', 'count')
        .addSelect('COALESCE(SUM(transaction.amount), 0)', 'volume')
        .where('transaction.type = :type', { type: TransactionType.WITHDRAWAL })
        .getRawOne();

      const stats: TransactionStats = {
        totalCount: parseInt(totalStats.count) || 0,
        totalVolume: parseFloat(totalStats.volume) || 0,
        depositCount: parseInt(depositStats.count) || 0,
        depositVolume: parseFloat(depositStats.volume) || 0,
        chargeCount: parseInt(chargeStats.count) || 0,
        chargeVolume: parseFloat(chargeStats.volume) || 0,
        refundCount: parseInt(refundStats.count) || 0,
        refundVolume: parseFloat(refundStats.volume) || 0,
        withdrawCount: parseInt(withdrawStats.count) || 0,
        withdrawVolume: parseFloat(withdrawStats.volume) || 0,
      };

      console.log('✅ Transaction statistics retrieved successfully:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Failed to get transaction statistics:', error);
      throw error;
    }
  }

  /**
   * Create a new transaction
   */
  async createTransaction(options: CreateTransactionOptions, tx?: EntityManager): Promise<Transaction> {
    try {
      console.log('💰 Creating transaction:', JSON.stringify(options, null, 2));

      const transactionRepository = (tx ?? AppDataSource.manager).getRepository(Transaction);

      // Create transaction entity
      const transaction = transactionRepository.create({
        fromUserId: options.fromUserId || null,
        toUserId: options.toUserId,
        type: options.type,
        category: options.category,
        amount: options.amount,
        metadata: options.metadata || {},
      });

      // Save transaction
      const savedTransaction = await transactionRepository.save(transaction);

      console.log('✅ Transaction created successfully:', savedTransaction.id);
      return savedTransaction;
    } catch (error) {
      console.error('❌ Failed to create transaction:', error);
      throw error;
    }
  }

  /**
   * List all transactions with optional filters and pagination
   */
  async listTransactions(options: ListTransactionsOptions = {}): Promise<PaginatedTransactions> {
    try {
      const { filters = {}, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = options;

      console.log('📋 Listing transactions with options:', JSON.stringify(options, null, 2));

      // Validate pagination parameters
      if (page < 1) {
        throw new Error('page must be a positive integer');
      }

      if (limit < 1 || limit > 100) {
        throw new Error('limit must be between 1 and 100');
      }

      // Build query
      const queryBuilder = this.buildTransactionsQuery(filters);

      // Add pagination
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      // Add ordering
      const validSortFields = ['id', 'fromUserId', 'toUserId', 'type', 'category', 'amount', 'createdAt', 'updatedAt'];
      const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
      queryBuilder.orderBy(`transaction.${sortField}`, sortOrder);

      // Execute query
      const [transactions, total] = await queryBuilder.getManyAndCount();

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);

      const result: PaginatedTransactions = {
        data: transactions,
        total,
        page,
        limit,
        totalPages,
      };

      console.log(`✅ Retrieved ${transactions.length} transactions (${total} total)`);
      return result;
    } catch (error) {
      console.error('❌ Failed to list transactions:', error);
      throw error;
    }
  }



  /**
   * Build query with filters (supports both basic and admin filters)
   */
  private buildTransactionsQuery(filters: ListTransactionsFilters | AdminTransactionFilters): SelectQueryBuilder<Transaction> {
    const transactionRepository = AppDataSource.manager.getRepository(Transaction);

    const queryBuilder = transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.fromUser', 'fromUser')
      .leftJoinAndSelect('transaction.toUser', 'toUser');

    // Apply basic filters
    if (filters.fromUserId !== undefined) {
      if (filters.fromUserId === null) {
        queryBuilder.andWhere('transaction.fromUserId IS NULL');
      } else {
        queryBuilder.andWhere('transaction.fromUserId = :fromUserId', { fromUserId: filters.fromUserId });
      }
    }

    if (filters.toUserId !== undefined) {
      queryBuilder.andWhere('transaction.toUserId = :toUserId', { toUserId: filters.toUserId });
    }

    if (filters.type !== undefined) {
      queryBuilder.andWhere('transaction.type = :type', { type: filters.type });
    }

    if (filters.category !== undefined) {
      queryBuilder.andWhere('transaction.category = :category', { category: filters.category });
    }

    // Apply admin-specific filters if present
    const adminFilters = filters as AdminTransactionFilters;

    if (adminFilters.id !== undefined) {
      queryBuilder.andWhere('transaction.id = :id', { id: adminFilters.id });
    }

    if (adminFilters.minAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount >= :minAmount', { minAmount: adminFilters.minAmount });
    }

    if (adminFilters.maxAmount !== undefined) {
      queryBuilder.andWhere('transaction.amount <= :maxAmount', { maxAmount: adminFilters.maxAmount });
    }

    if (adminFilters.createdAfter !== undefined) {
      queryBuilder.andWhere('transaction.createdAt >= :createdAfter', { createdAfter: adminFilters.createdAfter });
    }

    if (adminFilters.createdBefore !== undefined) {
      queryBuilder.andWhere('transaction.createdAt <= :createdBefore', { createdBefore: adminFilters.createdBefore });
    }

    if (adminFilters.search !== undefined && adminFilters.search.trim() !== '') {
      const searchTerm = `%${adminFilters.search.trim()}%`;
      queryBuilder.andWhere(
        '(CAST(transaction.id AS TEXT) LIKE :search OR ' +
        'CAST(transaction.fromUserId AS TEXT) LIKE :search OR ' +
        'CAST(transaction.toUserId AS TEXT) LIKE :search OR ' +
        'CAST(transaction.amount AS TEXT) LIKE :search OR ' +
        'transaction.type LIKE :search OR ' +
        'transaction.category LIKE :search OR ' +
        'CAST(transaction.metadata AS TEXT) LIKE :search)',
        { search: searchTerm }
      );
    }

    return queryBuilder;
  }

  /**
   * Delete a transaction
   */
  async deleteTransaction(id: number): Promise<boolean> {
    try {
      console.log('🗑️ Deleting transaction:', id);

      const transactionRepository = AppDataSource.manager.getRepository(Transaction);

      // Delete the transaction
      const result = await transactionRepository.delete(id);

      const deleted = result.affected !== undefined && result.affected !== null && result.affected > 0;

      if (deleted) {
        console.log('✅ Transaction deleted successfully');
      } else {
        console.log('❌ Transaction not found for deletion');
      }

      return deleted;
    } catch (error) {
      console.error('❌ Failed to delete transaction:', error);
      throw error;
    }
  }
}
