import { En<PERSON>ty<PERSON>ana<PERSON>, Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { ExtendedUser } from '@/entities/ExtendedUser';
import { TransactionsService } from './transactions.service';
import { TransactionCategory, TransactionType } from '@/enums/shared';

export interface CreateExtendedUserDto {
  externalId: number;
  points?: number;
  externalUsername?: string;
}

export interface BulkUpdateExtendedUserDto {
  id: number;
  externalId?: number;
  points?: number;
  externalUsername?: string;
}

export interface BulkUpdateResponse {
  updated: ExtendedUser[];
  failed: {
    id: number;
    error: string;
  }[];
  summary: {
    totalRequested: number;
    successCount: number;
    failureCount: number;
  };
}

export interface ExtendedUserPointsStats {
  totalUsers: number;
  totalPoints: number;
  averagePoints: number;
}

export interface ExtendedUserQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'externalId' | 'points' | 'externalUsername' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters Actually Used in Component
  externalId?: number; // Exact match
  minPoints?: number; // Minimum points amount
  maxPoints?: number; // Maximum points amount

  // Additional Parameters
  points?: number; // Exact points amount
  externalUsername?: string; // Exact external username match
  search?: string; // General search term (searches externalId and externalUsername)
}

export interface ExtendedUserListResponse {
  extendedUsers: ExtendedUser[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class ExtendedUserService {
  private extendedUserRepository: Repository<ExtendedUser>;
  private transactionsService: TransactionsService;

  constructor() {
    this.extendedUserRepository = AppDataSource.getRepository(ExtendedUser);
    this.transactionsService = new TransactionsService();
  }

  async createExtendedUser(createExtendedUserDto: CreateExtendedUserDto): Promise<ExtendedUser> {
    const extendedUser = this.extendedUserRepository.create(createExtendedUserDto);
    return await this.extendedUserRepository.save(extendedUser);
  }

  async findExtendedUserById(id: number): Promise<ExtendedUser | null> {
    return await this.extendedUserRepository.findOne({ where: { id } });
  }

  async findExtendedUserByExternalId(externalId: number): Promise<ExtendedUser | null> {
    return await this.extendedUserRepository.findOne({ where: { externalId } });
  }

  async findAllExtendedUsers(): Promise<ExtendedUser[]> {
    return await this.extendedUserRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findExtendedUsersWithQuery(queryParams: ExtendedUserQueryParams): Promise<ExtendedUserListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      // Filters
      externalId,
      minPoints,
      maxPoints,
      points,
      externalUsername,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.extendedUserRepository.createQueryBuilder('extendedUser');

    // Apply filters
    if (externalId !== undefined) {
      queryBuilder.andWhere('extendedUser.externalId = :externalId', { externalId });
    }

    if (points !== undefined) {
      queryBuilder.andWhere('extendedUser.points = :points', { points });
    }

    if (minPoints !== undefined) {
      queryBuilder.andWhere('extendedUser.points >= :minPoints', { minPoints });
    }

    if (maxPoints !== undefined) {
      queryBuilder.andWhere('extendedUser.points <= :maxPoints', { maxPoints });
    }

    if (externalUsername !== undefined) {
      queryBuilder.andWhere('extendedUser.externalUsername = :externalUsername', { externalUsername });
    }

    // General search term (searches externalId and externalUsername)
    if (search) {
      queryBuilder.andWhere(
        '(CAST(extendedUser.externalId AS TEXT) ILIKE :search OR extendedUser.externalUsername ILIKE :search)',
        {
          search: `%${search}%`,
        }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`extendedUser.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const extendedUsers = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      extendedUsers,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async updateExtendedUser(id: number, updateData: Partial<CreateExtendedUserDto>): Promise<ExtendedUser | null> {
    await this.extendedUserRepository.update(id, updateData);
    return await this.findExtendedUserById(id);
  }

  async deleteExtendedUser(id: number): Promise<boolean> {
    const result = await this.extendedUserRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async bulkUpdateExtendedUsers(updates: BulkUpdateExtendedUserDto[]): Promise<BulkUpdateResponse> {
    const updated: ExtendedUser[] = [];
    const failed: { id: number; error: string }[] = [];

    // Use transaction to ensure atomicity
    await AppDataSource.transaction(async (manager) => {
      const extendedUserRepo = manager.getRepository(ExtendedUser);

      for (const updateData of updates) {
        try {
          const { id, ...fieldsToUpdate } = updateData;

          // Validate ID
          if (!id || id <= 0) {
            failed.push({ id: id || 0, error: 'Invalid ID provided' });
            continue;
          }

          // Check if user exists
          const existingUser = await extendedUserRepo.findOne({ where: { id } });
          if (!existingUser) {
            failed.push({ id, error: 'Extended user not found' });
            continue;
          }

          // Validate externalId uniqueness if being updated
          if (fieldsToUpdate.externalId !== undefined) {
            if (fieldsToUpdate.externalId <= 0) {
              failed.push({ id, error: 'External ID must be a positive number' });
              continue;
            }

            const existingUserWithExternalId = await extendedUserRepo.findOne({
              where: { externalId: fieldsToUpdate.externalId },
            });

            if (existingUserWithExternalId && existingUserWithExternalId.id !== id) {
              failed.push({
                id,
                error: `External ID ${fieldsToUpdate.externalId} already exists for another user`,
              });
              continue;
            }
          }

          // Validate points if being updated
          if (fieldsToUpdate.points !== undefined && fieldsToUpdate.points < 0) {
            failed.push({ id, error: 'Points must be non-negative' });
            continue;
          }

          // Perform update
          await extendedUserRepo.update(id, fieldsToUpdate);

          // Fetch updated user
          const updatedUser = await extendedUserRepo.findOne({ where: { id } });
          if (updatedUser) {
            updated.push(updatedUser);
          }
        } catch (error) {
          failed.push({
            id: updateData.id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    });

    return {
      updated,
      failed,
      summary: {
        totalRequested: updates.length,
        successCount: updated.length,
        failureCount: failed.length,
      },
    };
  }

  async chargePoints(
    userId: number,
    amount: number,
    category: TransactionCategory,
    metadata?: Record<string, any>,
    tx?: EntityManager,
  ): Promise<void> {
    const _tx = tx || AppDataSource.manager;

    await _tx.transaction(async (tx) => {
      const userRepository = tx.getRepository(ExtendedUser);

      await this.transactionsService.createTransaction(
        {
          toUserId: userId,
          type: TransactionType.CHARGE,
          category,
          amount,
          metadata: metadata || {},
        },
        tx,
      );

      const result = await userRepository
        .createQueryBuilder()
        .update()
        .set({ points: () => `"points" - ${amount}` })
        .where('externalId = :id AND "points" >= :amount', { id: userId, amount })
        .execute();

      if (result.affected === 0) {
        throw new Error('Insufficient balance');
      }
    });
  }

  async depositPoints(
    userId: number,
    amount: number,
    category: TransactionCategory,
    metadata?: Record<string, any>,
    tx?: EntityManager,
  ): Promise<void> {
    const _tx = tx || AppDataSource.manager;

    await _tx.transaction(async (tx) => {
      const userRepository = tx.getRepository(ExtendedUser);

      await this.transactionsService.createTransaction(
        {
          toUserId: userId,
          type: TransactionType.DEPOSIT,
          category,
          amount,
          metadata: metadata || {},
        },
        tx,
      );

      const result = await userRepository
        .createQueryBuilder()
        .update()
        .set({ points: () => `"points" + ${amount}` })
        .where('externalId = :id', { id: userId })
        .execute();

      if (result.affected === 0) {
        throw new Error('User not found');
      }
    });
  }

  async refundPoints(
    userId: number,
    amount: number,
    metadata?: Record<string, any>,
    tx?: EntityManager,
  ): Promise<void> {
    const _tx = tx || AppDataSource.manager;

    await _tx.transaction(async (tx) => {
      const userRepository = tx.getRepository(ExtendedUser);

      await this.transactionsService.createTransaction(
        {
          toUserId: userId,
          type: TransactionType.REFUND,
          category: TransactionCategory.MARKET_PURCHASE,
          amount,
          metadata: metadata || {},
        },
        tx,
      );

      const result = await userRepository
        .createQueryBuilder()
        .update()
        .set({ points: () => `"points" + ${amount}` })
        .where('externalId = :id', { id: userId })
        .execute();

      if (result.affected === 0) {
        throw new Error('User not found');
      }
    });
  }

  async updateExtendedUserWithTransaction(
    id: number,
    updateData: Partial<CreateExtendedUserDto>,
    pointsDifference: number,
    adminMetadata: Record<string, any>,
  ): Promise<ExtendedUser | null> {
    return await AppDataSource.transaction(async (tx) => {
      const userRepository = tx.getRepository(ExtendedUser);

      // Get the user to update
      const user = await userRepository.findOne({ where: { id } });
      if (!user) {
        throw new Error('User not found');
      }

      // If there's a points change, create a transaction record
      if (pointsDifference !== 0) {
        const transactionType = pointsDifference > 0 ? TransactionType.DEPOSIT : TransactionType.CHARGE;
        const amount = Math.abs(pointsDifference);

        await this.transactionsService.createTransaction(
          {
            toUserId: user.externalId,
            type: transactionType,
            category: TransactionCategory.ADMIN_ADJUSTMENT,
            amount,
            metadata: {
              ...adminMetadata,
              pointsDifference,
              adjustmentType: pointsDifference > 0 ? 'increase' : 'decrease',
            },
          },
          tx,
        );
      }

      // Update the user
      await userRepository.update(id, updateData);

      // Return the updated user
      return await userRepository.findOne({ where: { id } });
    });
  }

  /**
   * Get extended user points statistics
   */
  async getPointsStats(): Promise<ExtendedUserPointsStats> {
    try {
      console.log('📊 Getting extended user points statistics');

      const userRepository = AppDataSource.manager.getRepository(ExtendedUser);

      // Get total users count and total points sum
      const stats = await userRepository
        .createQueryBuilder('user')
        .select('COUNT(*)', 'totalUsers')
        .addSelect('COALESCE(SUM(user.points), 0)', 'totalPoints')
        .addSelect('COALESCE(AVG(user.points), 0)', 'averagePoints')
        .getRawOne();

      const result: ExtendedUserPointsStats = {
        totalUsers: parseInt(stats.totalUsers) || 0,
        totalPoints: parseInt(stats.totalPoints) || 0,
        averagePoints: parseFloat(parseFloat(stats.averagePoints).toFixed(2)) || 0,
      };

      console.log('✅ Extended user points statistics retrieved successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to get extended user points statistics:', error);
      throw error;
    }
  }
}
