import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { MissionObjectiveAssignment } from '@/entities/MissionObjectiveAssignment';
import { CompareOperator } from '@/enums/shared';

export interface CreateMissionObjectiveAssignmentDto {
  userId: number;
  missionObjectiveId: number;
  progress?: number;
  lastCheckedRecordTimestamp?: number | null;
  isCompleted?: boolean;
  startDate?: number | null;
  endDate?: number | null;
}

export interface MissionObjectiveAssignmentQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'userId' | 'missionObjectiveId' | 'progress' | 'lastCheckedRecordTimestamp' | 'startDate' | 'endDate' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters
  userId?: number;
  missionObjectiveId?: number;
  progressMin?: number;
  progressMax?: number;

  // Date range filters
  createdAtFrom?: number;
  createdAtTo?: number;
  updatedAtFrom?: number;
  updatedAtTo?: number;
  lastCheckedFrom?: number;
  lastCheckedTo?: number;
  startDateFrom?: number;
  startDateTo?: number;
  endDateFrom?: number;
  endDateTo?: number;

  // Additional Parameters
  search?: string; // General search term (searches userId and missionObjectiveId)
}

export interface MissionObjectiveAssignmentListResponse {
  missionObjectiveAssignments: MissionObjectiveAssignment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Helper function to calculate if an objective assignment is complete
const calculateIsCompleted = (assignment: MissionObjectiveAssignment): boolean => {
  const { progress, missionObjective } = assignment;

  if (!missionObjective) {
    return false;
  }

  const targetValue = parseInt(missionObjective.targetValue || '0');
  const operator = missionObjective.operator;

  switch (operator) {
    case CompareOperator.EQUAL:
      return progress === targetValue;
    case CompareOperator.NOT_EQUAL:
      return progress !== targetValue;
    case CompareOperator.GREATER_THAN:
      return progress > targetValue;
    case CompareOperator.LESS_THAN:
      return progress < targetValue;
    case CompareOperator.GREATER_EQUAL:
      return progress >= targetValue;
    case CompareOperator.LESS_EQUAL:
      return progress <= targetValue;
    default:
      return false;
  }
};

// Helper function to add isCompleted field to assignment(s)
const addIsCompletedField = (assignment: MissionObjectiveAssignment): MissionObjectiveAssignment => {
  const isCompleted = calculateIsCompleted(assignment);
  return { ...assignment, isCompleted };
};

const addIsCompletedFieldToArray = (assignments: MissionObjectiveAssignment[]): MissionObjectiveAssignment[] => {
  return assignments.map(addIsCompletedField);
};

export class MissionObjectiveAssignmentService {
  private missionObjectiveAssignmentRepository: Repository<MissionObjectiveAssignment>;

  constructor() {
    this.missionObjectiveAssignmentRepository = AppDataSource.getRepository(MissionObjectiveAssignment);
  }

  async createMissionObjectiveAssignment(createMissionObjectiveAssignmentDto: CreateMissionObjectiveAssignmentDto): Promise<MissionObjectiveAssignment> {
    const missionObjectiveAssignment = this.missionObjectiveAssignmentRepository.create(createMissionObjectiveAssignmentDto);
    const savedAssignment = await this.missionObjectiveAssignmentRepository.save(missionObjectiveAssignment);

    // Fetch the complete assignment with relations to calculate isCompleted
    const completeAssignment = await this.findMissionObjectiveAssignmentById(savedAssignment.id);
    return completeAssignment!;
  }

  async findMissionObjectiveAssignmentById(id: number): Promise<MissionObjectiveAssignment | null> {
    const assignment = await this.missionObjectiveAssignmentRepository.findOne({
      where: { id },
      relations: ['missionObjective', 'user', 'missionObjective.mission']
    });
    return assignment ? addIsCompletedField(assignment) : null;
  }

  async findMissionObjectiveAssignmentByUserAndObjective(userId: number, missionObjectiveId: number): Promise<MissionObjectiveAssignment | null> {
    const assignment = await this.missionObjectiveAssignmentRepository.findOne({
      where: { userId, missionObjectiveId },
      relations: ['missionObjective', 'user', 'missionObjective.mission']
    });
    return assignment ? addIsCompletedField(assignment) : null;
  }

  async findAllMissionObjectiveAssignments(): Promise<MissionObjectiveAssignment[]> {
    return await this.missionObjectiveAssignmentRepository.find({
      order: { createdAt: 'DESC' },
      relations: ['missionObjective', 'user', 'missionObjective.mission']
    });
  }

  async findMissionObjectiveAssignmentsWithQuery(queryParams: MissionObjectiveAssignmentQueryParams): Promise<MissionObjectiveAssignmentListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      userId,
      missionObjectiveId,
      progressMin,
      progressMax,
      createdAtFrom,
      createdAtTo,
      updatedAtFrom,
      updatedAtTo,
      lastCheckedFrom,
      lastCheckedTo,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.missionObjectiveAssignmentRepository.createQueryBuilder('missionObjectiveAssignment')
      .leftJoinAndSelect('missionObjectiveAssignment.missionObjective', 'missionObjective')
      .leftJoinAndSelect('missionObjectiveAssignment.user', 'user')
      .leftJoinAndSelect('missionObjective.mission', 'mission');

    // Apply filters
    if (userId !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.userId = :userId', { userId });
    }

    if (missionObjectiveId !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.missionObjectiveId = :missionObjectiveId', { missionObjectiveId });
    }

    if (progressMin !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.progress >= :progressMin', { progressMin });
    }

    if (progressMax !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.progress <= :progressMax', { progressMax });
    }

    // Date range filters
    if (createdAtFrom !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionObjectiveAssignment.createdAt) >= :createdAtFrom', { createdAtFrom });
    }

    if (createdAtTo !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionObjectiveAssignment.createdAt) <= :createdAtTo', { createdAtTo });
    }

    if (updatedAtFrom !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionObjectiveAssignment.updatedAt) >= :updatedAtFrom', { updatedAtFrom });
    }

    if (updatedAtTo !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionObjectiveAssignment.updatedAt) <= :updatedAtTo', { updatedAtTo });
    }

    if (lastCheckedFrom !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.lastCheckedRecordTimestamp >= :lastCheckedFrom', { lastCheckedFrom });
    }

    if (lastCheckedTo !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.lastCheckedRecordTimestamp <= :lastCheckedTo', { lastCheckedTo });
    }

    if (startDateFrom !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.startDate >= :startDateFrom', { startDateFrom });
    }

    if (startDateTo !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.startDate <= :startDateTo', { startDateTo });
    }

    if (endDateFrom !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.endDate >= :endDateFrom', { endDateFrom });
    }

    if (endDateTo !== undefined) {
      queryBuilder.andWhere('missionObjectiveAssignment.endDate <= :endDateTo', { endDateTo });
    }

    // General search term (searches userId and missionObjectiveId as strings)
    if (search) {
      queryBuilder.andWhere(
        '(CAST(missionObjectiveAssignment.userId AS TEXT) ILIKE :search OR CAST(missionObjectiveAssignment.missionObjectiveId AS TEXT) ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`missionObjectiveAssignment.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const missionObjectiveAssignments = await queryBuilder.getMany();

    // Add isCompleted field to all assignments
    const assignmentsWithCompleted = addIsCompletedFieldToArray(missionObjectiveAssignments);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      missionObjectiveAssignments: assignmentsWithCompleted,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findAssignmentsByUserId(userId: number): Promise<MissionObjectiveAssignment[]> {
    const assignments = await this.missionObjectiveAssignmentRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      relations: ['missionObjective', 'user', 'missionObjective.mission']
    });
    return addIsCompletedFieldToArray(assignments);
  }

  async findAssignmentsByMissionObjectiveId(missionObjectiveId: number): Promise<MissionObjectiveAssignment[]> {
    const assignments = await this.missionObjectiveAssignmentRepository.find({
      where: { missionObjectiveId },
      order: { createdAt: 'DESC' },
      relations: ['missionObjective', 'user', 'missionObjective.mission']
    });
    return addIsCompletedFieldToArray(assignments);
  }

  async findAssignmentsByMissionId(missionId: number): Promise<MissionObjectiveAssignment[]> {
    const assignments = await this.missionObjectiveAssignmentRepository
      .createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.missionObjective', 'objective')
      .leftJoinAndSelect('assignment.user', 'user')
      .leftJoinAndSelect('objective.mission', 'mission')
      .where('mission.id = :missionId', { missionId })
      .orderBy('assignment.createdAt', 'DESC')
      .getMany();
    return addIsCompletedFieldToArray(assignments);
  }

  async updateMissionObjectiveAssignment(id: number, updateData: Partial<CreateMissionObjectiveAssignmentDto>): Promise<MissionObjectiveAssignment | null> {
    await this.missionObjectiveAssignmentRepository.update(id, updateData);
    return await this.findMissionObjectiveAssignmentById(id);
  }

  async deleteMissionObjectiveAssignment(id: number): Promise<boolean> {
    const result = await this.missionObjectiveAssignmentRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async deleteMissionObjectiveAssignmentByUserAndObjective(userId: number, missionObjectiveId: number): Promise<boolean> {
    const result = await this.missionObjectiveAssignmentRepository.delete({ userId, missionObjectiveId });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async updateProgress(id: number, progress: number, lastCheckedTimestamp?: number): Promise<MissionObjectiveAssignment | null> {
    const updateData: Partial<CreateMissionObjectiveAssignmentDto> = { progress };
    if (lastCheckedTimestamp !== undefined) {
      updateData.lastCheckedRecordTimestamp = lastCheckedTimestamp;
    }
    return await this.updateMissionObjectiveAssignment(id, updateData);
  }

  async countAssignmentsByMissionObjectiveId(missionObjectiveId: number): Promise<number> {
    return await this.missionObjectiveAssignmentRepository.count({ where: { missionObjectiveId } });
  }

  async countAssignmentsByUserId(userId: number): Promise<number> {
    return await this.missionObjectiveAssignmentRepository.count({ where: { userId } });
  }

  async getProgressStatsByMissionId(missionId: number): Promise<{
    totalAssignments: number;
    averageProgress: number;
    completedObjectives: number;
    objectiveBreakdown: Array<{
      objectiveId: number;
      objectiveName: string;
      totalAssignments: number;
      averageProgress: number;
    }>;
  }> {
    const assignments = await this.findAssignmentsByMissionId(missionId);

    const totalAssignments = assignments.length;
    const averageProgress = totalAssignments > 0
      ? assignments.reduce((sum, a) => sum + a.progress, 0) / totalAssignments
      : 0;

    // Group by objective
    const objectiveMap = new Map();
    assignments.forEach(assignment => {
      const objId = assignment.missionObjectiveId;
      if (!objectiveMap.has(objId)) {
        objectiveMap.set(objId, {
          objectiveId: objId,
          objectiveName: assignment.missionObjective?.description || `Objective ${objId}`,
          assignments: [],
        });
      }
      objectiveMap.get(objId).assignments.push(assignment);
    });

    const objectiveBreakdown = Array.from(objectiveMap.values()).map(obj => ({
      objectiveId: obj.objectiveId,
      objectiveName: obj.objectiveName,
      totalAssignments: obj.assignments.length,
      averageProgress: obj.assignments.length > 0
        ? obj.assignments.reduce((sum: number, a: any) => sum + a.progress, 0) / obj.assignments.length
        : 0,
    }));

    // Count completed objectives using the isCompleted field
    const completedObjectives = assignments.filter(a => a.isCompleted).length;

    return {
      totalAssignments,
      averageProgress: Math.round(averageProgress * 100) / 100,
      completedObjectives,
      objectiveBreakdown,
    };
  }
}
