import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { MissionParticipation } from '@/entities/MissionParticipation';

export interface CreateMissionParticipationDto {
  userId: number;
  missionId: number;
  isCompleted?: boolean;
}

export interface MissionParticipationQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'userId' | 'missionId' | 'isCompleted' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters
  userId?: number;
  missionId?: number;
  isCompleted?: boolean;
  
  // Date range filters
  createdAtFrom?: number;
  createdAtTo?: number;
  updatedAtFrom?: number;
  updatedAtTo?: number;
  
  // Additional Parameters
  search?: string; // General search term (searches userId and missionId)
}

export interface MissionParticipationListResponse {
  missionParticipations: MissionParticipation[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class MissionParticipationService {
  private missionParticipationRepository: Repository<MissionParticipation>;

  constructor() {
    this.missionParticipationRepository = AppDataSource.getRepository(MissionParticipation);
  }

  async createMissionParticipation(createMissionParticipationDto: CreateMissionParticipationDto): Promise<MissionParticipation> {
    const missionParticipation = this.missionParticipationRepository.create(createMissionParticipationDto);
    return await this.missionParticipationRepository.save(missionParticipation);
  }

  async findMissionParticipationById(id: number): Promise<MissionParticipation | null> {
    return await this.missionParticipationRepository.findOne({
      where: { id },
      relations: ['mission', 'user']
    });
  }

  async findMissionParticipationByUserAndMission(userId: number, missionId: number): Promise<MissionParticipation | null> {
    return await this.missionParticipationRepository.findOne({
      where: { userId, missionId },
      relations: ['mission', 'user']
    });
  }

  async findLatestMissionParticipationByUserAndMission(userId: number, missionId: number): Promise<MissionParticipation | null> {
    return await this.missionParticipationRepository.findOne({
      where: { userId, missionId },
      order: { createdAt: 'DESC' },
      relations: ['mission', 'user']
    });
  }

  async findAllMissionParticipations(): Promise<MissionParticipation[]> {
    return await this.missionParticipationRepository.find({
      order: { createdAt: 'DESC' },
      relations: ['mission', 'user']
    });
  }

  async findMissionParticipationsWithQuery(queryParams: MissionParticipationQueryParams): Promise<MissionParticipationListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      userId,
      missionId,
      isCompleted,
      createdAtFrom,
      createdAtTo,
      updatedAtFrom,
      updatedAtTo,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.missionParticipationRepository.createQueryBuilder('missionParticipation')
      .leftJoinAndSelect('missionParticipation.mission', 'mission')
      .leftJoinAndSelect('missionParticipation.user', 'user');

    // Apply filters
    if (userId !== undefined) {
      queryBuilder.andWhere('missionParticipation.userId = :userId', { userId });
    }

    if (missionId !== undefined) {
      queryBuilder.andWhere('missionParticipation.missionId = :missionId', { missionId });
    }

    if (isCompleted !== undefined) {
      queryBuilder.andWhere('missionParticipation.isCompleted = :isCompleted', { isCompleted });
    }

    // Date range filters
    if (createdAtFrom !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionParticipation.createdAt) >= :createdAtFrom', { createdAtFrom });
    }

    if (createdAtTo !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionParticipation.createdAt) <= :createdAtTo', { createdAtTo });
    }

    if (updatedAtFrom !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionParticipation.updatedAt) >= :updatedAtFrom', { updatedAtFrom });
    }

    if (updatedAtTo !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionParticipation.updatedAt) <= :updatedAtTo', { updatedAtTo });
    }

    // General search term (searches userId and missionId as strings)
    if (search) {
      queryBuilder.andWhere(
        '(CAST(missionParticipation.userId AS TEXT) ILIKE :search OR CAST(missionParticipation.missionId AS TEXT) ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`missionParticipation.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const missionParticipations = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      missionParticipations,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findParticipationsByUserId(userId: number): Promise<MissionParticipation[]> {
    return await this.missionParticipationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      relations: ['mission']
    });
  }

  async findParticipationsByMissionId(missionId: number): Promise<MissionParticipation[]> {
    return await this.missionParticipationRepository.find({
      where: { missionId },
      order: { createdAt: 'DESC' },
      relations: ['mission']
    });
  }

  async findCompletedParticipationsByUserId(userId: number): Promise<MissionParticipation[]> {
    return await this.missionParticipationRepository.find({
      where: { userId, isCompleted: true },
      order: { createdAt: 'DESC' },
      relations: ['mission']
    });
  }

  async updateMissionParticipation(id: number, updateData: Partial<CreateMissionParticipationDto>): Promise<MissionParticipation | null> {
    await this.missionParticipationRepository.update(id, updateData);
    return await this.findMissionParticipationById(id);
  }

  async deleteMissionParticipation(id: number): Promise<boolean> {
    const result = await this.missionParticipationRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async markParticipationAsCompleted(id: number): Promise<MissionParticipation | null> {
    return await this.updateMissionParticipation(id, { isCompleted: true });
  }

  async markParticipationAsIncomplete(id: number): Promise<MissionParticipation | null> {
    return await this.updateMissionParticipation(id, { isCompleted: false });
  }
}
