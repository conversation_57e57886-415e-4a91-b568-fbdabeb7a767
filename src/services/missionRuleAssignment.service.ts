import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { MissionRuleAssignment } from '@/entities/MissionRuleAssignment';

export interface CreateMissionRuleAssignmentDto {
  missionId: number;
  missionRuleId: number;
}

export interface MissionRuleAssignmentQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'missionId' | 'missionRuleId' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters
  missionId?: number;
  missionRuleId?: number;
  
  // Date range filters
  createdAtFrom?: number;
  createdAtTo?: number;
  updatedAtFrom?: number;
  updatedAtTo?: number;
  
  // Additional Parameters
  search?: string; // General search term (searches missionId and missionRuleId)
}

export interface MissionRuleAssignmentListResponse {
  missionRuleAssignments: MissionRuleAssignment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class MissionRuleAssignmentService {
  private missionRuleAssignmentRepository: Repository<MissionRuleAssignment>;

  constructor() {
    this.missionRuleAssignmentRepository = AppDataSource.getRepository(MissionRuleAssignment);
  }

  async createMissionRuleAssignment(createMissionRuleAssignmentDto: CreateMissionRuleAssignmentDto): Promise<MissionRuleAssignment> {
    const missionRuleAssignment = this.missionRuleAssignmentRepository.create(createMissionRuleAssignmentDto);
    return await this.missionRuleAssignmentRepository.save(missionRuleAssignment);
  }

  async findMissionRuleAssignmentById(id: number): Promise<MissionRuleAssignment | null> {
    return await this.missionRuleAssignmentRepository.findOne({ 
      where: { id },
      relations: ['mission', 'missionRule']
    });
  }

  async findMissionRuleAssignmentByMissionAndRule(missionId: number, missionRuleId: number): Promise<MissionRuleAssignment | null> {
    return await this.missionRuleAssignmentRepository.findOne({ 
      where: { missionId, missionRuleId },
      relations: ['mission', 'missionRule']
    });
  }

  async findAllMissionRuleAssignments(): Promise<MissionRuleAssignment[]> {
    return await this.missionRuleAssignmentRepository.find({
      order: { createdAt: 'DESC' },
      relations: ['mission', 'missionRule']
    });
  }

  async findMissionRuleAssignmentsWithQuery(queryParams: MissionRuleAssignmentQueryParams): Promise<MissionRuleAssignmentListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      missionId,
      missionRuleId,
      createdAtFrom,
      createdAtTo,
      updatedAtFrom,
      updatedAtTo,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.missionRuleAssignmentRepository.createQueryBuilder('missionRuleAssignment')
      .leftJoinAndSelect('missionRuleAssignment.mission', 'mission')
      .leftJoinAndSelect('missionRuleAssignment.missionRule', 'missionRule');

    // Apply filters
    if (missionId !== undefined) {
      queryBuilder.andWhere('missionRuleAssignment.missionId = :missionId', { missionId });
    }

    if (missionRuleId !== undefined) {
      queryBuilder.andWhere('missionRuleAssignment.missionRuleId = :missionRuleId', { missionRuleId });
    }

    // Date range filters
    if (createdAtFrom !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionRuleAssignment.createdAt) >= :createdAtFrom', { createdAtFrom });
    }

    if (createdAtTo !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionRuleAssignment.createdAt) <= :createdAtTo', { createdAtTo });
    }

    if (updatedAtFrom !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionRuleAssignment.updatedAt) >= :updatedAtFrom', { updatedAtFrom });
    }

    if (updatedAtTo !== undefined) {
      queryBuilder.andWhere('EXTRACT(EPOCH FROM missionRuleAssignment.updatedAt) <= :updatedAtTo', { updatedAtTo });
    }

    // General search term (searches missionId and missionRuleId as strings)
    if (search) {
      queryBuilder.andWhere(
        '(CAST(missionRuleAssignment.missionId AS TEXT) ILIKE :search OR CAST(missionRuleAssignment.missionRuleId AS TEXT) ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`missionRuleAssignment.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const missionRuleAssignments = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      missionRuleAssignments,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findAssignmentsByMissionId(missionId: number): Promise<MissionRuleAssignment[]> {
    return await this.missionRuleAssignmentRepository.find({
      where: { missionId },
      order: { createdAt: 'DESC' },
      relations: ['mission', 'missionRule']
    });
  }

  async findAssignmentsByMissionRuleId(missionRuleId: number): Promise<MissionRuleAssignment[]> {
    return await this.missionRuleAssignmentRepository.find({
      where: { missionRuleId },
      order: { createdAt: 'DESC' },
      relations: ['mission', 'missionRule']
    });
  }

  async updateMissionRuleAssignment(id: number, updateData: Partial<CreateMissionRuleAssignmentDto>): Promise<MissionRuleAssignment | null> {
    await this.missionRuleAssignmentRepository.update(id, updateData);
    return await this.findMissionRuleAssignmentById(id);
  }

  async deleteMissionRuleAssignment(id: number): Promise<boolean> {
    const result = await this.missionRuleAssignmentRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async deleteMissionRuleAssignmentByMissionAndRule(missionId: number, missionRuleId: number): Promise<boolean> {
    const result = await this.missionRuleAssignmentRepository.delete({ missionId, missionRuleId });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async countAssignmentsByMissionId(missionId: number): Promise<number> {
    return await this.missionRuleAssignmentRepository.count({ where: { missionId } });
  }

  async countAssignmentsByMissionRuleId(missionRuleId: number): Promise<number> {
    return await this.missionRuleAssignmentRepository.count({ where: { missionRuleId } });
  }
}
