import { HandlerUtils } from '@/handlers';

/**
 * Balance data interface matching the PG Dagur API response
 */
export interface BalanceData {
  mainBalance: { [currency: string]: number };
  sportBonusBalance: { [currency: string]: number };
  sportFreebetBalance: { [currency: string]: number };
  casinoBonusBalance: { [currency: string]: number };
}

/**
 * Balance service result interface
 */
export interface BalanceServiceResult {
  success: boolean;
  balance?: number;
  error?: string;
  rawData?: BalanceData;
}

/**
 * Service class for fetching customer balance data from PG Dagur API
 *
 * This service provides a unified interface for fetching total customer balance
 * for both daily and hourly reports, with proper date formatting and error handling.
 *
 * IMPLEMENTATION NOTES:
 * ====================
 *
 * API Endpoint: /api/pg-dagur/v1/internal/customers/balances
 * - Uses existing PG Dagur balance endpoint that was already implemented
 * - Requires endDate parameter in ISO format
 * - Returns balance data for all currencies, we extract TRY balance
 *
 * Date Formatting:
 * - Daily reports: Format target date to end of day (23:59:59.999Z)
 * - Hourly reports: Use baseDate as-is (current hour timestamp)
 *
 * Error Handling:
 * - Network timeouts (30 second limit)
 * - API response validation
 * - Balance data structure validation
 * - Fallback to 0 balance on any error
 *
 * Performance:
 * - Request timing and monitoring
 * - Detailed logging with unique request IDs
 * - Currency breakdown logging for debugging
 *
 * USAGE:
 * ======
 *
 * Daily Reports:
 * ```typescript
 * const result = await BalanceService.fetchDailyBalance('2025-07-19');
 * if (result.success) {
 *   console.log(`Balance: ${result.balance}`);
 * }
 * ```
 *
 * Hourly Reports:
 * ```typescript
 * const result = await BalanceService.fetchHourlyBalance('2025-07-19T14:30:00.000Z');
 * if (result.success) {
 *   console.log(`Balance: ${result.balance}`);
 * }
 * ```
 */
export class BalanceService {
  private static readonly BASE_URL = process.env['BASE_URL'] || 'http://localhost:3000';
  private static readonly BALANCE_ENDPOINT = '/api/pg-dagur/v1/internal/customers/balances';

  /**
   * Fetch total customer balance for daily reports
   * 
   * @param targetDate - The target date for the daily report
   * @returns Balance service result with TRY balance amount
   */
  static async fetchDailyBalance(targetDate: string): Promise<BalanceServiceResult> {
    try {
      // Format date to end of day (23:59:59) for daily reports
      const endDate = this.formatDateForDaily(targetDate);
      
      HandlerUtils.logActivity('BalanceService', 
        `Fetching daily balance for date: ${targetDate} (formatted as: ${endDate})`);

      return await this.makeBalanceRequest(endDate);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      HandlerUtils.logError('BalanceService', `Failed to fetch daily balance: ${errorMessage}`);
      
      return {
        success: false,
        error: `Daily balance fetch failed: ${errorMessage}`,
      };
    }
  }

  /**
   * Fetch total customer balance for hourly reports
   * 
   * @param baseDate - The base date/time for the hourly report
   * @returns Balance service result with TRY balance amount
   */
  static async fetchHourlyBalance(baseDate: string): Promise<BalanceServiceResult> {
    try {
      // Use the baseDate directly for hourly reports (should be current hour)
      const endDate = this.formatDateForHourly(baseDate);
      
      HandlerUtils.logActivity('BalanceService', 
        `Fetching hourly balance for baseDate: ${baseDate} (formatted as: ${endDate})`);

      return await this.makeBalanceRequest(endDate);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      HandlerUtils.logError('BalanceService', `Failed to fetch hourly balance: ${errorMessage}`);
      
      return {
        success: false,
        error: `Hourly balance fetch failed: ${errorMessage}`,
      };
    }
  }

  /**
   * Make the actual balance API request
   *
   * @param endDate - Formatted end date for the balance query
   * @returns Balance service result
   */
  private static async makeBalanceRequest(endDate: string): Promise<BalanceServiceResult> {
    const startTime = Date.now();
    let requestId = `balance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      const url = `${this.BASE_URL}${this.BALANCE_ENDPOINT}?endDate=${encodeURIComponent(endDate)}`;

      HandlerUtils.logActivity('BalanceService',
        `[${requestId}] Starting balance API request to: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 second timeout
      });

      const requestDuration = Date.now() - startTime;
      HandlerUtils.logActivity('BalanceService',
        `[${requestId}] Balance API response received in ${requestDuration}ms with status: ${response.status}`);

      if (!response.ok) {
        const errorMessage = `Balance API request failed with status: ${response.status} ${response.statusText}`;
        HandlerUtils.logError('BalanceService', `[${requestId}] ${errorMessage}`);
        throw new Error(errorMessage);
      }

      const responseData = await response.json() as any;
      HandlerUtils.logActivity('BalanceService',
        `[${requestId}] Balance API response parsed successfully`);

      if (!responseData.success) {
        const errorMessage = `Balance API returned error: ${responseData.message || 'Unknown error'}`;
        HandlerUtils.logError('BalanceService', `[${requestId}] ${errorMessage}`);
        throw new Error(errorMessage);
      }

      const balanceData: BalanceData = responseData.data;

      // Validate balance data structure
      if (!balanceData || typeof balanceData !== 'object') {
        throw new Error('Invalid balance data structure received from API');
      }

      if (!balanceData.mainBalance || typeof balanceData.mainBalance !== 'object') {
        throw new Error('Missing or invalid mainBalance in API response');
      }

      // Extract TRY balance from mainBalance
      const tryBalance = balanceData.mainBalance?.TRY || 0;

      // Validate that the balance is a valid number
      if (typeof tryBalance !== 'number' || isNaN(tryBalance)) {
        throw new Error(`Invalid TRY balance value: ${tryBalance}`);
      }
      
      const totalRequestDuration = Date.now() - startTime;
      HandlerUtils.logActivity('BalanceService',
        `[${requestId}] Balance API request completed successfully in ${totalRequestDuration}ms. TRY balance: ${tryBalance}`);

      // Log additional balance details for monitoring
      const otherCurrencies = Object.keys(balanceData.mainBalance).filter(c => c !== 'TRY');
      if (otherCurrencies.length > 0) {
        HandlerUtils.logActivity('BalanceService',
          `[${requestId}] Other currency balances available: ${otherCurrencies.join(', ')}`);
      }

      return {
        success: true,
        balance: tryBalance,
        rawData: balanceData,
      };
    } catch (error) {
      const totalRequestDuration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      HandlerUtils.logError('BalanceService',
        `[${requestId}] Balance API request failed after ${totalRequestDuration}ms: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Format date for daily reports (end of day: 23:59:59)
   * 
   * @param dateString - Input date string
   * @returns Formatted date string for daily balance query
   */
  private static formatDateForDaily(dateString: string): string {
    try {
      const date = new Date(dateString);
      
      // Set to end of day (23:59:59.999)
      date.setHours(23, 59, 59, 999);
      
      return date.toISOString();
    } catch (error) {
      HandlerUtils.logError('BalanceService', `Failed to format daily date: ${dateString}`);
      throw new Error(`Invalid date format for daily balance: ${dateString}`);
    }
  }

  /**
   * Format date for hourly reports (use baseDate as-is)
   * 
   * @param baseDateString - Input base date string
   * @returns Formatted date string for hourly balance query
   */
  private static formatDateForHourly(baseDateString: string): string {
    try {
      const date = new Date(baseDateString);
      
      // Validate the date
      if (isNaN(date.getTime())) {
        throw new Error(`Invalid date: ${baseDateString}`);
      }
      
      return date.toISOString();
    } catch (error) {
      HandlerUtils.logError('BalanceService', `Failed to format hourly date: ${baseDateString}`);
      throw new Error(`Invalid date format for hourly balance: ${baseDateString}`);
    }
  }

  /**
   * Get a fallback balance result for error scenarios
   * 
   * @param errorMessage - Error message to include
   * @returns Fallback balance result with zero balance
   */
  static getFallbackBalance(errorMessage: string): BalanceServiceResult {
    HandlerUtils.logActivity('BalanceService', `Using fallback balance due to: ${errorMessage}`);
    
    return {
      success: false,
      balance: 0,
      error: errorMessage,
    };
  }
}
