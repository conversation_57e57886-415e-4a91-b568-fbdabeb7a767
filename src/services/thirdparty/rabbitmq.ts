import amqplib from 'amqplib';

// @todo dependency injection
export const getRabbitMQChannel = async (queueName: string): Promise<[amqplib.Channel, () => Promise<void>]> => {
  const connection = await amqplib.connect(process.env['RABBITMQ_URL'] || 'amqp://localhost');
  const channel = await connection.createChannel();

  await channel.assertQueue(queueName, { durable: true });

  return [
    channel,
    async () => {
      await channel.close();
      await connection.close();
    },
  ];
};
