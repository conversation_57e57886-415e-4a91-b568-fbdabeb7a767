import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { CustomerDetailsPreloadRequest } from '@/network/pg-dagur/requests/customer/CustomerDetailsPreloadRequest';
import { CustomerDetailsDiscountCreateRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountCreateRequest';
import { CustomerDetailsDiscountDialogGetRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountDialogGetRequest';
import { CustomerDetailsDiscountEnterCreateModeRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountEnterCreateModeRequest';
import { CustomerDetailsDetailsTabChangeRequest } from '@/network/pg-dagur/requests/customer/details/CustomerDetailsDetailsTabChangeRequest';
import { CustomerDetailsDiscountSelectRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountSelectRequest';
import { CustomerDetailsDiscountTabChangeRequest } from '@/network/pg-dagur/requests/customer/discounts/CustomerDetailsDiscountTabChangeRequest';
import { CustomerDetailsBetsTabChangeRequest } from '@/network/pg-dagur/requests/customer/bets/CustomerDetailsBetsTabChangeRequest';
import { CustomerDetailsBetsSearchRequest } from '@/network/pg-dagur/requests/customer/bets/CustomerDetailsBetsSearchRequest';
import { CustomerDetailsBetsFilterRequest } from '@/network/pg-dagur/requests/customer/bets/CustomerDetailsBetsFilterRequest';
import {
  CustomerDetailsStatisticsTabChangeRequest,
  CustomerDetailsStatisticsLifetimeTurnoverRequest,
  CustomerDetailsStatisticsWeeklyStatisticsRequest,
  CustomerDetailsStatisticsDailyStatisticsRequest,
  CustomerDetailsStatisticsMonthlyStatisticsRequest,
} from '@/network/pg-dagur/requests/customer/statistics';
import {
  CustomerDetailsSummaryTabLoadRequest,
  CustomerSummaryResponse,
} from '@/network/pg-dagur/requests/customer/summary';
import { CustomerDetailsIpConflictPreloadRequest } from '@/network/pg-dagur/requests/customer/ip-conflict/CustomerDetailsIpConflictPreloadRequest';
import { CustomerDetailsIpConflictGetRequest } from '@/network/pg-dagur/requests/customer/ip-conflict/CustomerDetailsIpConflictGetRequest';
import { pgDagurApiClient } from '@/network/pg-dagur/PGDagurApiClient';
import { ListTransactionsOptions, ListTransactionsResponse } from '@/network/pg-dagur/AccountingApiClient';
import {
  GetTotalBalancesPreloadRequest,
  GetTotalBalancesRequest,
} from '@/network/pg-dagur/requests/customer/GetTotalBalancesRequest';

export class PgDagurService {
  static async createBonusNote(customerId: number, note: string) {
    await this.grantCash(customerId, note, 0.01);
  }

  static async grantCash(customerId: number, note: string, amount: number = 0.01) {
    // @todo maybe parse dynamically
    const reasonId = 1939;
    const type = 'dt';

    const preloadRequest = new CustomerDetailsPreloadRequest({ customerId: customerId.toString() });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new Error(`Failed to preload customer discounts: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    const tabChangeRequest = new CustomerDetailsDiscountTabChangeRequest();
    const tabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      tabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!tabChangeRequestResult.success) {
      throw new Error(
        `Failed to change tab to customer discounts: ${tabChangeRequestResult.message || 'Unknown error'}`,
      );
    }

    const enterCreateModeRequest = new CustomerDetailsDiscountEnterCreateModeRequest();
    const enterCreateModeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      enterCreateModeRequest,
      preloadRequestResult.viewState,
    );

    if (!enterCreateModeRequestResult.success) {
      throw new Error(
        `Failed to enter create mode for customer discounts: ${
          enterCreateModeRequestResult.message || 'Unknown error'
        }`,
      );
    }

    const dialogGetRequest = new CustomerDetailsDiscountDialogGetRequest();
    const dialogGetRequestResult = await pgDagurAdminHttpClient.makeRequest(
      dialogGetRequest,
      preloadRequestResult.viewState,
    );

    if (!dialogGetRequestResult.success) {
      throw new Error(`Failed to get customer discount dialog: ${dialogGetRequestResult.message || 'Unknown error'}`);
    }

    const selectRequest = new CustomerDetailsDiscountSelectRequest({
      discountType: type,
    });
    const selectRequestResult = await pgDagurAdminHttpClient.makeRequest(selectRequest, preloadRequestResult.viewState);

    if (!selectRequestResult.success) {
      throw new Error(
        `Failed to select discount type for customer discounts: ${selectRequestResult.message || 'Unknown error'}`,
      );
    }

    const createRequest = new CustomerDetailsDiscountCreateRequest({
      javax: {
        ...dialogGetRequestResult.data,
        ...selectRequestResult.data,
      },

      discountType: type,
      customerId: Number(customerId),
      reasonId: reasonId,
      amount: amount,
      note: note,
    });
    const createRequestResult = await pgDagurAdminHttpClient.makeRequest(createRequest, preloadRequestResult.viewState);

    if (!createRequestResult.success) {
      throw new Error(`Failed to create customer discount: ${createRequestResult.message || 'Unknown error'}`);
    }
  }

  /**
   * Retrieve customer bets from PG Dagur
   * @param customerId - The customer ID
   * @param startDate - Start date for bet search
   * @param endDate - End date for bet search
   * @param status - Optional status filter (L, O, W)
   * @returns Promise with bet data
   */
  static async getCustomerBets(options: { customerId: string; startDate: Date; endDate: Date; status?: string }) {
    const { customerId, startDate, endDate, status } = options;

    // Step 1: Preload customer details page
    const preloadRequest = new CustomerDetailsPreloadRequest({
      customerId: customerId,
    });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new Error(`Failed to preload customer bets: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    // Step 2: Change to bets tab
    const customerDetailsTabChangeRequest = new CustomerDetailsBetsTabChangeRequest();
    const customerDetailsTabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      customerDetailsTabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!customerDetailsTabChangeRequestResult.success) {
      throw new Error(
        `Failed to change tab to customer bets: ${customerDetailsTabChangeRequestResult.message || 'Unknown error'}`,
      );
    }

    // Step 3: Search for bets
    const searchRequest = new CustomerDetailsBetsSearchRequest({
      javax: customerDetailsTabChangeRequestResult.data,
      startDate: startDate,
      endDate: endDate,
    });
    const searchRequestResult = await pgDagurAdminHttpClient.makeRequest(searchRequest, preloadRequestResult.viewState);

    if (!searchRequestResult.success) {
      throw new Error(`Failed to fetch customer bets: ${searchRequestResult.message || 'Unknown error'}`);
    }

    // Step 4: Apply status filter if provided
    if (status) {
      // Required delay for proper JSF processing
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const filterRequest = new CustomerDetailsBetsFilterRequest({
        javax: {
          ...customerDetailsTabChangeRequestResult.data,
          ...searchRequestResult.data,
        },
        customerId: Number(customerId),
        status: status,
        startDate: startDate,
        endDate: endDate,
      });

      const filterRequestResult = await pgDagurAdminHttpClient.makeRequest(
        filterRequest,
        preloadRequestResult.viewState,
      );

      if (!filterRequestResult.success) {
        throw new Error(`Failed to filter customer bets: ${filterRequestResult.message || 'Unknown error'}`);
      }

      return filterRequestResult.data;
    }

    return searchRequestResult.data;
  }

  /**
   * Retrieve customer statistics from PG Dagur
   * @param customerId - The customer ID
   * @returns Promise with combined statistics data
   */
  static async getCustomerStatistics(customerId: string) {
    // Step 1: Preload customer details page
    const preloadRequest = new CustomerDetailsPreloadRequest({
      customerId: customerId,
    });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new Error(`Failed to preload customer statistics: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    let viewState = preloadRequestResult.viewState || '';
    const javax = preloadRequestResult.data;

    // Initialize statistics containers
    let gameStatistics: any = {};
    let depositWithdrawStatistics: any = {};
    let qrReferenceStatistics: any = {};
    let profitStatistics: any = {};
    let casinoStatistics: any = {};
    let manualDepositWithdrawStatistics: any = {};
    let weeklyStatistics: any = {};
    let dailyStatistics: any = {};
    let monthlyStatistics: any = {};

    // Step 2: Change to statistics tab to get basic statistics
    const tabChangeRequest = new CustomerDetailsStatisticsTabChangeRequest({
      customerId,
      javax,
      viewState,
    });
    const tabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(tabChangeRequest);

    if (!tabChangeRequestResult.success) {
      throw new Error(`Failed to fetch customer statistics: ${tabChangeRequestResult.message || 'Unknown error'}`);
    }

    // Store all statistics from tab change request
    gameStatistics = tabChangeRequestResult.data.statistics.gameStatistics;
    depositWithdrawStatistics = tabChangeRequestResult.data.statistics.depositWithdrawStatistics;
    qrReferenceStatistics = tabChangeRequestResult.data.statistics.qrReferenceStatistics;
    profitStatistics = tabChangeRequestResult.data.statistics.profitStatistics;
    casinoStatistics = tabChangeRequestResult.data.statistics.casinoStatistics;
    manualDepositWithdrawStatistics = tabChangeRequestResult.data.statistics.manualDepositWithdrawStatistics;
    viewState = tabChangeRequestResult.viewState || viewState;

    // Step 3: Load lifetime turnover statistics (optional, continues on failure)
    try {
      const lifetimeTurnoverRequest = new CustomerDetailsStatisticsLifetimeTurnoverRequest({
        customerId,
        javax,
        viewState,
      });
      const lifetimeTurnoverResult = await pgDagurAdminHttpClient.makeRequest(lifetimeTurnoverRequest);

      if (lifetimeTurnoverResult.success) {
        // Merge lifetime turnover statistics into existing game statistics
        gameStatistics = {
          ...gameStatistics,
          ...lifetimeTurnoverResult.data.statistics.gameStatistics,
        };
        viewState = lifetimeTurnoverResult.viewState || viewState;
      }
    } catch (error) {
      // Continue without lifetime turnover statistics
      console.warn('Failed to load lifetime turnover statistics:', error);
    }

    // Step 4: Load weekly statistics (optional, continues on failure)
    try {
      const weeklyStatsRequest = new CustomerDetailsStatisticsWeeklyStatisticsRequest({
        customerId,
        javax,
        viewState,
      });
      const weeklyStatsResult = await pgDagurAdminHttpClient.makeRequest(weeklyStatsRequest);

      if (weeklyStatsResult.success) {
        weeklyStatistics = weeklyStatsResult.data.statistics.weeklyStatistics;
        viewState = weeklyStatsResult.viewState || viewState;
      }
    } catch (error) {
      // Continue without weekly statistics
      console.warn('Failed to load weekly statistics:', error);
    }

    // Step 5: Load daily statistics (optional, continues on failure)
    try {
      const dailyStatsRequest = new CustomerDetailsStatisticsDailyStatisticsRequest({
        customerId,
        javax,
        viewState,
      });
      const dailyStatsResult = await pgDagurAdminHttpClient.makeRequest(dailyStatsRequest);

      if (dailyStatsResult.success) {
        dailyStatistics = dailyStatsResult.data.statistics.dailyStatistics;
        viewState = dailyStatsResult.viewState || viewState;
      }
    } catch (error) {
      // Continue without daily statistics
      console.warn('Failed to load daily statistics:', error);
    }

    // Step 6: Load monthly statistics (optional, continues on failure)
    try {
      const monthlyStatsRequest = new CustomerDetailsStatisticsMonthlyStatisticsRequest({
        customerId,
        javax,
        viewState,
      });
      const monthlyStatsResult = await pgDagurAdminHttpClient.makeRequest(monthlyStatsRequest);

      if (monthlyStatsResult.success) {
        monthlyStatistics = monthlyStatsResult.data.statistics.monthlyStatistics;
      }
    } catch (error) {
      // Continue without monthly statistics
      console.warn('Failed to load monthly statistics:', error);
    }

    // Combine all statistics data
    return {
      statistics: {
        gameStatistics,
        depositWithdrawStatistics,
        qrReferenceStatistics,
        profitStatistics,
        casinoStatistics,
        manualDepositWithdrawStatistics,
        weeklyStatistics,
        dailyStatistics,
        monthlyStatistics,
      },
      customerId,
    };
  }

  /**
   * Retrieve customer summary data from PG Dagur
   * @param customerId - The customer ID
   * @returns Promise with combined customer summary data
   */
  static async getCustomerSummary(customerId: string): Promise<CustomerSummaryResponse & { customerId: string }> {
    // Step 1: Preload customer details page
    const preloadRequest = new CustomerDetailsPreloadRequest({
      customerId: customerId,
    });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new Error(`Failed to preload customer summary: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    const viewState = preloadRequestResult.viewState || '';
    const javax = preloadRequestResult.data;

    // Step 2: Load customer summary data
    const summaryTabLoadRequest = new CustomerDetailsSummaryTabLoadRequest({
      customerId,
      javax,
      viewState,
    });
    const summaryTabLoadRequestResult = await pgDagurAdminHttpClient.makeRequest(summaryTabLoadRequest);

    if (!summaryTabLoadRequestResult.success) {
      throw new Error(`Failed to fetch customer summary: ${summaryTabLoadRequestResult.message || 'Unknown error'}`);
    }

    // Return combined summary data
    return {
      ...summaryTabLoadRequestResult.data,
      customerId,
    };
  }

  /**
   * Retrieve customer details data from PG Dagur
   * @param customerId - The customer ID
   * @returns Promise with customer details data
   */
  static async getCustomerDetails(customerId: string) {
    // Step 1: Preload customer details page
    const preloadRequest = new CustomerDetailsPreloadRequest({
      customerId: customerId,
    });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new Error(`Failed to preload customer details: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    // Step 2: Change to details tab to get customer information
    const detailsTabChangeRequest = new CustomerDetailsDetailsTabChangeRequest();
    const detailsTabChangeRequestResult = await pgDagurAdminHttpClient.makeRequest(
      detailsTabChangeRequest,
      preloadRequestResult.viewState,
    );

    if (!detailsTabChangeRequestResult.success) {
      throw new Error(`Failed to fetch customer details: ${detailsTabChangeRequestResult.message || 'Unknown error'}`);
    }

    return {
      ...detailsTabChangeRequestResult.data,
      customerId,
    };
  }

  /**
   * Retrieve customer IP conflicts from PG Dagur
   * @param customerId - The customer ID
   * @returns Promise with IP conflicts data
   */
  static async getCustomerIpConflicts(customerId: string) {
    // Step 1: Preload customer details page
    const preloadRequest = new CustomerDetailsPreloadRequest({
      customerId: customerId,
    });
    const preloadRequestResult = await pgDagurAdminHttpClient.makeRequest(preloadRequest);

    if (!preloadRequestResult.success) {
      throw new Error(`Failed to preload customer details: ${preloadRequestResult.message || 'Unknown error'}`);
    }

    // Step 2: Preload IP conflicts form
    const formPreloadRequest = new CustomerDetailsIpConflictPreloadRequest({
      javax: preloadRequestResult.data,
    });
    const formPreloadRequestResult = await pgDagurAdminHttpClient.makeRequest(
      formPreloadRequest,
      preloadRequestResult.viewState,
    );

    if (!formPreloadRequestResult.success) {
      throw new Error(
        `Failed to preload customer IP conflicts form: ${formPreloadRequestResult.message || 'Unknown error'}`,
      );
    }

    // Step 3: Get IP conflicts data
    const getRequest = new CustomerDetailsIpConflictGetRequest();
    const getRequestResult = await pgDagurAdminHttpClient.makeRequest(getRequest, preloadRequestResult.viewState);

    if (!getRequestResult.success) {
      throw new Error(`Failed to get customer IP conflicts: ${getRequestResult.message || 'Unknown error'}`);
    }

    return {
      ...getRequestResult.data,
      customerId,
    };
  }

  /**
   * List transactions from PG Dagur accounting system
   * @param options - Transaction listing options
   * @returns Promise with transactions data
   */
  static async listTransactions(options: ListTransactionsOptions): Promise<ListTransactionsResponse> {
    try {
      console.log(`📋 Fetching transactions from PG Dagur with options:`, options);

      const result = await pgDagurApiClient.accounting.listTransactions(options);

      console.log(
        `✅ Successfully retrieved ${result.transactions?.items?.length || 0} transactions${
          options.loadSubtotals !== false ? ' with subtotals' : ''
        }`,
      );
      return result;
    } catch (error) {
      console.error('❌ Failed to retrieve transactions from PG Dagur:', error);
      throw new Error(`Failed to list transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static getTotalBalances = async (endDate: Date) => {
    // Validate required fields
    if (!endDate) {
      throw new Error('endDate is required in request body');
    }

    let viewState = '';
    let testPlayerInputId = '';
    {
      const response = await pgDagurAdminHttpClient.makeRequest(new GetTotalBalancesPreloadRequest());
      if (!response.success) {
        throw new Error(`Failed to preload total balances: ${response.message || 'Unknown error'}`);
      }

      viewState = response.viewState || '';
      testPlayerInputId = response.data.testPlayerInputId;
    }

    const request = new GetTotalBalancesRequest({
      endDate: endDate,
      testPlayerInputId: testPlayerInputId,
    });
    const preloadRequest = request.getPreloadRequest();

    if (preloadRequest) {
      const result = await pgDagurAdminHttpClient.makeRequest(preloadRequest);
      viewState = result.viewState || '';
    }

    // Make authenticated request to PG Dagur total balances endpoint
    const result = await pgDagurAdminHttpClient.makeRequest(request, viewState);

    // Check if the request was successful
    if (!result.success) {
      throw new Error(`Failed to fetch total balances: ${result.message || 'Unknown error'}`);
    }

    return result.data;
  };

  /**
   * Assign a freebet to a customer
   * @param customerId - The customer ID
   * @param freebetId - The freebet ID to assign
   * @param amount - The freebet amount
   * @param note - Optional note for the assignment
   */
  static async assignCustomerFreebet(customerId: number, freebetId: number, amount: number, note?: string) {
    // For now, we'll create a bonus note to track the freebet assignment
    // This is a simplified implementation - the full implementation would require
    // the complex multi-step process from the controller
    await this.createBonusNote(
      customerId,
      note || `Freebet assigned: ${freebetId} - Amount: ${amount}`
    );

    // TODO: Implement the full freebet assignment process from CustomerFreebetsController
    // This would involve:
    // 1. Preload customer details
    // 2. Tab change to freebets
    // 3. Add freebet button press
    // 4. Dropdown select
    // 5. Assignment request
    // 6. Cleanup request

    console.log(`📝 Freebet assignment logged for customer ${customerId}: freebet ${freebetId}, amount ${amount}`);
  }
}
