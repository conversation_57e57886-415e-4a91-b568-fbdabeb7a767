import { CasinoFreespinBonus } from '@/entities/pronet/CasinoFreespinBonus';
import { HappyHoursBonus } from '@/entities/pronet/HappyHoursBonus';
import { TrialBonus } from '@/entities/pronet/TrialBonus';
import { pgCasinoTraderAdminHttpClient } from '@/network/pg-ct/PGCasinoTraderApiClient';
import { CustomerBonusAmountBlurRequest } from '@/network/pg-ct/requests/customer/CustomerBonusAmountBlurRequest';
import { CustomerBonusAmountFocusRequest } from '@/network/pg-ct/requests/customer/CustomerBonusAmountFocusRequest';
import { CustomerBonusCreateRequest } from '@/network/pg-ct/requests/customer/CustomerBonusCreateRequest';
import { CustomerBonusModalOpenRequest } from '@/network/pg-ct/requests/customer/CustomerBonusModalOpenRequest';
import { CustomerBonusModalShowRequest } from '@/network/pg-ct/requests/customer/CustomerBonusModalShowRequest';
import { CustomerBonusSelectRequest } from '@/network/pg-ct/requests/customer/CustomerBonusSelectRequest';
import { CustomerBonusTabChangeRequest } from '@/network/pg-ct/requests/customer/CustomerBonusTabChangeRequest';
import { CustomerEditModalShowRequest } from '@/network/pg-ct/requests/customer/CustomerEditModalShowRequest';
import { CustomerEnterEditModeRequest } from '@/network/pg-ct/requests/customer/CustomerEnterEditModeRequest';
import { CustomersPreloadRequest } from '@/network/pg-ct/requests/customer/CustomersPreloadRequest';
import { CustomersRowSelectRequest } from '@/network/pg-ct/requests/customer/CustomersRowSelectRequest';
import { CustomersSearchRequest } from '@/network/pg-ct/requests/customer/CustomersSearchRequest';
import { FreespinBonusPreloadRequest } from '@/network/pg-ct/requests/freespinBonus/FreespinBonusPreloadRequest';
import * as FreespinBonusCreate from '@/network/pg-ct/requests/freespinBonus/create';

export class PGCasinoTraderService {
  static async assignFreeSpinBonus(freespinBonus: CasinoFreespinBonus, customerCode: string) {
    try {
      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: freespinBonus.vendorId.toString(),
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      const selectGameRequests = freespinBonus.gameIds.map(
        (gameId: number) =>
          new FreespinBonusCreate.FreespinBonusSelectGameRequest({
            javax: {
              ...preloadResult.data,
              ...enterCreateModeResult.data,
            },
            gameId,
            gameIds: freespinBonus.gameIds,
          }),
      );
      for (const selectGameRequest of selectGameRequests) {
        const selectGameResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          selectGameRequest,
          preloadResult.viewState || '',
        );
        if (!selectGameResult.success) {
          throw new Error(selectGameResult.message);
        }
      }

      const tabChangeRequest = new FreespinBonusCreate.FreespinBonusTabChangeRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        tab: 'customers',
      });
      const tabChangeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        tabChangeRequest,
        preloadResult.viewState || '',
      );
      if (!tabChangeResult.success) {
        throw new Error(tabChangeResult.message);
      }

      const customerSearchRequest = new FreespinBonusCreate.FreespinBonusCustomerSearchRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
          ...tabChangeResult.data,
        },
        customerCode: customerCode,
      });
      const customerSearchResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        customerSearchRequest,
        preloadResult.viewState || '',
      );
      if (!customerSearchResult.success) {
        throw new Error(customerSearchResult.message);
      }

      const customerId = customerSearchResult.data[0]?.customerId;
      if (!customerId) {
        throw new Error('Customer not found');
      }

      const selectCustomerRequest = new FreespinBonusCreate.FreespinBonusSelectCustomerRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        customerId: customerId,
      });
      const selectCustomerResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        selectCustomerRequest,
        preloadResult.viewState || '',
      );
      if (!selectCustomerResult.success) {
        throw new Error(selectCustomerResult.message);
      }

      if (freespinBonus.values['currencyCode']) {
        const changeCurrencyCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          field: 'cmbCurrency',
          value: freespinBonus.values['currencyCode'] as string,
        });
        const changeCurrencyCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          changeCurrencyCreateFormRequest,
          preloadResult.viewState || '',
        );
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new FreespinBonusCreate.FreespinBonusCreateRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          providerId: freespinBonus.vendorId,
          customerId: customerId,

          expiresAt: freespinBonus.bonus.expiresAt ?? new Date(),
          gameIds: freespinBonus.gameIds,

          currencyId: freespinBonus.values['currencyId'] as number,
          betAmount: freespinBonus.values['betAmount'] as number,
          betPerLine: freespinBonus.values['betPerLine'] as number,
          maxWin: freespinBonus.values['maxWin'] as number,
          nOfFreespins: freespinBonus.values['nOfFreespins'] as number,
          minWinAmountRequired: freespinBonus.values['minWinAmountRequired'] as number,
          freeRoundBalance: freespinBonus.values['freeRoundBalance'] as number,
          lines: freespinBonus.values['lines'] as number,
          coins: freespinBonus.values['coins'] as number,
          denomination: freespinBonus.values['denomination'] as number,
        }),
        preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('❌ Failed to create freespin bonus:', error);

      throw error;
    }
  }

  static async assignCasinoTraderBonus(casinoTraderBonus: any, customerCode: string) {
    try {
      const preloadRequest = new CustomersPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const searchRequest = new CustomersSearchRequest({
        javax: preloadResult.data,
        customerCode,
      });
      const searchResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        searchRequest,
        preloadResult.viewState || '',
      );
      if (!searchResult.success) {
        throw new Error(searchResult.message);
      }

      if (searchResult.data.length === 0) {
        throw new Error('Customer not found');
      }

      const customerId = searchResult.data[0]?.id;
      if (!customerId) {
        throw new Error('Customer not found');
      }

      const rowSelectRequest = new CustomersRowSelectRequest({
        javax: preloadResult.data,
        id: customerId,
      });
      const rowSelectResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        rowSelectRequest,
        preloadResult.viewState || '',
      );
      if (!rowSelectResult.success) {
        throw new Error(rowSelectResult.message);
      }

      const enterEditModeRequest = new CustomerEnterEditModeRequest({
        javax: preloadResult.data,
        id: customerId,
      });
      const enterEditModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterEditModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterEditModeResult.success) {
        throw new Error(enterEditModeResult.message);
      }

      const editModalShowRequest = new CustomerEditModalShowRequest({
        javax: preloadResult.data,
      });
      const editModalShowResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        editModalShowRequest,
        preloadResult.viewState || '',
      );
      if (!editModalShowResult.success) {
        throw new Error(editModalShowResult.message);
      }

      const bonusTabChangeRequest = new CustomerBonusTabChangeRequest({
        javax: preloadResult.data,
      });
      const bonusTabChangeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        bonusTabChangeRequest,
        preloadResult.viewState || '',
      );
      if (!bonusTabChangeResult.success) {
        throw new Error(bonusTabChangeResult.message);
      }

      const bonusModalOpenRequest = new CustomerBonusModalOpenRequest({
        javax: bonusTabChangeResult.data,
      });
      const bonusModalOpenResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        bonusModalOpenRequest,
        preloadResult.viewState || '',
      );
      if (!bonusModalOpenResult.success) {
        throw new Error(bonusModalOpenResult.message);
      }

      const bonusModalShowRequest = new CustomerBonusModalShowRequest({
        javax: preloadResult.data,
      });
      const bonusModalShowResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        bonusModalShowRequest,
        preloadResult.viewState || '',
      );
      if (!bonusModalShowResult.success) {
        throw new Error(bonusModalShowResult.message);
      }

      const bonusSelectRequest = new CustomerBonusSelectRequest({
        javax: preloadResult.data,
        bonusId: casinoTraderBonus.externalBonusId,
      });
      const bonusSelectResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        bonusSelectRequest,
        preloadResult.viewState || '',
      );
      if (!bonusSelectResult.success) {
        throw new Error(bonusSelectResult.message);
      }

      const bonusAmountFocusRequest = new CustomerBonusAmountFocusRequest({
        javax: preloadResult.data,
        bonusId: casinoTraderBonus.externalBonusId,
        amount: casinoTraderBonus.amount,
      });
      const bonusAmountFocusResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        bonusAmountFocusRequest,
        preloadResult.viewState || '',
      );
      if (!bonusAmountFocusResult.success) {
        throw new Error(bonusAmountFocusResult.message);
      }

      const bonusAmountBlurRequest = new CustomerBonusAmountBlurRequest({
        javax: preloadResult.data,
        bonusId: casinoTraderBonus.externalBonusId,
        amount: casinoTraderBonus.amount,
      });
      const bonusAmountBlurResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        bonusAmountBlurRequest,
        preloadResult.viewState || '',
      );
      if (!bonusAmountBlurResult.success) {
        throw new Error(bonusAmountBlurResult.message);
      }

      const createRequest = new CustomerBonusCreateRequest({
        javax: preloadResult.data,
        bonusId: casinoTraderBonus.externalBonusId,
        amount: casinoTraderBonus.amount,
      });
      const createResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        createRequest,
        preloadResult.viewState || '',
      );
      if (!createResult.success) {
        throw new Error(createResult.message);
      }
    } catch (error) {
      console.error('❌ Failed to create bonus:', error);

      throw error;
    }
  }

  static async assignTrialBonus(trialBonus: TrialBonus, customerCode: string) {
    await this.assignCasinoTraderBonus(trialBonus, customerCode);
  }

  static async assignHappyHoursBonus(happyHoursBonus: HappyHoursBonus, customerCode: string) {
    await this.assignCasinoTraderBonus(happyHoursBonus, customerCode);
  }
}
