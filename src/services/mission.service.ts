import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { Mission } from '@/entities/Mission';
import { MissionType } from '@/enums/shared';

export interface CreateMissionDto {
  name: string;
  missionType: MissionType;
  reward: number;
  description: string;
  startDate: number;
  endDate: number;
  isActive?: boolean;
  name_i18n?: Record<string, string>;
  description_i18n?: Record<string, string>;
}

export interface MissionQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'name' | 'missionType' | 'reward' | 'startDate' | 'endDate' | 'isActive' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Filters Actually Used in Component
  name?: string; // Partial match
  description?: string; // Partial match
  missionType?: MissionType;
  status?: 'active' | 'upcoming' | 'expired';
  minReward?: number; // rewardMin in interface
  maxReward?: number; // rewardMax in interface
  isActive?: boolean; // Filter by active status

  // Additional Parameters in Interface (but not used in UI)
  reward?: number; // Exact reward amount
  startDateFrom?: number; // Start date range filtering
  startDateTo?: number; // Start date range filtering
  endDateFrom?: number; // End date range filtering
  endDateTo?: number; // End date range filtering
  search?: string; // General search term (includes i18n fields)
}

export interface MissionListResponse {
  missions: Mission[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class MissionService {
  private missionRepository: Repository<Mission>;

  constructor() {
    this.missionRepository = AppDataSource.getRepository(Mission);
  }

  async createMission(createMissionDto: CreateMissionDto): Promise<Mission> {
    const mission = this.missionRepository.create(createMissionDto);
    return await this.missionRepository.save(mission);
  }

  async findMissionById(id: number): Promise<Mission | null> {
    return await this.missionRepository.findOne({ where: { id } });
  }

  async findAllMissions(): Promise<Mission[]> {
    return await this.missionRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findMissionsWithQuery(queryParams: MissionQueryParams): Promise<MissionListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      // Filters Actually Used in Component
      name,
      description,
      missionType,
      status,
      minReward,
      maxReward,
      isActive,
      // Additional Parameters
      reward,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      search,
    } = queryParams;

    // Build query
    const queryBuilder = this.missionRepository.createQueryBuilder('mission');

    // Apply filters
    if (missionType) {
      queryBuilder.andWhere('mission.missionType = :missionType', { missionType });
    }

    if (name) {
      queryBuilder.andWhere('mission.name ILIKE :name', { name: `%${name}%` });
    }

    if (description) {
      queryBuilder.andWhere('mission.description ILIKE :description', { description: `%${description}%` });
    }

    if (reward !== undefined) {
      queryBuilder.andWhere('mission.reward = :reward', { reward });
    }

    if (minReward !== undefined) {
      queryBuilder.andWhere('mission.reward >= :minReward', { minReward });
    }

    if (maxReward !== undefined) {
      queryBuilder.andWhere('mission.reward <= :maxReward', { maxReward });
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('mission.isActive = :isActive', { isActive });
    }

    // Date range filters
    if (startDateFrom !== undefined) {
      queryBuilder.andWhere('mission.startDate >= :startDateFrom', { startDateFrom });
    }

    if (startDateTo !== undefined) {
      queryBuilder.andWhere('mission.startDate <= :startDateTo', { startDateTo });
    }

    if (endDateFrom !== undefined) {
      queryBuilder.andWhere('mission.endDate >= :endDateFrom', { endDateFrom });
    }

    if (endDateTo !== undefined) {
      queryBuilder.andWhere('mission.endDate <= :endDateTo', { endDateTo });
    }

    // Status filter (active, upcoming, expired)
    if (status) {
      const currentTimestamp = Math.floor(Date.now() / 1000);

      switch (status) {
        case 'active':
          queryBuilder.andWhere('mission.startDate <= :currentTime AND mission.endDate >= :currentTime', {
            currentTime: currentTimestamp
          });
          break;
        case 'upcoming':
          queryBuilder.andWhere('mission.startDate > :currentTime', { currentTime: currentTimestamp });
          break;
        case 'expired':
          queryBuilder.andWhere('mission.endDate < :currentTime', { currentTime: currentTimestamp });
          break;
      }
    }

    // General search term (searches in name, description, and i18n fields)
    if (search) {
      queryBuilder.andWhere(
        '(mission.name ILIKE :search OR mission.description ILIKE :search OR LOWER(CAST(mission.name_i18n AS TEXT)) LIKE :searchLower OR LOWER(CAST(mission.description_i18n AS TEXT)) LIKE :searchLower)',
        { search: `%${search}%`, searchLower: `%${search.toLowerCase()}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`mission.${sortBy}`, sortOrder);

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const missions = await queryBuilder.getMany();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      missions,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findMissionsByType(missionType: MissionType): Promise<Mission[]> {
    return await this.missionRepository.find({
      where: { missionType, isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  async findActiveMissions(currentTimestamp: number): Promise<Mission[]> {
    return await this.missionRepository
      .createQueryBuilder('mission')
      .where(
        // Only include active missions
        'mission.isActive = :isActive AND (' +
        // For custom missions, check date bounds
        // For periodic missions (daily/weekly/monthly), ignore date bounds
        '(mission.missionType = :customType AND mission.startDate <= :currentTimestamp AND mission.endDate >= :currentTimestamp) OR ' +
        '(mission.missionType IN (:...periodicTypes)))',
        {
          isActive: true,
          customType: MissionType.CUSTOM,
          periodicTypes: [MissionType.DAILY, MissionType.WEEKLY, MissionType.MONTHLY],
          currentTimestamp
        }
      )
      .orderBy('mission.createdAt', 'DESC')
      .getMany();
  }

  async updateMission(id: number, updateData: Partial<CreateMissionDto>): Promise<Mission | null> {
    await this.missionRepository.update(id, updateData);
    return await this.findMissionById(id);
  }

  async deleteMission(id: number): Promise<boolean> {
    // Use transaction to ensure all related data is deleted atomically
    return await AppDataSource.transaction(async manager => {
      const missionRepo = manager.getRepository(Mission);

      // Check if mission exists
      const mission = await missionRepo.findOne({ where: { id } });
      if (!mission) {
        return false;
      }

      // Delete the mission (CASCADE DELETE will handle related records)
      const result = await missionRepo.delete(id);
      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    });
  }
}
