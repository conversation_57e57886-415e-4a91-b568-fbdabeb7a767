import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { TOTP } from 'totp-generator';
import { InternalUserService } from './user.service';
import { User } from '@/entities/internal/User';
import { UnauthorizedError, ValidationError } from '@/types/errors';

export interface SignInDto {
  email: string;
  password: string;
  otp: string;
}

export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface JwtPayload {
  userId: number;
  email: string;
  iat: number;
  exp: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export class InternalAuthService {
  private userService: InternalUserService;
  private readonly JWT_SECRET: string;
  private readonly JWT_REFRESH_SECRET: string;
  private readonly ACCESS_TOKEN_EXPIRES_IN = '15m';
  private readonly REFRESH_TOKEN_EXPIRES_IN = '7d';
  private readonly SALT_ROUNDS = 12;

  constructor() {
    this.userService = new InternalUserService();

    // Get JWT secrets from environment variables
    this.JWT_SECRET = process.env['JWT_SECRET'] || 'your-secret-key';
    this.JWT_REFRESH_SECRET = process.env['JWT_REFRESH_SECRET'] || 'your-refresh-secret-key';

    if (!process.env['JWT_SECRET'] || !process.env['JWT_REFRESH_SECRET']) {
      console.warn('⚠️ JWT secrets not found in environment variables. Using default values.');
    }
  }

  async signIn(signInDto: SignInDto): Promise<{ user: User; tokens: AuthTokens }> {
    const { email, password, otp } = signInDto;

    // Find user by email
    const user = await this.userService.findUserByEmail(email);
    if (!user) {
      throw new UnauthorizedError('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedError('Invalid credentials');
    }

    // Verify OTP
    const isOtpValid = this.verifyOtp(otp, user.otpSecret);
    if (!isOtpValid) {
      throw new UnauthorizedError('Invalid OTP code');
    }

    // Generate tokens
    const tokens = this.generateTokens(user);

    return { user, tokens };
  }

  async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;

    // Find user
    const user = await this.userService.findUserById(userId);
    if (!user) {
      throw new UnauthorizedError('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedError('Current password is incorrect');
    }

    // Validate new password
    this.validatePassword(newPassword);

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, this.SALT_ROUNDS);

    // Update password
    await this.userService.updateUser(userId, { passwordHash: newPasswordHash });
  }

  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET) as JwtPayload;

      // Find user
      const user = await this.userService.findUserById(decoded.userId);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      // Generate new tokens
      return this.generateTokens(user);
    } catch (error) {
      throw new UnauthorizedError('Invalid refresh token');
    }
  }

  async verifyAccessToken(accessToken: string): Promise<User> {
    try {
      // Verify access token
      const decoded = jwt.verify(accessToken, this.JWT_SECRET) as JwtPayload;

      // Find user
      const user = await this.userService.findUserById(decoded.userId);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      return user;
    } catch (error) {
      throw new UnauthorizedError('Invalid access token');
    }
  }

  private generateTokens(user: User): AuthTokens {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
    };

    const accessToken = jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.ACCESS_TOKEN_EXPIRES_IN,
      algorithm: 'HS256',
    });

    const refreshToken = jwt.sign(payload, this.JWT_REFRESH_SECRET, {
      expiresIn: this.REFRESH_TOKEN_EXPIRES_IN,
      algorithm: 'HS256',
    });

    return { accessToken, refreshToken };
  }

  private verifyOtp(otp: string, secret: string): boolean {
    try {
      // Generate current OTP
      const currentOtp = TOTP.generate(secret, {
        period: 30,
      }).otp;

      return otp === currentOtp;
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return false;
    }
  }

  private validatePassword(password: string): void {
    if (!password || password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    // Add more password validation rules as needed
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      throw new ValidationError(
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      );
    }
  }

  async hashPassword(password: string): Promise<string> {
    this.validatePassword(password);
    return await bcrypt.hash(password, this.SALT_ROUNDS);
  }
}
