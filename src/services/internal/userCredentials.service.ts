import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { UserCredentials } from '@/entities/internal/UserCredentials';
import { ValidationError } from '@/types/errors';

export interface CreateUserCredentialsDto {
  panelName: string;
  login: string;
  password: string;
  otpSecret?: string;
}

export interface UpdateUserCredentialsDto {
  login?: string;
  password?: string;
  otpSecret?: string;
}

export class InternalUserCredentialsService {
  private credentialsRepository: Repository<UserCredentials>;

  constructor() {
    this.credentialsRepository = AppDataSource.getRepository(UserCredentials);
  }

  async createOrUpdateCredentials(
    userId: number,
    createDto: CreateUserCredentialsDto
  ): Promise<UserCredentials> {
    const { panelName, login, password, otpSecret } = createDto;

    // Validate panel name
    if (!panelName || panelName.trim() === '') {
      throw new ValidationError('Panel name is required');
    }

    // Validate login
    if (!login || login.trim() === '') {
      throw new ValidationError('Login is required');
    }

    // Validate password
    if (!password || password.trim() === '') {
      throw new ValidationError('Password is required');
    }

    // Check if credentials already exist for this user and panel
    const existingCredentials = await this.credentialsRepository.findOne({
      where: { userId, panelName },
    });

    if (existingCredentials) {
      // Update existing credentials
      existingCredentials.login = login;
      existingCredentials.password = password;
      existingCredentials.otpSecret = otpSecret || null;
      
      return await this.credentialsRepository.save(existingCredentials);
    } else {
      // Create new credentials
      const credentials = this.credentialsRepository.create({
        userId,
        panelName,
        login,
        password,
        otpSecret: otpSecret || null,
      });

      return await this.credentialsRepository.save(credentials);
    }
  }

  async findCredentialsByUserId(userId: number): Promise<UserCredentials[]> {
    return await this.credentialsRepository.find({
      where: { userId },
      order: { panelName: 'ASC' },
    });
  }

  async findCredentialsByUserIdAndPanel(
    userId: number,
    panelName: string
  ): Promise<UserCredentials | null> {
    return await this.credentialsRepository.findOne({
      where: { userId, panelName },
    });
  }

  async updateCredentials(
    userId: number,
    panelName: string,
    updateDto: UpdateUserCredentialsDto
  ): Promise<UserCredentials | null> {
    const credentials = await this.findCredentialsByUserIdAndPanel(userId, panelName);
    
    if (!credentials) {
      return null;
    }

    // Update only provided fields
    if (updateDto.login !== undefined) {
      if (!updateDto.login || updateDto.login.trim() === '') {
        throw new ValidationError('Login cannot be empty');
      }
      credentials.login = updateDto.login;
    }

    if (updateDto.password !== undefined) {
      if (!updateDto.password || updateDto.password.trim() === '') {
        throw new ValidationError('Password cannot be empty');
      }
      credentials.password = updateDto.password;
    }

    if (updateDto.otpSecret !== undefined) {
      credentials.otpSecret = updateDto.otpSecret || null;
    }

    return await this.credentialsRepository.save(credentials);
  }

  async deleteCredentials(userId: number, panelName: string): Promise<boolean> {
    const result = await this.credentialsRepository.delete({ userId, panelName });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async deleteAllUserCredentials(userId: number): Promise<number> {
    const result = await this.credentialsRepository.delete({ userId });
    return result.affected || 0;
  }
}
