import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/connection';
import { User } from '@/entities/internal/User';

export interface CreateInternalUserDto {
  email: string;
  passwordHash: string;
  otpSecret: string;
}

export interface UpdateInternalUserDto {
  email?: string;
  passwordHash?: string;
  otpSecret?: string;
}

export class InternalUserService {
  private userRepository: Repository<User>;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
  }

  async createUser(createUserDto: CreateInternalUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return await this.userRepository.save(user);
  }

  async findUserById(id: number): Promise<User | null> {
    return await this.userRepository.findOne({ where: { id } });
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({ where: { email } });
  }

  async updateUser(id: number, updateData: UpdateInternalUserDto): Promise<User | null> {
    await this.userRepository.update(id, updateData);
    return await this.findUserById(id);
  }

  async deleteUser(id: number): Promise<boolean> {
    const result = await this.userRepository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async userExists(email: string): Promise<boolean> {
    const count = await this.userRepository.count({ where: { email } });
    return count > 0;
  }
}
