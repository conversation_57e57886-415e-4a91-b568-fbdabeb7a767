import { Repository, SelectQueryBuilder } from 'typeorm';
import { AppDataSource } from '@/database/data-source';
import { MarketProductRequest } from '@/entities/MarketProductRequest';
import { MarketProduct } from '@/entities/MarketProduct';
import { ExtendedUser } from '@/entities/ExtendedUser';
import { MarketProductRequestStatus, TransactionCategory } from '@/enums/shared';
import { ExtendedUserService } from './extendedUser.service';

export interface CreateMarketProductRequestOptions {
  userId: number;
  productId: number;
  currency: string;
  providers?: Record<string, number[]>;
}

export interface ListMarketProductRequestsFilters {
  status?: MarketProductRequestStatus;
  statuses?: MarketProductRequestStatus[];
  userId?: number;
  productId?: number;
  currency?: string;
  currencies?: string[];
  createdFrom?: Date;
  createdTo?: Date;
  updatedFrom?: Date;
  updatedTo?: Date;
  search?: string;
  // Product-related filters (through joins)
  productType?: string;
  productCategory?: string;
  minPrice?: number;
  maxPrice?: number;
  hasRejectReason?: boolean;
}

export interface ListMarketProductRequestsOptions {
  filters?: ListMarketProductRequestsFilters;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedMarketProductRequests {
  data: MarketProductRequest[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface RejectRequestOptions {
  reason: string;
}

export interface RefundRequestOptions {
  reason: string;
}

export class MarketProductRequestService {
  private marketProductRequestRepository: Repository<MarketProductRequest>;
  private extendedUserService: ExtendedUserService;

  constructor() {
    this.marketProductRequestRepository = AppDataSource.getRepository(MarketProductRequest);
    this.extendedUserService = new ExtendedUserService();
  }

  /**
   * List all market product requests with optional filters and pagination
   */
  async listRequests(options: ListMarketProductRequestsOptions = {}): Promise<PaginatedMarketProductRequests> {
    try {
      const { filters = {}, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = options;

      console.log('📋 Listing market product requests with options:', JSON.stringify(options, null, 2));

      // Validate pagination parameters
      if (page < 1) {
        throw new Error('page must be a positive integer');
      }

      if (limit < 1 || limit > 100) {
        throw new Error('limit must be between 1 and 100');
      }

      // Build query
      const queryBuilder = this.buildRequestsQuery(filters);

      // Add pagination
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      // Add ordering
      const allowedSortFields = ['createdAt', 'updatedAt', 'status', 'userId', 'productId', 'currency'];
      const allowedProductSortFields = ['product.name', 'product.price', 'product.type', 'product.category'];

      let sortField = 'createdAt';
      let sortTable = 'request';

      if (allowedSortFields.includes(sortBy)) {
        sortField = sortBy;
        sortTable = 'request';
      } else if (allowedProductSortFields.includes(sortBy)) {
        const fieldParts = sortBy.split('.');
        sortField = fieldParts[1] || 'name';
        sortTable = 'product';
      } else if (sortBy.startsWith('product.') && allowedProductSortFields.some((field) => field === sortBy)) {
        const fieldParts = sortBy.split('.');
        sortField = fieldParts[1] || 'name';
        sortTable = 'product';
      }

      queryBuilder.orderBy(`${sortTable}.${sortField}`, sortOrder);

      // Execute query
      const [requests, total] = await queryBuilder.getManyAndCount();

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);

      const result: PaginatedMarketProductRequests = {
        data: requests,
        total,
        page,
        limit,
        totalPages,
      };

      console.log(`✅ Retrieved ${requests.length} market product requests (${total} total)`);
      return result;
    } catch (error) {
      console.error('❌ Failed to list market product requests:', error);
      throw error;
    }
  }

  /**
   * Build query for listing requests with filters
   */
  private buildRequestsQuery(filters: ListMarketProductRequestsFilters): SelectQueryBuilder<MarketProductRequest> {
    const queryBuilder = this.marketProductRequestRepository
      .createQueryBuilder('request')
      .leftJoinAndSelect('request.user', 'user')
      .leftJoinAndSelect('request.product', 'product');

    // Status filters
    if (filters.status) {
      queryBuilder.andWhere('request.status = :status', { status: filters.status });
    }

    if (filters.statuses && filters.statuses.length > 0) {
      queryBuilder.andWhere('request.status IN (:...statuses)', { statuses: filters.statuses });
    }

    // User and product filters
    if (filters.userId) {
      queryBuilder.andWhere('request.userId = :userId', { userId: filters.userId });
    }

    if (filters.productId) {
      queryBuilder.andWhere('request.productId = :productId', { productId: filters.productId });
    }

    // Currency filters
    if (filters.currency) {
      queryBuilder.andWhere('request.currency = :currency', { currency: filters.currency });
    }

    if (filters.currencies && filters.currencies.length > 0) {
      queryBuilder.andWhere('request.currency IN (:...currencies)', { currencies: filters.currencies });
    }

    // Date range filters
    if (filters.createdFrom) {
      queryBuilder.andWhere('request.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
    }

    if (filters.createdTo) {
      queryBuilder.andWhere('request.createdAt <= :createdTo', { createdTo: filters.createdTo });
    }

    if (filters.updatedFrom) {
      queryBuilder.andWhere('request.updatedAt >= :updatedFrom', { updatedFrom: filters.updatedFrom });
    }

    if (filters.updatedTo) {
      queryBuilder.andWhere('request.updatedAt <= :updatedTo', { updatedTo: filters.updatedTo });
    }

    // Product-related filters (through joins)
    if (filters.productType) {
      queryBuilder.andWhere('product.type = :productType', { productType: filters.productType });
    }

    if (filters.productCategory) {
      queryBuilder.andWhere('product.category = :productCategory', { productCategory: filters.productCategory });
    }

    if (filters.minPrice !== undefined) {
      queryBuilder.andWhere('product.price >= :minPrice', { minPrice: filters.minPrice });
    }

    if (filters.maxPrice !== undefined) {
      queryBuilder.andWhere('product.price <= :maxPrice', { maxPrice: filters.maxPrice });
    }

    // Reject reason filter
    if (filters.hasRejectReason !== undefined) {
      if (filters.hasRejectReason) {
        queryBuilder.andWhere('request.rejectReason IS NOT NULL');
      } else {
        queryBuilder.andWhere('request.rejectReason IS NULL');
      }
    }

    // Search filter (searches through product name, currency, reject reason, and user data)
    if (filters.search && filters.search.trim() !== '') {
      const searchTerm = `%${filters.search.toLowerCase()}%`;
      queryBuilder.andWhere(
        `(
          LOWER(product.name) LIKE :search OR
          LOWER(product.slug) LIKE :search OR
          LOWER(CAST(product.name_i18n AS TEXT)) LIKE :search OR
          LOWER(request.currency) LIKE :search OR
          LOWER(request.rejectReason) LIKE :search OR
          CAST(request.userId AS TEXT) LIKE :search OR
          CAST(request.productId AS TEXT) LIKE :search
        )`,
        { search: searchTerm },
      );
    }

    return queryBuilder;
  }

  /**
   * Create a new market product request and charge points from user
   * Performs availability checks using SQL-based logic before creating the request
   */
  async createRequest(options: CreateMarketProductRequestOptions): Promise<MarketProductRequest> {
    return await AppDataSource.transaction(async (tx) => {
      try {
        console.log('🛒 Creating market product request:', JSON.stringify(options, null, 2));

        const { userId, productId, currency, providers } = options;

        // Get repositories within transaction
        const requestRepository = tx.getRepository(MarketProductRequest);
        const productRepository = tx.getRepository(MarketProduct);
        const userRepository = tx.getRepository(ExtendedUser);

        // Validate product exists
        const product = await productRepository.findOne({ where: { id: productId } });
        if (!product) {
          throw new Error(`Product with ID ${productId} not found`);
        }

        // Validate providers based on product type
        if (product.type === 'general') {
          // For general products, providers are optional
          if (
            providers !== undefined &&
            providers !== null &&
            (typeof providers !== 'object' || Array.isArray(providers))
          ) {
            throw new Error('providers must be an object if provided for general products');
          }
        } else {
          // For non-general products, providers are required
          if (!providers || typeof providers !== 'object' || Object.keys(providers).length === 0) {
            throw new Error('providers is required and must be a non-empty object for non-general products');
          }
        }

        // Validate user exists
        const user = await userRepository.findOne({ where: { externalId: userId } });
        if (!user) {
          throw new Error(`User with ID ${userId} not found`);
        }

        // Check if user has sufficient points
        if (user.points < product.price) {
          throw new Error(`Insufficient balance. Required: ${product.price}, Available: ${user.points}`);
        }

        // Check if product is available to this user using SQL-based availability logic
        const availabilityQuery = await productRepository
          .createQueryBuilder('mp')
          .leftJoin('market_product_requests', 'mpr', 'mpr.productId = mp.id AND mpr.userId = :userId', {
            userId: user.externalId,
          })
          .select(['mp.id'])
          .where('mp.id = :productId', { productId })
          .andWhere(
            `(mp.availableAmount is NULL OR mp.availableAmount > 0) AND (mp.isMultiPerBuyer = true OR mpr.id is NULL)`,
          )
          .getRawOne();

        if (!availabilityQuery) {
          throw new Error(`Product with ID ${productId} is not available`);
        }

        // Create request history entry
        const historyEntry = {
          status: MarketProductRequestStatus.PENDING,
          timestamp: Date.now(),
          message: 'Request created and points charged',
        };

        // Create the request
        const request = requestRepository.create({
          status: MarketProductRequestStatus.PENDING,
          currency,
          providers: providers || null,
          userId: user.externalId,
          productId,
          history: [historyEntry],
          rejectReason: null,
        });

        const savedRequest = await requestRepository.save(request);

        // Charge points from user
        await this.extendedUserService.chargePoints(
          userId,
          product.price,
          TransactionCategory.MARKET_PURCHASE,
          {
            productId: productId,
            productName: product.name,
            requestId: request.id,
          },
          tx,
        );

        // Decrement available amount by 1 if it's not null
        if (product.availableAmount !== null) {
          await productRepository.update(
            { id: productId },
            {
              availableAmount: () => 'availableAmount - 1',
            },
          );
          console.log(`📦 Decremented available amount for product ${productId} by 1`);
        } else {
          console.log(`📦 Product ${productId} has unlimited availability (availableAmount is null)`);
        }

        console.log('✅ Market product request created successfully:', savedRequest.id);
        return savedRequest;
      } catch (error) {
        console.error('❌ Failed to create market product request:', error);
        throw error;
      }
    });
  }

  /**
   * Reject a market product request with reason
   */
  async rejectRequest(requestId: number, options: RejectRequestOptions): Promise<MarketProductRequest> {
    try {
      console.log(`❌ Rejecting market product request ${requestId}:`, JSON.stringify(options, null, 2));

      const { reason } = options;

      // Find the request
      const request = await this.marketProductRequestRepository.findOne({
        where: { id: requestId },
        relations: ['user', 'product'],
      });

      if (!request) {
        throw new Error(`Market product request with ID ${requestId} not found`);
      }

      // Check if request can be rejected
      if (request.status !== MarketProductRequestStatus.PENDING) {
        throw new Error(`Cannot reject request with status: ${request.status}`);
      }

      // Create history entry
      const historyEntry = {
        status: MarketProductRequestStatus.REJECTED,
        timestamp: Date.now(),
        message: `Request rejected: ${reason}`,
      };

      // Update request
      request.status = MarketProductRequestStatus.REJECTED;
      request.rejectReason = reason;
      request.history = [...request.history, historyEntry];

      const updatedRequest = await this.marketProductRequestRepository.save(request);

      console.log('✅ Market product request rejected successfully:', requestId);
      return updatedRequest;
    } catch (error) {
      console.error('❌ Failed to reject market product request:', error);
      throw error;
    }
  }

  /**
   * Refund a market product request with reason and deposit points back to user
   */
  async refundRequest(requestId: number, options: RefundRequestOptions): Promise<MarketProductRequest> {
    return await AppDataSource.transaction(async (tx) => {
      try {
        console.log(`💰 Refunding market product request ${requestId}:`, JSON.stringify(options, null, 2));

        const { reason } = options;

        // Get repositories within transaction
        const requestRepository = tx.getRepository(MarketProductRequest);

        // Find the request
        const request = await requestRepository.findOne({
          where: { id: requestId },
          relations: ['user', 'product'],
        });

        if (!request) {
          throw new Error(`Market product request with ID ${requestId} not found`);
        }

        // Check if request can be refunded
        if (
          ![
            MarketProductRequestStatus.PENDING,
            MarketProductRequestStatus.COMPLETED,
            MarketProductRequestStatus.REJECTED,
          ].includes(request.status)
        ) {
          throw new Error(`Cannot refund request with status: ${request.status}`);
        }

        // Refund points back to user using the new refund method
        await this.extendedUserService.refundPoints(
          request.user.externalId,
          request.product.price,
          {
            requestId: requestId,
            productId: request.productId,
            productName: request.product.name,
            refundReason: reason,
          },
          tx,
        );

        // Create history entry
        const historyEntry = {
          status: MarketProductRequestStatus.REFUNDED,
          timestamp: Date.now(),
          message: `Request refunded: ${reason}`,
        };

        // Update request
        request.status = MarketProductRequestStatus.REFUNDED;
        request.rejectReason = reason;
        request.history = [...request.history, historyEntry];

        const updatedRequest = await requestRepository.save(request);

        console.log('✅ Market product request refunded successfully:', requestId);
        return updatedRequest;
      } catch (error) {
        console.error('❌ Failed to refund market product request:', error);
        throw error;
      }
    });
  }

  /**
   * Complete a market product request
   */
  async completeRequest(requestId: number): Promise<MarketProductRequest> {
    try {
      console.log(`✅ Completing market product request ${requestId}`);

      // Find the request
      const request = await this.marketProductRequestRepository.findOne({
        where: { id: requestId },
        relations: ['user', 'product'],
      });

      if (!request) {
        throw new Error(`Market product request with ID ${requestId} not found`);
      }

      // Check if request can be completed
      if (request.status !== MarketProductRequestStatus.PENDING) {
        throw new Error(`Cannot complete request with status: ${request.status}`);
      }

      // Create history entry
      const historyEntry = {
        status: MarketProductRequestStatus.COMPLETED,
        timestamp: Date.now(),
        message: 'Request completed successfully',
      };

      // Update request
      request.status = MarketProductRequestStatus.COMPLETED;
      request.history = [...request.history, historyEntry];

      const updatedRequest = await this.marketProductRequestRepository.save(request);

      console.log('✅ Market product request completed successfully:', requestId);
      return updatedRequest;
    } catch (error) {
      console.error('❌ Failed to complete market product request:', error);
      throw error;
    }
  }

  /**
   * Delete a market product request
   */
  async deleteRequest(requestId: number): Promise<boolean> {
    try {
      console.log(`🗑️ Deleting market product request ${requestId}`);

      // Find the request first to check if it exists
      const request = await this.marketProductRequestRepository.findOne({
        where: { id: requestId },
      });

      if (!request) {
        throw new Error(`Market product request with ID ${requestId} not found`);
      }

      // Delete the request
      const result = await this.marketProductRequestRepository.delete(requestId);

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;

      if (success) {
        console.log('✅ Market product request deleted successfully:', requestId);
      } else {
        console.log('❌ Failed to delete market product request:', requestId);
      }

      return success;
    } catch (error) {
      console.error('❌ Failed to delete market product request:', error);
      throw error;
    }
  }

  /**
   * Get a single market product request by ID
   */
  async getRequestById(requestId: number): Promise<MarketProductRequest | null> {
    try {
      console.log(`🔍 Getting market product request ${requestId}`);

      const request = await this.marketProductRequestRepository.findOne({
        where: { id: requestId },
        relations: ['user', 'product'],
      });

      if (request) {
        console.log('✅ Market product request found:', requestId);
      } else {
        console.log('❌ Market product request not found:', requestId);
      }

      return request;
    } catch (error) {
      console.error('❌ Failed to get market product request:', error);
      throw error;
    }
  }
}
