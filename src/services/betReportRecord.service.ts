import { Repository } from 'typeorm';
import { AppDataSource } from '@/database/data-source';
import { BetReportRecord } from '@/entities/BetReportRecord';

export interface CreateBetReportRecordDto {
  totalCoupons: number;
  totalPlayAmount: number;
  totalOpenCoupons: number;
  totalOpenReturn: number;
  totalOpenAmount: number;
  totalWinCoupons: number;
  totalWinAmount: number;
  totalWinReturn: number;
  totalLoseCoupons: number;
  totalLoseAmount: number;
  totalVoidCoupons: number;
  totalVoidAmount: number;
  totalVoidReturn: number;
  totalPartCashoutAmount: number;
  totalPartCashoutCount: number;
  totalCompCashoutAmount: number;
  totalCompCashoutCount: number;
  totRealBalPlayAmount: number;
  totBonBalPlayAmount: number;
  totFreeBalPlayAmount: number;
  totRealBalWinReturn: number;
  totBonBalWinReturn: number;
  totRealBalVoidReturn: number;
  totBonBalVoidReturn: number;
  ngr: number;
  ggr: number;
  transactionCurrency: string | null;
  startDate: Date;
  endDate: Date;
  isHourlyReport?: boolean;
}

export interface BetReportRecordQueryParams {
  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: 'id' | 'startDate' | 'endDate' | 'createdAt' | 'updatedAt';
  sortOrder?: 'ASC' | 'DESC';

  // Date range filters
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  createdAtFrom?: Date;
  createdAtTo?: Date;

  // Report type filter
  isHourlyReport?: boolean;
}

export interface BetReportRecordListResponse {
  items: BetReportRecord[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class BetReportRecordService {
  private betReportRecordRepository: Repository<BetReportRecord>;

  constructor() {
    this.betReportRecordRepository = AppDataSource.getRepository(BetReportRecord);
  }

  async createBetReportRecord(createBetReportRecordDto: CreateBetReportRecordDto): Promise<BetReportRecord> {
    const betReportRecord = this.betReportRecordRepository.create(createBetReportRecordDto);
    return await this.betReportRecordRepository.save(betReportRecord);
  }

  async findBetReportRecordById(id: number): Promise<BetReportRecord | null> {
    return await this.betReportRecordRepository.findOne({ where: { id } });
  }

  async findAllBetReportRecords(params: BetReportRecordQueryParams = {}): Promise<BetReportRecordListResponse> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      createdAtFrom,
      createdAtTo,
      isHourlyReport,
    } = params;

    const queryBuilder = this.betReportRecordRepository.createQueryBuilder('betReportRecord');

    // Apply date range filters
    if (startDateFrom) {
      queryBuilder.andWhere('betReportRecord.startDate >= :startDateFrom', { startDateFrom });
    }
    if (startDateTo) {
      queryBuilder.andWhere('betReportRecord.startDate <= :startDateTo', { startDateTo });
    }
    if (endDateFrom) {
      queryBuilder.andWhere('betReportRecord.endDate >= :endDateFrom', { endDateFrom });
    }
    if (endDateTo) {
      queryBuilder.andWhere('betReportRecord.endDate <= :endDateTo', { endDateTo });
    }
    if (createdAtFrom) {
      queryBuilder.andWhere('betReportRecord.createdAt >= :createdAtFrom', { createdAtFrom });
    }
    if (createdAtTo) {
      queryBuilder.andWhere('betReportRecord.createdAt <= :createdAtTo', { createdAtTo });
    }
    if (isHourlyReport !== undefined) {
      queryBuilder.andWhere('betReportRecord.isHourlyReport = :isHourlyReport', { isHourlyReport });
    }

    // Apply sorting
    queryBuilder.orderBy(`betReportRecord.${sortBy}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateBetReportRecord(id: number, updateData: Partial<CreateBetReportRecordDto>): Promise<BetReportRecord | null> {
    await this.betReportRecordRepository.update(id, updateData);
    return await this.findBetReportRecordById(id);
  }

  async deleteBetReportRecord(id: number): Promise<boolean> {
    const result = await this.betReportRecordRepository.delete(id);
    return result.affected !== null && result.affected > 0;
  }

  /**
   * Helper method to convert API response totals to CreateBetReportRecordDto
   */
  static mapApiResponseToDto(totals: any, startDate: Date, endDate: Date, isHourlyReport: boolean = false): CreateBetReportRecordDto {
    return {
      totalCoupons: totals.totalCoupons || 0,
      totalPlayAmount: totals.totalPlayAmount || 0,
      totalOpenCoupons: totals.totalOpenCoupons || 0,
      totalOpenReturn: totals.totalOpenReturn || 0,
      totalOpenAmount: totals.totalOpenAmount || 0,
      totalWinCoupons: totals.totalWinCoupons || 0,
      totalWinAmount: totals.totalWinAmount || 0,
      totalWinReturn: totals.totalWinReturn || 0,
      totalLoseCoupons: totals.totalLoseCoupons || 0,
      totalLoseAmount: totals.totalLoseAmount || 0,
      totalVoidCoupons: totals.totalVoidCoupons || 0,
      totalVoidAmount: totals.totalVoidAmount || 0,
      totalVoidReturn: totals.totalVoidReturn || 0,
      totalPartCashoutAmount: totals.totalPartCashoutAmount || 0,
      totalPartCashoutCount: totals.totalPartCashoutCount || 0,
      totalCompCashoutAmount: totals.totalCompCashoutAmount || 0,
      totalCompCashoutCount: totals.totalCompCashoutCount || 0,
      totRealBalPlayAmount: totals.totRealBalPlayAmount || 0,
      totBonBalPlayAmount: totals.totBonBalPlayAmount || 0,
      totFreeBalPlayAmount: totals.totFreeBalPlayAmount || 0,
      totRealBalWinReturn: totals.totRealBalWinReturn || 0,
      totBonBalWinReturn: totals.totBonBalWinReturn || 0,
      totRealBalVoidReturn: totals.totRealBalVoidReturn || 0,
      totBonBalVoidReturn: totals.totBonBalVoidReturn || 0,
      ngr: totals.ngr || 0,
      ggr: totals.ggr || 0,
      transactionCurrency: totals.transactionCurrency || null,
      startDate,
      endDate,
      isHourlyReport,
    };
  }
}

export const betReportRecordService = new BetReportRecordService();
