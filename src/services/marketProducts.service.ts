import { Repository, SelectQueryBuilder } from 'typeorm';
import { AppDataSource } from '@/database/data-source';
import { MarketProduct } from '@/entities/MarketProduct';
import { MarketProductType, MarketProductCategory } from '@/enums/shared';
import { generateSlug } from '@/utils/slug';

export interface CreateMarketProductOptions {
  slug?: string;
  name_i18n: Record<string, string>;
  description_i18n?: Record<string, string>;
  type: MarketProductType;
  category: MarketProductCategory;
  availableAmount: number | null;
  isMultiPerBuyer: boolean;
  photoUrl: string;
  price: number;
  providers?: { provider: Record<string, any>; games: Record<string, any>[] }[];
  currencies: string[];
}

export interface UpdateMarketProductOptions {
  slug?: string;
  name_i18n?: Record<string, string>;
  description_i18n?: Record<string, string>;
  type?: MarketProductType;
  category?: MarketProductCategory;
  availableAmount?: number | null;
  isMultiPerBuyer?: boolean;
  photoUrl?: string;
  price?: number;
  providers?: { provider: Record<string, any>; games: Record<string, any>[] }[] | null;
  currencies?: string[];
}

export interface ListMarketProductsFilters {
  type?: MarketProductType;
  category?: MarketProductCategory;
  minPrice?: number;
  maxPrice?: number;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
}

export interface ListMarketProductsOptions {
  filters?: ListMarketProductsFilters;
  page?: number;
  limit?: number;
}

export interface PaginatedMarketProducts {
  data: MarketProduct[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ListPublicMarketProductsOptions {
  filters?: ListMarketProductsFilters;
  page?: number;
  limit?: number;
  userId?: number | undefined;
}

export class MarketProductsService {
  private marketProductRepository: Repository<MarketProduct>;

  constructor() {
    this.marketProductRepository = AppDataSource.getRepository(MarketProduct);
  }

  /**
   * Create a new market product
   */
  async createProduct(options: CreateMarketProductOptions): Promise<MarketProduct> {
    try {
      console.log('🛍️ Creating market product:', JSON.stringify(options, null, 2));

      const name_i18n = options.name_i18n;
      if (Object.keys(name_i18n).length === 0) {
        throw new Error('name_i18n is required');
      }

      const key = name_i18n['en'] ? 'en' : Object.keys(name_i18n)[0]!;
      const name = name_i18n[key]?.trim()!;
      const slug = options.slug || generateSlug(name);

      // Create product entity
      const product = this.marketProductRepository.create({
        name: name,
        slug: slug,
        name_i18n: options.name_i18n,
        description_i18n: options.description_i18n || {},
        type: options.type,
        category: options.category,
        availableAmount: options.availableAmount ?? null,
        isMultiPerBuyer: options.isMultiPerBuyer || false,
        photoUrl: options.photoUrl.trim(),
        price: options.price,
        currencies: options.currencies || [],
        providers: options.type === MarketProductType.GENERAL ? null : options.providers || [],
      });

      // Save product
      const savedProduct = await this.marketProductRepository.save(product);

      console.log('✅ Market product created successfully:', savedProduct.id);
      return savedProduct;
    } catch (error) {
      console.error('❌ Failed to create market product:', error);
      throw error;
    }
  }

  /**
   * Update an existing market product
   */
  async updateProduct(id: number, options: UpdateMarketProductOptions): Promise<MarketProduct> {
    try {
      console.log(`🔄 Updating market product ${id}:`, JSON.stringify(options, null, 2));

      // Find existing product
      const existingProduct = await this.marketProductRepository.findOne({
        where: { id },
      });

      if (!existingProduct) {
        throw new Error(`Product with ID ${id} not found`);
      }

      // Validate price if provided
      if (options.price !== undefined && options.price <= 0) {
        throw new Error('price must be a positive number');
      }

      // Update fields
      const updateData: Partial<MarketProduct> = {};

      if (options.slug !== undefined) updateData.slug = options.slug.trim();
      if (options.name_i18n !== undefined) {
        const name_i18n = options.name_i18n;

        if (Object.keys(name_i18n).length === 0) {
          throw new Error('name_i18n is required');
        }

        const key = name_i18n['en'] ? 'en' : Object.keys(name_i18n)[0]!;
        const name = name_i18n[key]?.trim()!;

        updateData.name_i18n = name_i18n;
        updateData.name = name;
      }
      if (options.description_i18n !== undefined) updateData.description_i18n = options.description_i18n;
      if (options.type !== undefined) updateData.type = options.type;
      if (options.category !== undefined) updateData.category = options.category;
      if (options.availableAmount !== undefined) updateData.availableAmount = options.availableAmount;
      if (options.isMultiPerBuyer !== undefined) updateData.isMultiPerBuyer = options.isMultiPerBuyer;
      if (options.photoUrl !== undefined) updateData.photoUrl = options.photoUrl.trim();
      if (options.price !== undefined) updateData.price = options.price;
      if (options.providers !== undefined) {
        // Determine the final product type (either updated type or existing type)
        const finalType = options.type !== undefined ? options.type : existingProduct.type;

        // Handle providers based on product type
        if (finalType === MarketProductType.GENERAL) {
          // For general products, providers can be null or a valid object
          updateData.providers = options.providers;
        } else {
          // For non-general products, providers cannot be null
          if (options.providers === null) {
            throw new Error('providers cannot be null for non-general products');
          }
          updateData.providers = options.providers;
        }
      }
      if (options.currencies !== undefined) updateData.currencies = options.currencies;

      // Perform update
      await this.marketProductRepository.update(id, updateData);

      // Fetch and return updated product
      const updatedProduct = await this.marketProductRepository.findOne({
        where: { id },
      });

      console.log('✅ Market product updated successfully:', id);
      return updatedProduct!;
    } catch (error) {
      console.error('❌ Failed to update market product:', error);
      throw error;
    }
  }

  /**
   * Delete a market product
   */
  async deleteProduct(id: number): Promise<void> {
    try {
      console.log(`🗑️ Deleting market product: ${id}`);

      // Check if product exists
      const existingProduct = await this.marketProductRepository.findOne({
        where: { id },
      });

      if (!existingProduct) {
        throw new Error(`Product with ID ${id} not found`);
      }

      // Delete product
      await this.marketProductRepository.delete(id);

      console.log('✅ Market product deleted successfully:', id);
    } catch (error) {
      console.error('❌ Failed to delete market product:', error);
      throw error;
    }
  }

  /**
   * List all market products with optional filters and pagination
   */
  async listProducts(options: ListMarketProductsOptions = {}): Promise<PaginatedMarketProducts> {
    try {
      const { filters = {}, page = 1, limit = 20 } = options;

      console.log('📋 Listing market products with options:', JSON.stringify(options, null, 2));

      // Validate pagination parameters
      if (page < 1) {
        throw new Error('page must be a positive integer');
      }

      if (limit < 1 || limit > 100) {
        throw new Error('limit must be between 1 and 100');
      }

      // Build query
      const queryBuilder = this.buildProductsQuery(filters);

      // Add pagination
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      // Add ordering (most recent first)
      queryBuilder.orderBy('product.createdAt', 'DESC');

      // Execute query
      const [products, total] = await queryBuilder.getManyAndCount();

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);

      const result: PaginatedMarketProducts = {
        data: products,
        total,
        page,
        limit,
        totalPages,
      };

      console.log(`✅ Retrieved ${products.length} market products (${total} total)`);
      return result;
    } catch (error) {
      console.error('❌ Failed to list market products:', error);
      throw error;
    }
  }

  /**
   * Get market product by ID
   */
  async getProductById(id: number): Promise<MarketProduct | null> {
    try {
      console.log(`🔍 Getting market product by ID: ${id}`);

      const product = await this.marketProductRepository.findOne({
        where: { id },
      });

      if (product) {
        console.log('✅ Market product found:', product.id);
      } else {
        console.log('❌ Market product not found');
      }

      return product;
    } catch (error) {
      console.error('❌ Failed to get market product by ID:', error);
      throw error;
    }
  }

  /**
   * Get market product by slug
   */
  async getProductBySlug(slug: string): Promise<MarketProduct | null> {
    try {
      console.log(`🔍 Getting market product by slug: ${slug}`);

      if (!slug?.trim()) {
        throw new Error('slug is required');
      }

      const product = await this.marketProductRepository.findOne({
        where: { slug: slug.trim() },
      });

      if (product) {
        console.log('✅ Market product found:', product.slug);
      } else {
        console.log('❌ Market product not found');
      }

      return product;
    } catch (error) {
      console.error('❌ Failed to get market product by slug:', error);
      throw error;
    }
  }

  /**
   * List all market products with user-specific availability information
   */
  async listPublicProducts(options: ListPublicMarketProductsOptions = {}): Promise<PaginatedMarketProducts> {
    try {
      const { filters = {}, page = 1, limit = 20, userId } = options;

      console.log('📋 Listing public market products with options:', JSON.stringify(options, null, 2));

      // Validate pagination parameters
      if (page < 1) {
        throw new Error('page must be a positive integer');
      }

      if (limit < 1 || limit > 100) {
        throw new Error('limit must be between 1 and 100');
      }

      // Build query with LEFT JOIN to market_product_requests using SQL-based availability logic
      const queryBuilder = this.marketProductRepository
        .createQueryBuilder('mp')
        .leftJoin('market_product_requests', 'mpr', 'mpr."productId" = mp.id AND mpr."userId" = :userId', {
          userId: userId,
        })
        .select(['mp.*'])
        .where(
          `(mp."availableAmount" IS NULL OR mp."availableAmount" > 0) AND (mp."isMultiPerBuyer" = true OR mpr.id IS NULL)`,
        )
        .groupBy('mp.id');

      // Apply filters
      if (filters.type !== undefined) {
        queryBuilder.andWhere('mp.type = :type', { type: filters.type });
      }

      if (filters.category !== undefined) {
        queryBuilder.andWhere('mp.category = :category', { category: filters.category });
      }

      // Add ordering (most recent first)
      queryBuilder.orderBy('mp."createdAt"', 'DESC');

      // Get total count for pagination (without LIMIT)
      const countQuery = queryBuilder.clone();
      const totalCount = await countQuery.getCount();

      // Add pagination
      const offset = (page - 1) * limit;
      queryBuilder.offset(offset).limit(limit);

      // Execute query
      const rawResults = await queryBuilder.getRawMany();

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / limit);

      const result: PaginatedMarketProducts = {
        data: rawResults,
        total: totalCount,
        page,
        limit,
        totalPages,
      };

      console.log(`✅ Retrieved ${rawResults.length} public market products (${totalCount} total)`);
      return result;
    } catch (error) {
      console.error('❌ Failed to list public market products:', error);
      throw error;
    }
  }

  /**
   * Build query with filters
   */
  private buildProductsQuery(filters: ListMarketProductsFilters): SelectQueryBuilder<MarketProduct> {
    const queryBuilder = this.marketProductRepository.createQueryBuilder('product');

    // Apply filters
    if (filters.type !== undefined) {
      queryBuilder.andWhere('product.type = :type', { type: filters.type });
    }

    if (filters.category !== undefined) {
      queryBuilder.andWhere('product.category = :category', { category: filters.category });
    }

    // Price filters
    if (filters.minPrice !== undefined) {
      queryBuilder.andWhere('product.price >= :minPrice', { minPrice: filters.minPrice });
    }

    if (filters.maxPrice !== undefined) {
      queryBuilder.andWhere('product.price <= :maxPrice', { maxPrice: filters.maxPrice });
    }

    // Amount filters (handle null values)
    if (filters.minAmount !== undefined) {
      queryBuilder.andWhere('(product.availableAmount IS NULL OR product.availableAmount >= :minAmount)', {
        minAmount: filters.minAmount,
      });
    }

    if (filters.maxAmount !== undefined) {
      queryBuilder.andWhere('(product.availableAmount IS NULL OR product.availableAmount <= :maxAmount)', {
        maxAmount: filters.maxAmount,
      });
    }

    // Search filter (searches through name, slug, name_i18n, description_i18n, currencies)
    if (filters.search !== undefined && filters.search.trim() !== '') {
      const searchTerm = `%${filters.search.toLowerCase()}%`;
      queryBuilder.andWhere(
        `(
          LOWER(product.name) LIKE :search OR
          LOWER(product.slug) LIKE :search OR
          LOWER(CAST(product.name_i18n AS TEXT)) LIKE :search OR
          LOWER(CAST(product.description_i18n AS TEXT)) LIKE :search OR
          LOWER(CAST(product.currencies AS TEXT)) LIKE :search
        )`,
        { search: searchTerm },
      );
    }

    return queryBuilder;
  }
}
