#!/usr/bin/env ts-node

/**
 * Dagur Auto-Refresh System Demonstration Script
 * 
 * This script demonstrates the auto-refresh functionality of the Dagur API client.
 * It shows how the system automatically refreshes sessions every 5 minutes and
 * handles authentication errors by forcing re-login.
 */

import { pgDagurAdminHttpClient } from '@/network/pg-dagur/PGDagurApiClient';
import { AccountingLoginRequest } from '@/network/pg-dagur/requests/auth/AccountingLoginRequest';
import { BetReportLoginRequest } from '@/network/pg-dagur/requests/auth/BetReportLoginRequest';

async function demonstrateAutoRefresh() {
  console.log('🚀 Starting Dagur Auto-Refresh System Demonstration');
  console.log('=' .repeat(60));

  try {
    // Test 1: Initial login and session creation
    console.log('\n📋 Test 1: Initial Login and Session Creation');
    console.log('-'.repeat(50));
    
    const accountingRequest = new AccountingLoginRequest();
    const accountingResponse = await pgDagurAdminHttpClient.makeRequest(accountingRequest);
    
    console.log('✅ Initial accounting login successful');
    console.log(`📊 Response success: ${accountingResponse.success}`);

    // Test 2: Immediate second request (should use cached session)
    console.log('\n📋 Test 2: Immediate Second Request (Cached Session)');
    console.log('-'.repeat(50));
    
    const betReportRequest = new BetReportLoginRequest();
    const betReportResponse = await pgDagurAdminHttpClient.makeRequest(betReportRequest);
    
    console.log('✅ Second request successful (using cached session)');
    console.log(`📊 Response success: ${betReportResponse.success}`);

    // Test 3: Force session expiry and test auto-refresh
    console.log('\n📋 Test 3: Force Session Expiry and Auto-Refresh');
    console.log('-'.repeat(50));
    
    // Access private properties to force expiry (for demonstration)
    console.log('🔄 Forcing session expiry...');
    (pgDagurAdminHttpClient as any).lastLoginTime = Date.now() - (6 * 60 * 1000); // 6 minutes ago
    
    const refreshRequest = new AccountingLoginRequest();
    const refreshResponse = await pgDagurAdminHttpClient.makeRequest(refreshRequest);
    
    console.log('✅ Auto-refresh successful');
    console.log(`📊 Response success: ${refreshResponse.success}`);

    // Test 4: Multiple rapid requests (should all use same session)
    console.log('\n📋 Test 4: Multiple Rapid Requests (Session Reuse)');
    console.log('-'.repeat(50));
    
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(
        pgDagurAdminHttpClient.makeRequest(new AccountingLoginRequest())
          .then(() => console.log(`✅ Request ${i + 1} completed`))
      );
    }
    
    await Promise.all(promises);
    console.log('✅ All rapid requests completed successfully');

    // Test 5: Show session timing information
    console.log('\n📋 Test 5: Session Timing Information');
    console.log('-'.repeat(50));
    
    const lastLoginTime = (pgDagurAdminHttpClient as any).lastLoginTime;
    const loginInterval = (pgDagurAdminHttpClient as any).LOGIN_INTERVAL;
    const nextRefreshTime = new Date(lastLoginTime + loginInterval);
    const timeUntilRefresh = nextRefreshTime.getTime() - Date.now();
    
    console.log(`🕐 Last login: ${new Date(lastLoginTime).toISOString()}`);
    console.log(`🕐 Next refresh: ${nextRefreshTime.toISOString()}`);
    console.log(`⏱️  Time until refresh: ${Math.floor(timeUntilRefresh / 1000)} seconds`);

  } catch (error) {
    console.error('❌ Demonstration failed:', error);
    
    if (error instanceof Error) {
      console.error('📋 Error details:', error.message);
      
      if (error.message.includes('Missing Dagur credentials')) {
        console.log('\n💡 Tip: Make sure the following environment variables are set:');
        console.log('   - PG_DAGUR_USERNAME');
        console.log('   - PG_DAGUR_PASSWORD');
        console.log('   - PG_DAGUR_OTP_SECRET');
      }
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🏁 Dagur Auto-Refresh System Demonstration Complete');
}

async function showSystemStatus() {
  console.log('\n📊 Dagur Auto-Refresh System Status');
  console.log('=' .repeat(40));
  
  const hasSession = !!(pgDagurAdminHttpClient as any).session;
  const lastLoginTime = (pgDagurAdminHttpClient as any).lastLoginTime;
  const loginInterval = (pgDagurAdminHttpClient as any).LOGIN_INTERVAL;
  
  console.log(`🔐 Has active session: ${hasSession ? '✅ Yes' : '❌ No'}`);
  console.log(`⏱️  Login interval: ${loginInterval / (60 * 1000)} minutes`);
  
  if (lastLoginTime > 0) {
    const nextRefreshTime = new Date(lastLoginTime + loginInterval);
    const timeUntilRefresh = nextRefreshTime.getTime() - Date.now();
    
    console.log(`🕐 Last login: ${new Date(lastLoginTime).toISOString()}`);
    console.log(`🕐 Next refresh: ${nextRefreshTime.toISOString()}`);
    console.log(`⏱️  Time until refresh: ${Math.max(0, Math.floor(timeUntilRefresh / 1000))} seconds`);
  } else {
    console.log(`🕐 No login performed yet`);
  }
  
  // Check environment variables
  const hasUsername = !!(process.env['PG_DAGUR_USERNAME']);
  const hasPassword = !!(process.env['PG_DAGUR_PASSWORD']);
  const hasOtpSecret = !!(process.env['PG_DAGUR_OTP_SECRET']);
  
  console.log('\n🔧 Environment Configuration:');
  console.log(`   PG_DAGUR_USERNAME: ${hasUsername ? '✅ Set' : '❌ Missing'}`);
  console.log(`   PG_DAGUR_PASSWORD: ${hasPassword ? '✅ Set' : '❌ Missing'}`);
  console.log(`   PG_DAGUR_OTP_SECRET: ${hasOtpSecret ? '✅ Set' : '❌ Missing'}`);
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--status')) {
    await showSystemStatus();
  } else if (args.includes('--help')) {
    console.log('Dagur Auto-Refresh System Demonstration');
    console.log('');
    console.log('Usage:');
    console.log('  ts-node src/scripts/test-dagur-auto-refresh.ts [options]');
    console.log('');
    console.log('Options:');
    console.log('  --status    Show current system status');
    console.log('  --help      Show this help message');
    console.log('  (no args)   Run full demonstration');
  } else {
    await demonstrateAutoRefresh();
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { demonstrateAutoRefresh, showSystemStatus };
