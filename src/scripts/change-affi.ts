import { EbetlabApiClient } from '@/network/ebetlab/EbetlabApiClient';
import { AffiliateSetRequest } from '@/network/ebetlab/requests/AffiliateSetRequest';

const TOKEN = '';
const NEW_CODE = 'superbonus';
const IDS = `2517995
2517956`
  .split('\n')
  .map(Number);

async function main() {
  const api = new EbetlabApiClient();

  for (const id of IDS) {
    console.log(`Setting affiliate code for ${id}`);

    const request = new AffiliateSetRequest({
      id: id,
      code: NEW_CODE,
    });

    await api.makeAuthenticatedRequest(request, `Bearer ${TOKEN}`);

    console.log('Set affiliate code for', id);
  }
}

main().then(() => {
  process.exit(0);
});
