version: '3.8'

services:
  # bo-panel-api:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: api
  #   restart: unless-stopped
  #   # Required for OpenVPN
  #   privileged: true
  #   cap_add:
  #     - NET_ADMIN
  #     - SYS_MODULE
  #   # Mount OpenVPN configuration
  #   volumes:
  #     - ./docker/openvpn/client.ovpn:/etc/openvpn/client.ovpn:ro
  #     - ./docker/openvpn/auth.txt:/etc/openvpn/auth.txt:ro
  #     - /dev/net/tun:/dev/net/tun
  #   # Environment variables
  #   environment:
  #     - NODE_ENV=production
  #     - HOST=0.0.0.0
  #     - POSTGRES_URI=****************************************/makroz_dev
  #     - API_USERNAME="<EMAIL>"
  #     - API_PASSWORD="Betroz1234.."
  #     - API_OTP_SECRET="GBV6I2AXV7CQVZDICNCWC6BMS4K2A7HM"
  #     - JWT_SECRET=your-super-secret-jwt-key
  #     - JWT_EXPIRES_IN=7d
  #   # Expose port
  #   ports:
  #     - "3000:80"
  #   networks:
  #     - makroz
  #   depends_on:
  #     - postgresql

  postgresql:
    image: postgres:14
    container_name: postgres14
    environment:
      POSTGRES_USER: makroz
      POSTGRES_PASSWORD: makroz
      POSTGRES_DB: makroz_dev
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data
    networks:
      - makroz
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - makroz
    restart: unless-stopped

volumes:
  pg_data:

networks:
  makroz:
    driver: bridge
