FROM node:20-alpine

# Install OpenVPN and required packages
RUN apk add --no-cache \
    python3 \
    g++ \
    make \
    openvpn \
    iptables \
    curl \
    bash \
    bind-tools \
    openresolv \
    supervisor \
    dnsmasq \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN yarn build

# Create OpenVPN directory and supervisor directories
RUN mkdir -p /etc/openvpn /var/log /var/log/supervisor /var/run

# Copy supervisor configuration
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
# Copy OpenVPN startup script
COPY docker/start-openvpn.sh /usr/local/bin/start-openvpn.sh
COPY docker/openvpn /etc/openvpn
# Copy application startup script
COPY docker/start-app.sh /usr/local/bin/start-app.sh
# Copy DNS update script for OpenVPN
COPY docker/update-resolv-conf /etc/openvpn/update-resolv-conf
# Copy health check script
COPY docker/healthcheck.sh /usr/local/bin/healthcheck.sh
# Copy DNS test script
COPY docker/test-dns.sh /usr/local/bin/test-dns.sh

# Expose the application port
EXPOSE 80

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

ENV NODE_ENV=production
ENV PORT=80

# Use supervisor to manage both OpenVPN and Node.js
# CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
CMD ["node", "dist/src/server.js"]
