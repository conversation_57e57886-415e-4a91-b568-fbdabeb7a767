{"jsc": {"parser": {"syntax": "typescript", "tsx": false, "decorators": true, "dynamicImport": true}, "target": "es2022", "loose": false, "externalHelpers": false, "keepClassNames": true, "preserveAllComments": false, "transform": {"legacyDecorator": true, "decoratorMetadata": true}, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"], "@/utils/*": ["utils/*"]}}, "module": {"type": "commonjs", "strict": false, "strictMode": true, "lazy": false, "noInterop": false}, "minify": false, "sourceMaps": true, "inlineSourcesContent": true}