#!/bin/bash

echo "🔍 DNS and Network Connectivity Test"
echo "===================================="

echo ""
echo "📋 Current DNS Configuration:"
cat /etc/resolv.conf

echo ""
echo "🌐 Network Interfaces:"
ip addr show

echo ""
echo "🛣️  Routing Table:"
ip route

echo ""
echo "🔍 DNS Resolution Tests:"
echo "Testing google.com..."
nslookup google.com || echo "❌ Failed to resolve google.com"

echo ""
echo "Testing httpbin.org..."
nslookup httpbin.org || echo "❌ Failed to resolve httpbin.org"

echo ""
echo "Testing studio.ebetlab.com..."
nslookup studio.ebetlab.com || echo "❌ Failed to resolve studio.ebetlab.com"

echo ""
echo "Testing service.ebetlab.com..."
nslookup service.ebetlab.com || echo "❌ Failed to resolve service.ebetlab.com"

echo ""
echo "🌍 Connectivity Tests:"
echo "Testing HTTP connectivity to google.com..."
curl -s --max-time 5 --connect-timeout 5 http://google.com > /dev/null && echo "✅ HTTP to google.com works" || echo "❌ HTTP to google.com failed"

echo ""
echo "Testing HTTPS connectivity to httpbin.org..."
curl -s --max-time 5 --connect-timeout 5 https://httpbin.org/ip > /dev/null && echo "✅ HTTPS to httpbin.org works" || echo "❌ HTTPS to httpbin.org failed"

echo ""
echo "Testing connectivity to studio.ebetlab.com..."
curl -s --max-time 5 --connect-timeout 5 https://studio.ebetlab.com > /dev/null && echo "✅ HTTPS to studio.ebetlab.com works" || echo "❌ HTTPS to studio.ebetlab.com failed"

echo ""
echo "Testing connectivity to service.ebetlab.com..."
curl -s --max-time 5 --connect-timeout 5 https://service.ebetlab.com > /dev/null && echo "✅ HTTPS to service.ebetlab.com works" || echo "❌ HTTPS to service.ebetlab.com failed"

echo ""
echo "🔐 VPN Status:"
if pgrep openvpn > /dev/null; then
    echo "✅ OpenVPN process is running"
    if ip route | grep -q "tun"; then
        echo "✅ VPN tunnel is active"
        VPN_IP=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "unknown")
        echo "🌐 VPN IP: $VPN_IP"
    else
        echo "❌ VPN tunnel not found"
    fi
else
    echo "❌ OpenVPN process not running"
fi

echo ""
echo "📊 Process Status:"
ps aux | grep -E "(openvpn|node)" | grep -v grep

echo ""
echo "Test completed!"
