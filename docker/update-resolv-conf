#!/bin/bash

# OpenVPN DNS update script
# This script updates /etc/resolv.conf when VPN connects/disconnects

case "$script_type" in
    up)
        echo "🔧 VPN connected - updating DNS..."
        
        # Backup current resolv.conf
        cp /etc/resolv.conf /etc/resolv.conf.vpn-backup
        
        # Create new resolv.conf with VPN DNS
        {
            echo "# Generated by OpenVPN for $dev"

            # echo "search makroz-prod.svc.cluster.local svc.cluster.local cluster.local eu-central-1.compute.internal"

            # Docker
            echo "no-resolv"
            # Parse existing nameservers from original resolv.conf (excluding Docker DNS)
            # if [ -f /etc/resolv.conf.vpn-backup ]; then
            #     grep "^nameserver" /etc/resolv.conf.vpn-backup | grep -v "127.0.0.11" | head -1 | sed 's/nameserver /server=/'
            # fi
            echo "server=***********"
            echo "server=127.0.0.11"

            # Add VPN-provided DNS servers
            # if [ -n "$foreign_option_1" ]; then
            #     echo "$foreign_option_1" | sed -e 's/dhcp-option DNS /nameserver /'
            # fi
            # if [ -n "$foreign_option_2" ]; then
            #     echo "$foreign_option_2" | sed -e 's/dhcp-option DNS /nameserver /'
            # fi
            # if [ -n "$foreign_option_3" ]; then
            #     echo "$foreign_option_3" | sed -e 's/dhcp-option DNS /nameserver /'
            # fi
            
            # Add fallback DNS servers
            echo "server=8.8.8.8"

            echo "server=8.8.4.4"
            echo "server=1.1.1.1"
            echo "server=1.0.0.1"
            
            # echo "options ndots:5"
        } > /etc/dnsmasq.conf
        # } > /etc/resolv.conf

        echo "nameserver 127.0.0.1" > /etc/resolv.conf

        dnsmasq --no-daemon --conf-file=/etc/dnsmasq.conf
        
        echo "✅ DNS updated for VPN connection"
        ;;
        
    down)
        echo "🔧 VPN disconnected - restoring DNS..."
        
        # Restore original resolv.conf
        if [ -f /etc/resolv.conf.vpn-backup ]; then
            mv /etc/resolv.conf.vpn-backup /etc/resolv.conf
        else
            # Fallback DNS configuration
            {
                # Docker
                echo "nameserver 127.0.0.11"

                echo "nameserver 8.8.8.8"
                echo "nameserver 8.8.4.4"
                echo "nameserver 1.1.1.1"
            } > /etc/resolv.conf
        fi
        
        echo "✅ DNS restored"
        ;;
esac
