#!/bin/bash

set -e

echo "🔐 Starting OpenVPN as superuser..."

# Ensure we're running as root
if [ "$(id -u)" != "0" ]; then
    echo "❌ This script must be run as root (superuser)"
    exit 1
fi

# Check if OpenVPN config file exists
if [ ! -f "/etc/openvpn/client.ovpn" ]; then
    echo "❌ OpenVPN config file not found at /etc/openvpn/client.ovpn"
    echo "Please mount your OpenVPN config file to /etc/openvpn/client.ovpn"
    exit 1
fi

# Create TUN device if it doesn't exist
if [ ! -c /dev/net/tun ]; then
    echo "📡 Creating TUN device..."
    mkdir -p /dev/net
    mknod /dev/net/tun c 10 200
    chmod 600 /dev/net/tun
fi

# Set up iptables for proper routing
echo "🔧 Setting up iptables rules..."
iptables -t nat -A POSTROUTING -o tun+ -j MASQUERADE 2>/dev/null || true
iptables -A FORWARD -i tun+ -j ACCEPT 2>/dev/null || true
iptables -A FORWARD -o tun+ -j ACCEPT 2>/dev/null || true

# Check if auth file exists (if required)
if [ -f "/etc/openvpn/auth.txt" ]; then
    echo "🔑 Using authentication file"
    AUTH_OPTION="--auth-user-pass /etc/openvpn/auth.txt"
else
    AUTH_OPTION=""
fi

# Function to start Node.js app via supervisorctl
start_nodejs_app() {
    echo "🚀 Starting Node.js application via supervisor..."
    supervisorctl -c /etc/supervisor/conf.d/supervisord.conf start nodejs
}

# Function to check VPN connection and start Node.js
check_vpn_and_start_app() {
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if ip route | grep -q "tun"; then
            echo "✅ VPN connection established successfully"
            # Get VPN IP
            VPN_IP=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "unknown")
            echo "🌐 VPN IP: $VPN_IP"

            VPN_REMOTE_IP=$(grep -E '^remote\s' /etc/openvpn/client.ovpn | awk '{print $2}')
            echo "🌐 VPN Remote IP: $VPN_REMOTE_IP"

            ORIG_GW=$(ip route | awk '/default/ {print $3}' | head -n 1)
            echo "🌐 Original Gateway: $ORIG_GW"
            VPN_GW=$(ip -4 addr show dev tun0 | awk '/inet / && $2 != "127.0.0.1" { print $2 }')
            echo "🌐 VPN Gateway: $VPN_GW"

            # ******** via ********* dev tun0
            if ! ip route show | grep -q "******** via $VPN_GW dev tun0"; then
                ip route add ******** via $VPN_GW dev tun0
                echo "🌐 Set up vpn gateway!"
            else
                echo "🌐 VPN gateway route already exists"
            fi

            # ************* via *********** dev eth0
            if ! ip route show | grep -q "$VPN_REMOTE_IP via $ORIG_GW dev eth0"; then
                ip route add $VPN_REMOTE_IP via $ORIG_GW dev eth0
                echo "🌐 Set up vpn remote to original gateway!"
            else
                echo "🌐 VPN remote route already exists"
            fi

            if ! ip route show | grep -q "0.0.0.0/1 via $VPN_GW dev tun0"; then
                ip route add 0.0.0.0/1 via $VPN_GW dev tun0
                echo "🌐 Set up 0.0.0.0/1 to vpn gateway!"
            else
                echo "🌐 0.0.0.0/1 route already exists"
            fi

            # *********/1 via ********* dev tun0
            if ! ip route show | grep -q "*********/1 via $VPN_GW dev tun0"; then
                ip route add *********/1 via $VPN_GW dev tun0
                echo "🌐 Set up ********* to vpn gateway!"
            else
                echo "🌐 *********/1 route already exists"
            fi

            if ! ip route show | grep -q "$VPC_CIDR via $ORIG_GW dev eth0"; then
                ip route add $VPC_CIDR via $ORIG_GW dev eth0
                echo "🌐 Set up $VPC_CIDR to original gateway!"
            else
                echo "🌐 $VPC_CIDR route already exists"
            fi

            # Docker
            if ! ip route show | grep -q "************/24 via $ORIG_GW dev eth0"; then
                ip route add ************/24 via $ORIG_GW dev eth0
                echo "🌐 Set up ************/24 to vpn gateway!"
            else
                echo "🌐 ************/24 route already exists"
            fi

            # if ! ip route show | grep -q "***********/32 via $ORIG_GW dev eth0"; then
            #     ip route add ***********/32 via $ORIG_GW dev eth0
            #     echo "🌐 Set up ***********/32 to vpn gateway!"
            # else
            #     echo "🌐 ***********/32 route already exists"
            # fi
            if ! ip route show | grep -q "**********/16 via $ORIG_GW dev eth0"; then
                ip route add **********/16 via $ORIG_GW dev eth0
                echo "🌐 Set up **********/16 to vpn gateway!"
            else
                echo "🌐 **********/16 route already exists"
            fi

            # [X] default via *********** dev eth0 
            # ******** via ********* dev tun0 
            # [X] ********* dev tun0 proto kernel scope link src ********* 
            # ************* via *********** dev eth0 
            # *********/1 via ********* dev tun0 
            # [X] *********** dev eth0 scope link 

            # Test DNS resolution
            echo "🔍 Testing DNS resolution..."
            if nslookup google.com > /dev/null 2>&1; then
                echo "✅ DNS resolution working"
            else
                echo "⚠️  DNS resolution failed, updating DNS..."
                /etc/openvpn/update-resolv-conf up
            fi
            
            # Start Node.js application
            start_nodejs_app
            return 0
        fi
        
        echo "⏳ Waiting for VPN connection... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    echo "❌ Failed to establish VPN connection after $max_attempts attempts"
    return 1
}

# Start OpenVPN
echo "🔌 Starting OpenVPN..."
openvpn --config /etc/openvpn/client.ovpn \
        --daemon \
        --log /var/log/openvpn.log \
        --status /var/log/openvpn-status.log 10 \
        --verb 3 \
        --script-security 2 \
        --up /etc/openvpn/update-resolv-conf \
        --down /etc/openvpn/update-resolv-conf \
        $AUTH_OPTION

# Wait for VPN connection and start Node.js app
check_vpn_and_start_app &

# Keep the script running to maintain the VPN connection
while true; do
    if ! pgrep openvpn > /dev/null; then
        echo "❌ OpenVPN process died, restarting..."
        openvpn --config /etc/openvpn/client.ovpn \
                --daemon \
                --log /var/log/openvpn.log \
                --status /var/log/openvpn-status.log 10 \
                --verb 3 \
                --script-security 2 \
                --up /etc/openvpn/update-resolv-conf \
                --down /etc/openvpn/update-resolv-conf \
                $AUTH_OPTION
        
        # Try to start Node.js app again after VPN restart
        sleep 10
        check_vpn_and_start_app &
    fi
    sleep 10
done
