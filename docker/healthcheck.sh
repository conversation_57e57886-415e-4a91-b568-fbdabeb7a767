#!/bin/bash

# Health check script for the containerized application

# Check if OpenVPN is running
if ! pgrep openvpn > /dev/null; then
    echo "❌ OpenVPN process not running"
    exit 1
fi

# Check if VPN tunnel is up
if ! ip route | grep -q "tun"; then
    echo "❌ VPN tunnel not established"
    exit 1
fi

# Check if Node.js application is running
if ! pgrep node > /dev/null; then
    echo "❌ Node.js application not running"
    exit 1
fi

# Check if the application responds to health check
if curl -f -s --max-time 5 http://localhost:80/health > /dev/null; then
    echo "✅ Application health check passed"
    exit 0
else
    echo "❌ Application health check failed"
    exit 1
fi
