[supervisord]
nodaemon=true
user=root
logfile=/dev/null
logfile_maxbytes=0 
pidfile=/var/run/supervisord.pid
loglevel=info

[unix_http_server]
file=/dev/shm/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///dev/shm/supervisor.sock

[program:openvpn]
command=/usr/local/bin/start-openvpn.sh
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/openvpn.stdout.log
stderr_logfile=/var/log/supervisor/openvpn.stderr.log
user=root
priority=100
startsecs=10
startretries=3

[program:nodejs]
command=/usr/local/bin/start-app.sh
autostart=false
autorestart=true
stdout_logfile=/var/log/supervisor/nodejs.stdout.log
stderr_logfile=/var/log/supervisor/nodejs.stderr.log
user=root
priority=200
startsecs=10
startretries=3
