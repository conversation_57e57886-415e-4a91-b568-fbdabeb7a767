#!/bin/bash

set -e

echo "🚀 Starting Node.js application..."

# Wait for VPN connection to be fully established
echo "⏳ Waiting for VPN connection to stabilize..."
sleep 5

# Check if we have internet connectivity through VPN
echo "🌐 Testing internet connectivity through VPN..."

# Test DNS resolution first
echo "🔍 Testing DNS resolution..."
if nslookup google.com > /dev/null 2>&1; then
    echo "✅ DNS resolution working"
else
    echo "⚠️  DNS resolution failed, trying to fix..."
    # Force DNS update
    /etc/openvpn/update-resolv-conf up
fi

# Test HTTP connectivity
if curl -s --max-time 10 --dns-servers ******* https://httpbin.org/ip > /dev/null 2>&1; then
    echo "✅ Internet connectivity confirmed through VPN"
    EXTERNAL_IP=$(curl -s --max-time 10 --dns-servers ******* https://httpbin.org/ip | grep -o '"origin":"[^"]*' | cut -d'"' -f4 || echo "unknown")
    echo "🌍 External IP: $EXTERNAL_IP"
else
    echo "⚠️  Warning: Could not verify internet connectivity through VPN"
    echo "📋 Current DNS configuration:"
    cat /etc/resolv.conf
    echo "🔍 Testing basic DNS lookup:"
    nslookup httpbin.org || echo "DNS lookup failed"
fi

# Change to app directory
cd /app

# Set NODE_ENV if not set
export NODE_ENV=${NODE_ENV:-production}

# Set Node.js DNS configuration
export NODE_OPTIONS="--dns-result-order=ipv4first"
export UV_THREADPOOL_SIZE=128

# Log environment info
echo "📋 Environment: $NODE_ENV"
echo "📂 Working directory: $(pwd)"
echo "🔧 Node.js version: $(node --version)"
echo "📦 NPM version: $(npm --version)"
echo "🔧 DNS configuration:"
cat /etc/resolv.conf

# Start the Node.js application
echo "🎯 Starting bo-panel-api server..."
export PORT=80
exec node dist/src/server.js
