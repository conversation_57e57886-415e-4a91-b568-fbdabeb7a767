# Call Requests Endpoint Example

This document demonstrates how to use the `POST /pronet/v1/call-requests` endpoint to forward call requests to the Pronet API.

## Endpoint Details

- **URL**: `POST /pronet/v1/call-requests`
- **Authentication**: Automatic (handled by Pronet proxy system)
- **Content-Type**: `application/json`

## Request Format

The endpoint accepts the following request body structure:

```json
{
  "id": 1,
  "type": 3,
  "token": "AABBCCDDEEFFGGHHIIJJKKLL",
  "code": "2025060100000",
  "time": "10:00 - 11:00"
}
```

### Field Descriptions

- `id` (number): Unique identifier for the call request
- `type` (number): Type of call request (e.g., 3 for specific call type)
- `token` (string): Session token for authentication
- `code` (string): Call request code identifier
- `time` (string): Time slot for the call (format: "HH:MM - HH:MM")

## How It Works

1. **Client Request**: Your application sends a POST request to `/pronet/v1/call-requests`
2. **Proxy Processing**: The request is automatically caught by the wildcard proxy route
3. **Authentication**: The `pronetAuth` middleware generates the required checksum header
4. **Forwarding**: The request is forwarded to `[PRONET_HOST]/api/pronet/v1/call-requests`
5. **Response**: The raw Pronet API response is returned to the client

## Example Usage

### JavaScript/Frontend
```javascript
async function submitCallRequest(callData) {
  try {
    const response = await fetch('/pronet/v1/call-requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(callData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Call request submitted successfully:', result);
      return result;
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Failed to submit call request:', error);
    throw error;
  }
}

// Usage
const callRequest = {
  id: 1,
  type: 3,
  token: "AABBCCDDEEFFGGHHIIJJKKLL",
  code: "2025060100000",
  time: "10:00 - 11:00"
};

submitCallRequest(callRequest);
```

### Node.js/Backend
```javascript
const fetch = require('node-fetch');

async function forwardCallRequest(callData) {
  const response = await fetch('http://localhost:3000/pronet/v1/call-requests', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(callData)
  });

  return await response.json();
}
```

### cURL Example
```bash
curl -X POST http://localhost:3000/pronet/v1/call-requests \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "type": 3,
    "token": "AABBCCDDEEFFGGHHIIJJKKLL",
    "code": "2025060100000",
    "time": "10:00 - 11:00"
  }'
```

## Authentication Details

The endpoint uses the existing Pronet authentication system:

1. **Automatic Checksum**: The system automatically generates a SHA-512 checksum of the request body
2. **Header Injection**: The checksum is added as a `checksum` header
3. **Credential Management**: Uses environment variables (`PRONET_HOST`, `PUBLIC_API_USERNAME`, `PRONET_API_KEY`)

## Error Handling

The endpoint returns standard HTTP status codes:

- `200 OK`: Request successfully forwarded and processed
- `400 Bad Request`: Invalid request format
- `500 Internal Server Error`: Pronet API communication error or authentication failure

## Testing

Use the provided test script to verify the endpoint:

```bash
node test-call-requests.js
```

This will send a test request to the endpoint and display the response.
