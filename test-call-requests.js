/**
 * Test script for the call-requests endpoint
 * 
 * This script tests the POST /pronet/v1/call-requests endpoint
 * to ensure it properly forwards requests to the Pronet API.
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testCallRequestsEndpoint() {
  console.log('🧪 Testing POST /api/pronet/v1/call-requests endpoint...');

  const testPayload = {
    id: 1,
    type: 3,
    token: "AABBCCDDEEFFGGHHIIJJKKLL", // session token
    code: "2025060100000",
    time: "10:00 - 11:00"
  };

  try {
    console.log('📤 Sending request to:', `${BASE_URL}/api/pronet/v1/call-requests`);
    console.log('📋 Request payload:', JSON.stringify(testPayload, null, 2));

    const response = await fetch(`${BASE_URL}/api/pronet/v1/call-requests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log('📥 Response status:', response.status);
    console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

    const responseData = await response.text();
    console.log('📥 Response body:', responseData);

    if (response.ok) {
      console.log('✅ Call requests endpoint is working correctly!');
    } else {
      console.log('⚠️ Call requests endpoint returned an error status');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testCallRequestsEndpoint();
