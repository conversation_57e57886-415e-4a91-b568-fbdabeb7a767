# BO Panel API

A Node.js v20 backend application built with Express.js, TypeScript, and SWC for fast compilation.

## Features

- **Express.js** - Fast, unopinionated web framework
- **TypeScript** - Type-safe JavaScript
- **SWC** - Super-fast TypeScript/JavaScript compiler
- **Security** - Helmet for security headers, CORS configuration
- **Error Handling** - Centralized error handling with custom error classes
- **Request Logging** - Built-in request/response logging
- **Health Checks** - Health check endpoints for monitoring
- **Sample CRUD API** - User management endpoints as examples

## Prerequisites

- Node.js v20 or higher
- Yarn package manager

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   yarn install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your configuration

## Database Configuration

The application supports PostgreSQL database connection using a single URI string:

```bash
# Recommended: Use single connection string
POSTGRES_URI=postgresql://username:password@host:port/database

# Alternative: Individual credentials (fallback)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=bo_panel
```

The application will prioritize `POSTGRES_URI` if provided, otherwise fall back to individual database credentials for backward compatibility.

## Scripts

- `yarn dev` - Start development server with hot reload
- `yarn build` - Build the application for production
- `yarn start` - Start the production server
- `yarn clean` - Clean the build directory
- `yarn type-check` - Run TypeScript type checking

## Development

Start the development server:

```bash
yarn dev
```

OR

```bash
docker-compose up
```

The server will start on `http://localhost:3000` (or the port specified in your `.env` file).

The application can be deployed in a Docker container with OpenVPN connection for secure networking.

### Prerequisites

1. Docker and Docker Compose installed
2. Put openvpn config in `docker/openvpn/client.ovpn`
2. Put openvpn auth in `docker/openvpn/auth.txt`
