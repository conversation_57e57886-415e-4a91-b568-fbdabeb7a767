# Daily Bet Report API

This document describes the Daily Bet Report proxy endpoint that uses Dagur authentication to fetch data from the external Pronet Gaming API.

## Endpoint

**POST** `/api/pg-dagur/v1/internal/reports/daily-bet-report`

## Description

This endpoint acts as a proxy to the external daily bet report API at `https://jord.pronetgaming.eu/api/backoffice/report/dailyBetReport/byDay`. It uses Dagur authentication to obtain a bearer token, then uses that token to make authenticated requests to the external API and returns the response in a standardized format.

## Authentication

The endpoint uses Dagur bearer token authentication automatically. The system will:
1. Authenticate with <PERSON><PERSON><PERSON> using environment variables:
   - `PG_DAGUR_USERNAME`
   - `PG_DAGUR_PASSWORD`
   - `PG_DAGUR_OTP_SECRET`
2. Make a GET request to `https://dagur.pgbo.io/restricted/new-bo-daily-bet-report.xhtml` to obtain a bearer token
3. Use the bearer token in the Authorization header to authenticate with the external API

## Request Format

### Headers
```
Content-Type: application/json
```

### Request Body
The request body should match the format expected by the external API:

```json
{
  "timeZoneOffset": "GMT+3",
  "newFetch": true,
  "betBuilder": "a",
  "traderIdsList": null,
  "bySettlementDate": true,
  "channel": "a",
  "columns": "a",
  "earlyPayout": "a",
  "currency": 1,
  "device": "a",
  "endDate": "2025-07-17",
  "startDate": "2025-07-17",
  "testPlayer": "a",
  "custom": "a",
  "transactionCurrencyIds": [],
  "isWithoutCurrency": false,
  "pageSize": 10,
  "offset": 1,
  "sortBy": "playedDate",
  "sortAsc": true
}
```

### Required Fields
- `startDate` (string): Start date in YYYY-MM-DD format
- `endDate` (string): End date in YYYY-MM-DD format
- `currency` (number): Currency ID
- `pageSize` (number): Number of records per page
- `offset` (number): Page offset for pagination

### Optional Fields
- `timeZoneOffset` (string): Timezone offset (default: "GMT+3")
- `newFetch` (boolean): Whether to fetch new data
- `betBuilder` (string): Bet builder filter
- `traderIdsList` (array): List of trader IDs to filter by
- `bySettlementDate` (boolean): Whether to filter by settlement date
- `channel` (string): Channel filter
- `columns` (string): Columns to include
- `earlyPayout` (string): Early payout filter
- `device` (string): Device filter
- `testPlayer` (string): Test player filter
- `custom` (string): Custom filter
- `transactionCurrencyIds` (array): Transaction currency IDs
- `isWithoutCurrency` (boolean): Whether to include records without currency
- `sortBy` (string): Field to sort by
- `sortAsc` (boolean): Sort direction (true for ascending)

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Daily bet report retrieved successfully",
  "data": {
    "totals": {
      "totalCoupons": 686,
      "totalPlayAmount": 362660.94,
      "totalOpenCoupons": 0,
      "totalOpenReturn": 0.0,
      "totalOpenAmount": 0.0,
      "totalWinCoupons": 186,
      "totalWinAmount": 0.0,
      "totalWinReturn": 287987.0,
      "totalLoseCoupons": 0,
      "totalLoseAmount": 0.0,
      "totalVoidCoupons": 10,
      "totalVoidAmount": 4463.13,
      "totalVoidReturn": 0.0,
      "totalPartCashoutAmount": 0.0,
      "totalPartCashoutCount": 0,
      "totalCompCashoutAmount": 346.52,
      "totalCompCashoutCount": 4,
      "totRealBalPlayAmount": 362460.94,
      "totBonBalPlayAmount": 200.0,
      "totFreeBalPlayAmount": 0.0,
      "totRealBalWinReturn": 287987.0,
      "totBonBalWinReturn": 0.0,
      "totRealBalVoidReturn": 3031.63,
      "totBonBalVoidReturn": 0.0,
      "ngr": 71095.79,
      "ggr": 71295.79,
      "transactionCurrency": null
    },
    "items": [
      {
        "playedDate": "2025-07-17",
        "traderName": "MAKROBET",
        "totalCoupons": 686,
        "totalPlayAmount": 362660.94,
        "totalWinCoupons": 186,
        "totalWinReturn": 287987.0,
        "totalVoidCoupons": 10,
        "totalVoidAmount": 4463.13,
        "ngr": 71095.79,
        "ggr": 71295.79
      }
    ],
    "currentPage": 1,
    "totalPageCount": 1,
    "totalRecordCount": 1,
    "pageSize": 10,
    "success": false,
    "reportName": "TRADER_REPORTS_API.APP_SET_DAILY_BETS_TRN_RPT",
    "columns": [...]
  },
  "timestamp": "2025-07-17T10:30:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Failed to retrieve daily bet report",
  "error": "External API request failed with status: 500 Internal Server Error",
  "timestamp": "2025-07-17T10:30:00.000Z"
}
```

## Usage Examples

### Basic Request
```bash
curl -X POST http://localhost:3000/api/pg-dagur/v1/internal/reports/daily-bet-report \
  -H "Content-Type: application/json" \
  -d '{
    "timeZoneOffset": "GMT+3",
    "newFetch": true,
    "currency": 1,
    "endDate": "2025-07-17",
    "startDate": "2025-07-17",
    "pageSize": 10,
    "offset": 1,
    "sortBy": "playedDate",
    "sortAsc": true
  }'
```

### JavaScript/TypeScript Example
```typescript
const response = await fetch('/api/pg-dagur/v1/internal/reports/daily-bet-report', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    timeZoneOffset: 'GMT+3',
    newFetch: true,
    currency: 1,
    endDate: '2025-07-17',
    startDate: '2025-07-17',
    pageSize: 10,
    offset: 1,
    sortBy: 'playedDate',
    sortAsc: true,
  }),
});

const data = await response.json();
console.log('Daily bet report:', data);
```

## Error Handling

The endpoint handles various error scenarios:

1. **Missing Request Body**: Returns 400 with validation error
2. **Authentication Failure**: Returns 500 if Dagur authentication fails
3. **External API Error**: Returns 500 if the external API request fails
4. **Network Issues**: Returns 500 with appropriate error message

## Environment Variables

Ensure these environment variables are set:

- `PG_DAGUR_USERNAME`: Dagur username for authentication
- `PG_DAGUR_PASSWORD`: Dagur password for authentication  
- `PG_DAGUR_OTP_SECRET`: Dagur OTP secret for two-factor authentication

## Notes

- The endpoint automatically handles Dagur session management
- Requests are logged for debugging purposes
- The external API response is returned as-is within the standardized response wrapper
- Authentication cookies are automatically included in requests to the external API
