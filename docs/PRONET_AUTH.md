# Pronet Authentication System

This document describes the Pronet API authentication system implemented in the BO Panel API.

## Overview

The Pronet authentication system provides secure communication with the Pronet API using a custom checksum-based authentication mechanism. All requests to Pronet endpoints are automatically authenticated using SHA-512 checksums.

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
PRONET_HOST=https://api.pronet.example.com
PUBLIC_API_USERNAME=your-public-api-username
PRONET_API_KEY=your-pronet-api-key
```

### Required Variables

- `PRONET_HOST`: The base URL of the Pronet API server
- `PUBLIC_API_USERNAME`: Your public API username for Pronet
- `PRONET_API_KEY`: Your secret API key for generating checksums

## Authentication Mechanism

### Checksum Generation

The Pronet API requires a `checksum` header for all requests. The checksum is calculated as follows:

1. **Prepare JSON Body**: Minify the JSON request body (remove unnecessary whitespace)
2. **Concatenate**: Combine the minified JSON body with the secret API key
3. **Hash**: Generate SHA-512 hash of the concatenated string
4. **Encode**: Base64 encode the hash result

```typescript
const input = jsonBody + secret;
const hash = crypto.createHash('sha512').update(input, 'utf8').digest();
const checksum = hash.toString('base64');
```

### URL Structure

All Pronet API requests follow this URL pattern:
```
[PRONET_HOST]/api/pronet/v1/[endpoint]
```

Example:
- Local endpoint: `POST /pronet/users/profile`
- Actual Pronet API: `POST https://api.pronet.example.com/api/pronet/v1/users/profile`

## API Endpoints

### System Endpoints

#### Health Check
```http
GET /pronet/health
```
Checks if the Pronet API is accessible and properly configured.

#### Configuration Info
```http
GET /pronet/config
```
Returns current Pronet configuration (without sensitive data).

#### System Test
```http
GET /pronet/test
```
Validates the entire Pronet authentication system and generates sample requests.

### Proxy Endpoints

#### Wildcard Proxy
```http
POST /pronet/*
PUT /pronet/*
PATCH /pronet/*
DELETE /pronet/*
GET /pronet/*
```
Proxies any request to the corresponding Pronet API endpoint with proper authentication.

## Usage Examples

### Basic Request
```javascript
// Frontend request
const response = await fetch('/pronet/users/profile', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    userId: 123,
    action: 'get_profile'
  })
});

// This gets proxied to:
// POST https://api.pronet.example.com/api/pronet/v1/users/profile
// With proper checksum header automatically added
```

### Call Requests Example
```javascript
// Forward a call request to Pronet API
const response = await fetch('/pronet/v1/call-requests', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    id: 1,
    type: 3,
    token: "AABBCCDDEEFFGGHHIIJJKKLL", // session token
    code: "2025060100000",
    time: "10:00 - 11:00"
  })
});

// This gets proxied to:
// POST https://api.pronet.example.com/api/pronet/v1/call-requests
// With proper checksum header automatically added
```

### Using the Pronet Client Directly
```typescript
import { pronetApiClient } from '@/network/pronet/PronetApiClient';

// Make a direct request
const result = await pronetApiClient.post('users/profile', {
  userId: 123,
  action: 'get_profile'
});

// Or use convenience methods
const userData = await pronetApiClient.get('users/123');
const updateResult = await pronetApiClient.patch('users/123', { name: 'New Name' });
```

## Architecture

### Components

1. **PronetAuth Utilities** (`src/utils/pronetAuth.ts`)
   - Checksum generation
   - JSON minification
   - URL building
   - Credential management

2. **Authentication Middleware** (`src/middleware/pronetAuth.ts`)
   - Request preprocessing
   - Checksum calculation
   - Header injection

3. **API Client** (`src/network/pronet/PronetApiClient.ts`)
   - Direct Pronet API communication
   - Request/response handling
   - Error management

4. **Controller** (`src/controllers/pronet.controller.ts`)
   - Proxy endpoint handling
   - System validation
   - Health checks

5. **Validator** (`src/utils/pronetValidator.ts`)
   - System testing
   - Configuration validation
   - Sample request generation

### Request Flow

1. Client makes request to `/pronet/endpoint`
2. Express routes to Pronet controller
3. Pronet auth middleware processes request:
   - Validates configuration
   - Minifies JSON body
   - Generates checksum
   - Adds required headers
4. Controller proxies to actual Pronet API
5. Response is returned to client

## Testing

### System Validation
```bash
curl http://localhost:3000/pronet/test
```

### Health Check
```bash
curl http://localhost:3000/pronet/health
```

### Configuration Check
```bash
curl http://localhost:3000/pronet/config
```

## Error Handling

The system provides detailed error messages for common issues:

- **Missing Environment Variables**: Clear indication of which variables are missing
- **Invalid JSON**: Validation errors for malformed request bodies
- **Checksum Failures**: Detailed logging for authentication issues
- **Network Errors**: Proper error propagation from Pronet API

## Security Considerations

- API keys are never exposed in responses or logs
- Checksums are generated server-side only
- All requests are logged for debugging (without sensitive data)
- Environment variables are validated on startup

## Troubleshooting

### Common Issues

1. **"PRONET_HOST environment variable is required"**
   - Ensure all required environment variables are set in `.env`

2. **"Failed to generate Pronet checksum"**
   - Check that the request body is valid JSON
   - Verify the PRONET_API_KEY is correctly set

3. **"Pronet API request failed: 401"**
   - Verify the API key is correct
   - Check that the checksum is being generated properly

4. **"Pronet API request failed: 404"**
   - Verify the endpoint exists in the Pronet API
   - Check the URL construction in logs

### Debug Mode

Set `NODE_ENV=development` to enable detailed logging and stack traces in error responses.
