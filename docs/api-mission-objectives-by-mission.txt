MISSION OBJECTIVES BY MISSION API ENDPOINT
==========================================

GET /api/v1/makroz/admin/mission-objectives/mission/:missionId
--------------------------------------------------------------

Description: Retrieves all objectives for a specific mission. This admin endpoint provides complete objective details including mission information.

Authentication: Admin authentication required
- Must be authenticated as an admin user
- Uses the adminAuth middleware for validation

Path Parameters:
- missionId: number (required) - The ID of the mission to get objectives for

Request:
- Method: GET
- URL: /api/v1/makroz/admin/mission-objectives/mission/:missionId
- Headers:
  - Authorization: Bearer <ADMIN_JWT_TOKEN>
- Body: None

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "missionId": number,
    "mission": {
      "id": number,
      "name": string,
      "missionType": string,
      "reward": number,
      "description": string,
      "startDate": number,
      "endDate": number,
      "createdAt": string,
      "updatedAt": string
    },
    "objectives": [
      {
        "id": number,
        "missionId": number,
        "objectiveType": string,
        "subtype": string | null,
        "operator": string,
        "targetValue": string,
        "description": string | null,
        "timeframeStart": number | null,
        "timeframeEnd": number | null,
        "metadata": any,
        "createdAt": string,
        "updatedAt": string,
        "mission": {
          "id": number,
          "name": string,
          // ... mission details
        }
      }
    ],
    "totalObjectives": number
  }
}

Response Examples:

Success Response (200 OK):
```json
{
  "success": true,
  "message": "Mission objectives retrieved successfully",
  "data": {
    "missionId": 1,
    "mission": {
      "id": 1,
      "name": "Daily Login Bonus",
      "missionType": "daily",
      "reward": 100,
      "description": "Login daily to earn bonus points",
      "startDate": 1704067200,
      "endDate": 1735689600,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    "objectives": [
      {
        "id": 1,
        "missionId": 1,
        "objectiveType": "slot",
        "subtype": null,
        "operator": "ge",
        "targetValue": "10",
        "description": "Play at least 10 slot games",
        "timeframeStart": null,
        "timeframeEnd": null,
        "metadata": null,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "mission": {
          "id": 1,
          "name": "Daily Login Bonus",
          "missionType": "daily",
          "reward": 100,
          "description": "Login daily to earn bonus points",
          "startDate": 1704067200,
          "endDate": 1735689600,
          "createdAt": "2024-01-01T00:00:00.000Z",
          "updatedAt": "2024-01-01T00:00:00.000Z"
        }
      },
      {
        "id": 2,
        "missionId": 1,
        "objectiveType": "deposit",
        "subtype": null,
        "operator": "ge",
        "targetValue": "50",
        "description": "Make a deposit of at least $50",
        "timeframeStart": 1704067200,
        "timeframeEnd": 1704153600,
        "metadata": {
          "currency": "USD",
          "minAmount": 50
        },
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "mission": {
          "id": 1,
          "name": "Daily Login Bonus"
          // ... mission details
        }
      }
    ],
    "totalObjectives": 2
  }
}
```

Mission with No Objectives Response (200 OK):
```json
{
  "success": true,
  "message": "Mission objectives retrieved successfully",
  "data": {
    "missionId": 5,
    "mission": {
      "id": 5,
      "name": "Simple Mission",
      "missionType": "custom",
      "reward": 50,
      "description": "A mission with no objectives",
      "startDate": 1704067200,
      "endDate": 1735689600,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    "objectives": [],
    "totalObjectives": 0
  }
}
```

Error Responses:

Invalid Mission ID (400 Bad Request):
```json
{
  "success": false,
  "message": "Valid mission ID is required"
}
```

Mission Not Found (404 Not Found):
```json
{
  "success": false,
  "message": "Mission with this ID does not exist"
}
```

Unauthorized (401/403):
```json
{
  "success": false,
  "message": "Admin authentication required"
}
```

Internal Server Error (500):
```json
{
  "success": false,
  "message": "Failed to retrieve mission objectives",
  "error": "Detailed error message"
}
```

ENDPOINT BEHAVIOR
=================

Validation:
1. Validates that missionId is a positive integer
2. Checks that the mission exists in the database
3. Requires admin authentication

Data Retrieval:
1. Fetches mission details to include in response
2. Retrieves all objectives for the specified mission
3. Includes mission relation data in each objective
4. Orders objectives by creation date (newest first)

Response Structure:
- Includes complete mission information
- Lists all objectives with full details
- Provides total count of objectives
- Each objective includes its parent mission data

Use Cases:
- Admin dashboard showing mission details with objectives
- Mission management interface
- Objective assignment and tracking
- Mission configuration review
- Debugging mission setup issues

Notes:
- Admin authentication is required (uses adminAuth middleware)
- Returns empty objectives array if mission has no objectives
- Includes complete mission and objective details for comprehensive view
- Objectives are ordered by creation date (DESC)
- Each objective includes its parent mission relation data

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/admin/mission-objectives/mission/1 \
  -H "Authorization: Bearer <ADMIN_JWT_TOKEN>" \
  -H "Content-Type: application/json"

Related Endpoints:
- GET /api/v1/makroz/admin/missions/:id - Get mission details
- GET /api/v1/makroz/admin/mission-objectives - List all objectives with filtering
- POST /api/v1/makroz/admin/mission-objectives - Create new objective
- GET /api/v1/makroz/admin/mission-objective-assignments/mission/:missionId - Get objective assignments for mission
