EXTENDED USER POINTS STATISTICS API ENDPOINT
============================================

GET /api/v1/makroz/admin/extended-users/stats
---------------------------------------------

Description: Retrieves statistics about extended user points, including total users, total points, and average points across all users.

Authentication: Admin Bearer JWT Token (required)
- The JWT token must be provided in the Authorization header
- Admin authentication is validated via EbetLab API using adminAuth middleware

Request:
- Method: GET
- URL: /api/v1/makroz/admin/extended-users/stats
- Headers:
  - Authorization: Bearer <ADMIN_JWT_TOKEN>
- Body: None

Response Format:
{
  "success": true,
  "message": "Extended user points statistics retrieved successfully",
  "data": {
    "totalUsers": 150,
    "totalPoints": 45000,
    "averagePoints": 300.00
  }
}

Response Fields:
- totalUsers (number): Total count of extended users in the system
- totalPoints (number): Sum of all points across all extended users
- averagePoints (number): Average points per user (rounded to 2 decimal places)

Error Response:
{
  "success": false,
  "message": "Failed to fetch extended user points statistics",
  "error": "Error description"
}

HTTP Status Codes:
- 200: Success - Statistics retrieved successfully
- 401: Unauthorized - Invalid or missing admin credentials
- 500: Internal Server Error - Database or server error

Notes:
- This endpoint requires admin authentication via the adminAuth middleware
- Statistics are calculated in real-time from the extended_users table
- Average points is calculated as total points divided by total users
- If no users exist, all values will be 0
- The endpoint follows the same pattern as other admin statistics endpoints

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/admin/extended-users/stats \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

Example Response:
{
  "success": true,
  "message": "Extended user points statistics retrieved successfully",
  "data": {
    "totalUsers": 1250,
    "totalPoints": 375000,
    "averagePoints": 300.00
  }
}
