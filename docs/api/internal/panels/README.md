# Internal Panels API

## Overview
The Internal Panels API provides authenticated access to various external panel systems for internal users. All endpoints require JWT Bearer token authentication.

## Available Panels

### Ebetlab Panel
- **Endpoint**: `POST /api/internal/panels/ebetlab`
- **Purpose**: Login to Ebetlab system
- **Documentation**: [Ebetlab Panel API](./ebetlab.md)

### PG Dagur Panel  
- **Endpoint**: `POST /api/internal/panels/pgdagur`
- **Purpose**: Login to PG Dagur system
- **Documentation**: [PG Dagur Panel API](./pgdagur.md)

## Authentication

All panel endpoints require internal authentication using JWT Bearer tokens:

```
Authorization: Bearer <your-jwt-token>
```

## Common Request Format

All panel login endpoints accept the same request format:

```json
{
  "username": "string",
  "password": "string",
  "otpSecret": "string"
}
```

**Note**: The `otpSecret` parameter should contain the TOTP secret key. The endpoints will automatically generate the 6-digit OTP code internally using a 30-second interval.

## Common Response Format

**Success Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    // Panel-specific response data
  },
  "timestamp": "2025-07-18T10:30:00.000Z"
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Error description",
  "details": "Additional error information"
}
```

## Error Codes

- `400 Bad Request`: Invalid request data or validation errors
- `401 Unauthorized`: Missing or invalid authentication token  
- `500 Internal Server Error`: Server-side errors

## Usage Examples

### Ebetlab Login
```bash
curl -X POST http://localhost:3000/api/internal/panels/ebetlab \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"username": "user", "password": "pass", "otpSecret": "your-totp-secret-key"}'
```

### PG Dagur Login
```bash
curl -X POST http://localhost:3000/api/internal/panels/pgdagur \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"username": "user", "password": "pass", "otpSecret": "your-totp-secret-key"}'
```
