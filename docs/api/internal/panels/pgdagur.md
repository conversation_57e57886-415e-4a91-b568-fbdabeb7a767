# PG Dagur Panel API

## Overview
This API provides internal access to PG Dagur functionality for authenticated users.

## Authentication
All endpoints require internal authentication using JWT Bearer tokens.

Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### POST /api/internal/panels/pgdagur

Login to PG Dagur using provided credentials.

#### Request Body
```json
{
  "username": "string",
  "password": "string",
  "otpSecret": "string"
}
```

#### Request Body Parameters
- `username` (string, required): PG Dagur username
- `password` (string, required): PG Dagur password
- `otpSecret` (string, required): TOTP secret key (the endpoint will generate the 6-digit OTP code internally)

#### Response

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "PG Dagur login successful",
  "data": {
    "session": {
      "cookies": {
        "DAGUR_CLIENT_TIMEZONE": "-180",
        "JSESSIONID": "session-id",
        "__nxquid": "nxquid-value"
      }
    }
  },
  "timestamp": "2025-07-18T10:30:00.000Z"
}
```

**Error Responses:**

**400 Bad Request - Missing Fields:**
```json
{
  "success": false,
  "message": "Validation Error",
  "details": "Missing required fields: username, password"
}
```

**400 Bad Request - Invalid OTP Secret:**
```json
{
  "success": false,
  "message": "Validation Error",
  "details": "OTP secret must be a non-empty string"
}
```

**400 Bad Request - TOTP Generation Failed:**
```json
{
  "success": false,
  "message": "Validation Error",
  "details": "TOTP generation failed: Invalid secret format"
}
```

**401 Unauthorized - Authentication Failed:**
```json
{
  "success": false,
  "message": "Authorization header is required"
}
```

**400 Bad Request - PG Dagur Login Failed:**
```json
{
  "success": false,
  "message": "Validation Error",
  "details": "PG Dagur authentication failed: Failed to login: Invalid credentials"
}
```

#### Example Usage

```bash
curl -X POST http://localhost:3000/api/internal/panels/pgdagur \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "username": "your-username",
    "password": "your-password",
    "otpSecret": "your-totp-secret-key"
  }'
```

## Error Handling

The API follows standard HTTP status codes and returns consistent error responses:

- `400 Bad Request`: Invalid request data or validation errors
- `401 Unauthorized`: Missing or invalid authentication token
- `500 Internal Server Error`: Server-side errors

All error responses include:
- `success: false`
- `message`: Error description
- `details` (optional): Additional error information

## Notes

- The PG Dagur login process involves multiple steps including cookie retrieval and view state management
- The session returned contains cookies that can be used for subsequent PG Dagur API calls
- Sessions have a limited lifetime and may need to be refreshed periodically
