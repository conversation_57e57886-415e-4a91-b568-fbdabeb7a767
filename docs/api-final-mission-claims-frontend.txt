FRONTEND API REFERENCE - FINAL MISSION CLAIMS
===========================================

This document provides detailed information about the Final Mission Claims API endpoints for frontend implementation.

AUTHENTICATION
--------------
All endpoints require JWT Bearer token authentication:
Authorization: Bearer <JWT_TOKEN>

The JWT token's "sub" field contains the user's external ID used for identification.

ENDPOINT 1: CLAIM FINAL MISSION REWARD
======================================

POST /api/v1/makroz/final-mission-claims

Description:
Claims the final reward for completing all missions of a specific type (daily, weekly, or monthly) within the current time period.

How it works:
1. Validates JWT token and extracts user external ID
2. Finds the extended user record
3. Gets all active missions of the specified type
4. Validates that user has completed ALL missions of that type in current period
5. Checks if user has already claimed this reward type in current period
6. Calculates reward amount based on completed missions count
7. Creates final mission claim record
8. Awards points to user with transaction logging
9. Returns claim details

Request Headers:
- Authorization: Bearer <JWT_TOKEN> (required)
- Content-Type: application/json

Request Body:
{
  "claimType": "daily" | "weekly" | "monthly"
}

Reward Calculation:
- Daily: 10 * N points (where N = number of completed daily missions)
- Weekly: 30 * N points (where N = number of completed weekly missions)  
- Monthly: 100 * N points (where N = number of completed monthly missions)

Success Response (201):
{
  "success": true,
  "message": "Final mission reward claimed successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "claimType": "daily",
    "grantedReward": 150,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "user": {
      "id": 1,
      "externalId": 12345,
      "points": 500,
      "external_username": "user123",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}

Error Responses:

400 Bad Request - Invalid claim type:
{
  "success": false,
  "message": "Invalid claim type. Must be 'daily', 'weekly', or 'monthly'"
}

400 Bad Request - No active missions:
{
  "success": false,
  "message": "No active daily missions found"
}

400 Bad Request - Not all missions completed:
{
  "success": false,
  "message": "You have not completed all daily missions yet. Completed: 2/3"
}

409 Conflict - Already claimed:
{
  "success": false,
  "message": "You have already claimed the daily final mission reward for this period"
}

401 Unauthorized - Invalid token:
{
  "success": false,
  "message": "Authorization header is required"
}

404 Not Found - User not found:
{
  "success": false,
  "message": "Extended user not found"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Failed to claim final mission reward",
  "error": "Detailed error message"
}

ENDPOINT 2: GET LATEST FINAL MISSION CLAIMS
===========================================

GET /api/v1/makroz/final-mission-claims/me

Description:
Retrieves the user's latest final mission claims for each claim type (daily, weekly, monthly). Returns null for any claim type that the user has never claimed.

Request Headers:
- Authorization: Bearer <JWT_TOKEN> (required)

Request Body: None

Success Response (200):
{
  "success": true,
  "message": "Latest final mission claims retrieved successfully",
  "data": {
    "daily": {
      "id": 1,
      "userId": 12345,
      "claimType": "daily",
      "grantedReward": 150,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "user": {
        "id": 1,
        "externalId": 12345,
        "points": 500,
        "external_username": "user123",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    },
    "weekly": {
      "id": 2,
      "userId": 12345,
      "claimType": "weekly",
      "grantedReward": 300,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "user": {
        "id": 1,
        "externalId": 12345,
        "points": 500,
        "external_username": "user123",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    },
    "monthly": null
  }
}

Error Responses:

401 Unauthorized - Invalid token:
{
  "success": false,
  "message": "Authorization header is required"
}

404 Not Found - User not found:
{
  "success": false,
  "message": "Extended user not found"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Failed to retrieve latest final mission claims",
  "error": "Detailed error message"
}

IMPLEMENTATION NOTES
===================

1. Claim Eligibility:
   - User must have completed ALL missions of the specified type within current time period
   - User can only claim each reward type once per time period
   - Time periods reset at midnight UTC for daily, Monday 00:00 UTC for weekly, 1st of month 00:00 UTC for monthly

2. Reward Calculation:
   - Rewards are calculated dynamically based on number of completed missions
   - Formula: multiplier * completed_missions_count
   - Daily multiplier: 10, Weekly multiplier: 30, Monthly multiplier: 100

3. Transaction Logging:
   - All final mission claims create transaction records
   - Transaction category: MISSION_REWARD
   - Transaction type: DEPOSIT
   - Metadata includes claim details for audit trail

4. Database Operations:
   - Claims use database transactions for atomicity
   - Points are awarded and claim record created in single transaction
   - Prevents double-claiming through database constraints

5. Frontend Integration Tips:
   - Check latest claims on page load to show claim status
   - Use mission statistics endpoint to determine claim eligibility
   - Handle 409 errors gracefully (already claimed)
   - Show countdown timers using mission reset times endpoint
   - Display reward amounts before claiming using the calculation formula

6. Error Handling:
   - Always check success field in response
   - Handle network errors and timeouts
   - Show user-friendly messages for different error types
   - Retry logic for 500 errors (with exponential backoff)

EXAMPLE USAGE FLOW
==================

1. Get user's mission statistics to check completion status
2. If all missions of a type are completed, show claim button
3. Get latest claims to check if already claimed this period
4. If not claimed, allow user to claim reward
5. After successful claim, update UI and refresh user balance
6. Handle errors appropriately with user feedback

Example JavaScript implementation:

// Check if user can claim daily reward
const stats = await fetch('/api/v1/makroz/extended-users/me/missions/stats');
const latestClaims = await fetch('/api/v1/makroz/final-mission-claims/me');

// Determine if daily claim is available
const canClaimDaily = stats.completedEligibleMissions.daily > 0 && 
                     !latestClaims.data.daily;

// Claim daily reward
if (canClaimDaily) {
  const claimResponse = await fetch('/api/v1/makroz/final-mission-claims', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ claimType: 'daily' })
  });
}
