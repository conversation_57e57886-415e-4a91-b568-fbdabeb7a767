MAKROZ ADMIN API - MISSION RULE ASSIGNMENTS
==========================================

Mission Rule Assignments are junction table records that link missions with mission rules, creating many-to-many relationships between missions and their eligibility rules.

Entity Structure:
- id: Primary key (auto-generated)
- missionId: Integer (references missions with foreign key)
- missionRuleId: Integer (references mission_rules with foreign key)
- createdAt: Timestamp
- updatedAt: Timestamp

Unique Constraint: One assignment per mission-rule pair (missionId + missionRuleId)

POST /api/v1/makroz/admin/mission-rule-assignments
-------------------------------------------------

Description: Creates a new mission rule assignment linking a mission to a rule.

Authentication: Admin authentication required

Request:
- Method: POST
- URL: /api/v1/makroz/admin/mission-rule-assignments
- Headers:
  - Authorization: Bearer <ADMIN_TOKEN>
  - Content-Type: application/json
- Body:
{
  "missionId": number,
  "missionRuleId": number
}

Success Response (201):
{
  "success": true,
  "message": "Mission rule assignment created successfully",
  "data": {
    "id": 1,
    "missionId": 1,
    "missionRuleId": 1,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T10:30:00.000Z",
    "mission": {
      "id": 1,
      "name": "Daily Login",
      "missionType": "daily",
      "reward": 100,
      "description": "Login daily to earn rewards",
      "startDate": 1703000000,
      "endDate": 1703086400,
      "createdAt": "2025-06-20T08:00:00.000Z",
      "updatedAt": "2025-06-20T08:00:00.000Z"
    },
    "missionRule": {
      "id": 1,
      "ruleType": "totalDeposit",
      "compare": "ge",
      "compareValue": "100",
      "minDate": null,
      "maxDate": null,
      "createdAt": "2025-06-20T08:00:00.000Z",
      "updatedAt": "2025-06-20T08:00:00.000Z"
    }
  }
}

GET /api/v1/makroz/admin/mission-rule-assignments
------------------------------------------------

Description: Retrieves mission rule assignments with filtering and pagination.

Authentication: Admin authentication required

Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- sortBy: 'id' | 'missionId' | 'missionRuleId' | 'createdAt' | 'updatedAt' (default: 'createdAt')
- sortOrder: 'ASC' | 'DESC' (default: 'DESC')
- missionId: number (filter by mission ID)
- missionRuleId: number (filter by mission rule ID)
- createdAtFrom: number (timestamp filter)
- createdAtTo: number (timestamp filter)
- updatedAtFrom: number (timestamp filter)
- updatedAtTo: number (timestamp filter)
- search: string (searches missionId and missionRuleId)

Success Response (200):
{
  "success": true,
  "message": "Mission rule assignments retrieved successfully",
  "data": {
    "missionRuleAssignments": [...],
    "total": 25,
    "page": 1,
    "limit": 20,
    "totalPages": 2
  }
}

GET /api/v1/makroz/admin/mission-rule-assignments/:id
----------------------------------------------------

Description: Retrieves a specific mission rule assignment by ID.

Authentication: Admin authentication required

Path Parameters:
- id: number (mission rule assignment ID)

Success Response (200):
{
  "success": true,
  "message": "Mission rule assignment retrieved successfully",
  "data": {
    "id": 1,
    "missionId": 1,
    "missionRuleId": 1,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T10:30:00.000Z",
    "mission": {...},
    "missionRule": {...}
  }
}

PATCH /api/v1/makroz/admin/mission-rule-assignments/:id
------------------------------------------------------

Description: Updates a mission rule assignment record.

Authentication: Admin authentication required

Path Parameters:
- id: number (mission rule assignment ID)

Request Body (all fields optional):
{
  "missionId": number,
  "missionRuleId": number
}

Success Response (200):
{
  "success": true,
  "message": "Mission rule assignment updated successfully",
  "data": {
    "id": 1,
    "missionId": 2,
    "missionRuleId": 1,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T11:45:00.000Z",
    "mission": {...},
    "missionRule": {...}
  }
}

DELETE /api/v1/makroz/admin/mission-rule-assignments/:id
-------------------------------------------------------

Description: Deletes a mission rule assignment record.

Authentication: Admin authentication required

Path Parameters:
- id: number (mission rule assignment ID)

Success Response (200):
{
  "success": true,
  "message": "Mission rule assignment deleted successfully",
  "data": {
    "id": 1,
    "deletedAt": "2025-06-25T12:00:00.000Z"
  }
}

Common Error Responses:

400 Bad Request:
{
  "success": false,
  "message": "Mission ID is required and must be a positive number"
}

401 Unauthorized:
{
  "success": false,
  "message": "Invalid or expired admin credentials"
}

404 Not Found:
{
  "success": false,
  "message": "Mission rule assignment not found"
}

409 Conflict:
{
  "success": false,
  "message": "Mission rule assignment already exists for this mission and rule combination"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Failed to create mission rule assignment",
  "error": "Detailed error message"
}

PUT /api/v1/makroz/admin/mission-rule-assignments/mission/:missionId/rules
------------------------------------------------------------------------

Description: Assigns multiple rules to a specific mission.

Authentication: Admin authentication required

Path Parameters:
- missionId: number (mission ID from URL)

Request Body:
{
  "missionRuleIds": [1, 2, 3]
}

Success Response (200):
{
  "success": true,
  "message": "Processed 3 rule assignments for mission",
  "data": {
    "missionId": 1,
    "mission": {
      "id": 1,
      "name": "Daily Login",
      "missionType": "daily",
      "reward": 100,
      "description": "Login daily to earn rewards",
      "startDate": 1703000000,
      "endDate": 1703086400,
      "createdAt": "2025-06-20T08:00:00.000Z",
      "updatedAt": "2025-06-20T08:00:00.000Z"
    },
    "summary": {
      "total": 3,
      "created": 2,
      "skipped": 1
    },
    "created": [
      {
        "id": 1,
        "missionId": 1,
        "missionRuleId": 2,
        "createdAt": "2025-06-25T10:30:00.000Z",
        "updatedAt": "2025-06-25T10:30:00.000Z",
        "mission": {...},
        "missionRule": {...}
      }
    ],
    "skipped": [
      {
        "missionRuleId": 1,
        "reason": "Assignment already exists",
        "assignment": {...}
      }
    ]
  }
}

Error Responses:
- 400: Invalid mission ID, empty array, or invalid rule IDs
- 400: Mission or mission rules not found

GET /api/v1/makroz/admin/mission-rule-assignments/mission/:missionId
-------------------------------------------------------------------

Description: Retrieves all rules assigned to a specific mission.

Authentication: Admin authentication required

Path Parameters:
- missionId: number (mission ID)

Success Response (200):
{
  "success": true,
  "message": "Mission rules retrieved successfully",
  "data": {
    "missionId": 1,
    "mission": {
      "id": 1,
      "name": "Daily Login",
      "missionType": "daily",
      "reward": 100,
      "description": "Login daily to earn rewards",
      "startDate": 1703000000,
      "endDate": 1703086400,
      "createdAt": "2025-06-20T08:00:00.000Z",
      "updatedAt": "2025-06-20T08:00:00.000Z"
    },
    "assignments": [
      {
        "id": 1,
        "missionId": 1,
        "missionRuleId": 2,
        "createdAt": "2025-06-25T10:30:00.000Z",
        "updatedAt": "2025-06-25T10:30:00.000Z",
        "mission": {...},
        "missionRule": {
          "id": 2,
          "ruleType": "totalDeposit",
          "compare": "ge",
          "compareValue": "100",
          "minDate": null,
          "maxDate": null,
          "createdAt": "2025-06-20T08:00:00.000Z",
          "updatedAt": "2025-06-20T08:00:00.000Z"
        }
      }
    ],
    "totalRules": 1
  }
}

Error Responses:
- 400: Invalid mission ID
- 404: Mission not found

Notes:
- The unique constraint ensures one assignment per mission-rule pair
- Both mission and missionRule relationships are automatically loaded
- Foreign key constraints ensure referenced missions and rules exist
- CASCADE delete removes assignments when missions or rules are deleted
- All endpoints require admin authentication
- Useful for creating complex mission eligibility systems
- PUT endpoint creates new assignments (idempotent - returns 409 if already exists)
- GET endpoint includes complete mission and rule details for context
