MISSION RULE VALIDATION EXAMPLES
=================================

This document provides examples of how mission rule validation works in the system.

RULE TYPES AND VALIDATION
=========================

1. JOINED_AT (Registration timestamp)
   - Validates against customer.registration_ts
   - Example: User must have joined after January 1, 2024
   - Rule: { ruleType: "joinedAt", compare: "gt", compareValue: "1704067200" }

2. TOTAL_DEPOSIT (Total deposit amount in USD)
   - Validates against customer.summary.total_in_usd
   - Example: User must have deposited at least $100
   - Rule: { ruleType: "totalDeposit", compare: "ge", compareValue: "100" }

3. TOTAL_WITHDRAW (Total withdrawal amount in USD)
   - Validates against customer.summary.total_out_usd
   - Example: User must have withdrawn less than $500
   - Rule: { ruleType: "totalWithdraw", compare: "lt", compareValue: "500" }

4. NET_PROFIT (Calculated as total_in_usd - total_out_usd)
   - Validates against calculated net profit
   - Example: User must have positive net profit
   - Rule: { ruleType: "netProfit", compare: "gt", compareValue: "0" }

5. VIP_RANK (VIP rank with hierarchical comparison)
   - Validates against customer.rank using VIP level hierarchy
   - Supports hierarchical comparison (e.g., gold > silver > bronze)
   - Example: User must be Gold rank or higher
   - Rule: { ruleType: "vipRank", compare: "ge", compareValue: "gold" }
   - VIP Hierarchy (lowest to highest): no-vip, iron, copper, bronze, brass, nickel, steel, cobalt, titanium, tungsten, silver, gold, platinum, palladium, rhodium, osmium, iridium, mithril, adamantite, orichalcum, vibranium, unobtanium, eternium

6. COUNTRY (Registration country)
   - Validates against customer.registration_country
   - Example: User must be from specific countries
   - Rule: { ruleType: "country", compare: "eq", compareValue: "US" }

7. PHONE_NUMBER (Phone number)
   - Validates against customer.phone.full
   - Example: User must have a phone number
   - Rule: { ruleType: "phoneNumber", compare: "ne", compareValue: "" }

8. KYC_LEVEL (Verification level)
   - Validates against customer.profile.verification_level
   - Example: User must be fully verified (level 3)
   - Rule: { ruleType: "kycLevel", compare: "ge", compareValue: "3" }

9. REFERRER (Referrer ID)
   - Validates against customer.ref_id
   - Example: User must have been referred
   - Rule: { ruleType: "referrer", compare: "ne", compareValue: "" }

COMPARISON OPERATORS
===================

- eq (equal): Exact match
- ne (not equal): Must not match
- gt (greater than): Numeric comparison
- lt (less than): Numeric comparison
- ge (greater or equal): Numeric comparison
- le (less or equal): Numeric comparison

VIP LEVEL HIERARCHY COMPARISON
==============================

VIP levels are compared using a hierarchical ranking system where each level has a numeric rank:

VIP Level Hierarchy (rank 0 to 22):
0.  no-vip
1.  iron
2.  copper
3.  bronze
4.  brass
5.  nickel
6.  steel
7.  cobalt
8.  titanium
9.  tungsten
10. silver
11. gold
12. platinum
13. palladium
14. rhodium
15. osmium
16. iridium
17. mithril
18. adamantite
19. orichalcum
20. vibranium
21. unobtanium
22. eternium

Comparison Examples:
- gold > silver (rank 11 > rank 10)
- platinum >= gold (rank 12 >= rank 11)
- bronze < titanium (rank 3 < rank 8)
- silver == silver (rank 10 == rank 10)

Special Cases:
- Unknown VIP levels fall back to string comparison
- Case-insensitive matching (Gold = gold = GOLD)
- Handles different formats (gold, Gold, GOLD)

DATE RANGE FILTERING
===================

Rules can have optional minDate and maxDate fields (Unix timestamps):
- minDate: Rule only applies after this date
- maxDate: Rule only applies before this date

VALIDATION PROCESS
==================

1. Extract user ID from JWT token (user token only used for identification)
2. Check if mission exists and user isn't already participating
3. Get all rules assigned to the mission
4. If rules exist, perform admin authentication and fetch customer data:
   - Generate TOTP from API_OTP_SECRET environment variable (30-second intervals)
   - Log OTP generation with time remaining and validity window
   - Login to EbetLab using API_USERNAME, API_PASSWORD, and generated OTP
   - Obtain admin JWT token from EbetLab login response
   - Use admin token to fetch customer data via POST /api/v1/operator/customers/show/{userId}
   - Leverages existing proxy infrastructure with proper admin authentication
5. For each rule:
   - Check if current time is within date range (if specified)
   - Extract actual value from customer data based on rule type
   - Compare actual value with expected value using comparison operator
   - Record any failures with detailed error messages
6. If any rule fails, return 403 Forbidden with detailed error message
7. If all rules pass, proceed with mission participation creation using database transaction

ERROR MESSAGE FORMAT
====================

Failed rules are reported in the format:
"You do not meet the mission requirements: {ruleType} {operator} {expectedValue} (actual: {actualValue}), ..."

Examples:
"You do not meet the mission requirements: totalDeposit ge 100 (actual: 50), vipRank ge gold (actual: silver)"
"You do not meet the mission requirements: vipRank gt bronze (actual: iron), kycLevel ge 3 (actual: 1)"

ENVIRONMENT VARIABLES REQUIRED
==============================

The following environment variables must be configured for admin authentication:

- API_USERNAME: EbetLab admin username
- API_PASSWORD: EbetLab admin password
- API_OTP_SECRET: Base32-encoded TOTP secret for 2FA authentication

These credentials are used to obtain admin JWT tokens for accessing EbetLab's customer data endpoints.

OTP LOGGING AND MONITORING
===========================

The TOTP implementation includes comprehensive logging for debugging and monitoring:

- **30-Second Intervals**: OTP codes are generated using standard 30-second time windows
- **Change Detection**: Logs when a new OTP is generated vs when cached OTP is reused
- **Time Remaining**: Shows how many seconds the current OTP remains valid
- **Time Window**: Displays the current time window and timestamp for debugging

Example log output:
```
🔐 OTP Generated: 123456 (valid for 25s)
📅 Time window: 58123456 (2024-01-15T10:30:15.000Z)
🔑 Using OTP: 123456 for admin login
✅ Admin token obtained successfully
```

IMPLEMENTATION NOTES
====================

- Rule validation happens before any database changes
- Uses database transactions to ensure atomicity for database operations
- Fetches fresh customer data from EbetLab for each validation
- Uses internal proxy endpoints to handle complex EbetLab authentication
- Generates TOTP codes using the `totp-generator` library (RFC 6238 compliant)
- Follows proper admin authentication flow with 2FA support
- Supports both string and numeric comparisons
- Handles missing or null values gracefully
- Provides detailed error messages for debugging
- Separates external API calls from database transactions for better error handling
