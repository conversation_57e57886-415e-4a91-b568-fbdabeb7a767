MAKROZ PUBLIC API - MI<PERSON><PERSON> RESET TIMES ENDPOINT
==============================================

This endpoint returns the time in seconds until the next reset for daily, weekly, and monthly missions.

GET /api/v1/makroz/missions/reset-times
---------------------------------------

Description: Returns countdown timers for when periodic missions reset. This endpoint:
1. Calculates seconds until next daily reset (start of next day)
2. Calculates seconds until next weekly reset (start of next week, typically Monday)
3. Calculates seconds until next monthly reset (start of next month)
4. Provides both seconds and ISO timestamp for each reset time

Authentication: None required (public endpoint)

Request:
- Method: GET
- URL: /api/v1/makroz/missions/reset-times
- Headers: None required
- Body: None

Success Response (200):
{
  "success": true,
  "message": "Mission reset times retrieved successfully",
  "data": {
    "daily": {
      "secondsUntilReset": 43200,
      "resetTime": "2025-06-28T00:00:00.000Z"
    },
    "weekly": {
      "secondsUntilReset": 259200,
      "resetTime": "2025-06-30T00:00:00.000Z"
    },
    "monthly": {
      "secondsUntilReset": 345600,
      "resetTime": "2025-07-01T00:00:00.000Z"
    }
  }
}

Error Response (500):
{
  "success": false,
  "message": "Failed to get mission reset times",
  "error": "Error details"
}

RESPONSE FIELDS
===============

data.daily.secondsUntilReset: Number of seconds until daily missions reset
data.daily.resetTime: ISO timestamp when daily missions will reset

data.weekly.secondsUntilReset: Number of seconds until weekly missions reset
data.weekly.resetTime: ISO timestamp when weekly missions will reset

data.monthly.secondsUntilReset: Number of seconds until monthly missions reset
data.monthly.resetTime: ISO timestamp when monthly missions will reset

USAGE EXAMPLES
==============

Frontend Countdown Timer:
```javascript
fetch('/api/v1/makroz/missions/reset-times')
  .then(response => response.json())
  .then(data => {
    const dailySeconds = data.data.daily.secondsUntilReset;
    const hours = Math.floor(dailySeconds / 3600);
    const minutes = Math.floor((dailySeconds % 3600) / 60);
    const seconds = dailySeconds % 60;
    
    console.log(`Daily missions reset in: ${hours}h ${minutes}m ${seconds}s`);
  });
```

React Hook Example:
```javascript
const useMissionResetTimes = () => {
  const [resetTimes, setResetTimes] = useState(null);
  
  useEffect(() => {
    const fetchResetTimes = async () => {
      const response = await fetch('/api/v1/makroz/missions/reset-times');
      const data = await response.json();
      setResetTimes(data.data);
    };
    
    fetchResetTimes();
    const interval = setInterval(fetchResetTimes, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, []);
  
  return resetTimes;
};
```

NOTES
=====

- All times are calculated in UTC
- Weekly reset typically occurs on Monday at 00:00:00 UTC
- Monthly reset occurs on the 1st day of the month at 00:00:00 UTC
- The endpoint is lightweight and can be called frequently for real-time countdown timers
- No authentication required - this is public information
- Times are calculated server-side to ensure accuracy regardless of client timezone
- The resetTime field provides the exact moment when missions will reset
- secondsUntilReset will be 0 or very small when reset is imminent

INTEGRATION TIPS
================

1. Use this endpoint to show countdown timers in the UI
2. Call periodically (every minute) to keep timers accurate
3. Use the resetTime field to schedule automatic UI updates
4. Consider caching the response for 30-60 seconds to reduce server load
5. Handle the case where secondsUntilReset is 0 (reset just happened)
