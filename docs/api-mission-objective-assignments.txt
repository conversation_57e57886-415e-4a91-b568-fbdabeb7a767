MAKROZ ADMIN API - MISSION OBJECTIVE ASSIGNMENTS
==============================================

Mission Objective Assignments track user progress on specific mission objectives. Each assignment represents a user's progress toward completing a specific objective within a mission.

Entity Structure:
- id: Primary key (auto-generated)
- userId: Integer (references extended_users.externalId)
- missionObjectiveId: Integer (references mission_objectives.id with foreign key)
- progress: Integer (default: 0) - Current progress value
- lastCheckedRecordTimestamp: BigInt (nullable) - Last time progress was checked
- isCompleted: Bo<PERSON>an (default: false) - Calculated field indicating if objective is completed
- startDate: BigInt (nullable) - When the assignment started (timestamp in seconds)
- endDate: BigInt (nullable) - When the assignment expires (timestamp in seconds)
- createdAt: Timestamp
- updatedAt: Timestamp

Unique Constraint: One assignment per user per objective (userId + missionObjectiveId)

POST /api/v1/makroz/admin/mission-objective-assignments
------------------------------------------------------

Description: Creates a new mission objective assignment for a user.

Authentication: Admin authentication required

Request Body:
{
  "userId": number,
  "missionObjectiveId": number,
  "progress": number (optional, default: 0),
  "lastCheckedRecordTimestamp": number | null (optional)
}

Note: The isCompleted field is automatically calculated based on progress vs target value and cannot be set directly.

Success Response (201):
{
  "success": true,
  "message": "Mission objective assignment created successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "missionObjectiveId": 1,
    "progress": 0,
    "lastCheckedRecordTimestamp": null,
    "isCompleted": false,
    "startDate": 1735689000,
    "endDate": 1735775400,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T10:30:00.000Z",
    "missionObjective": {...},
    "user": {...}
  }
}

GET /api/v1/makroz/admin/mission-objective-assignments
-----------------------------------------------------

Description: Retrieves mission objective assignments with filtering and pagination.

Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- sortBy: 'id' | 'userId' | 'missionObjectiveId' | 'progress' | 'lastCheckedRecordTimestamp' | 'startDate' | 'endDate' | 'createdAt' | 'updatedAt'
- sortOrder: 'ASC' | 'DESC' (default: 'DESC')
- userId: number (filter by user ID)
- missionObjectiveId: number (filter by objective ID)
- progressMin: number (minimum progress filter)
- progressMax: number (maximum progress filter)
- createdAtFrom/To: number (timestamp filters)
- updatedAtFrom/To: number (timestamp filters)
- lastCheckedFrom/To: number (timestamp filters)
- startDateFrom/To: number (timestamp filters for assignment start dates)
- endDateFrom/To: number (timestamp filters for assignment end dates)
- search: string (searches userId and missionObjectiveId)

GET /api/v1/makroz/admin/mission-objective-assignments/:id
---------------------------------------------------------

Description: Retrieves a specific mission objective assignment by ID.

PATCH /api/v1/makroz/admin/mission-objective-assignments/:id
-----------------------------------------------------------

Description: Updates a mission objective assignment record.

Request Body (all fields optional):
{
  "userId": number,
  "missionObjectiveId": number,
  "progress": number,
  "lastCheckedRecordTimestamp": number | null
}

Note: The isCompleted field is automatically recalculated when progress is updated and cannot be set directly.

DELETE /api/v1/makroz/admin/mission-objective-assignments/:id
------------------------------------------------------------

Description: Deletes a mission objective assignment record.

MISSION-SPECIFIC ENDPOINTS
==========================

PUT /api/v1/makroz/admin/mission-objective-assignments/objective/:missionObjectiveId/users
------------------------------------------------------------------------------------------

Description: Assigns a specific objective to a user.

Path Parameters:
- missionObjectiveId: number (objective ID)

Request Body:
{
  "userId": number,
  "progress": number (optional, default: 0),
  "lastCheckedRecordTimestamp": number | null (optional)
}

Success Response (201):
{
  "success": true,
  "message": "Objective assigned to user successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "missionObjectiveId": 1,
    "progress": 0,
    "lastCheckedRecordTimestamp": null,
    "isCompleted": false,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T10:30:00.000Z",
    "missionObjective": {...},
    "user": {...}
  }
}

GET /api/v1/makroz/admin/mission-objective-assignments/objective/:missionObjectiveId
-----------------------------------------------------------------------------------

Description: Retrieves all user assignments for a specific objective.

Success Response (200):
{
  "success": true,
  "message": "Mission objective assignments retrieved successfully",
  "data": {
    "missionObjectiveId": 1,
    "missionObjective": {...},
    "assignments": [...],
    "totalAssignments": 15
  }
}

GET /api/v1/makroz/admin/mission-objective-assignments/mission/:missionId
------------------------------------------------------------------------

Description: Retrieves all objective assignments for a specific mission.

Success Response (200):
{
  "success": true,
  "message": "Mission objective assignments retrieved successfully",
  "data": {
    "missionId": 1,
    "mission": {...},
    "assignments": [...],
    "totalAssignments": 25
  }
}

GET /api/v1/makroz/admin/mission-objective-assignments/mission/:missionId/stats
------------------------------------------------------------------------------

Description: Retrieves progress statistics for a specific mission.

Success Response (200):
{
  "success": true,
  "message": "Mission progress statistics retrieved successfully",
  "data": {
    "missionId": 1,
    "mission": {...},
    "statistics": {
      "totalAssignments": 25,
      "averageProgress": 67.5,
      "completedObjectives": 8,
      "objectiveBreakdown": [
        {
          "objectiveId": 1,
          "objectiveName": "Complete 10 slot games",
          "totalAssignments": 15,
          "averageProgress": 7.2
        }
      ]
    }
  }
}

Common Error Responses:

400 Bad Request:
{
  "success": false,
  "message": "User ID is required and must be a positive number"
}

401 Unauthorized:
{
  "success": false,
  "message": "Invalid or expired admin credentials"
}

404 Not Found:
{
  "success": false,
  "message": "Mission objective assignment not found"
}

409 Conflict:
{
  "success": false,
  "message": "Mission objective assignment already exists for this user and objective"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Failed to create mission objective assignment",
  "error": "Detailed error message"
}

Notes:
- The unique constraint ensures one assignment per user per objective
- Both missionObjective and user relationships are automatically loaded
- Foreign key constraints ensure referenced users and objectives exist
- CASCADE delete removes assignments when users or objectives are deleted
- Progress tracking enables real-time mission completion monitoring
- Statistics endpoint provides comprehensive mission analytics
- All endpoints require admin authentication
- Useful for tracking user progress on complex mission objectives

isCompleted Field Calculation:
- The isCompleted field is automatically calculated based on the progress value and the objective's target value and operator
- Calculation logic uses the objective's CompareOperator (eq, ne, gt, lt, ge, le) to determine completion status
- For example: if operator is "ge" (greater or equal) and targetValue is "100", then isCompleted = (progress >= 100)
- The field is recalculated every time an assignment is retrieved from the database
- Cannot be set directly via API requests - it's always computed based on current progress
