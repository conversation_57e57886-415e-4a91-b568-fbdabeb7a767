USER ELIGIBLE MISSIONS API ENDPOINT
===================================

GET /api/v1/makroz/extended-users/me/missions
--------------------------------------------

Description: Retrieves all missions that the authenticated user is eligible for based on mission rules validation, including missions they are currently participating in.

Authentication: Bearer JWT Token (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the user ID for eligibility checking

Request:
- Method: GET
- URL: /api/v1/makroz/extended-users/me/missions
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
- Body: None

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "eligibleMissions": [
      {
        "id": number,
        "name": string,
        "missionType": string,
        "reward": number,
        "description": string,
        "startDate": number,
        "endDate": number,
        "createdAt": string,
        "updatedAt": string,
        "objectives": [
          {
            "id": number,
            "missionId": number,
            "objectiveType": string,
            "subtype": string | null,
            "operator": string,
            "targetValue": string,
            "description": string | null,
            "timeframeStart": number | null,
            "timeframeEnd": number | null,
            "metadata": any,
            "createdAt": string,
            "updatedAt": string,
            "mission": {
              "id": number,
              "name": string,
              // ... mission details
            }
          }
        ],
        "userAssignments": [
          {
            "id": number,
            "userId": number,
            "missionObjectiveId": number,
            "progress": number,
            "lastCheckedRecordTimestamp": number | null,
            "isCompleted": boolean,
            "createdAt": string,
            "updatedAt": string,
            "missionObjective": {
              "id": number,
              // ... objective details
            },
            "user": {
              "id": number,
              "externalId": number,
              "points": number,
              // ... user details
            }
          }
        ],
        "latestParticipation": {
          "id": number,
          "userId": number,
          "missionId": number,
          "isCompleted": boolean,
          "nextAllowedParticipationSeconds": number,
          "createdAt": string,
          "updatedAt": string,
          "mission": {
            "id": number,
            "name": string,
            // ... mission details
          }
        } | null
      }
    ],
    "totalEligible": number,
    "totalActive": number
  }
}

Response Examples:

Success Response (200 OK):
```json
{
  "success": true,
  "message": "Eligible missions retrieved successfully",
  "data": {
    "eligibleMissions": [
      {
        "id": 1,
        "name": "Daily Login Bonus",
        "missionType": "daily",
        "reward": 100,
        "description": "Login daily to earn bonus points",
        "startDate": 1704067200,
        "endDate": 1735689600,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "objectives": [
          {
            "id": 1,
            "missionId": 1,
            "objectiveType": "slot",
            "subtype": null,
            "operator": "ge",
            "targetValue": "10",
            "description": "Play at least 10 slot games",
            "timeframeStart": null,
            "timeframeEnd": null,
            "metadata": null,
            "createdAt": "2024-01-01T00:00:00.000Z",
            "updatedAt": "2024-01-01T00:00:00.000Z"
          }
        ],
        "userAssignments": [
          {
            "id": 1,
            "userId": 2791,
            "missionObjectiveId": 1,
            "progress": 5,
            "lastCheckedRecordTimestamp": 1704067200,
            "isCompleted": false,
            "createdAt": "2024-01-01T00:00:00.000Z",
            "updatedAt": "2024-01-01T00:00:00.000Z"
          }
        ],
        "latestParticipation": {
          "id": 1,
          "userId": 2791,
          "missionId": 1,
          "isCompleted": false,
          "nextAllowedParticipationSeconds": 4372,
          "createdAt": "2024-01-01T00:00:00.000Z",
          "updatedAt": "2024-01-01T00:00:00.000Z"
        }
      }
    ],
    "totalEligible": 1,
    "totalActive": 3
  }
}
```

No Eligible Missions Response (200 OK):
```json
{
  "success": true,
  "message": "No active missions found",
  "data": {
    "eligibleMissions": [],
    "totalEligible": 0,
    "totalActive": 0
  }
}
```

Error Responses:

Unauthorized (401):
```json
{
  "success": false,
  "message": "Authorization header is required"
}
```

Invalid Token (401):
```json
{
  "success": false,
  "message": "Invalid token"
}
```

Unable to Extract User ID (401):
```json
{
  "success": false,
  "message": "Unable to extract user ID from token"
}
```

Customer Data Fetch Error (502):
```json
{
  "success": false,
  "message": "Unable to fetch customer data for rule validation"
}
```

Internal Server Error (500):
```json
{
  "success": false,
  "message": "Failed to get eligible missions",
  "error": "Detailed error message"
}
```

ENDPOINT BEHAVIOR
=================

Mission Eligibility Logic:
1. Extracts user ID from JWT token
2. Fetches all currently active missions (startDate <= now <= endDate)
3. For each mission, checks if user meets all mission rules:
   - If mission has no rules: automatically eligible
   - If mission has rules: fetches user data from EbetLab once and validates all rules
4. For eligible missions, includes:
   - Complete mission details
   - All mission objectives
   - User's existing objective assignments (if any)
   - Latest mission participation record (if user has started the mission)

Performance Optimizations:
- User data is fetched from EbetLab only once and cached for all rule validations
- Only active missions are checked (filtered by current timestamp)
- Objective assignments are filtered per mission to avoid unnecessary data

Rule Validation:
- Supports all rule types: joinedAt, totalDeposit, totalWithdraw, netProfit, vipRank, country, phoneNumber, kycLevel, referrer
- Uses VIP level hierarchy for vipRank comparisons
- Supports all comparison operators: eq, ne, gt, lt, ge, le
- Respects date range filters (minDate/maxDate) on rules

Data Structure:
- Each eligible mission includes its objectives and the user's current assignments
- User assignments show progress, completion status, and last checked timestamps
- Latest participation shows if user has started the mission and completion status
- nextAllowedParticipationSeconds shows seconds until user can participate again (0 if can participate now)
- Missions with latestParticipation: null indicate the user hasn't started them yet

Use Cases:
- Frontend mission dashboard showing available missions
- Mobile app mission listing
- User progress tracking
- Mission recommendation system

Notes:
- Returns missions the user is eligible for (including ones they're already participating in)
- Includes missions with no rules (open to all users)
- User data fetching requires admin authentication with EbetLab
- Comprehensive logging for debugging eligibility issues
- Handles edge cases like missing user data or API failures gracefully
- The isCompleted field in userAssignments is automatically calculated based on progress vs target
- The latestParticipation field shows the user's current participation status for each mission
- The nextAllowedParticipationSeconds field is calculated at runtime based on mission type and periodic validator logic:
  * For daily missions: seconds until next day starts (0 if last participation was not today)
  * For weekly missions: seconds until next week starts (0 if last participation was not this week)
  * For monthly missions: seconds until next month starts (0 if last participation was not this month)
  * For custom missions: always 0 (handled by business logic elsewhere)

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/extended-users/me/missions \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"
