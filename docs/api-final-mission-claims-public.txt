MAKROZ PUBLIC API - FINAL MISSION CLAIMS ENDPOINTS
===============================================

This document describes the public endpoints for final mission claims functionality.

GET /api/v1/makroz/final-mission-claims/me
------------------------------------------

Description: Retrieves the latest final mission claims for the authenticated user for each claim type (daily, weekly, monthly). Returns null for any claim type that the user has never claimed.

Authentication: Bearer JWT Token (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the external ID to find the extended user

Request:
- Method: GET
- URL: /api/v1/makroz/final-mission-claims/me
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
- Body: None

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "daily": FinalMissionClaim | null,
    "weekly": FinalMissionClaim | null,
    "monthly": FinalMissionClaim | null
  }
}

FinalMissionClaim Object Structure:
{
  "id": number,
  "userId": number,
  "claimType": "daily" | "weekly" | "monthly",
  "grantedReward": number,
  "createdAt": string (ISO date),
  "updatedAt": string (ISO date),
  "user": {
    "id": number,
    "externalId": number,
    "points": number,
    "createdAt": string (ISO date),
    "updatedAt": string (ISO date)
  }
}

Success Response (200):
{
  "success": true,
  "message": "Latest final mission claims retrieved successfully",
  "data": {
    "daily": {
      "id": 1,
      "userId": 123,
      "claimType": "daily",
      "grantedReward": 50,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "user": {
        "id": 1,
        "externalId": 123,
        "points": 1250,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    },
    "weekly": null,
    "monthly": {
      "id": 3,
      "userId": 123,
      "claimType": "monthly",
      "grantedReward": 300,
      "createdAt": "2024-01-01T12:00:00.000Z",
      "updatedAt": "2024-01-01T12:00:00.000Z",
      "user": {
        "id": 1,
        "externalId": 123,
        "points": 1250,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    }
  }
}

Error Responses:

401 Unauthorized - Missing Authorization Header:
{
  "success": false,
  "message": "Authorization header is required"
}

401 Unauthorized - Invalid Token:
{
  "success": false,
  "message": "Invalid token format"
}

401 Unauthorized - Expired Token:
{
  "success": false,
  "message": "Token has expired"
}

401 Unauthorized - Unable to Extract User ID:
{
  "success": false,
  "message": "Unable to extract user ID from token"
}

404 Not Found - User Not Found:
{
  "success": false,
  "message": "Extended user not found"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Internal server error"
}

Notes:
- This endpoint returns the most recent claim for each claim type (daily, weekly, monthly)
- If a user has never claimed a particular type of final mission reward, that field will be null
- The endpoint includes the full user object in each claim for convenience
- Claims are ordered by creation date (most recent first) when multiple claims of the same type exist
- The endpoint does not require admin authentication (unlike admin final mission claim endpoints)
- The JWT token is validated for structure and expiration before processing

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/final-mission-claims/me \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"


POST /api/v1/makroz/final-mission-claims
----------------------------------------

Description: Claims a final mission reward for completing all missions of a specific type (daily, weekly, or monthly) within the current time period.

Authentication: Bearer JWT Token (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the external ID to find the extended user

Request:
- Method: POST
- URL: /api/v1/makroz/final-mission-claims
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
  - Content-Type: application/json
- Body:
{
  "claimType": "daily" | "weekly" | "monthly"
}

Response Format:
{
  "success": boolean,
  "message": string,
  "data": FinalMissionClaim
}

Success Response (201):
{
  "success": true,
  "message": "Final mission reward claimed successfully",
  "data": {
    "id": 1,
    "userId": 123,
    "claimType": "daily",
    "grantedReward": 50,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "user": {
      "id": 1,
      "externalId": 123,
      "points": 1300,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  }
}

Error Responses:

400 Bad Request - Invalid Claim Type:
{
  "success": false,
  "message": "Invalid claim type. Must be 'daily', 'weekly', or 'monthly'"
}

400 Bad Request - Already Claimed:
{
  "success": false,
  "message": "You have already claimed the daily final mission reward today. Next claim available: 2024-01-16T00:00:00.000Z"
}

400 Bad Request - No Completed Missions:
{
  "success": false,
  "message": "No completed daily missions found for the current period"
}

Notes:
- This endpoint validates that the user has completed all required missions of the specified type within the current time period
- Users can only claim each type of reward once per time period (daily/weekly/monthly)
- The reward amount is calculated as: 10 * N for daily, 30 * N for weekly, 100 * N for monthly (where N is the number of completed missions)
- The user's points are automatically updated when a claim is successful
- The endpoint uses database transactions to ensure atomicity

Example Usage:

curl -X POST http://localhost:3000/api/v1/makroz/final-mission-claims \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{"claimType": "daily"}'
