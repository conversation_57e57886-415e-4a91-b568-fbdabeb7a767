USER MISSION STATISTICS API ENDPOINT
====================================

GET /api/v1/makroz/extended-users/me/missions/stats
--------------------------------------------------

Description: Retrieves mission statistics for the authenticated user, including balance (points), total participations, completed participations, and eligible missions breakdown by type.

Authentication: Bearer JWT <PERSON>ken (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the user ID for statistics calculation

Request:
- Method: GET
- URL: /api/v1/makroz/extended-users/me/missions/stats
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
- Body: None

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "balance": number,
    "totalParticipations": number,
    "completedParticipations": number,
    "eligibleMissions": {
      "daily": number,
      "weekly": number,
      "monthly": number,
      "custom": number
    },
    "completedEligibleMissions": {
      "daily": number,
      "weekly": number,
      "monthly": number,
      "custom": number
    }
  }
}

Response Fields:
- balance: User's current point balance (total points earned from completed missions)
- totalParticipations: Total number of missions the user has participated in
- completedParticipations: Total number of missions the user has completed
- eligibleMissions: Number of currently active missions the user can participate in, grouped by mission type
- completedEligibleMissions: Number of currently active missions the user has completed, grouped by mission type

Mission Types:
- daily: Daily recurring missions
- weekly: Weekly recurring missions  
- monthly: Monthly recurring missions
- custom: Custom one-time or special missions

Success Response (200):
{
  "success": true,
  "message": "User mission statistics retrieved successfully",
  "data": {
    "balance": 450,
    "totalParticipations": 15,
    "completedParticipations": 8,
    "eligibleMissions": {
      "daily": 3,
      "weekly": 2,
      "monthly": 1,
      "custom": 4
    },
    "completedEligibleMissions": {
      "daily": 2,
      "weekly": 1,
      "monthly": 0,
      "custom": 1
    }
  }
}

Error Responses:

1. Missing Authorization Header (401):
{
  "success": false,
  "message": "Authorization header is required"
}

2. Invalid Token Format (401):
{
  "success": false,
  "message": "Invalid token format"
}

3. Expired Token (401):
{
  "success": false,
  "message": "Token has expired"
}

4. Unable to Extract User ID (401):
{
  "success": false,
  "message": "Unable to extract user ID from token"
}

5. Server Error (500):
{
  "success": false,
  "message": "Failed to retrieve user mission statistics",
  "error": "Detailed error message"
}

Statistics Calculation Logic:
- totalParticipations: Count of all mission participation records for the user
- completedParticipations: Count of mission participation records where isCompleted = true
- eligibleMissions: Count of currently active missions (startDate <= now <= endDate) that the user has not participated in yet, grouped by mission type
- completedEligibleMissions: Count of currently active missions that the user has participated in and completed, grouped by mission type

Notes:
- Only currently active missions are considered for eligible/completed eligible counts
- A mission is considered "active" if the current timestamp falls between its startDate and endDate
- Eligible missions exclude missions the user has already participated in (regardless of completion status)
- Completed eligible missions include only missions the user has both participated in AND completed
- The endpoint uses JWT token authentication similar to other /makroz/extended-users/me/ endpoints
- Statistics are calculated in real-time based on current mission participation data

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/extended-users/me/missions/stats \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
