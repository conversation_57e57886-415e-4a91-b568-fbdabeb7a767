MAKROZ PUBLIC API - MISSION COMPLETION ENDPOINT
==============================================

This endpoint allows users to complete missions after all objectives have been fulfilled.

POST /api/v1/makroz/missions/:missionId/complete
-----------------------------------------------

Description: Completes a mission for the authenticated user. This endpoint:
1. Extracts user ID from JWT token
2. Finds the corresponding extended user
3. Validates the mission exists
4. Checks if user has started the mission (has participation record)
5. Verifies all mission objective assignments are complete
6. Increments user points by the mission reward value
7. Marks the mission participation as complete

Authentication: Bearer JWT Token (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the external ID to identify the user

Request:
- Method: POST
- URL: /api/v1/makroz/missions/{missionId}/complete
- Path Parameters:
  - missionId (number): The ID of the mission to complete
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
- Body: None (empty request body)

Success Response (200):
{
  "success": true,
  "message": "Mission completed successfully",
  "data": {
    "missionId": number,
    "userId": number,
    "pointsAwarded": number,
    "newPointsTotal": number
  }
}

Error Responses:

400 Bad Request - Invalid mission ID:
{
  "success": false,
  "message": "Valid mission ID is required"
}

400 Bad Request - Objectives not complete:
{
  "success": false,
  "message": "Not all mission objectives are completed yet",
  "data": {
    "totalObjectives": number,
    "completedObjectives": number
  }
}

401 Unauthorized - Missing authorization header:
{
  "success": false,
  "message": "Authorization header is required"
}

401 Unauthorized - Invalid token:
{
  "success": false,
  "message": "Unable to extract user ID from token"
}

404 Not Found - Extended user not found:
{
  "success": false,
  "message": "Extended user not found"
}

404 Not Found - Mission not found:
{
  "success": false,
  "message": "Mission not found"
}

404 Not Found - Mission participation not found:
{
  "success": false,
  "message": "Mission participation not found. User must start the mission first."
}

409 Conflict - Mission already completed:
{
  "success": false,
  "message": "Mission is already completed"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Failed to complete mission",
  "error": "Error details"
}

Business Logic:
- User must have an extended user record (created automatically when starting missions)
- User must have started the mission (have a participation record with isCompleted: false)
- All mission objectives must be completed before the mission can be completed
- Objective completion is determined by comparing progress against targetValue using the operator:
  - EQUAL (eq): progress === targetValue
  - NOT_EQUAL (ne): progress !== targetValue  
  - GREATER_THAN (gt): progress > targetValue
  - LESS_THAN (lt): progress < targetValue
  - GREATER_EQUAL (ge): progress >= targetValue
  - LESS_EQUAL (le): progress <= targetValue
- Points are awarded only once per mission completion
- The operation is atomic - both point increment and participation completion happen in a transaction

Prerequisites:
1. User must have a valid JWT token
2. User must have started the mission via POST /api/v1/makroz/missions/:id/participations
3. All mission objective assignments must be complete (progress meets target criteria)

Example Usage:

curl -X POST http://localhost:3000/api/v1/makroz/missions/1/complete \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

Example Success Response:
{
  "success": true,
  "message": "Mission completed successfully",
  "data": {
    "missionId": 1,
    "userId": 12345,
    "pointsAwarded": 100,
    "newPointsTotal": 350
  }
}

Notes:
- This is a public endpoint that does not require admin authentication
- The endpoint uses JWT token authentication similar to other /makroz/ public endpoints
- Mission completion is irreversible - once completed, it cannot be undone
- Points are added to the user's current point total
- The endpoint ensures data consistency using database transactions
- All objective assignments for the mission must be complete before completion is allowed
- Users can only complete missions they have previously started
