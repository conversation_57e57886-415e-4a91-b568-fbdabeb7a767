MAKROZ ENTITIES - RELATIONSHIPS AND <PERSON><PERSON><PERSON><PERSON><PERSON> KEYS
===============================================

This document clarifies the relationships between custom entities and their foreign key constraints.

ENTITY OVERVIEW
===============

1. ExtendedUser
   - Primary Key: id (auto-generated)
   - Unique Key: externalId (integer) - References external system user ID
   - Fields: externalId, points, createdAt, updatedAt

2. Mission
   - Primary Key: id (auto-generated)
   - Fields: name, missionType, reward, description, startDate, endDate, createdAt, updatedAt

3. MissionRule
   - Primary Key: id (auto-generated)
   - Fields: ruleType, compare, compareValue, minDate, maxDate, createdAt, updatedAt

4. MissionObjective
   - Primary Key: id (auto-generated)
   - Foreign Key: missionId -> Mission.id (CASCADE DELETE)
   - Fields: missionId, objectiveType, subtype, operator, targetValue, description, timeframeStart, timeframeEnd, metadata, createdAt, updatedAt

5. MissionParticipation
   - Primary Key: id (auto-generated)
   - Foreign Key: missionId -> Mission.id (CASCADE DELETE)
   - Foreign Key: userId -> ExtendedUser.externalId (CASCADE DELETE)
   - Unique Constraint: (userId, missionId) - One participation per user per mission
   - Fields: userId, missionId, isCompleted, createdAt, updatedAt

6. MissionRuleAssignment
   - Primary Key: id (auto-generated)
   - Foreign Key: missionId -> Mission.id (CASCADE DELETE)
   - Foreign Key: missionRuleId -> MissionRule.id (CASCADE DELETE)
   - Unique Constraint: (missionId, missionRuleId) - One assignment per mission-rule pair
   - Fields: missionId, missionRuleId, createdAt, updatedAt

CRITICAL RELATIONSHIP CLARIFICATION
===================================

MissionParticipation.userId -> ExtendedUser.externalId
------------------------------------------------------

IMPORTANT: The 'userId' field in MissionParticipation does NOT reference ExtendedUser.id
Instead, it references ExtendedUser.externalId

This is because:
- ExtendedUser.externalId represents the user ID from the external system (EbetLab)
- MissionParticipation.userId stores the external system user ID
- This maintains consistency with the external system's user identification

Example:
- ExtendedUser: { id: 1, externalId: 12345, points: 100 }
- MissionParticipation: { id: 1, userId: 12345, missionId: 1, isCompleted: false }
- The foreign key constraint links userId (12345) to externalId (12345)

FOREIGN KEY CONSTRAINTS
=======================

Database Level:
- FK_MISSION_OBJECTIVES_MISSION_ID: MissionObjective.missionId -> Mission.id
- FK_MISSION_PARTICIPATIONS_MISSION_ID: MissionParticipation.missionId -> Mission.id
- FK_MISSION_PARTICIPATIONS_USER_EXTERNAL_ID: MissionParticipation.userId -> ExtendedUser.externalId
- FK_MISSION_RULE_ASSIGNMENTS_MISSION_ID: MissionRuleAssignment.missionId -> Mission.id
- FK_MISSION_RULE_ASSIGNMENTS_MISSION_RULE_ID: MissionRuleAssignment.missionRuleId -> MissionRule.id

Application Level:
- All controllers validate foreign key existence before creating/updating records
- Proper error messages returned when referenced entities don't exist
- Unique constraints enforced to prevent duplicate relationships

RELATIONSHIP TYPES
==================

One-to-Many:
- Mission -> MissionObjective (one mission can have multiple objectives)
- Mission -> MissionParticipation (one mission can have multiple participants)
- ExtendedUser -> MissionParticipation (one user can participate in multiple missions)

Many-to-Many (via junction tables):
- Mission <-> MissionRule (via MissionRuleAssignment)
  - One mission can have multiple rules
  - One rule can be used by multiple missions

ENTITY LOADING PATTERNS
=======================

When loading entities, relationships are included:

MissionParticipation:
- Always loads: mission, user
- Provides complete participation context

MissionObjective:
- Always loads: mission
- Provides mission context for objectives

MissionRuleAssignment:
- Always loads: mission, missionRule
- Provides complete assignment context

DATA INTEGRITY GUARANTEES
=========================

1. Referential Integrity:
   - All foreign keys enforced at database level
   - CASCADE DELETE ensures orphaned records are cleaned up

2. Unique Constraints:
   - One participation per user per mission
   - One assignment per mission-rule pair
   - Unique external IDs for extended users

3. Application Validation:
   - Foreign key existence validated before creation
   - Proper error handling for constraint violations
   - Type safety enforced through TypeScript

4. Shared Enums:
   - Consistent enum values across entities
   - No duplication of enum definitions
   - Type safety for enum fields

MIGRATION HISTORY
=================

001: Create extended_users table
002: Create missions table
003: Create mission_rules table
004: Create mission_participations table (missing user FK initially)
005: Create mission_rule_assignments table
006: Create mission_objectives table
007: Add missing foreign key constraint for mission_participations.userId -> extended_users.externalId

This ensures complete data integrity and proper relationships between all entities.
