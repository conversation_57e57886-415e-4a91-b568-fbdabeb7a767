# Frontend API Reference - Mission Claims System

This document provides a comprehensive API reference for implementing the mission claims system on the frontend.

## Authentication

All endpoints require JWT Bearer token authentication:
```
Authorization: Bearer <JWT_TOKEN>
```

The JWT token's `sub` field contains the user's external ID used for identification.

## 1. User Profile & Extended User

### Create/Update Extended User
**POST** `/api/v1/makroz/extended-users/me`

Creates or updates the authenticated user's extended profile.

**Response:**
```json
{
  "success": true,
  "message": "Extended user created/updated successfully",
  "data": {
    "id": 1,
    "externalId": 12345,
    "points": 0,
    "external_username": "user123",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### Get Extended User Profile
**GET** `/api/v1/makroz/extended-users/me`

Retrieves the authenticated user's extended profile.

**Response:** Same as POST response above.

---

## 2. Mission Discovery & Participation

### Get Eligible Missions
**GET** `/api/v1/makroz/extended-users/me/missions`

Returns all missions the user is eligible to participate in, including their current progress.

**Response:**
```json
{
  "success": true,
  "message": "Eligible missions retrieved successfully",
  "data": {
    "eligibleMissions": [
      {
        "id": 1,
        "name_i18n": {"en": "Daily Login", "tr": "Günlük Giriş"},
        "description_i18n": {"en": "Login daily", "tr": "Günlük giriş yap"},
        "missionType": "daily",
        "rewardValue": 50,
        "startDate": 1704067200,
        "endDate": 1735689599,
        "objectives": [
          {
            "id": 1,
            "objectiveType": "login_count",
            "targetValue": 1,
            "compareOperator": ">=",
            "metadata": {}
          }
        ],
        "userAssignments": [
          {
            "id": 1,
            "userId": 12345,
            "objectiveId": 1,
            "progress": 0,
            "isCompleted": false,
            "startDate": "2024-01-01T00:00:00.000Z",
            "endDate": "2024-01-02T00:00:00.000Z"
          }
        ],
        "latestParticipation": {
          "id": 1,
          "userId": 12345,
          "missionId": 1,
          "isCompleted": false,
          "createdAt": "2024-01-01T00:00:00.000Z",
          "nextAllowedParticipationSeconds": 3600
        }
      }
    ],
    "totalEligible": 5,
    "totalActive": 10
  }
}
```

### Start Mission Participation
**POST** `/api/v1/makroz/missions/:id/participations`

Starts participation in a specific mission.

**Parameters:**
- `id` (path): Mission ID

**Response:**
```json
{
  "success": true,
  "message": "Mission participation started successfully",
  "data": {
    "missionId": 1,
    "userId": 12345,
    "message": "You have successfully joined the mission and all objectives have been assigned to you."
  }
}
```

### Complete Mission
**POST** `/api/v1/makroz/missions/:missionId/complete`

Completes a mission after all objectives are fulfilled.

**Parameters:**
- `missionId` (path): Mission ID

**Response:**
```json
{
  "success": true,
  "message": "Mission completed successfully",
  "data": {
    "missionId": 1,
    "userId": 12345,
    "pointsAwarded": 50,
    "newPointsTotal": 350
  }
}
```

---

## 3. Mission Statistics & Progress

### Get Mission Statistics
**GET** `/api/v1/makroz/extended-users/me/missions/stats`

Returns comprehensive mission statistics for the user.

**Response:**
```json
{
  "success": true,
  "message": "User mission statistics retrieved successfully",
  "data": {
    "balance": 350,
    "totalParticipations": 15,
    "completedParticipations": 12,
    "eligibleMissions": {
      "daily": 3,
      "weekly": 2,
      "monthly": 1,
      "custom": 0
    },
    "completedEligibleMissions": {
      "daily": 2,
      "weekly": 1,
      "monthly": 0,
      "custom": 0
    }
  }
}
```

### Get Mission Reset Times
**GET** `/api/v1/makroz/missions/reset-times`

Returns countdown timers for mission resets.

**Response:**
```json
{
  "success": true,
  "message": "Mission reset times retrieved successfully",
  "data": {
    "daily": {
      "secondsUntilReset": 3600,
      "resetTime": "2024-01-02T00:00:00.000Z"
    },
    "weekly": {
      "secondsUntilReset": 86400,
      "resetTime": "2024-01-08T00:00:00.000Z"
    },
    "monthly": {
      "secondsUntilReset": 2592000,
      "resetTime": "2024-02-01T00:00:00.000Z"
    }
  }
}
```

---

## 4. Final Mission Claims

### Claim Final Mission Reward
**POST** `/api/v1/makroz/final-mission-claims`

Claims the final reward for completing all missions of a specific type.

**Request Body:**
```json
{
  "claimType": "daily" | "weekly" | "monthly"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Final mission reward claimed successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "claimType": "daily",
    "grantedReward": 150,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "user": {
      "id": 1,
      "externalId": 12345,
      "points": 500,
      "external_username": "user123"
    }
  }
}
```

### Get Latest Final Mission Claims
**GET** `/api/v1/makroz/final-mission-claims/me`

Returns the user's latest final mission claims for each type.

**Response:**
```json
{
  "success": true,
  "message": "Latest final mission claims retrieved successfully",
  "data": {
    "daily": {
      "id": 1,
      "userId": 12345,
      "claimType": "daily",
      "grantedReward": 150,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "user": {
        "id": 1,
        "externalId": 12345,
        "points": 500,
        "external_username": "user123"
      }
    },
    "weekly": null,
    "monthly": null
  }
}
```

---

## 5. Transaction History

### Get User Transactions
**GET** `/api/v1/makroz/extended-users/me/transactions`

Returns the user's transaction history with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `type` (optional): Filter by transaction type
- `category` (optional): Filter by transaction category

**Response:**
```json
{
  "success": true,
  "message": "User transactions retrieved successfully",
  "data": [
    {
      "id": 1,
      "fromUserId": null,
      "toUserId": 12345,
      "type": "deposit",
      "category": "mission_reward",
      "amount": 50,
      "metadata": {
        "missionId": 1,
        "missionName": "Daily Login"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "fromUser": null,
      "toUser": {
        "id": 1,
        "externalId": 12345,
        "external_username": "user123"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "totalPages": 2
  }
}
```

---

## Error Responses

All endpoints may return these common error responses:

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authorization header is required"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "error": "Detailed error message"
}
```

---

## Implementation Notes

1. **Authentication**: All endpoints require valid JWT tokens
2. **Rate Limiting**: Mission participation has cooldown periods
3. **Validation**: Mission rules are validated before participation
4. **Atomicity**: Mission completion and point awards use database transactions
5. **Real-time**: Statistics and progress are calculated in real-time
6. **Internationalization**: Mission names/descriptions support i18n with `name_i18n` and `description_i18n` fields
