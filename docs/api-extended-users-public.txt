MAKROZ PUBLIC API - EXTENDED USERS ENDPOINTS
===========================================

POST /api/v1/makroz/extended-users/me
------------------------------------

Description: Creates an extended user record based on JWT token authentication.
If the user already exists, returns the existing record.

Authentication: Bearer JWT <PERSON>ken (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the external ID for the extended user

Request:
- Method: POST
- URL: /api/v1/makroz/extended-users/me
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
  - Content-Type: application/json
- Body: None (empty request body)

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "id": number,
    "externalId": number,
    "points": number,
    "createdAt": string,
    "updatedAt": string
  }
}

Success Responses:

1. New User Created (201):
{
  "success": true,
  "message": "Extended user created successfully",
  "data": {
    "id": 1,
    "externalId": 12345,
    "points": 0,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T10:30:00.000Z"
  }
}

2. Existing User Returned (200):
{
  "success": true,
  "message": "Extended user already exists",
  "data": {
    "id": 1,
    "externalId": 12345,
    "points": 150,
    "createdAt": "2025-06-20T08:15:00.000Z",
    "updatedAt": "2025-06-24T14:22:00.000Z"
  }
}

Error Responses:

1. Missing Authorization Header (401):
{
  "success": false,
  "message": "Authorization header is required"
}

2. Invalid Token (401):
{
  "success": false,
  "message": "Invalid token format"
}

3. Expired Token (401):
{
  "success": false,
  "message": "Token has expired"
}

4. Unable to Extract User ID (401):
{
  "success": false,
  "message": "Unable to extract user ID from token"
}

5. Server Error (500):
{
  "success": false,
  "message": "Failed to create extended user",
  "error": "Detailed error message"
}

Notes:
- New extended users are always initialized with 0 points
- The external ID is extracted from the JWT token's "sub" field
- If an extended user with the same external ID already exists, the existing record is returned
- The endpoint does not require admin authentication (unlike other extended user endpoints)
- The JWT token is validated for structure and expiration before processing

Example Usage:

curl -X POST http://localhost:3000/api/v1/makroz/extended-users/me \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"


GET /api/v1/makroz/extended-users/me
-----------------------------------

Description: Retrieves an extended user record based on JWT token authentication.
Returns 404 if the user doesn't exist.

Authentication: Bearer JWT Token (required)
- The JWT token must be provided in the Authorization header
- The token's "sub" field is used as the external ID to find the extended user

Request:
- Method: GET
- URL: /api/v1/makroz/extended-users/me
- Headers:
  - Authorization: Bearer <JWT_TOKEN>
- Body: None

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "id": number,
    "externalId": number,
    "points": number,
    "createdAt": string,
    "updatedAt": string
  }
}

Success Response (200):
{
  "success": true,
  "message": "Extended user retrieved successfully",
  "data": {
    "id": 1,
    "externalId": 12345,
    "points": 150,
    "createdAt": "2025-06-20T08:15:00.000Z",
    "updatedAt": "2025-06-24T14:22:00.000Z"
  }
}

Error Responses:

1. Missing Authorization Header (401):
{
  "success": false,
  "message": "Authorization header is required"
}

2. Invalid Token (401):
{
  "success": false,
  "message": "Invalid token format"
}

3. Expired Token (401):
{
  "success": false,
  "message": "Token has expired"
}

4. Unable to Extract User ID (401):
{
  "success": false,
  "message": "Unable to extract user ID from token"
}

5. User Not Found (404):
{
  "success": false,
  "message": "Extended user not found"
}

6. Server Error (500):
{
  "success": false,
  "message": "Failed to retrieve extended user",
  "error": "Detailed error message"
}

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/extended-users/me \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."


GET /api/v1/makroz/extended-users/:externalId
--------------------------------------------

Description: Retrieves an extended user record by external ID.
No authentication required - this is a public endpoint.

Request:
- Method: GET
- URL: /api/v1/makroz/extended-users/{externalId}
- Path Parameters:
  - externalId (number): The external ID of the user to retrieve
- Headers: None required
- Body: None

Response Format:
{
  "success": boolean,
  "message": string,
  "data": {
    "id": number,
    "externalId": number,
    "points": number,
    "createdAt": string,
    "updatedAt": string
  }
}

Success Response (200):
{
  "success": true,
  "message": "Extended user retrieved successfully",
  "data": {
    "id": 1,
    "externalId": 12345,
    "points": 150,
    "createdAt": "2025-06-20T08:15:00.000Z",
    "updatedAt": "2025-06-24T14:22:00.000Z"
  }
}

Error Responses:

1. Invalid External ID (400):
{
  "success": false,
  "message": "Valid external ID is required"
}

2. User Not Found (404):
{
  "success": false,
  "message": "Extended user not found"
}

3. Server Error (500):
{
  "success": false,
  "message": "Failed to retrieve extended user",
  "error": "Detailed error message"
}

Example Usage:

curl -X GET http://localhost:3000/api/v1/makroz/extended-users/12345
