MAKROZ ADMIN API - MISSION PARTICIPATIONS
========================================

Mission Participations track user progress on missions. Each participation represents a user's enrollment in a specific mission and their completion status.

Entity Structure:
- id: Primary key (auto-generated)
- userId: Integer (references extended users)
- missionId: Integer (references missions with foreign key)
- isCompleted: <PERSON><PERSON><PERSON> (default: false)
- createdAt: Timestamp
- updatedAt: Timestamp

Unique Constraint: One participation per user per mission (userId + missionId)

POST /api/v1/makroz/admin/mission-participations
-----------------------------------------------

Description: Creates a new mission participation record.

Authentication: Admin authentication required

Request:
- Method: POST
- URL: /api/v1/makroz/admin/mission-participations
- Headers:
  - Authorization: Bearer <ADMIN_TOKEN>
  - Content-Type: application/json
- Body:
{
  "userId": number,
  "missionId": number,
  "isCompleted": boolean (optional, default: false)
}

Success Response (201):
{
  "success": true,
  "message": "Mission participation created successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "missionId": 1,
    "isCompleted": false,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T10:30:00.000Z",
    "mission": {
      "id": 1,
      "name": "Daily Login",
      "missionType": "daily",
      "reward": 100,
      "description": "Login daily to earn rewards",
      "startDate": 1703000000,
      "endDate": 1703086400,
      "createdAt": "2025-06-20T08:00:00.000Z",
      "updatedAt": "2025-06-20T08:00:00.000Z"
    }
  }
}

GET /api/v1/makroz/admin/mission-participations
----------------------------------------------

Description: Retrieves mission participations with filtering and pagination.

Authentication: Admin authentication required

Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- sortBy: 'id' | 'userId' | 'missionId' | 'isCompleted' | 'createdAt' | 'updatedAt' (default: 'createdAt')
- sortOrder: 'ASC' | 'DESC' (default: 'DESC')
- userId: number (filter by user ID)
- missionId: number (filter by mission ID)
- isCompleted: boolean (filter by completion status)
- createdAtFrom: number (timestamp filter)
- createdAtTo: number (timestamp filter)
- updatedAtFrom: number (timestamp filter)
- updatedAtTo: number (timestamp filter)
- search: string (searches userId and missionId)

Success Response (200):
{
  "success": true,
  "message": "Mission participations retrieved successfully",
  "data": {
    "missionParticipations": [...],
    "total": 50,
    "page": 1,
    "limit": 20,
    "totalPages": 3
  }
}

GET /api/v1/makroz/admin/mission-participations/:id
--------------------------------------------------

Description: Retrieves a specific mission participation by ID.

Authentication: Admin authentication required

Path Parameters:
- id: number (mission participation ID)

Success Response (200):
{
  "success": true,
  "message": "Mission participation retrieved successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "missionId": 1,
    "isCompleted": true,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T11:45:00.000Z",
    "mission": {
      "id": 1,
      "name": "Daily Login",
      "missionType": "daily",
      "reward": 100,
      "description": "Login daily to earn rewards",
      "startDate": 1703000000,
      "endDate": 1703086400,
      "createdAt": "2025-06-20T08:00:00.000Z",
      "updatedAt": "2025-06-20T08:00:00.000Z"
    }
  }
}

PATCH /api/v1/makroz/admin/mission-participations/:id
----------------------------------------------------

Description: Updates a mission participation record.

Authentication: Admin authentication required

Path Parameters:
- id: number (mission participation ID)

Request Body (all fields optional):
{
  "userId": number,
  "missionId": number,
  "isCompleted": boolean
}

Success Response (200):
{
  "success": true,
  "message": "Mission participation updated successfully",
  "data": {
    "id": 1,
    "userId": 12345,
    "missionId": 1,
    "isCompleted": true,
    "createdAt": "2025-06-25T10:30:00.000Z",
    "updatedAt": "2025-06-25T11:45:00.000Z",
    "mission": {...}
  }
}

DELETE /api/v1/makroz/admin/mission-participations/:id
-----------------------------------------------------

Description: Deletes a mission participation record.

Authentication: Admin authentication required

Path Parameters:
- id: number (mission participation ID)

Success Response (200):
{
  "success": true,
  "message": "Mission participation deleted successfully",
  "data": {
    "id": 1,
    "deletedAt": "2025-06-25T12:00:00.000Z"
  }
}

Common Error Responses:

400 Bad Request:
{
  "success": false,
  "message": "User ID is required and must be a positive number"
}

401 Unauthorized:
{
  "success": false,
  "message": "Invalid or expired admin credentials"
}

404 Not Found:
{
  "success": false,
  "message": "Mission participation not found"
}

409 Conflict:
{
  "success": false,
  "message": "Mission participation already exists for this user and mission"
}

500 Internal Server Error:
{
  "success": false,
  "message": "Failed to create mission participation",
  "error": "Detailed error message"
}

GET /api/v1/makroz/admin/mission-participations/mission/:missionId
----------------------------------------------------------------

Description: Retrieves all mission participations for a specific mission.

Authentication: Admin authentication required

Path Parameters:
- missionId: number (mission ID)

Success Response (200):
{
  "success": true,
  "message": "Mission participations retrieved successfully",
  "data": {
    "missionId": 1,
    "participations": [
      {
        "id": 1,
        "userId": 12345,
        "missionId": 1,
        "isCompleted": true,
        "createdAt": "2025-06-25T10:30:00.000Z",
        "updatedAt": "2025-06-25T11:45:00.000Z",
        "mission": {...}
      },
      ...
    ],
    "totalParticipations": 15
  }
}

GET /api/v1/makroz/admin/mission-participations/mission/:missionId/stats
-----------------------------------------------------------------------

Description: Retrieves participation statistics for a specific mission.

Authentication: Admin authentication required

Path Parameters:
- missionId: number (mission ID)

Success Response (200):
{
  "success": true,
  "message": "Mission participation statistics retrieved successfully",
  "data": {
    "missionId": 1,
    "statistics": {
      "totalParticipations": 25,
      "completedParticipations": 18,
      "pendingParticipations": 7,
      "uniqueParticipants": 25,
      "totalExtendedUsers": 150,
      "completionRate": 72.0,
      "participationRate": 16.67
    },
    "breakdown": {
      "completed": {
        "count": 18,
        "percentage": 72.0
      },
      "pending": {
        "count": 7,
        "percentage": 28.0
      }
    }
  }
}

Statistics Explanation:
- totalParticipations: Total number of participation records for this mission
- completedParticipations: Number of completed participations
- pendingParticipations: Number of incomplete participations
- uniqueParticipants: Number of unique users who participated
- totalExtendedUsers: Total number of extended users in the system
- completionRate: Percentage of participations that are completed
- participationRate: Percentage of total users who participated in this mission

PUBLIC ENDPOINTS
===============

POST /api/v1/makroz/missions/:id/participations
-----------------------------------------------

Description: Allows users to start participating in a mission. This endpoint extracts the user ID from the JWT token, validates the user against mission rules, creates a mission participation record, and automatically creates mission objective assignments for all objectives associated with the mission.

Authentication: JWT Bearer token required

Path Parameters:
- id: Mission ID (integer, required)

Request Headers:
- Authorization: Bearer <JWT_TOKEN> (required)

Request Body: None required

Response (201 Created):
```json
{
  "success": true,
  "message": "Mission participation started successfully",
  "data": {
    "missionId": 1,
    "userId": 123,
    "message": "You have successfully joined the mission and all objectives have been assigned to you."
  }
}
```

Response (409 Conflict - Already participating):
```json
{
  "success": false,
  "message": "You are already participating in this mission"
}
```

Response (404 Not Found - Mission doesn't exist):
```json
{
  "success": false,
  "message": "Mission with this ID does not exist"
}
```

Response (401 Unauthorized - Invalid/missing token):
```json
{
  "success": false,
  "message": "Invalid token"
}
```

Response (403 Forbidden - Mission rules not satisfied):
```json
{
  "success": false,
  "message": "You do not meet the mission requirements: totalDeposit ge 100 (actual: 50), vipRank eq gold (actual: silver)"
}
```

Response (502 Bad Gateway - Unable to fetch customer data):
```json
{
  "success": false,
  "message": "Unable to fetch customer data for rule validation"
}
```

Rule Validation Process:
- User JWT token is used only to extract the user's external ID
- Admin authentication is performed using API_USERNAME, API_PASSWORD, and API_OTP_SECRET from environment
- Generates TOTP from API_OTP_SECRET for 2FA authentication with EbetLab
- Obtains admin JWT token through EbetLab login process
- Fetches customer data from EbetLab via internal proxy endpoint using admin token
- Validates all mission rules assigned to the mission
- Supports all rule types: joinedAt, totalDeposit, totalWithdraw, netProfit, vipRank, country, phoneNumber, kycLevel, referrer
- Supports all comparison operators: eq, ne, gt, lt, ge, le
- Respects date range filters (minDate/maxDate) on rules
- Returns detailed error messages showing which rules failed and actual vs expected values

Transaction Behavior:
- Uses database transactions to ensure atomicity
- Validates mission rules before creating participation
- Creates mission participation record
- Creates mission objective assignments for all mission objectives
- If any step fails, all changes are rolled back
- Extended user is created automatically if doesn't exist

Notes:
- The unique constraint ensures one participation per user per mission
- Mission relationship is automatically loaded with participation data
- Completion status can be toggled via PATCH requests
- Admin endpoints require admin authentication
- Public endpoint requires JWT Bearer token authentication
- Foreign key constraint ensures mission exists before creating participation
- Statistics are calculated in real-time and include completion and participation rates
- Transaction support ensures data consistency during mission deletion and participation creation
